import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:io';

import 'social_media_feed_detail_page.dart';
import 'login_page.dart';

class SocialMediaFeedsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedSocialMediaFeeds;
  final bool isFromDetailPage;

  const SocialMediaFeedsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedSocialMediaFeeds,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _SocialMediaFeedsPageState createState() => _SocialMediaFeedsPageState();
}

class _SocialMediaFeedsPageState extends State<SocialMediaFeedsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('social_media_feeds_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _socialMediaFeeds = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _selectedPlatform = 'All';
  List<String> _platformOptions = ['All'];

  @override
  void initState() {
    super.initState();
    print("SocialMediaFeedsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedSocialMediaFeeds != null &&
        widget.preloadedSocialMediaFeeds!.isNotEmpty) {
      setState(() {
        _socialMediaFeeds = List.from(widget.preloadedSocialMediaFeeds!);
        _isLoading = false;
        _updatePlatformOptions();
      });
    } else {
      _loadSocialMediaFeedsFromDatabase();
    }
  }

  void _updatePlatformOptions() {
    Set<String> platforms = {'All'};
    for (var feed in _socialMediaFeeds) {
      if (feed['platform'] != null && (feed['platform'] as String).isNotEmpty) {
        platforms.add(feed['platform'] as String);
      }
    }
    setState(() {
      _platformOptions = platforms.toList()..sort();
    });
  }

  void _setupRealtime() {
    final socialMediaFeedsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';
    _realtimeChannel = Supabase.instance.client
        .channel('social_media_feeds_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: socialMediaFeedsTableName,
      callback: (payload) async {
        print("Realtime update received for social media feeds: ${payload.eventType}");
        _loadSocialMediaFeedsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadSocialMediaFeedsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _socialMediaFeeds = [];
    });

    await _loadMoreSocialMediaFeeds();
  }

  Future<void> _loadMoreSocialMediaFeeds() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final socialMediaFeedsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';
      var query = Supabase.instance.client
          .from(socialMediaFeedsTableName)
          .select('*');
      
      if (_selectedPlatform != 'All') {
        query = query.eq('platform', _selectedPlatform);
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _socialMediaFeeds.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
        _updatePlatformOptions();
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading social media feeds: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading social media feeds: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreSocialMediaFeeds();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return FontAwesomeIcons.facebook;
      case 'twitter':
      case 'x':
        return FontAwesomeIcons.twitter;
      case 'instagram':
        return FontAwesomeIcons.instagram;
      case 'linkedin':
        return FontAwesomeIcons.linkedin;
      case 'youtube':
        return FontAwesomeIcons.youtube;
      case 'tiktok':
        return FontAwesomeIcons.tiktok;
      case 'snapchat':
        return FontAwesomeIcons.snapchat;
      case 'pinterest':
        return FontAwesomeIcons.pinterest;
      case 'reddit':
        return FontAwesomeIcons.reddit;
      case 'whatsapp':
        return FontAwesomeIcons.whatsapp;
      case 'telegram':
        return FontAwesomeIcons.telegram;
      case 'discord':
        return FontAwesomeIcons.discord;
      case 'github':
        return FontAwesomeIcons.github;
      case 'medium':
        return FontAwesomeIcons.medium;
      default:
        return FontAwesomeIcons.globe;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Social Media Feeds',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Platform filter dropdown
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Filter by Platform: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedPlatform,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface,
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.colorScheme.surface,
                        items: _platformOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != _selectedPlatform) {
                            setState(() {
                              _selectedPlatform = newValue;
                              _page = 0;
                              _socialMediaFeeds = [];
                              _hasMore = true;
                            });
                            _loadMoreSocialMediaFeeds();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Social media feeds list
          Expanded(
            child: VisibilityDetector(
              key: const Key('social_media_feeds_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _socialMediaFeeds.isEmpty && !_isLoading) {
                  _loadSocialMediaFeedsFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadSocialMediaFeedsFromDatabase,
                child: _socialMediaFeeds.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No social media feeds found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _socialMediaFeeds.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _socialMediaFeeds.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildSocialMediaFeedCard(
                            _socialMediaFeeds[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildSocialMediaFeedCard(
    Map<String, dynamic> feed,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = feed['fullname'] ?? 'Unknown';
    final String platform = feed['platform'] ?? '';
    final String link = feed['link'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SocialMediaFeedDetailPage(
                socialMediaFeed: feed,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: FaIcon(
                  _getPlatformIcon(platform),
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (platform.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          platform,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (link.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          link,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
