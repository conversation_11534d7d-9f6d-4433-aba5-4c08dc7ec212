import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

class CsvExtractor {
  /// Extract text from a CSV file
  static Future<String> extractText(Uint8List bytes) async {
    try {
      // Try to decode the CSV file with UTF-8 encoding
      String csvContent;
      try {
        csvContent = utf8.decode(bytes);
      } catch (e) {
        // If UTF-8 decoding fails, try Latin-1 encoding
        csvContent = latin1.decode(bytes);
      }

      // Return the raw CSV content
      return csvContent;
    } catch (e) {
      return '[Error extracting text from CSV: $e]';
    }
  }

  /// Extract data from a CSV file as a list of maps
  /// This is useful for data analysis
  static Future<List<Map<String, dynamic>>> extractData(Uint8List bytes) async {
    try {
      final List<Map<String, dynamic>> result = [];
      
      // Try to decode the CSV file with UTF-8 encoding
      String csvContent;
      try {
        csvContent = utf8.decode(bytes);
      } catch (e) {
        // If UTF-8 decoding fails, try Latin-1 encoding
        csvContent = latin1.decode(bytes);
      }

      // Split the CSV content into lines
      final lines = csvContent.split(RegExp(r'\r?\n'));
      if (lines.isEmpty) {
        return [];
      }

      // Parse the header row
      final headerLine = lines.first;
      final headers = _parseCsvLine(headerLine);
      if (headers.isEmpty) {
        return [];
      }

      // Parse the data rows
      for (int i = 1; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.isEmpty) {
          continue;
        }

        final values = _parseCsvLine(line);
        if (values.isEmpty || values.length != headers.length) {
          continue;
        }

        final Map<String, dynamic> rowData = {};
        for (int j = 0; j < headers.length; j++) {
          final value = values[j];
          
          // Try to convert to number if possible
          final num? numValue = num.tryParse(value);
          if (numValue != null) {
            rowData[headers[j]] = numValue;
          } else {
            rowData[headers[j]] = value;
          }
        }

        result.add(rowData);
      }

      return result;
    } catch (e) {
      print('Error extracting data from CSV: $e');
      return [];
    }
  }

  /// Parse a CSV line into a list of values
  /// Handles quoted values and commas within quotes
  static List<String> _parseCsvLine(String line) {
    if (line.isEmpty) {
      return [];
    }

    final List<String> result = [];
    bool inQuotes = false;
    StringBuffer currentValue = StringBuffer();

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        // Check if this is an escaped quote (double quote)
        if (i + 1 < line.length && line[i + 1] == '"') {
          currentValue.write('"');
          i++; // Skip the next quote
        } else {
          // Toggle quote mode
          inQuotes = !inQuotes;
        }
      } else if (char == ',' && !inQuotes) {
        // End of value
        result.add(currentValue.toString().trim());
        currentValue = StringBuffer();
      } else {
        // Add character to current value
        currentValue.write(char);
      }
    }

    // Add the last value
    result.add(currentValue.toString().trim());

    return result;
  }
}
