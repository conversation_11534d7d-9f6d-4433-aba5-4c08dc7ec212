import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'public_art_detail_page.dart';

class PublicArtPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedPublicArt;
  final bool isFromDetailPage;
  final String? roomFilter;
  final String? buildingFilter;

  const PublicArtPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedPublicArt,
    this.isFromDetailPage = false,
    this.roomFilter,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<PublicArtPage> createState() => _PublicArtPageState();
}

class _PublicArtPageState extends State<PublicArtPage> {
  List<Map<String, dynamic>> _publicArt = [];
  List<Map<String, dynamic>> _filteredPublicArt = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late final RealtimeChannel _publicArtChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadPublicArt();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _publicArtChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterPublicArt();
    });
  }

  void _filterPublicArt() {
    if (_searchQuery.isEmpty && widget.roomFilter == null && widget.buildingFilter == null) {
      _filteredPublicArt = List.from(_publicArt);
      return;
    }

    _filteredPublicArt = _publicArt.where((art) {
      // Apply room filter if provided
      if (widget.roomFilter != null && art['room'] != widget.roomFilter) {
        return false;
      }

      // Apply building filter if provided
      if (widget.buildingFilter != null && art['building'] != widget.buildingFilter) {
        return false;
      }

      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          art['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (art['building']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (art['room']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (art['email']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (art['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      return matchesSearch;
    }).toList();
  }

  Future<void> _loadPublicArt() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedPublicArt.isNotEmpty) {
        setState(() {
          _publicArt = widget.preloadedPublicArt;
          _filteredPublicArt = widget.preloadedPublicArt;
          _isLoading = false;
        });
        print('Using preloaded public art data for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterPublicArt();
        }
        // Still fetch in background to refresh cache
        _fetchPublicArtFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _publicArt = cachedData;
          _filteredPublicArt = cachedData;
          _isLoading = false;
        });
        print('Loaded public art from cache for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterPublicArt();
        }
      }

      // Then fetch from Supabase
      await _fetchPublicArtFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading public art: $e';
      });
      print('Error in _loadPublicArt: $e');
    }
  }

  Future<void> _fetchPublicArtFromSupabase() async {
    try {
      final publicArtTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_publicart';
      print('Fetching from table: $publicArtTableName');

      // Create the query
      String query = '*';

      // Create filter conditions if needed
      Map<String, Object> filterConditions = {};
      if (widget.roomFilter != null) {
        filterConditions['room'] = widget.roomFilter!;
      }
      if (widget.buildingFilter != null) {
        filterConditions['building'] = widget.buildingFilter!;
      }

      // Fetch the data
      List<Map<String, dynamic>> response;

      if (filterConditions.isNotEmpty) {
        print('Applying filters: $filterConditions');
        response = await Supabase.instance.client
            .from(publicArtTableName)
            .select(query)
            .match(filterConditions)
            .order('fullname', ascending: true);
      } else {
        response = await Supabase.instance.client
            .from(publicArtTableName)
            .select(query)
            .order('fullname', ascending: true);
      }

      final publicArt = List<Map<String, dynamic>>.from(response);
      print('Fetched ${publicArt.length} public art items from Supabase');

      // Cache the data
      await _saveToCache(publicArt);

      if (mounted) {
        setState(() {
          _publicArt = publicArt;
          _filteredPublicArt = publicArt;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching public art from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_publicArt.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading public art: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'publicart_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} public art items in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached public art found');
      }
    } catch (e) {
      print('Error loading public art from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'publicart_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} public art items to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved public art to cache');
    } catch (e) {
      print('Error saving public art to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final publicArtTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_publicart';
    print('Setting up realtime listener for table: $publicArtTableName');
    _publicArtChannel = Supabase.instance.client
        .channel('publicart_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: publicArtTableName,
          callback: (payload) {
            print('Realtime update received for public art');
            _fetchPublicArtFromSupabase();
          },
        )
        .subscribe();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    String title = 'Public Art';
    if (widget.roomFilter != null) {
      title = 'Art in ${widget.roomFilter}';
    } else if (widget.buildingFilter != null) {
      title = 'Art in ${widget.buildingFilter}';
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('publicart-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _publicArt.isEmpty && !_isLoading) {
            _loadPublicArt();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search art...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadPublicArt,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredPublicArt.isEmpty
                          ? const Center(
                              child: Text('No public art found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredPublicArt.length,
                              itemBuilder: (context, index) {
                                final art = _filteredPublicArt[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      art['fullname'] ?? 'Unnamed Art',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Building: ${art['building'] ?? 'N/A'}'),
                                        Text('Room: ${art['room'] ?? 'N/A'}'),
                                        if (art['email'] != null && art['email'].toString().isNotEmpty)
                                          Text('Contact: ${art['email']}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PublicArtDetailPage(
                                            art: art,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
