import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';

class HelpdeskLinksListPage extends StatefulWidget {
  final String helpdeskName;
  final List<Map<String, dynamic>> links;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const HelpdeskLinksListPage({
    Key? key,
    required this.helpdeskName,
    required this.links,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<HelpdeskLinksListPage> createState() => _HelpdeskLinksListPageState();
}

class _HelpdeskLinksListPageState extends State<HelpdeskLinksListPage> {
  late List<Map<String, dynamic>> _links;
  bool _isLoading = false;
  late RealtimeChannel _linksRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _links = List<Map<String, dynamic>>.from(widget.links);
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _linksRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    final linksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    _linksRealtimeChannel = Supabase.instance.client
        .channel('links_list_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: linksTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert ||
            payload.eventType == PostgresChangeEvent.update ||
            payload.eventType == PostgresChangeEvent.delete) {
          _refreshLinks();
        }
      },
    ).subscribe();
  }

  Future<void> _refreshLinks() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final linksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    final helpdeskName = widget.helpdeskName;

    try {
      final response = await Supabase.instance.client
          .from(linksTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _links = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      }
    } catch (error) {
      print('Error refreshing links: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _launchLink(String url) async {
    if (url.isNotEmpty) {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not launch link.')),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No link available.')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${widget.helpdeskName} Links',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshLinks,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _links.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No links available for ${widget.helpdeskName}.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _links.length,
                    itemBuilder: (context, index) {
                      final link = _links[index];
                      return Card(
                        color: theme.colorScheme.surface,
                        surfaceTintColor: Colors.transparent,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(16),
                          leading: CircleAvatar(
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.link,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          title: Text(
                            link['fullname'] ?? 'Unnamed Link',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          subtitle: link['about'] != null && link['about'].toString().isNotEmpty
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Text(
                                    link['about'],
                                    style: TextStyle(
                                      color: currentIsDarkMode ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7),
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                )
                              : null,
                          trailing: Icon(
                            Icons.open_in_new,
                            color: currentIsDarkMode ? Colors.white.withOpacity(0.5) : Colors.black.withOpacity(0.5),
                            size: 20,
                          ),
                          onTap: () => _launchLink(link['link'] ?? ''),
                        ),
                      );
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}