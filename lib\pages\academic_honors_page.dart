import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_honor_detail_page.dart';
import 'login_page.dart';

class AcademicHonorsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicHonors;
  final bool isFromDetailPage;

  const AcademicHonorsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicHonors,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicHonorsPageState createState() => _AcademicHonorsPageState();
}

class _AcademicHonorsPageState extends State<AcademicHonorsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_honors_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicHonors = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedMajor = 'All Majors';
  List<String> _majors = ['All Majors'];

  @override
  void initState() {
    super.initState();
    print("AcademicHonorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedAcademicHonors != null &&
        widget.preloadedAcademicHonors!.isNotEmpty) {
      setState(() {
        _academicHonors = List.from(widget.preloadedAcademicHonors!);
        _isLoading = false;
        _extractMajors();
      });
    } else {
      _loadAcademicHonorsFromDatabase();
    }
  }

  void _extractMajors() {
    Set<String> majorsSet = {'All Majors'};

    for (var honor in _academicHonors) {
      if (honor['major'] != null && honor['major'].toString().isNotEmpty) {
        majorsSet.add(honor['major']);
      }
    }

    setState(() {
      _majors = majorsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final academicHonorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academichonors';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_honors_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicHonorsTableName,
      callback: (payload) async {
        print("Realtime update received for academic honors: ${payload.eventType}");
        _loadAcademicHonorsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadAcademicHonorsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _academicHonors = [];
    });

    await _loadMoreAcademicHonors();
  }

  Future<void> _loadMoreAcademicHonors() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final academicHonorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academichonors';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply major filter
      if (_selectedMajor != 'All Majors') {
        conditions.add("major.eq.${_selectedMajor}");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(academicHonorsTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _academicHonors.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractMajors();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading academic honors: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading academic honors: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreAcademicHonors();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadAcademicHonorsFromDatabase();
  }

  void _filterByMajor(String major) {
    setState(() {
      _selectedMajor = major;
    });
    _loadAcademicHonorsFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Honors',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search academic honors...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          if (_majors.length > 1)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Major',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                value: _selectedMajor,
                items: _majors.map((String major) {
                  return DropdownMenuItem<String>(
                    value: major,
                    child: Text(
                      major,
                      style: TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    _filterByMajor(newValue);
                  }
                },
              ),
            ),

          const SizedBox(height: 16),

          // Academic honors list
          Expanded(
            child: VisibilityDetector(
              key: const Key('academic_honors_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _academicHonors.isEmpty && !_isLoading) {
                  _loadAcademicHonorsFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadAcademicHonorsFromDatabase,
                child: _academicHonors.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No academic honors found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _academicHonors.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _academicHonors.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildAcademicHonorCard(
                            _academicHonors[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildAcademicHonorCard(
    Map<String, dynamic> honor,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = honor['fullname'] ?? 'Unknown';
    final String major = honor['major'] ?? '';
    final String about = honor['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AcademicHonorDetailPage(
                honor: honor,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.military_tech,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (major.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'For: $major',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
