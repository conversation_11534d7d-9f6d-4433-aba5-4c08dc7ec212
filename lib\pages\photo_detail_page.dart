// photo_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';

class PhotoDetailPage extends StatefulWidget {
  final Map<String, dynamic> photo;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const PhotoDetailPage({
    Key? key,
    required this.photo,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<PhotoDetailPage> createState() => _PhotoDetailPageState();
}

class _PhotoDetailPageState extends State<PhotoDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _photoRealtimeChannel; // Realtime channel for photo updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupPhotoRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _photoRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.photo['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupPhotoRealtimeListener() {
    final photosTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_photos';
    _photoRealtimeChannel = Supabase.instance.client
        .channel('photo_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: photosTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current photo's ID
        if (payload.newRecord['id'] == widget.photo['id']) {
          print("Realtime UPDATE event received for THIS photo (manual filter applied): ${widget.photo['fullname']}");
          _fetchUpdatedPhotoData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER photo, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedPhotoData() async {
    final photosTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_photos';
    try {
      final updatedPhotoResponse = await Supabase.instance.client
          .from(photosTableName)
          .select('*')
          .eq('id', widget.photo['id'])
          .single();

      if (mounted && updatedPhotoResponse != null) {
        Map<String, dynamic> updatedPhoto = Map.from(updatedPhotoResponse);
        // Update the widget.photo with the new data
        setState(() {
          widget.photo.clear(); // Clear old data
          widget.photo.addAll(updatedPhoto); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Photo data updated in detail page for ${widget.photo['fullname']}");
          _updatePhotosCache(updatedPhoto); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated photo data: $error");
    }
  }

  Future<void> _updatePhotosCache(Map<String, dynamic> updatedPhoto) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'photos_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedPhotosJson = prefs.getString(cacheKey);

    if (cachedPhotosJson != null) {
      List<Map<String, dynamic>> cachedPhotos = (jsonDecode(cachedPhotosJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the photo in the cached list
      for (int i = 0; i < cachedPhotos.length; i++) {
        if (cachedPhotos[i]['id'] == updatedPhoto['id']) {
          cachedPhotos[i] = updatedPhoto;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedPhotos));
      print("Photos cache updated with realtime change for ${updatedPhoto['fullname']}");
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> photo) {
    final day = photo['day'] as int?;
    final month = photo['month'] as int?;
    final year = photo['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final link = widget.photo['link'] as String? ?? '';
    final platform = widget.photo['platform'] as String? ?? '';
    final dateStr = _formatDate(widget.photo);

    final bool isLinkAvailable = link.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.photo['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          if (isLinkAvailable)
            IconButton(
              icon: Icon(
                Icons.link,
                color: theme.colorScheme.primary,
              ),
              onPressed: () => _launchURL(link),
              tooltip: 'Visit website',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _isLoadingImage
                ? const SizedBox(height: 300, child: Center(child: CircularProgressIndicator()))
                : CachedNetworkImage(
              imageUrl: _imageUrl,
              placeholder: (context, url) => Image.asset(
                'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                fit: BoxFit.contain,
                height: 300,
              ),
              errorWidget: (context, url, error) => Image.asset(
                'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                fit: BoxFit.contain,
                height: 300,
              ),
              fit: BoxFit.cover,
              height: 300,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.photo['fullname'] ?? '',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (dateStr.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          dateStr,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                      ],
                      if (platform.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Platform: $platform',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                      ],
                      if (isLinkAvailable) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.link),
                          label: const Text('View Original'),
                          onPressed: () => _launchURL(link),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
