// partnership_opportunity_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class PartnershipOpportunityDetailPage extends StatefulWidget {
  final Map<String, dynamic> opportunity;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const PartnershipOpportunityDetailPage({
    Key? key,
    required this.opportunity,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<PartnershipOpportunityDetailPage> createState() => _PartnershipOpportunityDetailPageState();
}

class _PartnershipOpportunityDetailPageState extends State<PartnershipOpportunityDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _opportunityRealtimeChannel; // Realtime channel for opportunity updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupOpportunityRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _opportunityRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.opportunity['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupOpportunityRealtimeListener() {
    final opportunitiesTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';
    _opportunityRealtimeChannel = Supabase.instance.client
        .channel('opportunity_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: opportunitiesTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current opportunity's ID
        if (payload.newRecord['id'] == widget.opportunity['id']) {
          print("Realtime UPDATE event received for THIS opportunity (manual filter applied): ${widget.opportunity['fullname']}");
          _fetchUpdatedOpportunityData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER opportunity, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedOpportunityData() async {
    final opportunitiesTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';
    try {
      final updatedOpportunityResponse = await Supabase.instance.client
          .from(opportunitiesTableName)
          .select('*')
          .eq('id', widget.opportunity['id'])
          .single();

      if (mounted && updatedOpportunityResponse != null) {
        Map<String, dynamic> updatedOpportunity = Map.from(updatedOpportunityResponse);
        // Update the widget.opportunity with the new data
        setState(() {
          widget.opportunity.clear(); // Clear old data
          widget.opportunity.addAll(updatedOpportunity); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Opportunity data updated in detail page for ${widget.opportunity['fullname']}");
          _updateOpportunitiesCache(updatedOpportunity); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated opportunity data: $error");
    }
  }

  Future<void> _updateOpportunitiesCache(Map<String, dynamic> updatedOpportunity) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'partnershipopportunities_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedOpportunitiesJson = prefs.getString(cacheKey);

    if (cachedOpportunitiesJson != null) {
      List<Map<String, dynamic>> cachedOpportunities = (jsonDecode(cachedOpportunitiesJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the opportunity in the cached list
      for (int i = 0; i < cachedOpportunities.length; i++) {
        if (cachedOpportunities[i]['id'] == updatedOpportunity['id']) {
          cachedOpportunities[i] = updatedOpportunity;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedOpportunities));
      print("Opportunities cache updated with realtime change for ${updatedOpportunity['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.opportunity['phone'] as String? ?? '';
    final whatsappNumber = widget.opportunity['whatsapp'] as String? ?? '';
    final email = widget.opportunity['email'] as String? ?? '';
    final about = widget.opportunity['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.opportunity['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                  if (email.isNotEmpty)
                    _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                  if (phone.isNotEmpty)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                  if (whatsappNumber.isNotEmpty)
                    _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isWhatsappAvailable ? 1.0 : 0.5,
                    child: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email' || title == 'WhatsApp';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
