import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'room_assignment_detail_page.dart';

class RoomAssignmentsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedRoomAssignments;
  final bool isFromDetailPage;
  final String? roomFilter;
  final String? buildingFilter;

  const RoomAssignmentsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedRoomAssignments,
    this.isFromDetailPage = false,
    this.roomFilter,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<RoomAssignmentsPage> createState() => _RoomAssignmentsPageState();
}

class _RoomAssignmentsPageState extends State<RoomAssignmentsPage> {
  List<Map<String, dynamic>> _roomAssignments = [];
  List<Map<String, dynamic>> _filteredRoomAssignments = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _roomAssignmentsChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadRoomAssignments();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _roomAssignmentsChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterRoomAssignments();
    });
  }

  void _filterRoomAssignments() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All' && widget.roomFilter == null && widget.buildingFilter == null) {
      _filteredRoomAssignments = List.from(_roomAssignments);
      return;
    }

    _filteredRoomAssignments = _roomAssignments.where((assignment) {
      // Apply room filter if provided
      if (widget.roomFilter != null && assignment['room'] != widget.roomFilter) {
        return false;
      }

      // Apply building filter if provided
      if (widget.buildingFilter != null && assignment['building'] != widget.buildingFilter) {
        return false;
      }

      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          assignment['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (assignment['building']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (assignment['room']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (assignment['gender']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        matchesFilter = assignment['gender'] == _selectedFilter;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadRoomAssignments() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedRoomAssignments.isNotEmpty) {
        setState(() {
          _roomAssignments = widget.preloadedRoomAssignments;
          _filteredRoomAssignments = widget.preloadedRoomAssignments;
          _isLoading = false;
        });
        print('Using preloaded room assignments data for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterRoomAssignments();
        }
        // Still fetch in background to refresh cache
        _fetchRoomAssignmentsFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _roomAssignments = cachedData;
          _filteredRoomAssignments = cachedData;
          _isLoading = false;
        });
        print('Loaded room assignments from cache for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterRoomAssignments();
        }
      }

      // Then fetch from Supabase
      await _fetchRoomAssignmentsFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading room assignments: $e';
      });
      print('Error in _loadRoomAssignments: $e');
    }
  }

  Future<void> _fetchRoomAssignmentsFromSupabase() async {
    try {
      final roomAssignmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomassignments';
      print('Fetching from table: $roomAssignmentsTableName');

      // Create the query
      String query = '*';

      // Create filter conditions if needed
      Map<String, Object> filterConditions = {};
      if (widget.roomFilter != null) {
        filterConditions['room'] = widget.roomFilter!;
      }
      if (widget.buildingFilter != null) {
        filterConditions['building'] = widget.buildingFilter!;
      }

      // Fetch the data
      List<Map<String, dynamic>> response;

      if (filterConditions.isNotEmpty) {
        print('Applying filters: $filterConditions');
        response = await Supabase.instance.client
            .from(roomAssignmentsTableName)
            .select(query)
            .match(filterConditions)
            .order('fullname', ascending: true);
      } else {
        response = await Supabase.instance.client
            .from(roomAssignmentsTableName)
            .select(query)
            .order('fullname', ascending: true);
      }

      final roomAssignments = List<Map<String, dynamic>>.from(response);
      print('Fetched ${roomAssignments.length} room assignments from Supabase');

      // Cache the data
      await _saveToCache(roomAssignments);

      if (mounted) {
        setState(() {
          _roomAssignments = roomAssignments;
          _filteredRoomAssignments = roomAssignments;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching room assignments from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_roomAssignments.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading room assignments: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomassignments_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} room assignments in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached room assignments found');
      }
    } catch (e) {
      print('Error loading room assignments from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomassignments_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} room assignments to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved room assignments to cache');
    } catch (e) {
      print('Error saving room assignments to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final roomAssignmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomassignments';
    print('Setting up realtime listener for table: $roomAssignmentsTableName');
    _roomAssignmentsChannel = Supabase.instance.client
        .channel('roomassignments_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomAssignmentsTableName,
          callback: (payload) {
            print('Realtime update received for room assignments');
            _fetchRoomAssignmentsFromSupabase();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> genderTypes = {'All'};

    for (final assignment in _roomAssignments) {
      if (assignment['gender'] != null && assignment['gender'].toString().isNotEmpty) {
        genderTypes.add(assignment['gender'].toString());
      }
    }

    return genderTypes.toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    String title = 'Room Assignments';
    if (widget.roomFilter != null) {
      title = 'Assignments for ${widget.roomFilter}';
    } else if (widget.buildingFilter != null) {
      title = 'Assignments in ${widget.buildingFilter}';
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('roomassignments-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _roomAssignments.isEmpty && !_isLoading) {
            _loadRoomAssignments();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search assignments...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterRoomAssignments();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadRoomAssignments,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredRoomAssignments.isEmpty
                          ? const Center(
                              child: Text('No assignments found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredRoomAssignments.length,
                              itemBuilder: (context, index) {
                                final assignment = _filteredRoomAssignments[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      assignment['fullname'] ?? 'Unnamed Assignment',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Building: ${assignment['building'] ?? 'N/A'}'),
                                        Text('Room: ${assignment['room'] ?? 'N/A'}'),
                                        Text('Gender: ${assignment['gender'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => RoomAssignmentDetailPage(
                                            assignment: assignment,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
