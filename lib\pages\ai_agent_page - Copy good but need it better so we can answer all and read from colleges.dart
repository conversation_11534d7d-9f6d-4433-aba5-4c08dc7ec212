import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async'; // For TimeoutException
import 'dart:io';    // For SocketException
import 'package:collection/collection.dart'; // For firstWhereOrNull, sortedBy

// --- Helper Classes (Moved Outside State Class) ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping; // Now used for multi-stage indicator
  ChatMessage({required this.text, required this.isUser, this.isTyping = false});

  @override bool operator ==(Object other) => identical(this, other) || other is ChatMessage && runtimeType == other.runtimeType && text == other.text && isUser == other.isUser && isTyping == other.isTyping;
  @override int get hashCode => text.hashCode ^ isUser.hashCode ^ isTyping.hashCode;
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  // Corrected Constructor
  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override Widget build(BuildContext context) {
    if (message.isTyping) {
       return Align( alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration( color: theme.colorScheme.surfaceVariant.withOpacity(0.8), borderRadius: const BorderRadius.only( topLeft: Radius.circular(4.0), topRight: Radius.circular(18.0), bottomLeft: Radius.circular(18.0), bottomRight: Radius.circular(18.0) ) ),
            // Use a Row for better alignment with potential future icons
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                 // Optional: Add a small animated indicator later if desired
                 // SizedBox(width: 12, height: 12, child: CircularProgressIndicator(strokeWidth: 1.5, color: theme.colorScheme.onSurfaceVariant)),
                 // const SizedBox(width: 8),
                 Text( message.text, style: TextStyle( color: theme.colorScheme.onSurfaceVariant, fontStyle: FontStyle.italic, fontSize: 14 ) ),
              ],
            ),
          ),
       );
    }
    final Color userBubbleColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final Color aiBubbleColor = theme.colorScheme.surfaceVariant;
    final Color userTextColor = theme.colorScheme.onSurface;
    final Color aiTextColor = theme.colorScheme.onSurfaceVariant;
    final bubbleColor = message.isUser ? userBubbleColor : aiBubbleColor;
    final textColor = message.isUser ? userTextColor : aiTextColor;
    final borderRadius = BorderRadius.only( topLeft: Radius.circular(message.isUser ? 18.0 : 4.0), topRight: Radius.circular(message.isUser ? 4.0 : 18.0), bottomLeft: const Radius.circular(18.0), bottomRight: const Radius.circular(18.0) );

    return Align( alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints: BoxConstraints( maxWidth: MediaQuery.of(context).size.width * 0.8 ),
        decoration: BoxDecoration( color: bubbleColor, borderRadius: borderRadius, boxShadow: [ BoxShadow( color: Colors.black.withOpacity(0.06), blurRadius: 3, offset: const Offset(1, 2) ) ] ),
        child: SelectableText( message.text, style: TextStyle( color: textColor, fontSize: 15, height: 1.35 ) ),
      ),
    );
  }
}

// --- Extensions (Moved Outside State Class) ---
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return split(' ')
       .map((word) {
         if (word.isEmpty) return "";
         if (word.length > 1 && word == word.toUpperCase()) return word; // Preserve acronyms
         return word[0].toUpperCase() + substring(1).toLowerCase();
       })
       .join(' ');
  }

  // Helper to convert camelCase or snake_case to Title Case
  String readableFieldName() {
    if (isEmpty) return "";
    String spaced = replaceAll('_', ' ');
    spaced = spaced.replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}');
    spaced = spaced.replaceAllMapped(RegExp(r'([A-Z])([A-Z][a-z])'), (match) => '${match.group(1)} ${match.group(2)}');
    return spaced.capitalize();
  }
}


// --- Main Widget ---
class AiAgentPage extends StatefulWidget {
 final bool isDarkMode;
 final VoidCallback toggleTheme;
 final Map<String, dynamic>? collegeData;

 const AiAgentPage({
   Key? key,
   required this.isDarkMode,
   required this.toggleTheme,
   this.collegeData,
 }) : super(key: key);

 @override
 _AiAgentPageState createState() => _AiAgentPageState();
}

// Simpler state - only idle or processing
enum ProcessingStep { idle, processing } // Keep this simple for overall state

class _AiAgentPageState extends State<AiAgentPage> {
 final TextEditingController _messageController = TextEditingController();
 final List<ChatMessage> _messages = [];
 ProcessingStep _processingStep = ProcessingStep.idle; // Simplified state
 final ScrollController _scrollController = ScrollController();

 // --- State Variables ---
 bool _isListening = false;
 bool _isContinuousListening = false;

 // Gemini API configuration
 // WARNING: Replace with your actual API key securely (e.g., environment variables)
 // Using the key provided by the user
 final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s';
 // Using the model specified by the user
 final String _model = 'gemini-2.0-flash-lite'; // Updated model name

 // Daily token tracking (using character count)
 int _dailyTokenCount = 0;
 final int _maxDailyTokens = 300000; // Adjusted limit for multiple calls
 int _lastPromptTokenCount = 0;
 int _lastApiResponseCharCount = 0;

 // Cost rates (Approximate - check Gemini pricing for latest)
 // NOTE: Costs vary by model. These are generic placeholders. Verify against gemini-2.0-flash-lite pricing.
 final double _inputCostPer1kChars = 0.00013125;
 final double _outputCostPer1kChars = 0.00046875;
 final double _exchangeRate = 2000.0; // Example exchange rate

 // Speech & TTS
 late stt.SpeechToText _speechToText;
 late FlutterTts _flutterTts;
 bool _isMuted = false;

 // TTS Voice Variables
 List<Map<String, String>> _availableVoices = [];
 Map<String, String>? _selectedVoice;
 bool _voicesLoaded = false;

 // --- Keyword Maps and Manifest (Keep as provided in previous steps) ---
 final Map<String, List<String>> _tableKeywords = {
      'helpdesks': ['help desk', 'it support', 'tech support', 'library help', 'student services desk', 'assistance', 'computer problem', 'wifi issue', 'password reset'],
      'accessibility': ['accessibility', 'disability', 'disabled', 'ada', 'special needs', 'accomodation', 'access', 'mobility', 'learning support'],
      'faq': ['faq', 'frequently asked', 'questions', 'common questions', 'q&a', 'ask', 'wondering'],
      'links': ['link', 'website', 'url', 'resource', 'webpage', 'portal', 'online form', 'find'],
      'construction': ['construction', 'building work', 'renovation', 'campus updates', 'project', 'noise', 'closure'],
      'printing': ['print', 'printer', 'printing', 'copies', 'photocopy', 'scan', 'cost to print'],
      'daycares': ['daycare', 'childcare', 'nursery', 'kids', 'child care', 'preschool'],
      'sustainability': ['sustainability', 'green', 'environment', 'eco', 'recycling', 'conservation', 'solar'],
      'notices': ['notice', 'alert', 'announcement', 'update', 'important info', 'news', 'bulletin'],
      'socialmediafeeds': ['social media', 'twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube channel'],
      'admissionsprocess': ['admission', 'admissions', 'apply', 'application', 'how to apply', 'enroll', 'acceptance', 'get in', 'prospectus'],
      'registrationprocess': ['registration', 'register', 'enrollment', 'course selection', 'sign up', 'add drop class', 'choose courses', 'academic planning'],
      'selection': ['selection criteria', 'admission profile', 'student demographics', 'acceptance rate', 'average gpa', 'sat score', 'act score'],
      'costsorrates': ['cost', 'fee', 'fees', 'tuition', 'rate', 'price', 'how much', 'payment plan', 'expense'],
      'scholarships': ['scholarship', 'scholarships', 'grant', 'grants', 'financial aid', 'bursary', 'funding', 'award', 'loan', 'fafsa'],
      'payments': ['payment', 'pay', 'billing', 'invoice', 'tuition payment', 'how to pay', 'deadline', 'finance office'],
      'orientations': ['orientation', 'welcome week', 'new student', 'campus tour', 'introduction', 'onboarding'],
      'symposiums': ['symposium', 'conference', 'seminar', 'lecture series', 'guest speaker', 'academic event'],
      'graduation': ['graduation', 'commencement', 'ceremony', 'degree', 'graduate', 'diploma', 'finish school'],
      'people': ['faculty', 'staff', 'directory', 'contact', 'professor', 'teacher', 'employee list', 'instructor', 'advisor', 'dean', 'department head', 'phone number for', 'email for'],
      'currentstudents': ['current student', 'student portal', 'student id', 'student life', 'enrolled student'],
      'housing': ['housing', 'residence', 'dorm', 'dormitory', 'accommodation', 'living on campus', 'room assignment', 'ra', 'res life'],
      'locallodging': ['hotel', 'lodging', 'bnb', 'accomodation', 'off-campus stay', 'nearby hotel', 'place to stay', 'visitor housing'],
      'shopsoreateries': ['store', 'shop', 'cafe', 'dining', 'eatery', 'food', 'restaurant', 'canteen', 'bookstore', 'campus store', 'merchandise', 'eat'],
      'mealplans': ['meal plan', 'dining plan', 'food points', 'swipes', 'how much is meal plan'],
      'localareadining': ['nearby restaurant', 'off-campus food', 'places to eat near campus', 'town dining'],
      'studentdiscounts': ['discount', 'student deal', 'coupon', 'offer', 'save money'],
      'inventory': ['bookstore inventory', 'item stock', 'merchandise', 'textbook', 'supplies'],
      'menus': ['menu', 'dining hall menu', 'whats for lunch', 'food options', 'cafeteria food', 'nutrition'],
      'campusshuttle': ['shuttle', 'bus', 'transport', 'campus bus', 'route', 'schedule', 'getting around'],
      'parkingspaces': ['parking', 'permit', 'lot', 'where to park', 'car park', 'vehicle registration', 'ticket'],
      'localtransport': ['public transport', 'local bus', 'train', 'metro', 'getting here', 'travel to campus'],
      'schools': ['school of', 'college of', 'division', 'faculty', 'academic school'],
      'departments': ['department', 'academic department', 'program contact', 'major department', 'minor department'],
      'centers': ['research center', 'institute', 'lab', 'facility', 'program center'],
      'documents': ['form', 'report', 'pdf', 'download', 'handbook', 'policy document', 'transcript request', 'application form'],
      'majors': ['major', 'degree program', 'field of study', 'course of study', 'what majors', 'list of majors'],
      'minors': ['minor', 'concentration', 'certificate program', 'list of minors'],
      'funding': ['research funding', 'grant opportunity', 'project support', 'financial support for research'],
      'coursecatalog': ['course catalog', 'course list', 'class description', 'module details', 'subject list', 'curriculum'],
      'enrollmentexercise': ['mock registration', 'practice enrollment', 'course signup simulation'],
      'academicresources': ['tutoring', 'academic support', 'writing center', 'library resources', 'study help', 'advisor', 'mentor', 'struggling academically'],
      'academichonors': ['honors', 'dean\'s list', 'gpa requirement', 'academic distinction', 'cum laude'],
      'academicprizes': ['prize', 'award', 'competition', 'student award', 'scholarship award'],
      'academicdress': ['graduation gown', 'regalia', 'cap and gown', 'hood', 'academic attire'],
      'entryrequirements': ['admission requirements', 'prerequisites', 'how to get in', 'application criteria', 'gpa needed', 'test scores'],
      'gradingscale': ['grading system', 'gpa scale', 'how grades work', 'marks', 'pass fail'],
      'programs': ['student program', 'special program', 'initiative', 'extracurricular', 'leadership program'],
      'signatureevents': ['homecoming', 'annual event', 'campus tradition', 'key ceremony', 'founder day'],
      'traditions': ['campus traditions', 'rituals', 'history', 'customs'],
      'partnershipopportunities': ['partnership', 'collaboration', 'industry link', 'community engagement'],
      'athletics': ['athletics', 'sports', 'team', 'game schedule', 'varsity', 'intramural', 'coach', 'stadium', 'gym'],
      'orgsorclubs': ['clubs', 'student organizations', 'student group', 'society', 'join a club', 'extracurricular activities'],
      'researchgroups': ['research group', 'lab group', 'research team', 'project group'],
      'committees': ['committee', 'governance', 'board', 'student government', 'faculty senate'],
      'news': ['news', 'latest news', 'press release', 'campus updates', 'announcements'],
      'periodicals': ['campus newspaper', 'magazine', 'journal', 'student publication'],
      'radio': ['campus radio', 'radio station', 'broadcast'],
      'television': ['campus tv', 'tv station', 'student broadcast'],
      'photos': ['photos', 'gallery', 'images', 'pictures', 'campus scenery'],
      'videos': ['videos', 'youtube', 'promotional video', 'recordings'],
      'accelerators': ['startup accelerator', 'incubator', 'entrepreneurship program', 'business support'],
      'makerspaces': ['makerspace', 'fab lab', 'diy space', '3d printer', 'workshop'],
      'startupfunds': ['seed fund', 'venture capital', 'student startup funding', 'pitch competition'],
      'startups': ['student startup', 'campus venture', 'spin-off company'],
      'researchprojects': ['research project', 'faculty research', 'student research', 'study'],
      'theses': ['thesis', 'dissertation', 'capstone project', 'final paper', 'senior project'],
      'books': ['faculty books', 'alumni books', 'library books', 'textbooks'],
      'articles': ['journal article', 'research paper', 'faculty publication'],
      'patents': ['patent', 'invention', 'intellectual property', 'ip'],
      'building': ['building map', 'building directory', 'specific building', 'hall name', 'facility location'],
      'rooms': ['room schedule', 'classroom location', 'lab number', 'book a room', 'study space', 'lecture hall'],
      'roomequipment': ['projector', 'smartboard', 'av equipment', 'classroom tech', 'computer lab'],
      'roomassignments': ['dorm assignment', 'room key', 'housing placement', 'move-in'],
      'publicart': ['campus art', 'sculpture', 'mural', 'art installation', 'gallery exhibit'],
      'emergencyequipment': ['aed location', 'fire extinguisher', 'safety equipment', 'first aid kit'],
      'classschedules': ['class schedule', 'my schedule', 'course times', 'timetable', 'find class location'],
      'weeklyschedule': ['weekly events', 'this week schedule', 'regular meetings'],
      'events': ['events', 'calendar', 'upcoming events', 'activity', 'workshop', 'register for event', 'what happening'],
      'academiccalendar': ['academic calendar', 'term dates', 'semester dates', 'important dates', 'deadline', 'holiday', 'break'],
      'feedback': ['feedback', 'suggestion', 'complaint', 'survey', 'course evaluation'],
      'historicaltimeline': ['history', 'college history', 'timeline', 'milestones', 'founding'],
      'rentals': ['equipment rental', 'space rental', 'book equipment', 'reserve room'],
      'rentalequipmentcalendar': ['equipment availability', 'rental booking', 'gear schedule'],
      'jobs': ['job', 'campus job', 'student employment', 'work study', 'career services', 'vacancy', 'hiring'],
      'services': ['student services', 'support services', 'health services', 'it services', 'counseling services', 'career services'],
      'atms': ['atm', 'cash machine', 'bank machine', 'withdraw cash'],
      'clinicsorhospitals': ['health center', 'clinic', 'hospital', 'doctor', 'nurse', 'medical appointment', 'sick'],
      'counselingservices': ['counseling', 'mental health', 'therapist', 'psychologist', 'support group', 'wellness center', 'stress', 'anxiety'],
      'emergencycontacts': ['emergency number', 'campus security', 'campus police', 'report incident', 'hotline'],
      'safetyprocedures': ['safety plan', 'emergency procedure', 'evacuation', 'lockdown', 'fire safety'],
      'connectivity': ['wifi', 'internet access', 'network', 'eduroam', 'connect to wifi', 'internet down'],
      'giving': ['donate', 'donation', 'support', 'fundraising', 'alumni giving', 'gift'],
  };
 final Map<String, String> _tableManifest = {
      'helpdesks': 'Campus help desk locations and services (IT, library, student services, etc.)',
      'accessibility': 'Disability support services and campus accessibility resources',
      'faq': 'Frequently asked questions about admissions, academics, and campus life',
      'links': 'Important website links for departments, applications, and resources',
      'construction': 'Current/pending campus construction projects with locations and timelines',
      'printing': 'Printing service locations, costs, and availability',
      'daycares': 'On-campus childcare facilities and registration information',
      'sustainability': 'Environmental initiatives and green campus programs',
      'notices': 'Campus-wide announcements and time-sensitive alerts',
      'socialmediafeeds': 'Official college social media accounts and links',
      'admissionsprocess': 'Step-by-step application procedures and requirements',
      'registrationprocess': 'Course enrollment steps and academic planning',
      'selection': 'Demographic/academic profiles of admitted students',
      'costsorrates': 'Tuition fees, housing costs, and financial rates',
      'scholarships': 'Available grants, awards, and financial aid opportunities',
      'payments': 'Payment methods, portals, and billing information',
      'orientations': 'New student orientation programs and schedules',
      'symposiums': 'Academic conference details and participation info',
      'graduation': 'Commencement ceremony logistics and graduate data',
      'people': 'Faculty/staff directories with contact info and roles',
      'currentstudents': 'Profiles of enrolled students (majors, housing, etc.)',
      'housing': 'Residence hall details, policies, and living arrangements',
      'locallodging': 'Off-campus hotels/B&Bs near the college',
      'shopsoreateries': 'On-campus stores, cafes, and dining options',
      'mealplans': 'Dining plan options and associated costs',
      'localareadining': 'Nearby restaurants and food discounts',
      'studentdiscounts': 'Local business offers for students',
      'inventory': 'Campus store products and pricing',
      'menus': 'Daily dining hall meal offerings and nutritional info',
      'campusshuttle': 'Transportation routes and schedules',
      'parkingspaces': 'Parking lot locations, permits, and regulations',
      'localtransport': 'Public transit options and regional travel',
      'schools': 'Academic divisions and their leadership',
      'departments': 'Academic department info and faculty contacts',
      'centers': 'Research centers and special program facilities',
      'documents': 'Official forms, reports, and policy PDFs',
      'majors': 'Undergraduate degree programs and requirements',
      'minors': 'Minor programs and certification details',
      'funding': 'Research grants and project funding opportunities',
      'coursecatalog': 'Course descriptions and class details',
      'enrollmentexercise': 'Registration practice simulations',
      'academicresources': 'Tutoring, libraries, and study support',
      'academichonors': 'Dean’s list, honors programs, and GPA requirements',
      'academicprizes': 'Student achievement awards and competitions',
      'academicdress': 'Graduation regalia info and ordering',
      'entryrequirements': 'Admission criteria and prerequisites',
      'gradingscale': 'Letter grade definitions and GPA calculations',
      'programs': 'Special academic initiatives and partnerships',
      'signatureevents': 'Major annual campus traditions/ceremonies',
      'traditions': 'Historical campus customs and rituals',
      'partnershipopportunities': 'Community/corporate collaboration programs',
      'athletics': 'Sports teams, schedules, and athlete resources',
      'orgsorclubs': 'Student organizations and club listings',
      'researchgroups': 'Active academic research teams/projects',
      'committees': 'Campus governance groups and their functions',
      'news': 'College news articles and press releases',
      'periodicals': 'Student-run publications and magazines',
      'radio': 'Campus radio station programming and staff',
      'television': 'Student-produced TV shows and content',
      'photos': 'Campus photo archives and event galleries',
      'videos': 'Official college videos and student projects',
      'accelerators': 'Entrepreneurship programs and startup support',
      'makerspaces': 'Creative labs with equipment/tech resources',
      'startupfunds': 'Funding opportunities for student ventures',
      'startups': 'Student-run businesses and their profiles',
      'researchprojects': 'Ongoing faculty/student research studies',
      'theses': 'Senior capstone projects and research papers',
      'books': 'Publications by faculty/alumni',
      'articles': 'Academic papers and journal contributions',
      'patents': 'Innovations/IP created at the college',
      'building': 'Campus building info and facilities',
      'rooms': 'Classroom/lab specifications and reservations',
      'roomequipment': 'AV/tech gear available in spaces',
      'roomassignments': 'Student housing placements',
      'publicart': 'Campus art installations and exhibits',
      'emergencyequipment': 'Safety devices and their locations',
      'classschedules': 'Course times, locations, and instructors',
      'weeklyschedule': 'Recurring events and meetings',
      'events': 'Campus activities calendar and RSVP info',
      'academiccalendar': 'Term dates and deadlines',
      'feedback': 'Student surveys and feedback forms',
      'historicaltimeline': 'Key moments in college history',
      'rentals': 'Equipment/space rental options and policies',
      'rentalequipmentcalendar': 'Reservation schedule for gear',
      'jobs': 'Campus employment and career opportunities',
      'services': 'Support services (IT, health, etc.) and contacts',
      'atms': 'On-campus cash machine locations',
      'clinicsorhospitals': 'Health center services and hours',
      'counselingservices': 'Mental health resources and appointments',
      'emergencycontacts': 'Critical phone numbers and protocols',
      'safetyprocedures': 'Emergency response guidelines',
      'connectivity': 'WiFi, tech resources, and IT support',
      'giving': 'Donation opportunities and alumni fundraising',
  };
 final Map<String, List<String>> _collegeFieldKeywords = {
      'about': ['about', 'overview', 'information', 'general info', 'tell me about'],
      'address': ['address', 'location', 'located', 'where is', 'find you', 'campus address'],
      'daysnhours': ['hours', 'opening hours', 'closing time', 'open', 'close', 'days open', 'schedule', 'operating hours'],
      'postaladdress': ['postal address', 'mailing address', 'zip code', 'postcode', 'mail'],
      'mission': ['mission', 'mission statement', 'purpose', 'aim'],
      'vision': ['vision', 'vision statement', 'aspiration', 'future goals'],
      'corevalues': ['values', 'core values', 'principles', 'ethics'],
      'motto': ['motto', 'tagline', 'slogan'],
      'goals': ['goals', 'objectives', 'targets', 'aims'],
      'mandate': ['mandate', 'authority', 'charge', 'official purpose'],
      'founded': ['founded', 'established', 'since', 'when was it founded', 'history start'],
      'accreditation': ['accreditation', 'accredited', 'certified', 'recognized'],
      'freewifi': ['wifi', 'internet', 'wireless', 'free wifi', 'connect'],
      'objectives': ['objectives', 'aims', 'goals'],
      'aims': ['aims', 'objectives', 'goals'],
      'pledge': ['pledge', 'commitment', 'promise', 'dedication'],
      'statementoffaith': ['faith', 'belief', 'statement of faith', 'religious statement', 'creed'],
      'religiousaffiliation': ['religious', 'faith', 'denomination', 'affiliation', 'church'],
      'whychooseus': ['why choose', 'why us', 'advantages', 'benefits', 'choose us', 'selling points', 'why attend'],
      'institutiontype': ['type', 'public', 'private', 'institution type', 'kind of school', 'college type'],
      'campussetting': ['campus setting', 'setting', 'urban', 'rural', 'suburban', 'campus environment', 'location type'],
      'highestqualificationoffered': ['qualification', 'degree', 'certificate', 'highest degree', 'level of study', 'programs offered', 'diploma'],
      'studentpopulation': ['population', 'students', 'enrollment', 'how many students', 'student body size', 'number of students'],
      'academicyearcalendar': ['academic calendar', 'terms', 'semesters', 'academic year', 'school year schedule', 'term dates', 'session dates'],
      'website': ['website', 'site', 'url', 'web address', 'online', 'homepage'],
      'city': ['city', 'town', 'located in'],
      'state': ['state', 'region', 'province'],
      'fullname': ['name', 'full name', 'official name'],
      'phone': ['phone', 'number', 'contact number', 'call', 'telephone'],
      'email': ['email', 'email address', 'contact email', 'mail address'],
  };


 @override
 void initState() {
   super.initState();
   _speechToText = stt.SpeechToText();
   _flutterTts = FlutterTts();
   WidgetsBinding.instance.addPostFrameCallback((_) {
     _initializeAndLoadData();
     _addInitialGreeting();
   });
   _messageController.addListener(() {
      if(mounted) setState(() {});
   });
 }

 @override
 void dispose() {
   _messageController.removeListener(() { if(mounted) setState(() {}); });
   _messageController.dispose();
   _scrollController.dispose();
   _speechToText.stop();
   _flutterTts.stop();
   super.dispose();
 }

 // --- Initialization & Core Helpers ---
 Future<void> _initializeAndLoadData() async {
    await _initSpeech();
    await _configureTts();
    await _loadTtsVoices();
 }

 Future<void> _initSpeech() async {
     try {
       bool available = await _speechToText.initialize(
         onError: (errorNotification) { print('Speech Init Error: $errorNotification'); if (mounted) { setState(() => _isContinuousListening = false); _showError("Speech recognition error: ${errorNotification.errorMsg}"); } },
         onStatus: (status) {
           print('Speech Status: $status');
           if (mounted) {
             final isCurrentlyListening = status == stt.SpeechToText.listeningStatus;
             if (_isListening != isCurrentlyListening) { setState(() { _isListening = isCurrentlyListening; }); }
             if (status == stt.SpeechToText.notListeningStatus && _isContinuousListening && _processingStep == ProcessingStep.idle) {
               print("STT stopped naturally. Restarting listening loop.");
               // Short delay before restarting to avoid tight loops if errors occur
               Future.delayed(const Duration(milliseconds: 500), () { if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle) { _startListeningSession(); } });
             }
           }
         }
       );
       if (!available && mounted) { print("Speech recognition not available."); setState(() => _isContinuousListening = false); _showError("Speech recognition is not available on this device."); }
       else if (available) { print("Speech recognition initialized."); }
     } catch (e) { print("Exception initializing speech: $e"); if (mounted) { setState(() => _isContinuousListening = false); _showError("Failed to initialize speech recognition."); } }
 }

 Future<void> _configureTts() async {
     await _flutterTts.awaitSpeakCompletion(true);
     await _flutterTts.setVolume(1.0);
     await _flutterTts.setSpeechRate(0.5); // Adjust rate as needed
     await _flutterTts.setPitch(1.0); // Adjust pitch as needed
     if (_selectedVoice != null && mounted) {
       try {
         // Check if voice exists (optional but good practice)
         List<dynamic>? voices = await _flutterTts.getVoices;
         bool voiceExists = voices?.any((v) => v is Map && v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale']) ?? false;
         if (voiceExists) {
           await _flutterTts.setVoice(_selectedVoice!);
           print("TTS voice set: ${_selectedVoice!['name']}");
         } else {
           print("Selected voice not found, using default.");
           if(mounted) setState(() => _selectedVoice = null); // Reset selection if invalid
           await _flutterTts.setLanguage("en-US"); // Fallback language
         }
       } catch (e) {
         print("Error setting TTS voice: $e");
         if (mounted) setState(() => _selectedVoice = null); // Reset on error
         await _flutterTts.setLanguage("en-US"); // Fallback language
       }
     } else {
       // Default voice if none selected
       await _flutterTts.setLanguage("en-US");
       print("Using default US English TTS voice.");
     }
  }

 Future<void> _loadTtsVoices() async {
     if (!mounted) return;
     try {
       var voices = await _flutterTts.getVoices;
       if (voices != null && voices is List && mounted) {
         // Filter for English voices and sort them
         List<Map<String, String>> englishVoices = voices
               .map((v) => Map<String, String>.from(v as Map)) // Ensure type safety
               .where((v) => v['locale']?.startsWith('en-') ?? false) // Filter English locales
               .sortedBy<String>((v) => v['name'] ?? '') // Sort by name
               .toList();

         setState(() {
           _availableVoices = englishVoices;
           _voicesLoaded = true;
           // Set a default voice if none is selected or the selected one isn't available
           if (_selectedVoice == null || !_availableVoices.any((v) => v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale'])) {
             // Prefer a US female voice, fallback to first US, fallback to first English
             _selectedVoice = _availableVoices.firstWhereOrNull( (v) => v['locale'] == 'en-US' && (v['name']?.toLowerCase().contains('female') ?? false))
                            ?? _availableVoices.firstWhereOrNull((v) => v['locale'] == 'en-US')
                            ?? _availableVoices.firstOrNull;
           }
         });
         await _configureTts(); // Apply the selected/default voice
         print("Loaded ${_availableVoices.length} English voices. Selected: ${_selectedVoice?['name']}");
       }
     } catch (e) {
       print("Error getting TTS voices: $e");
       if (mounted) setState(() => _voicesLoaded = false); // Indicate failure
     }
   }

 void _toggleContinuousListening() async {
      if (_processingStep != ProcessingStep.idle) return; // Don't toggle while processing

      if (_isContinuousListening) {
        // Stop listening
        setState(() => _isContinuousListening = false);
        if (_speechToText.isListening) await _speechToText.stop();
        print("Continuous listening STOPPED by toggle.");
      } else {
        // Start listening
        if (!await _speechToText.hasPermission) {
          _showError("Speech permission required to use microphone.");
          // Consider prompting for permission here
          return;
        }
        // Ensure STT is initialized and available
        if (!_speechToText.isAvailable) {
          bool initSuccess = await _speechToText.initialize(); // Re-initialize if needed
          if(!initSuccess || !mounted){
             _showError("Could not start speech recognition service.");
             return;
          }
        }
        // Set state and start the session
        if(mounted){
           setState(() => _isContinuousListening = true);
           print("Continuous listening STARTED by toggle.");
           _startListeningSession(); // Begin the first listening attempt
        }
      }
   }

 Future<void> _startListeningSession() async {
      // Conditions to prevent starting: already listening, not continuous mode, processing, unmounted
      if (!_isContinuousListening || _isListening || _processingStep != ProcessingStep.idle || !mounted) {
         print("Skipping startListeningSession. Continuous: $_isContinuousListening, Listening: $_isListening, Processing: $_processingStep, Mounted: $mounted");
         return;
      }

      await _flutterTts.stop(); // Stop any ongoing TTS

      if (!_speechToText.isAvailable) {
          if (mounted) {
              setState(() => _isContinuousListening = false); // Turn off toggle if STT fails
              _showError("Speech recognition service became unavailable.");
          }
          return;
      }

      print("Starting STT listening session...");
      if(mounted) setState(() => _isListening = true); // Update UI to show listening state

      _speechToText.listen(
        listenFor: const Duration(seconds: 60), // Max duration for one listen cycle
        pauseFor: const Duration(seconds: 5), // Silence duration before finalizing
        partialResults: true, // Get results as they come in
        onResult: (result) {
          if (!mounted) return;
          // Update text field with recognized words
          _messageController.text = result.recognizedWords;
          // Move cursor to end
          _messageController.selection = TextSelection.fromPosition(TextPosition(offset: _messageController.text.length));

          // If result is final AND continuous listening is still active
          if (result.finalResult && _isContinuousListening) {
            final recognizedText = result.recognizedWords.trim();
            print("STT Final Result: '$recognizedText'");

            // Stop the current STT session; onStatus handler will restart if needed
             if (_speechToText.isListening && mounted) _speechToText.stop();


            if (recognizedText.isNotEmpty) {
               // Send the message if text was recognized
               _sendMessage(recognizedText);
            } else {
               // If empty result, prepare to listen again (onStatus handles restart)
               print("Empty final STT result.");
                // The onStatus handler should catch the 'notListening' state and restart
            }
          }
        },
        listenMode: stt.ListenMode.dictation, // Suitable for longer speech
      ).catchError((error) {
         // Handle errors during listening
         print("STT listen error: $error");
         if (mounted) {
            setState(() {
              _isListening = false; // Update UI state
              _isContinuousListening = false; // Stop continuous mode on error
            });
            _showError("Speech recognition failed during listening.");
         }
      });
   }

 void _handleManualInput(String value) {
     // If user starts typing manually while continuous listening is on, turn it off.
     if (_isContinuousListening && _messageController.text.isNotEmpty && mounted) {
        print("Manual typing detected, stopping continuous listening.");
        setState(() => _isContinuousListening = false);
        _speechToText.stop(); // Stop STT
     }
 }


 // --- Message Sending (NEW Multi-Step Logic) ---
 Future<void> _sendMessage(String message) async {
   final String userMessage = message.trim();
   if (userMessage.isEmpty || _processingStep != ProcessingStep.idle) return;

   // --- Rule-Based Quick Answers (Client-Side) - KEEP FOR EFFICIENCY ---
   String? quickAnswer = _getQuickAnswer(userMessage);
   if (quickAnswer != null) {
     print("Quick Answer triggered for: '$userMessage'");
     if (mounted) _messageController.clear();
     final userCharCount = _calculateTokenCount(userMessage);
     final responseCharCount = _calculateTokenCount(quickAnswer);

     setState(() {
       _messages.add(ChatMessage(text: userMessage, isUser: true));
       _messages.add(ChatMessage(text: quickAnswer, isUser: false));
       _dailyTokenCount += userCharCount; // Only count user message towards limit for quick answers
       _lastPromptTokenCount = 0; // No Gemini prompt
       _lastApiResponseCharCount = responseCharCount; // Count the quick answer response
     });
     _scrollToBottom();
     if (!_isMuted) {
       await _configureTts(); await Future.delayed(const Duration(milliseconds: 100));
       if (!_isMuted && mounted) await _flutterTts.speak(quickAnswer);
     }
     // Restart listening if in continuous mode
     if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle) {
        print("Quick answer sent, restarting listening session.");
        Future.delayed(const Duration(milliseconds: 500), () => _startListeningSession());
     }
     return;
   }
   // --- End Quick Answers ---


   if (mounted) _messageController.clear();

   final userCharCount = _calculateTokenCount(userMessage);
   if (_dailyTokenCount + userCharCount > _maxDailyTokens) {
     _showError("Daily usage limit reached. Cannot send message.");
     if (mounted) setState(() => _isContinuousListening = false); // Stop listening if limit hit
     return;
   }

   if (_speechToText.isListening) await _speechToText.stop(); // Ensure STT is stopped

   setState(() {
     _processingStep = ProcessingStep.processing; // Enter processing state
     _isListening = false; // Ensure listening UI state is off
     _messages.add(ChatMessage(text: userMessage, isUser: true));
     _dailyTokenCount += userCharCount; // Count user message towards limit
     _lastPromptTokenCount = 0; // Reset counts for the new interaction
     _lastApiResponseCharCount = 0;
   });
   _scrollToBottom();

   List<String> tablesToFetch = [];
   String contextData = "";
   String finalResponseText = "";
   bool errorOccurred = false;
   int totalPromptCharsThisTurn = userCharCount; // Start with user message chars
   int totalResponseCharsThisTurn = 0;

   try {
     // ========== STAGE 1: Identify Relevant Tables via Gemini ==========
     _addOrUpdateTypingIndicator("Analyzing query...");
     _scrollToBottom();

     final tableSelectionPrompt = _buildTableSelectionPrompt(userMessage);
     final tableSelectionPromptCharCount = _calculateTokenCount(tableSelectionPrompt);
     totalPromptCharsThisTurn += tableSelectionPromptCharCount;

     // Check limit *before* the first API call
     if (_dailyTokenCount + totalPromptCharsThisTurn > _maxDailyTokens) {
       throw Exception("Daily limit exceeded before table selection request.");
     }

     print("Sending table selection request to Gemini...");
     // Call Gemini using the model defined in _model
     String tableSelectionResponse = await _callGeminiApi(tableSelectionPrompt);
     if (!mounted) return; // Check mount status after await

     final tableSelectionResponseCharCount = _calculateTokenCount(tableSelectionResponse);
     totalResponseCharsThisTurn += tableSelectionResponseCharCount;
     // Update display for the cost of this specific step
     _lastPromptTokenCount = tableSelectionPromptCharCount;
     _lastApiResponseCharCount = tableSelectionResponseCharCount;

     // Parse Gemini's response for table names
     tablesToFetch = tableSelectionResponse
         .split(',') // Split by comma
         .map((t) => t.trim().toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'),'')) // Clean and lowercase each part
         .where((t) => t.isNotEmpty && t != 'none' && _tableManifest.containsKey(t)) // Validate against manifest keys
         .toSet() // Use Set to automatically handle duplicates
         .toList(); // Convert back to List
     print("Gemini suggested tables (validated): $tablesToFetch");

     // ========== STAGE 2: Fetch Data (Base College Info + Gemini Tables) ==========
     _addOrUpdateTypingIndicator("Fetching relevant details...");
     _scrollToBottom();

     // Fetch data using the validated list from Gemini.
     // _getCollegeDataForPrompt handles fetching base 'colleges' data AND the specific tables.
     contextData = await _getCollegeDataForPrompt(userMessage, tablesToFetch);
     if (!mounted) return; // Check mount status
     if (contextData.startsWith('No specific college data')) {
       // This indicates a critical failure fetching base info
       throw Exception("Error loading essential college information.");
     }
     if (contextData == 'Component unmounted') return; // Exit if unmounted during fetch

     // ========== STAGE 3: Generate Final Answer via Gemini ==========
     _addOrUpdateTypingIndicator("Generating response...");
     _scrollToBottom();

     final history = _buildConversationHistory();
     final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
     // Get phone number for fallback instruction, provide a default if null
     final mainPhoneNumber = widget.collegeData?['phone'] ?? 'the main college phone number';

     // Build the final prompt with context and instructions
     final finalPrompt = '''SYSTEM: You are a specialized AI assistant for $collegeName. Answer the user's query based ONLY on the information provided in the 'Provided Context' section below.

Instructions:
- Use the 'Provided Context' which contains 'General Info' about the college and 'Relevant Specific Information' fetched based on the query.
- Format your response clearly for reading (paragraphs, use '*' for bullet points). Avoid technical jargon or raw database field names (e.g., use 'Postal Address' instead of 'postaladdress'). Do not use markdown headers like '##'.
- **If the information needed to answer the query accurately is NOT present in the 'Provided Context', explicitly state that the specific detail is unavailable in the current data and politely suggest the user call $mainPhoneNumber for further assistance.**
- Do NOT use external knowledge or make assumptions beyond the provided context. Be concise and directly answer the question.

--- Provided Context ---
$contextData
--- End Provided Context ---

--- Recent Conversation History ---
$history
--- End Conversation History ---

user: $userMessage
model:'''; // Note: 'model:' at the end primes the model for response

     final finalPromptCharCount = _calculateTokenCount(finalPrompt);
     totalPromptCharsThisTurn += finalPromptCharCount; // Add final prompt chars to this turn's total

     // Update last prompt count for display (shows cost of the final prompt)
     if (mounted) setState(() => _lastPromptTokenCount = finalPromptCharCount);


     // Check limit AGAIN before the final, potentially large, API call
     final potentialDailyTotal = _dailyTokenCount + totalPromptCharsThisTurn + totalResponseCharsThisTurn;
     if (potentialDailyTotal > _maxDailyTokens) {
       throw Exception("Daily limit exceeded before final response generation request.");
     }

     print("Sending final generation request to Gemini...");
     // Call Gemini using the model defined in _model
     finalResponseText = await _callGeminiApi(finalPrompt);
     if (!mounted) return; // Check mount status

     // Process Final Response
     final responseCharCount = _calculateTokenCount(finalResponseText);
     totalResponseCharsThisTurn += responseCharCount;
     // Update display for the cost of the final response
      if (mounted) setState(() => _lastApiResponseCharCount = responseCharCount);


     // Calculate the final total character count after this interaction
     final finalDailyTotalWithResponse = _dailyTokenCount + totalPromptCharsThisTurn + totalResponseCharsThisTurn;

     if (finalDailyTotalWithResponse > _maxDailyTokens) {
       print("Warning: Total chars for this turn ($totalPromptCharsThisTurn prompts + $totalResponseCharsThisTurn responses = ${totalPromptCharsThisTurn+totalResponseCharsThisTurn}) potentially exceeded limit. Final calculated total: $finalDailyTotalWithResponse");
       _showError("Received response, but daily limit may have been exceeded during processing.");
       errorOccurred = true;
       // Set count to max, but still show the response if received
       if(mounted) {
         setState(() => _dailyTokenCount = _maxDailyTokens);
         // Add the response even if limit hit, as Gemini might return partial data
         final responseMessage = ChatMessage(text: finalResponseText.isEmpty ? "Sorry, response generation failed possibly due to limits." : finalResponseText, isUser: false);
         _removeTypingIndicator(); // Ensure indicator is removed first
         setState(() { _messages.add(responseMessage); });
       }

     } else {
       // Success path - limit not exceeded
       final responseMessage = ChatMessage(text: finalResponseText.isEmpty ? "Sorry, I couldn't generate a response for that query based on the available information." : finalResponseText, isUser: false);
       if (!mounted) return;
       _removeTypingIndicator(); // Remove indicator first
       setState(() {
         _messages.add(responseMessage);
         // Update daily count accurately
         _dailyTokenCount = finalDailyTotalWithResponse;
       });
       // Speak the response if not muted
       if (!_isMuted && finalResponseText.isNotEmpty) {
         await _configureTts(); await Future.delayed(const Duration(milliseconds: 100));
         if (!_isMuted && mounted) await _flutterTts.speak(finalResponseText);
       }
     }

   } catch (e, stacktrace) {
     errorOccurred = true;
     print('Error in multi-step pipeline: $e\n$stacktrace');
     String errorMessage = e.toString().replaceFirst("Exception: ", "");
     // Handle specific error messages cleanly
     if (errorMessage.contains("Daily limit exceeded")) {
       _showError("Daily usage limit reached. Please try again tomorrow.");
       if(mounted) setState(() { _dailyTokenCount = _maxDailyTokens; _isContinuousListening = false; });
     } else if (errorMessage.contains("Error loading essential college information")) {
       _showError("Sorry, there was a problem loading essential information about the college.");
     } else if (errorMessage.contains("blocked by AI")) {
       _showError("Sorry, the request was blocked due to safety settings.");
     } else if (errorMessage.contains("timed out")) {
        _showError("Sorry, the request to the AI service timed out.");
     } else if (errorMessage.contains("Network error")) {
         _showError("Sorry, there seems to be a network issue. Please check your connection.");
     } else {
        // Generic error message
        _showError('Sorry, an unexpected error occurred: $errorMessage');
     }
     if (mounted) setState(() => _isContinuousListening = false); // Stop listening on any error
   } finally {
     // This block always runs, whether try succeeds or fails
     if (mounted) {
       _removeTypingIndicator(); // Ensure indicator is always removed
       setState(() { _processingStep = ProcessingStep.idle; }); // Return to idle state
       _scrollToBottom();
       // Restart listening ONLY if no error occurred and continuous mode is on
       if (!errorOccurred && _isContinuousListening && mounted && _processingStep == ProcessingStep.idle) {
         print("Multi-step finished without errors, checking STT status for restart...");
         if (!_speechToText.isListening) {
           print("Triggering listening session restart check.");
           // Short delay before restarting
           Future.delayed(const Duration(milliseconds: 500), () {
             if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle && !_speechToText.isListening) {
               _startListeningSession();
             }
           });
         }
       }
     }
   }
 }

 // --- Helper to build the prompt for table selection ---
 String _buildTableSelectionPrompt(String userQuery) {
   // Format the manifest for the prompt
   final manifestFormatted = _tableManifest.entries
       .map((e) => '- ${e.key}: ${e.value}') // Format as "key: value"
       .join('\n'); // Join with newlines

   // Construct the prompt
   return '''SYSTEM: You are an AI assistant helping to select relevant database tables to answer a user's query about a college.
User Query: "$userQuery"

Available Data Tables (Manifest):
Each line has the format: table_name: description
---
$manifestFormatted
---

Instruction: Based *only* on the User Query and the table descriptions in the manifest, list the short table names (e.g., 'admissionsprocess', 'housing', 'costs') from the manifest that are MOST likely to contain the information needed to answer the query.
- List ONLY the relevant table_names, separated by commas (e.g., housing,costs,mealplans).
- Prioritize tables that directly match keywords in the query and description.
- If the query is very general (e.g., "tell me about the college"), list only a few core tables like 'faq', 'links', 'about' (if 'about' were a table).
- If no specific table seems relevant based on the query and descriptions, respond with the single word 'NONE'.
- Do NOT add any explanation, preamble, or concluding text. Just provide the comma-separated list or 'NONE'.
model:'''; // Priming token for the model's response
 }


 // --- Rule-Based Quick Answers ---
 String? _getQuickAnswer(String query) {
     // Only proceed if college data is available
     if (widget.collegeData == null) return null;

     final lowerQuery = query.toLowerCase(); // Normalize query for matching

     // Check for website keywords
     if (RegExp(r'\b(website|site|url|web address|homepage)\b').hasMatch(lowerQuery)) {
         return widget.collegeData!['website'] != null
             ? "The website is: ${widget.collegeData!['website']}"
             : null; // Return null if website data is missing
     }
     // Check for main phone number keywords (avoiding departmental requests)
     if (RegExp(r'\b(phone|contact number|call|telephone)\b').hasMatch(lowerQuery) &&
         !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff")) {
         return widget.collegeData!['phone'] != null
             ? "The main phone number is: ${widget.collegeData!['phone']}"
             : null; // Return null if phone data is missing
     }
     // Check for main email keywords (avoiding departmental requests)
     if (RegExp(r'\b(email|email address|contact email)\b').hasMatch(lowerQuery) &&
          !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff")) {
         return widget.collegeData!['email'] != null
             ? "The main contact email is: ${widget.collegeData!['email']}"
             : null; // Return null if email data is missing
     }
     // Check for address keywords
     if (RegExp(r'\b(address|location|located|where is|find you|campus address)\b').hasMatch(lowerQuery)) {
         // Safely get address components
         String? address = widget.collegeData!['address']?.toString();
         String? city = widget.collegeData!['city']?.toString();
         String? state = widget.collegeData!['state']?.toString();
         // Combine non-null, non-empty parts
         List<String?> parts = [address, city, state];
         List<String> validParts = parts.whereNotNull().where((s) => s.trim().isNotEmpty).toList();
         return validParts.isNotEmpty
             ? "The address is: ${validParts.join(', ')}"
             : null; // Return null if no valid address parts found
      }
     // No quick answer matched
     return null;
 }


 // --- Client-Side Keyword Matching (RETAINED for _getRelevantCollegeFields) ---


 // --- Get Data for Prompt (Modified Slightly for Clarity) ---
 Future<String> _getCollegeDataForPrompt(String userQuery, List<String> specificTablesToFetch) async {
    if (!mounted) return 'Component unmounted';
    if (widget.collegeData == null || widget.collegeData!['fullname']?.isEmpty == true) {
      // This check ensures base data is present before proceeding
      print("Error: _getCollegeDataForPrompt called with null or empty collegeData.");
      return 'No specific college data available.'; // Indicate failure clearly
    }

    String collegeName = widget.collegeData!['fullname']!;
    // Derive table prefix robustly, handling potential nulls or emptiness
    String collegeTablePrefix = widget.collegeData!['tableprefix']?.toString().trim() ?? '';
    if (collegeTablePrefix.isEmpty) {
       collegeTablePrefix = collegeName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');
    }
    if (collegeTablePrefix.isEmpty) collegeTablePrefix = "college"; // Absolute fallback prefix
    print("Using table prefix: $collegeTablePrefix");

    final sb = StringBuffer();
    int totalFetchedDataLength = 0;
    // Slightly increased budget allows for richer context
    const maxTotalFetchedDataLength = 3500;

    // Section 1: General College Information (Always include relevant base fields)
    sb.writeln('## General Info ($collegeName):');
    // Use keywords to get the *most* relevant base fields from widget.collegeData for the specific query
    final relevantBaseFields = _getRelevantCollegeFields(userQuery, max: 8);
    bool baseDataAdded = false;
    for (var field in relevantBaseFields) {
      // Check if the field exists and has a non-null, non-empty value
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final label = field.readableFieldName(); // Get user-friendly label
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        // Add if value is meaningful (not empty or 'n/a')
        if (value.isNotEmpty && value.toLowerCase() != 'n/a') {
          String displayValue = value;
          // Apply truncation for potentially long text fields
          int maxLength = (['about', 'mission', 'vision'].contains(field)) ? 300 : 120; // Shorter limit for most fields
          if (displayValue.length > maxLength) displayValue = displayValue.substring(0, maxLength) + '... (truncated)';
          sb.writeln('- $label: $displayValue');
          baseDataAdded = true;
        }
      }
    }
    // Add a note if no relevant general info was found for the query context
    if (!baseDataAdded) sb.writeln("(No relevant general details found in base info for this query's context)");
    sb.writeln('---');
    totalFetchedDataLength += sb.length; // Rough estimate of length so far

    // Section 2: Relevant Specific Information (Fetched based on Gemini's suggestions)
    List<String> actuallyFetchedTableNames = [];
    if (specificTablesToFetch.isNotEmpty) {
       sb.writeln('\n## Relevant Specific Information (Data fetched based on query analysis):');
       List<Future<Map<String, String>>> fetchFutures = [];

       for (String tableNameShort in specificTablesToFetch) {
           // Check budget before starting fetch for a table
           if (totalFetchedDataLength >= maxTotalFetchedDataLength) {
               print("Context length budget ($maxTotalFetchedDataLength) hit before fetching table '$tableNameShort'.");
               break; // Stop fetching more tables
           }
           // Construct full table name using the derived prefix
           String fullTableName = "${collegeTablePrefix}_$tableNameShort";
           // Calculate remaining budget for this specific fetch operation
           int budgetForThisTable = maxTotalFetchedDataLength - totalFetchedDataLength;
           if (budgetForThisTable < 100) { // Don't attempt fetch if remaining budget is too small
               print("Skipping fetch for '$tableNameShort' due to low remaining budget ($budgetForThisTable).");
               break;
           }

           // Add the fetch operation to the list of futures
           fetchFutures.add(
              _fetchSupabaseDataForRow(fullTableName, limit: 3, maxLengthBudget: budgetForThisTable) // Fetch up to 3 rows if budget allows
                 .then((data) => {'tableName': tableNameShort, 'data': data}) // Return map with name and data
                 .catchError((e) {
                    // Gracefully handle errors during individual table fetches
                    print("Error fetching data for $tableNameShort ($fullTableName): $e");
                    return {'tableName': tableNameShort, 'data': ''}; // Return empty data on error
                 })
           );
       }

       // Wait for all fetch operations to complete
       final results = await Future.wait(fetchFutures);
       if (!mounted) return 'Component unmounted'; // Check mount status again after awaits

       bool specificDataAdded = false;
       // Process results from fetches
       for (var result in results) {
         final name = result['tableName']!;
         final data = result['data']!;
         if (data.isNotEmpty) { // Only add if data was actually retrieved
           final sectionTitle = name.capitalize(); // Use readable table name
           sb.writeln('\n### From $sectionTitle Data:'); // Clear heading for data source
           sb.writeln(data); // Add the fetched data
           sb.writeln('---'); // Separator
           // Update total length estimate (data + title + formatting)
           totalFetchedDataLength += data.length + sectionTitle.length + 25;
           specificDataAdded = true;
           actuallyFetchedTableNames.add(name); // Track successfully fetched tables
         }
       }
        // Add summary message about fetched data
        if (!specificDataAdded) {
            sb.writeln("(No specific data retrieved for suggested tables: ${specificTablesToFetch.join(', ')}).");
        } else {
             sb.writeln("(Fetched data details from: ${actuallyFetchedTableNames.join(', ')})"); // List what was actually fetched
        }
    } else {
        // Case where Gemini suggested 'NONE' or validation removed all tables
        sb.writeln('\n## Relevant Specific Information (Fetched):');
        sb.writeln("(No specific tables were identified as relevant for fetching additional data based on the query).");
    }

    // Section 3: Available Data Tables (Manifest) - KEEP THIS
    // This helps Gemini understand the *scope* of what *could* be known, even if not fetched for this query.
    sb.writeln('\n## Available Data Tables (Manifest):');
    sb.writeln('(This lists all possible data areas. Data above in "Relevant Specific Information" was fetched only for tables deemed relevant to the current query.)');
    _tableManifest.forEach((tableName, description) {
       String displayTableName = tableName.capitalize();
       sb.writeln('- **$displayTableName**: $description');
    });

    // Add note if context might be truncated due to budget limits
    if (totalFetchedDataLength >= maxTotalFetchedDataLength) {
      sb.writeln("\n(Note: Fetched context details might be incomplete due to length limits.)");
    }

    print("Context built. Gemini suggested: ${specificTablesToFetch.join(', ')}. Actually fetched from: ${actuallyFetchedTableNames.join(', ')}. Total Context Chars (approx): $totalFetchedDataLength");
    return sb.toString(); // Return the complete context string
  }


  // --- Fetch Supabase Data Helper ---
  // (Restored full implementation from previous step)
  Future<String> _fetchSupabaseDataForRow(String tableName, {required int limit, required int maxLengthBudget}) async {
      if (maxLengthBudget <= 0 || !mounted) return ""; // Return empty string if no budget or unmounted
      print("Fetching: $tableName (Limit: $limit, Budget: $maxLengthBudget)");
      try {
         // Fetch data from Supabase
         final response = await Supabase.instance.client
             .from(tableName)
             .select()
             .limit(limit)
             .timeout(const Duration(seconds: 10)); // Add a timeout

         if (!mounted) return ""; // Check mount status again after await

         // Process the response if it's a non-empty list
         if (response is List && response.isNotEmpty) {
            final sb = StringBuffer();
            for (int i = 0; i < response.length; i++) {
               // Stop if the buffer length exceeds the budget
               if (sb.length >= maxLengthBudget) break;

               final row = response[i] as Map<String, dynamic>;
               List<String> parts = [];
               row.forEach((key, value) {
                  // Include non-null values, excluding common metadata fields
                  if (value != null && key != 'id' && key != 'created_at' && key != 'updated_at') {
                     String valStr = value.toString().trim();
                     if (valStr.isNotEmpty && valStr.toLowerCase() != 'n/a') {
                        // Truncate long values for summary
                        int maxLen = 80; // Tight summary limit per field
                        if (valStr.length > maxLen) valStr = valStr.substring(0, maxLen) + '...';
                        // Format as "Readable Key: Value"
                        parts.add('${key.readableFieldName()}: $valStr');
                     }
                  }
               });

               // Join parts into a row summary, provide fallback text if empty
               String rowSummary = parts.isNotEmpty ? parts.join('; ') : "(Item details unavailable)";
               int estimatedAddedLength = rowSummary.length + 10; // Estimate added length (text + formatting)

               // Add to buffer only if within budget
               if (sb.length + estimatedAddedLength < maxLengthBudget) {
                  sb.writeln('- $rowSummary');
               } else {
                  print("Budget hit while processing row for $tableName");
                  break; // Stop processing rows for this table
               }
            }
            String result = sb.toString().trim();
            print("Fetched ${result.length} chars from $tableName.");
            return result; // Return the formatted string
         } else {
            // Return empty string if response is empty or not a list
            return '';
         }
      } on PostgrestException catch (e) {
         // Handle specific Supabase errors (e.g., table not found)
         if (e.code == '42P01') {
            print("Info: Table '$tableName' not found (Supabase code 42P01).");
         } else {
            print("Supabase error fetching '$tableName': ${e.message} (Code: ${e.code})");
         }
         return ''; // Return empty string on Supabase errors
      } catch (e) {
         // Catch any other errors during fetch/processing
         print("General error fetching '$tableName': $e");
         return ''; // Return empty string on general errors
      }
   }


  // --- Gemini API Call ---
  // Uses the _model defined at the class level (gemini-2.0-flash-lite)
  Future<String> _callGeminiApi(String prompt) async {
    // No modelOverride parameter needed now, always uses _model
    if (_apiKey == 'YOUR_API_KEY_HERE' || _apiKey.length < 30 || !_apiKey.startsWith('AIzaSy')) { // Basic check
      print("ERROR: Gemini API Key is missing, invalid, or a placeholder.");
      throw Exception("Configuration error: Invalid API key.");
    }
    // Construct the URL using the class _model variable
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$_model:generateContent?key=$_apiKey');
    final headers = {'Content-Type': 'application/json'};

    // Define generation configuration (can be adjusted)
    final generationConfig = {
      'temperature': 0.6, // Controls randomness (0.0 = deterministic, 1.0 = max creative)
      'topP': 0.95,       // Nucleus sampling parameter
      'topK': 40,         // Top-k sampling parameter
      'maxOutputTokens': 2048 // Max tokens in the response (adjust based on model limits/needs)
    };

    // Construct the request body
    final body = jsonEncode({
      'contents': [{'role': 'user', 'parts': [{'text': prompt}]}],
      'generationConfig': generationConfig,
      // Standard safety settings to block harmful content
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'}
      ]
    });

    print("Sending Generation request to $_model...");
    // print("Prompt (first 200 chars): ${prompt.substring(0, prompt.length > 200 ? 200 : prompt.length)}...");
    print("Prompt length (chars): ${_calculateTokenCount(prompt)}");

    try {
       // Make the HTTP POST request with a timeout
       final response = await http.post(url, headers: headers, body: body)
           .timeout(const Duration(seconds: 90)); // Generous timeout for potentially slow responses

       print("Gemini Response Status: ${response.statusCode}");
       String responseBody = response.body;

       // Handle successful response (Status code 200)
       if (response.statusCode == 200) {
          final decoded = jsonDecode(responseBody);

          // Check for prompt blocks first (input rejected by safety filters)
          if (decoded['promptFeedback'] != null && decoded['promptFeedback']['blockReason'] != null) {
            final reason = decoded['promptFeedback']['blockReason'];
            print("Prompt blocked by Gemini: $reason.");
            throw Exception("Request blocked by AI (input safety: $reason).");
          }

          // Check if candidates exist in the response
          final candidates = decoded['candidates'];
          if (candidates != null && candidates.isNotEmpty) {
             final candidate = candidates[0];
             // Check for content blocks / unusual finish reasons
             final finishReason = candidate['finishReason'];
             if (finishReason != null && finishReason != 'STOP' && finishReason != 'MAX_TOKENS') {
               if (finishReason == 'SAFETY') {
                 print("Response blocked by Gemini (output safety).");
                 throw Exception("Response blocked by AI (output safety).");
               } else if (finishReason == 'RECITATION') {
                 print("Response blocked by Gemini (recitation).");
                 // Recitation might not be a critical error, could return empty or log warning
                 throw Exception("Response blocked by AI (recitation).");
               } else {
                 // Log other reasons (e.g., 'OTHER') but potentially continue
                 print("Warning: Gemini response incomplete or finished unexpectedly. Reason: $finishReason");
               }
             }

             // Extract text content safely
             final content = candidate['content'];
             if (content != null && content['parts'] != null && content['parts'].isNotEmpty) {
               final textPart = content['parts'][0]['text'];
               String resultText = textPart?.trim() ?? ''; // Use null-aware access and trim
               print("Gemini response OK (length: ${resultText.length}). Finish Reason: $finishReason");
               return resultText; // Return the extracted text
             } else {
                 // Handle cases where content/parts structure is missing
                 print("Response OK, but content part is missing. Finish Reason: $finishReason");
                 return ''; // Return empty string
             }
          } else {
              // Handle cases where response is OK but has no candidates
              print("Response OK, but no candidates returned by Gemini.");
              return ''; // Return empty string
          }
       } else {
          // Handle API errors (non-200 status codes)
          String errorMessage = 'AI API request failed (${response.statusCode}).';
          try {
            // Try to parse error message from response body
            final errorJson = jsonDecode(responseBody);
            errorMessage += ' Error: ${errorJson['error']?['message'] ?? 'Details unavailable.'}';
          } catch (_) {
            // Fallback if response body isn't valid JSON
            errorMessage += ' Response Body: ${responseBody.length > 200 ? responseBody.substring(0, 200) + "..." : responseBody}';
          }
          print(errorMessage);
          // Throw specific exceptions for common errors
          if (response.statusCode == 400) throw Exception('AI service error: Bad request (check prompt/parameters).');
          if (response.statusCode == 403) throw Exception('AI service error: Forbidden (Invalid API key or permissions?).');
          if (response.statusCode == 429) throw Exception('AI service error: Quota exceeded or rate limit hit.');
          if (response.statusCode >= 500) throw Exception('AI service error: Server error (${response.statusCode}).');
          // Generic exception for other client-side errors
          throw Exception('AI service error (Code: ${response.statusCode}).');
       }
    // Handle network-level errors
    } on TimeoutException catch (_) {
       print("API call timed out after 90 seconds.");
       throw Exception("Request to AI service timed out.");
    } on SocketException catch (e) {
       print("Network error during API call: $e");
       throw Exception("Network error connecting to AI service. Check internet connection.");
    } on http.ClientException catch (e) {
       print("HTTP Client error during API call: $e");
       throw Exception("Network error contacting AI service.");
    } catch (e) {
       // Catch any other unexpected errors during the API call process
       print("Unhandled error during Gemini API call: $e");
       if (e is Exception) { // Rethrow if it's already an Exception
            throw e;
       }
       // Wrap other errors in a generic exception
       throw Exception("An unexpected error occurred during the AI call.");
    }
  }

 // --- UI Build Method ---
 @override
 Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    String appBarTitle = 'College AI Assistant';
    if (widget.collegeData?['fullname']?.isNotEmpty == true) {
      appBarTitle = "${widget.collegeData!['fullname']} AI";
    }
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    // Can interact if idle AND not over limit
    final bool canInteract = _processingStep == ProcessingStep.idle && !tokenLimitReached;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent, // Modern look
        elevation: 1.0, // Subtle shadow
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: iconColor),
          onPressed: () => Navigator.pop(context), // Standard back navigation
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis, // Prevent long titles from overflowing
        ),
        actions: [
          // Voice Selection Dropdown
          if (_voicesLoaded && _availableVoices.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Map<String, String>>(
                  value: _selectedVoice, // Current selection
                  hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)),
                  icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                  // Show selected voice icon in the app bar
                  selectedItemBuilder: (context) => _availableVoices
                      .map((_) => Center(
                            child: Tooltip( // Tooltip for selected voice
                                message: _selectedVoice != null ? "${_selectedVoice!['name']} (${_selectedVoice!['locale']})" : "Select Voice",
                                child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)
                             )
                          )).toList(),
                  // Build dropdown items
                  items: _availableVoices
                      .map((voice) => DropdownMenuItem<Map<String, String>>(
                          value: voice,
                          child: Tooltip( // Tooltip for each item in dropdown
                              message: "${voice['name']} (${voice['locale']})",
                              child: SizedBox(
                                width: 180, // Fixed width for dropdown items
                                child: Text("${voice['name']} (${voice['locale']})",
                                    style: const TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis) // Prevent overflow
                              )
                          )
                       )).toList(),
                  onChanged: canInteract // Only allow changing when idle and not limit reached
                      ? (Map<String, String>? newValue) {
                          if (newValue != null && mounted) {
                            setState(() => _selectedVoice = newValue);
                            _configureTts(); // Apply the newly selected voice
                          }
                        }
                      : null, // Disable dropdown when processing
                  style: TextStyle(color: theme.colorScheme.onSurface), // Text style for dropdown
                  dropdownColor: theme.colorScheme.surfaceVariant, // Background color of dropdown
                  borderRadius: BorderRadius.circular(8),
                  elevation: 4, // Shadow for dropdown
                ),
              ),
            ),
          // Refresh (New Conversation) Button
          IconButton(
            // Dim icon if cannot interact
            icon: Icon(Icons.refresh, color: canInteract ? iconColor : theme.disabledColor),
            tooltip: "Start New Conversation",
            // Disable button if processing or limit reached
            onPressed: canInteract ? _startNewConversation : null,
          ),
        ],
      ),
      body: SafeArea( // Ensure content avoids notches, etc.
        child: Column(
          children: [
            // Chat Message List Area
            Expanded(
              child: ListView.builder(
                controller: _scrollController, // Controls scrolling
                itemCount: _messages.length, // Number of messages/indicators
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 8.0), // Padding around list
                itemBuilder: (context, index) {
                  final msg = _messages[index];
                  // Use a unique key for each chat bubble for efficient updates
                  return ChatBubble(
                    key: ValueKey("msg-${msg.hashCode}-${msg.text.length}-$index"), // More robust key
                    message: msg,
                    isDarkMode: isDark,
                    theme: theme,
                  );
                },
              ),
            ),
            // Input Area (Bottom)
            _buildInputAreaReverted(theme), // Use the updated input area builder
          ],
        ),
      ),
    );
 }

 // --- Input Area Widget (UI Updated) ---
 Widget _buildInputAreaReverted(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    final sendProgressColor = isDark ? Colors.white : Colors.black; // Color for spinner/send
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    // Determine if user can interact (idle state AND not over token limit)
    final bool canInteract = _processingStep == ProcessingStep.idle && !tokenLimitReached;

    // Dynamic Hint Text
    String hintText = 'Ask a question...'; // Default hint
    if (widget.collegeData?['fullname'] != null && widget.collegeData!['fullname']!.isNotEmpty) {
      hintText = 'Ask about ${widget.collegeData!['fullname']}...'; // College-specific hint
    }
    // Update hint based on current state
    if (!canInteract && _processingStep != ProcessingStep.idle) {
       // If processing, try to get text from the typing indicator bubble
       final typingMsg = _messages.lastWhereOrNull((m) => m.isTyping);
       hintText = typingMsg?.text ?? "Processing..."; // Use indicator text or a default
    } else if (_isContinuousListening) {
       hintText = 'Listening... Speak now'; // Hint for continuous listening mode
    } else if (tokenLimitReached){
       hintText = 'Daily limit reached'; // Hint when limit is hit
    }

    // Calculate metrics for display (keep this calculation)
    final threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = (threadMetrics['totalCost'] ?? 0.0) * _exchangeRate; // Example cost conversion

    return Container(
      // Styling for the input area container
      padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 10.0), // Padding inside the container
      margin: const EdgeInsets.only(bottom: 4.0), // Bottom margin for separation
      decoration: BoxDecoration(
        color: theme.colorScheme.surface, // Background color from theme
        boxShadow: [ // Subtle shadow above the input bar
          BoxShadow(
              offset: const Offset(0, -1), // Shadow position
              blurRadius: 3, // Shadow blurriness
              color: Colors.black.withOpacity(0.08)) // Shadow color and opacity
        ],
      ),
      child: Column( // Use Column to stack cost display and input row
        mainAxisSize: MainAxisSize.min, // Take minimum vertical space
        crossAxisAlignment: CrossAxisAlignment.start, // Align cost display to the left
        children: [
          // Cost/Token Display Row (only shown if relevant)
          if (_messageController.text.isNotEmpty || (threadMetrics['totalChars'] ?? 0) > 0 || _lastPromptTokenCount > 0 || _dailyTokenCount > 0 || _lastApiResponseCharCount > 0)
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 8.0, right: 8.0), // Padding for the cost row
              child: DefaultTextStyle(
                 style: TextStyle( // Style for the cost text
                     color: theme.colorScheme.onSurface.withOpacity(0.7), // Slightly transparent text
                     fontSize: 11, // Smaller font size
                 ),
                 child: Wrap( // Use Wrap to allow costs to flow to next line on small screens
                    spacing: 10.0, // Horizontal space between cost items
                    runSpacing: 2.0,  // Vertical space if items wrap
                    children: [
                       // Current Message Cost (updates as user types)
                       ValueListenableBuilder<TextEditingValue>(
                          valueListenable: _messageController,
                          builder: (context, value, child) {
                             int chars = _calculateTokenCount(value.text);
                             // Hide if nothing typed and no previous interaction cost shown
                             if (chars == 0 && _processingStep == ProcessingStep.idle && _lastPromptTokenCount == 0 && _lastApiResponseCharCount == 0) return const SizedBox.shrink();
                             double cost = (chars / 1000.0) * _inputCostPer1kChars;
                             double costMkw = cost * _exchangeRate;
                             if (chars > 0) return Text("Msg: ~${chars}c (\$${cost.toStringAsFixed(6)}/MKW${costMkw.toStringAsFixed(2)})");
                             return const SizedBox.shrink(); // Hide if 0 chars typed currently
                          },
                       ),
                       // Last Prompt Cost
                       if (_lastPromptTokenCount > 0) Text("Last Prompt: ~${_lastPromptTokenCount}c"),
                       // Last Response Cost
                       if (_lastApiResponseCharCount > 0) Text("Last Resp: ~${_lastApiResponseCharCount}c"),
                       // Total Thread Cost
                       if ((threadMetrics['totalChars'] ?? 0) > 0) Text("Thread: ~${threadMetrics['totalChars']}c (\$${threadMetrics['totalCost'].toStringAsFixed(6)}/MKW${threadCostMkw.toStringAsFixed(2)})"),
                       // Daily Usage Display
                       Text(
                          "Daily Chars: ${_dailyTokenCount}/${_maxDailyTokens}",
                          style: TextStyle( // Special style for daily limit
                             // Highlight in red if limit reached
                             color: tokenLimitReached ? Colors.redAccent : theme.colorScheme.onSurface.withOpacity(0.7),
                             fontWeight: tokenLimitReached ? FontWeight.bold : FontWeight.normal,
                          ),
                       ),
                    ],
                 ),
              ),
            ),

          // Input Row (TextField and Buttons)
          Row(
            crossAxisAlignment: CrossAxisAlignment.center, // Vertically center items in the row
            children: [
              // Text Input Field
              Expanded(
                child: TextField(
                  controller: _messageController, // Links to the text controller
                  onChanged: _handleManualInput, // Detects manual typing to stop STT
                  onTap: () { // Handle tap event
                     // Stop continuous listening if user taps the field
                     if (_isContinuousListening && mounted && canInteract) {
                        print("TextField tapped, stopping continuous listening.");
                        setState(() => _isContinuousListening = false);
                        _speechToText.stop();
                     }
                  },
                  // *** UI Changes for Single Line and Alignment ***
                  maxLines: 1, // Ensure it stays single line
                  keyboardType: TextInputType.text, // Standard text keyboard
                  textInputAction: TextInputAction.send, // Show 'send' button on keyboard
                  // *** End UI Changes ***
                  decoration: InputDecoration(
                    hintText: hintText, // Display dynamic hint text
                    hintStyle: TextStyle( // Style for hint text
                      color: !canInteract
                          ? theme.disabledColor.withOpacity(0.6) // Dimmed hint when disabled
                          : (_isContinuousListening
                             ? iconColor.withOpacity(0.9) // Brighter hint when actively listening
                             : theme.hintColor.withOpacity(0.6)), // Standard hint color
                    ),
                    // Remove internal borders for a cleaner look
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    // Adjust padding for vertical centering within the single line height
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  ),
                  style: TextStyle( // Style for the actual typed text
                    color: canInteract ? theme.colorScheme.onSurface : theme.disabledColor, // Dim text if disabled
                    fontSize: 15,
                  ),
                  textCapitalization: TextCapitalization.sentences, // Auto-capitalize sentences
                  onSubmitted: (value) { // Handle submission via keyboard action
                    if (canInteract && value.trim().isNotEmpty) {
                      _sendMessage(value); // Send message if interactable and text exists
                    }
                  },
                  enabled: canInteract, // Disable text field input if cannot interact
                  cursorColor: canInteract ? theme.colorScheme.primary : Colors.transparent, // Themed cursor color
                ),
              ),
              // Microphone Button (for STT)
              IconButton(
                 // Toggle icon based on listening state
                 icon: Icon(
                   _isContinuousListening ? Icons.mic : Icons.mic_none,
                   // Dim icon if cannot interact
                   color: canInteract ? iconColor : theme.disabledColor,
                 ),
                 tooltip: _isContinuousListening ? "Stop Listening" : "Start Continuous Listening",
                 visualDensity: VisualDensity.compact, // Reduce button padding
                 splashRadius: 20, // Smaller splash effect
                 // Disable button if cannot interact
                 onPressed: canInteract ? _toggleContinuousListening : null,
              ),
              // Send Button OR Loading Spinner
              (_processingStep != ProcessingStep.idle)
                  // Show spinner if processing
                  ? Padding(
                      // Use consistent padding like IconButtons
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        width: 24, height: 24, // Standard icon button dimensions
                        child: CircularProgressIndicator( strokeWidth: 2.5, color: sendProgressColor, ), // Spinner
                      ),
                    )
                  // Show Send button if idle
                  : IconButton(
                      icon: Icon(
                        Icons.send,
                        // Dim send icon if text is empty or cannot interact
                        color: _messageController.text.trim().isEmpty || !canInteract
                            ? theme.disabledColor
                            : sendProgressColor,
                      ),
                      tooltip: "Send Message",
                      visualDensity: VisualDensity.compact, // Reduce padding
                      splashRadius: 20, // Smaller splash
                      // Disable send button if text is empty or cannot interact
                      onPressed: !canInteract || _messageController.text.trim().isEmpty
                          ? null
                          : () => _sendMessage(_messageController.text),
                    ),
              // Mute/Unmute Button (for TTS)
              IconButton(
                 // Toggle icon based on mute state
                 icon: Icon(
                    _isMuted ? Icons.volume_off_outlined : Icons.volume_up_outlined,
                    // Dim icon if cannot interact
                    color: canInteract ? iconColor : theme.disabledColor,
                    size: 22, // Slightly smaller icon size
                 ),
                 visualDensity: VisualDensity.compact, // Reduce padding
                 splashRadius: 20, // Smaller splash
                 tooltip: _isMuted ? "Unmute TTS" : "Mute TTS",
                 // Disable button if cannot interact
                 onPressed: canInteract ? () {
                    if (mounted) {
                       setState(() => _isMuted = !_isMuted); // Toggle mute state
                       if (_isMuted) _flutterTts.stop(); // Stop speaking immediately if muted
                    }
                 } : null,
              ),
            ],
          ),
        ],
      ),
    );
 }


 // --- Helper Methods ---
 bool get tokenLimitReached => _dailyTokenCount >= _maxDailyTokens;

 // Calculate Thread Metrics
 // (Restored full implementation from previous step)
 Map<String, dynamic> _calculateThreadMetrics() {
     int inputChars = 0;
     int outputChars = 0;
     int totalChars = 0; // Include greeting in total display

     for (int i = 0; i < _messages.length; i++) {
       final msg = _messages[i];
       // Only count non-typing messages with actual content
       if (!msg.isTyping && msg.text.trim().isNotEmpty) {
         int chars = _calculateTokenCount(msg.text);
         totalChars += chars; // Add all message chars to total display count

         // Determine if it's the initial greeting message
         // (Consider making this check more robust if greeting text changes)
         bool isInitialGreeting = i == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");

         if (msg.isUser) {
           // Count all user message chars for input cost
           inputChars += chars;
         } else if (!isInitialGreeting) {
           // Count AI responses (excluding initial greeting) for output cost
           outputChars += chars;
         }
       }
     }
     // Calculate costs based on character counts
     double inCost = (inputChars / 1000.0) * _inputCostPer1kChars;
     double outCost = (outputChars / 1000.0) * _outputCostPer1kChars;

     // Return the map with calculated values
     return {
       'inputChars': inputChars,
       'outputChars': outputChars, // Costed output chars (excludes greeting)
       'totalChars': totalChars,    // Total displayed chars (includes greeting)
       'inputCost': inCost,
       'outputCost': outCost,
       'totalCost': inCost + outCost, // Total cost (excludes greeting cost)
     };
 }

 // Add/Update Typing Indicator
 void _addOrUpdateTypingIndicator(String text) {
   if (!mounted) return;
   // Remove any existing typing indicators first to prevent duplicates
   final initialLength = _messages.length;
   _messages.removeWhere((m) => m.isTyping);
   bool removed = _messages.length < initialLength;

   // Add the new indicator message
   final newIndicator = ChatMessage(text: text, isUser: false, isTyping: true);
   _messages.add(newIndicator);

   // Only call setState if the list actually changed (indicator removed or added)
   // This prevents unnecessary rebuilds if called multiple times with same text
   // We must call setState to reflect the addition/update
   setState(() {});
   _scrollToBottom(); // Scroll after state update to show the indicator
 }

 // Remove Typing Indicator
 void _removeTypingIndicator() {
   if (!mounted) return;
   final initialLength = _messages.length;
   _messages.removeWhere((m) => m.isTyping); // Remove all typing indicators
   // Only call setState if an indicator was actually removed
   if (_messages.length < initialLength) {
     setState(() {});
   }
 }

 // Build Conversation History for Prompt Context
 // (Restored full implementation from previous step)
 String _buildConversationHistory() {
     final sb = StringBuffer();
     const maxHistoryMessages = 6; // Number of recent messages to include
     // Calculate start index, ensuring it's not negative
     final startIndex = (_messages.length > maxHistoryMessages)
         ? _messages.length - maxHistoryMessages
         : 0;

     for (int i = startIndex; i < _messages.length; i++) {
        final msg = _messages[i];
        // Conditions to EXCLUDE a message from history:
        // 1. It's a typing indicator.
        // 2. Its text is empty/whitespace.
        // 3. It's the *very last* message AND it's from the user AND we are currently processing it.
        // 4. It's the initial greeting message.
        bool isCurrentUserMessageBeingProcessed = msg.isUser &&
                                                i == _messages.length - 1 &&
                                                _processingStep != ProcessingStep.idle;
        bool isInitialGreeting = i == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");

        if (!msg.isTyping && msg.text.trim().isNotEmpty && !isCurrentUserMessageBeingProcessed && !isInitialGreeting) {
          // Append formatted message (user: or model:) to the history string buffer
          sb.writeln(msg.isUser ? 'user: ${msg.text}' : 'model: ${msg.text}');
        }
     }
     return sb.toString(); // Return the compiled history string
 }

 // Add Initial Greeting Message
 void _addInitialGreeting() {
     String collegeName = widget.collegeData?['fullname']?.toString().isNotEmpty == true
         ? widget.collegeData!['fullname']!
         : 'the selected institution'; // Fallback name
     String greeting = "Hi! I'm the AI Assistant for $collegeName. How can I help you today?";
     final initialMessage = ChatMessage(text: greeting, isUser: false);

     if(mounted){
       setState(() {
           _messages.add(initialMessage);
           // Don't count initial greeting towards billable token count in _calculateThreadMetrics
           // But DO count it towards the daily display count here for consistency? Or ignore?
           // Let's ignore it for daily count too, to match cost calculation.
           // _dailyTokenCount += _calculateTokenCount(greeting);
        });
       // Speak the greeting if not muted
       if (!_isMuted) {
           _configureTts().then((_) { // Ensure TTS is configured before speaking
               if (!_isMuted && mounted) _flutterTts.speak(greeting);
           });
       }
     }
 }

 // Start New Conversation (Clear History and State)
 void _startNewConversation() {
     // Only allow if not currently processing
     if(_processingStep != ProcessingStep.idle) return;

     if (mounted) {
       // Stop continuous listening if active
       if (_isContinuousListening) {
           setState(() => _isContinuousListening = false);
           _speechToText.stop();
       }
       // Clear messages and reset tracking variables
       setState(() {
           _messages.clear();
           _lastPromptTokenCount = 0;
           _lastApiResponseCharCount = 0;
           _dailyTokenCount = 0; // Reset daily count for new conversation
       });
       _addInitialGreeting(); // Add the greeting message back
     }
 }

 // Show Error Message in Chat
 void _showError(String errorMessageText) {
     final errorMsg = ChatMessage(text: errorMessageText, isUser: false);
     if (mounted) {
       _removeTypingIndicator(); // Remove processing indicator before showing error
       setState(() { _messages.add(errorMsg); }); // Add error message to chat list
       _scrollToBottom(); // Scroll to show the error
       // Speak the error message if not muted
       if (!_isMuted) {
           _configureTts().then((_) {
               if(!_isMuted && mounted) _flutterTts.speak(errorMessageText);
           });
       }
     }
 }

 // Scroll To Bottom of Chat List
 void _scrollToBottom() {
     // Use addPostFrameCallback to ensure scroll happens after build
     WidgetsBinding.instance.addPostFrameCallback((_) {
         // Check if scroll controller is attached and widget is still mounted
         if (_scrollController.hasClients && mounted) {
             _scrollController.animateTo(
                 _scrollController.position.maxScrollExtent, // Target position: end of list
                 duration: const Duration(milliseconds: 300), // Animation duration
                 curve: Curves.easeOut); // Animation curve
         }
     });
 }

 // Get Relevant College Fields using Keywords (from base collegeData)
 // (Restored full implementation from previous step)
 List<String> _getRelevantCollegeFields(String query, {int max = 7}) {
      final lowerQuery = query.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), ''); // Clean query
      final scores = <String, int>{};

      // --- Initial Scores (Prioritize key fields) ---
      // High priority for core identification and contact
      scores['fullname'] = 100;
      scores['about'] = 50;
      scores['website'] = 50;
      scores['address'] = 40;
      scores['city'] = 40;
      scores['state'] = 40;
      scores['phone'] = 40;
      scores['email'] = 40;
      // Moderate priority for other common general info
      scores['mission'] = 30;
      scores['vision'] = 30;
      // Map 'daysnhours' keywords to 'hours' field if it exists in collegeData
      scores['daysnhours'] = 25; // Score for keywords matching 'daysnhours'
      scores['institutiontype'] = 20;
      scores['accreditation'] = 20;
      scores['founded'] = 15;

      // --- Keyword Scoring ---
      // Iterate through the keywords defined for the main 'colleges' table fields
      _collegeFieldKeywords.forEach((field, keywords) {
        // Start with initial score or 0 if not initially prioritized
        int currentScore = scores[field] ?? 0;
        for (var kw in keywords) {
          if (lowerQuery.contains(kw)) {
             // Increase score based on keyword match
             // Give more weight to multi-word keywords and exact word matches
             currentScore += (kw.contains(' ') ? 3 : 1); // Base score
             if (RegExp(r'\b' + RegExp.escape(kw) + r'\b').hasMatch(lowerQuery)) {
                currentScore += 2; // Bonus for whole word match
             }
          }
        }
        // Bonus if the field name itself (case-insensitive) is in the query
        if (RegExp(r'\b' + RegExp.escape(field.toLowerCase()) + r'\b', caseSensitive: false).hasMatch(lowerQuery)) {
           currentScore += 5;
        }
        // Update score in the map if it has increased
        if (currentScore > (scores[field] ?? 0) ) {
           scores[field] = currentScore;
        }
      });

      // --- Sorting and Selection ---
      // Get field names with scores > 0
      final scoredFields = scores.keys.where((key) => scores[key]! > 0).toList();
      // Sort these fields by score descending
      scoredFields.sort((a, b) => scores[b]!.compareTo(scores[a]!));

      // Take the top 'max' fields after sorting
      List<String> finalFields = scoredFields.take(max).toList();

      // Ensure 'fullname' is always included if it has a score, and place it first
      if (scores['fullname']! > 0 && !finalFields.contains('fullname')) {
          // If 'fullname' has score but isn't in top 'max', add it
          if (finalFields.length >= max) finalFields.removeLast(); // Make space if list is full
          finalFields.insert(0, 'fullname'); // Add 'fullname' at the beginning
      } else if (finalFields.contains('fullname')) {
          // If 'fullname' is already in the list, move it to the beginning
          finalFields.remove('fullname');
          finalFields.insert(0, 'fullname');
      }

      // Log the selected fields and their scores for debugging
      final selectedScores = Map.fromEntries(finalFields.map((f) => MapEntry(f, scores[f] ?? 0)));
      print("Relevant college fields selected for query context: $finalFields (Scores: $selectedScores)");

      return finalFields; // Return the final list of field names
 }


 // Calculate Token Count (Using simple character count)
 int _calculateTokenCount(String text) {
    // Simple character count is a proxy for token count.
    // Actual token count depends on the model's tokenizer.
    return text.trim().length;
 }

} // End of _AiAgentPageState class
