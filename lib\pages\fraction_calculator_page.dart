import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FractionCalculatorPage extends StatefulWidget {
  const FractionCalculatorPage({Key? key}) : super(key: key);

  @override
  _FractionCalculatorPageState createState() => _FractionCalculatorPageState();
}

class _FractionCalculatorPageState extends State<FractionCalculatorPage> {
  String _fraction1 = '';
  String _fraction2 = '';
  String _result = '';
  String _operation = '+'; // Default operation

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Fraction Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Fraction 1 (e.g., 1/2)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _fraction1 = value;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  DropdownButton<String>(
                    value: _operation,
                    dropdownColor: theme.colorScheme.surface,
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    items: <String>['+', '-', '*', '/'].map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, style: TextStyle(color: theme.colorScheme.onSurface)),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        _operation = newValue!;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Fraction 2 (e.g., 3/4)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _fraction2 = value;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      // Implement fraction calculation logic here
                      setState(() {
                        _result = 'Result will be here'; // Placeholder result
                      });
                    },
                    child: const Text('Calculate Fractions'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_result',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}