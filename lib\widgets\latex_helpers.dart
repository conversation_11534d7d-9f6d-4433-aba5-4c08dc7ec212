import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_math_fork/flutter_math.dart';

// Content segment enum
enum ContentType { text, latex, codeBlock, table }

// Content segment class
class ContentSegment {
  final String content;
  final ContentType type;

  ContentSegment(this.content, this.type);
}

// Helper method to remove preliminary text from LLM responses
String removePreliminaryText(String content) {
  // Remove common prefixes like "Here's the solution:" or "Here's your answer:"
  final prefixRegExp = RegExp(r'^(Here is|Here are|I have|Let me).*?\n\n', caseSensitive: false);
  return content.replaceFirst(prefixRegExp, '');
}

// Split content by regex pattern
void splitByRegex(String content, RegExp pattern, List<ContentSegment> segments, ContentType matchType) {
  int lastMatchEnd = 0;
  for (var match in pattern.allMatches(content)) {
    if (match.start > lastMatchEnd) {
      // Add text segment before the match
      segments.add(ContentSegment(content.substring(lastMatchEnd, match.start), ContentType.text));
    }
    // Add the matched segment with the specified type
    segments.add(ContentSegment(match.group(0)!, matchType));
    lastMatchEnd = match.end;
  }
  if (lastMatchEnd < content.length) {
    // Add remaining text after the last match
    segments.add(ContentSegment(content.substring(lastMatchEnd), ContentType.text));
  }
  // If no matches were found, add the entire content as text
  if (segments.isEmpty) {
    segments.add(ContentSegment(content, ContentType.text));
  }
}

// Build markdown widget
Widget buildMarkdownWidget(String content, Color textColor, double fontSize) {
  // Process <sup> and <sub> tags before rendering markdown
  content = _processSupSubTags(content);

  return MarkdownBody(
    data: content,
    styleSheet: MarkdownStyleSheet(
      p: TextStyle(fontSize: fontSize, color: textColor, height: 1.8),
      h1: TextStyle(fontSize: fontSize * 1.5, color: textColor, fontWeight: FontWeight.bold),
      h2: TextStyle(fontSize: fontSize * 1.3, color: textColor, fontWeight: FontWeight.bold),
      h3: TextStyle(fontSize: fontSize * 1.1, color: textColor, fontWeight: FontWeight.bold),
      code: TextStyle(fontSize: fontSize * 0.9, color: textColor, fontFamily: 'monospace'),
      codeblockDecoration: BoxDecoration(
        color: textColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
    ),
  );
}

// Process <sup> and <sub> tags to convert them to Unicode superscript/subscript characters
String _processSupSubTags(String content) {
  // Handle <sup> tags
  content = content.replaceAllMapped(
    RegExp(r'<sup>(.*?)</sup>'),
    (match) => _convertToSuperscript(match.group(1) ?? ''),
  );

  // Handle <sub> tags
  content = content.replaceAllMapped(
    RegExp(r'<sub>(.*?)</sub>'),
    (match) => _convertToSubscript(match.group(1) ?? ''),
  );

  return content;
}

// Convert text to superscript Unicode characters
String _convertToSuperscript(String text) {
  final Map<String, String> superscriptMap = {
    '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
    '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
    'n': 'ⁿ', 'i': 'ⁱ', 'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ',
    'd': 'ᵈ', 'e': 'ᵉ', 'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ',
    'j': 'ʲ', 'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'o': 'ᵒ',
    'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ',
    'v': 'ᵛ', 'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += superscriptMap[char] ?? char;
  }
  return result;
}

// Convert text to subscript Unicode characters
String _convertToSubscript(String text) {
  final Map<String, String> subscriptMap = {
    '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
    '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
    '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
    'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ', 'i': 'ᵢ', 'j': 'ⱼ',
    'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ',
    'p': 'ₚ', 'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ',
    'v': 'ᵥ', 'x': 'ₓ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += subscriptMap[char] ?? char;
  }
  return result;
}

// Build LaTeX widget with improved rendering and retry logic
Widget buildLatexWidget(String content, Color textColor, Color errorColor, double fontSize) {
  // Handle different LaTeX delimiters
  String texString = content;
  bool isDisplayMode = false;

  if (content.startsWith(r'$$') && content.endsWith(r'$$')) {
    texString = content.substring(2, content.length - 2);
    isDisplayMode = true;
  } else if (content.startsWith(r'$') && content.endsWith(r'$')) {
    texString = content.substring(1, content.length - 1);
  } else if (content.startsWith(r'\[') && content.endsWith(r'\]')) {
    texString = content.substring(2, content.length - 2);
    isDisplayMode = true;
  } else if (content.startsWith(r'\(') && content.endsWith(r'\)')) {
    texString = content.substring(2, content.length - 2);
  }

  // Fix common LaTeX errors
  texString = _fixCommonLatexErrors(texString);

  // Use a StatefulBuilder to allow refreshing the widget if needed
  return StatefulBuilder(
    builder: (context, setState) {
      return FutureBuilder(
        // Use a small delay to ensure fonts are loaded
        future: Future.delayed(const Duration(milliseconds: 50)),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show loading indicator while waiting for fonts
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Center(
                child: SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.0,
                    valueColor: AlwaysStoppedAnimation<Color>(textColor),
                  ),
                ),
              ),
            );
          }

          return Container(
            padding: EdgeInsets.symmetric(vertical: isDisplayMode ? 8.0 : 4.0),
            decoration: BoxDecoration(
              // Ensure white background in light mode for better contrast
              color: textColor == Colors.black ? Colors.white : Colors.transparent,
              borderRadius: BorderRadius.circular(4),
            ),
            width: double.infinity,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Math.tex(
                texString,
                textStyle: TextStyle(fontSize: fontSize, color: textColor),
                mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
                onErrorFallback: (error) {
                  print('LaTeX parsing error: $error for input: $texString');

                  // Try to fix more complex LaTeX errors
                  final fixedTeX = _fixAdvancedLatexErrors(texString);
                  if (fixedTeX != texString) {
                    try {
                      return Math.tex(
                        fixedTeX,
                        textStyle: TextStyle(fontSize: fontSize, color: textColor),
                        mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
                      );
                    } catch (e) {
                      print('Still failed after fixing: $e');
                    }
                  }

                  // If we still have an error, show the raw LaTeX with a retry button
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        content,
                        style: TextStyle(fontSize: fontSize * 0.9, color: errorColor, fontFamily: 'monospace'),
                      ),
                      TextButton.icon(
                        icon: Icon(Icons.refresh, size: 16, color: textColor),
                        label: Text('Retry Rendering', style: TextStyle(fontSize: 12, color: textColor)),
                        onPressed: () {
                          // Force a rebuild of the widget
                          setState(() {});
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
          );
        },
      );
    },
  );
}

// Advanced LaTeX error fixing for more complex cases
String _fixAdvancedLatexErrors(String texString) {
  String fixed = texString;

  // Fix missing backslashes before common LaTeX commands
  final commonCommands = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
                         'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'rho', 'sigma',
                         'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega', 'sum', 'prod', 'int',
                         'frac', 'sqrt', 'times', 'div', 'pm', 'leq', 'geq', 'neq', 'approx',
                         'begin', 'end', 'matrix', 'bmatrix', 'pmatrix', 'vmatrix'];

  for (final cmd in commonCommands) {
    // Only add backslash if it's not already there
    fixed = fixed.replaceAllMapped(RegExp('(?<!\\\\)$cmd'), (match) => '\\$cmd');
  }

  // Fix matrix formatting issues - ensure proper row separators
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\begin\{([bpv]?matrix)\}(.*?)\\end\{\1\}', dotAll: true),
    (match) {
      String matrixType = match.group(1)!;
      String content = match.group(2)!;

      // Ensure proper row separators (\\) and column separators (&)
      content = content.replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
      content = content.replaceAll(RegExp(r'(?<!\\\)(\s+)(?=\d|\w)'), r' & '); // Add column separators
      content = content.replaceAll(RegExp(r'(\d|\w)\s+(\d|\w)'), r'$1 & $2'); // Fix missing column separators

      return '\\begin{$matrixType}$content\\end{$matrixType}';
    }
  );

  // Fix unbalanced braces
  int openBraces = 0;
  int closeBraces = 0;
  for (int i = 0; i < fixed.length; i++) {
    if (fixed[i] == '{') openBraces++;
    if (fixed[i] == '}') closeBraces++;
  }

  // Add missing closing braces
  if (openBraces > closeBraces) {
    fixed += '}' * (openBraces - closeBraces);
  }

  // Fix missing closing brackets in \frac{}{} commands
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\frac\{([^{}]*)\}\{([^{}]*)(?!\})'),
    (match) => '\\frac{${match.group(1)}}{${match.group(2)}}'
  );

  return fixed;
}

// Build code block widget
Widget buildCodeBlockWidget(String content, Color textColor, double fontSize, bool isDarkMode) {
  // Extract code from markdown code block
  final codeMatch = RegExp(r'```(?:.*?)\n(.*?)```', dotAll: true).firstMatch(content);
  final code = codeMatch != null ? codeMatch.group(1)! : content.replaceAll('```', '');

  return Container(
    decoration: BoxDecoration(
      color: isDarkMode ? Colors.grey[900] : Colors.grey[100],
      borderRadius: BorderRadius.circular(4),
    ),
    padding: const EdgeInsets.all(8),
    margin: const EdgeInsets.symmetric(vertical: 8),
    child: SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Text(
        code,
        style: TextStyle(
          fontSize: fontSize * 0.9,
          color: textColor,
          fontFamily: 'monospace',
        ),
      ),
    ),
  );
}

// Build table widget
Widget buildTableWidget(String content, Color textColor, double fontSize) {
  // Parse markdown table
  final lines = content.trim().split('\n');
  if (lines.length < 3) return Text(content, style: TextStyle(color: textColor));

  // Extract header and rows
  final headerCells = lines[0].split('|')
      .where((cell) => cell.trim().isNotEmpty)
      .map((cell) => cell.trim())
      .toList();

  final rows = <List<String>>[];
  for (int i = 2; i < lines.length; i++) {
    final rowCells = lines[i].split('|')
        .where((cell) => cell.trim().isNotEmpty)
        .map((cell) => cell.trim())
        .toList();
    if (rowCells.isNotEmpty) rows.add(rowCells);
  }

  return SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Table(
      border: TableBorder.all(color: textColor.withOpacity(0.3)),
      defaultColumnWidth: const IntrinsicColumnWidth(),
      children: [
        // Header row
        TableRow(
          decoration: BoxDecoration(color: textColor.withOpacity(0.1)),
          children: headerCells.map((cell) => TableCell(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(cell, style: TextStyle(fontWeight: FontWeight.bold, fontSize: fontSize, color: textColor)),
            ),
          )).toList(),
        ),
        // Data rows
        ...rows.map((rowCells) => TableRow(
          children: rowCells.map((cell) => TableCell(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(cell, style: TextStyle(fontSize: fontSize, color: textColor)),
            ),
          )).toList(),
        )),
      ],
    ),
  );
}

// Helper method to fix common LaTeX errors
String _fixCommonLatexErrors(String texString) {
  // Replace common LaTeX errors
  String fixed = texString;

  // Fix spacing issues around operators
  fixed = fixed.replaceAll(r'\\', r'\\\\');

  // Fix common fraction issues
  fixed = fixed.replaceAll(r'\frac', r'\frac');

  // Fix matrix environments - ensure proper brackets
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\begin\{matrix\}(.*?)\\end\{matrix\}', dotAll: true),
    (match) => '\\begin{bmatrix}${match.group(1)}\\end{bmatrix}'
  );

  // Fix pmatrix (parentheses matrix) environments
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\begin\{pmatrix\}(.*?)\\end\{pmatrix\}', dotAll: true),
    (match) => '\\begin{pmatrix}${match.group(1)}\\end{pmatrix}'
  );

  // Fix vmatrix (vertical bar matrix) environments
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\begin\{vmatrix\}(.*?)\\end\{vmatrix\}', dotAll: true),
    (match) => '\\begin{vmatrix}${match.group(1)}\\end{vmatrix}'
  );

  // Fix missing braces in subscripts and superscripts
  fixed = fixed.replaceAllMapped(
    RegExp(r'_([a-zA-Z0-9])(?![a-zA-Z0-9\{])'),
    (match) => '_{${match.group(1)}}'
  );

  fixed = fixed.replaceAllMapped(
    RegExp(r'\^([a-zA-Z0-9])(?![a-zA-Z0-9\{])'),
    (match) => '^{${match.group(1)}}'
  );

  return fixed;
}

// Combined Markdown and LaTeX renderer widget with auto-retry
Widget buildLatexContent(String content, bool isDarkMode, double fontSize) {
  final textColor = isDarkMode ? Colors.white : Colors.black;
  final errorColor = isDarkMode ? Colors.red[300]! : Colors.red[800]!;

  // Use StatefulBuilder to allow refreshing the entire content if needed
  return StatefulBuilder(
    builder: (context, setState) {
      // Remove LLM preliminary text
      content = removePreliminaryText(content);

      // Regular expressions for different elements
      final codeBlockRegExp = RegExp(r'```(?:.*?)\n(.*?)```', dotAll: true);
      final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
      final tableRegExp = RegExp(r'(\|.*\|\n\|[-:\s|]*\|\n(?:\|.*\|\n)+)', dotAll: true);

      // Process the content in stages
      List<ContentSegment> segments = [];

  // First, identify code blocks (they take precedence)
  splitByRegex(content, codeBlockRegExp, segments, ContentType.codeBlock);

  // Then process tables in the non-code segments
  List<ContentSegment> afterTableSegments = [];
  for (var segment in segments) {
    if (segment.type == ContentType.codeBlock) {
      afterTableSegments.add(segment);
    } else {
      splitByRegex(segment.content, tableRegExp, afterTableSegments, ContentType.table);
    }
  }

  // Finally, process LaTeX in the non-code, non-table segments
  List<ContentSegment> finalSegments = [];
  for (var segment in afterTableSegments) {
    if (segment.type == ContentType.codeBlock || segment.type == ContentType.table) {
      finalSegments.add(segment);
    } else {
      splitByRegex(segment.content, latexRegExp, finalSegments, ContentType.latex);
    }
  }

      // Build widgets from the segments
      List<Widget> widgets = [];
      for (var segment in finalSegments) {
        switch (segment.type) {
          case ContentType.text:
            widgets.add(buildMarkdownWidget(segment.content, textColor, fontSize));
            break;

          case ContentType.latex:
            widgets.add(buildLatexWidget(segment.content, textColor, errorColor, fontSize));
            break;

          case ContentType.codeBlock:
            widgets.add(buildCodeBlockWidget(segment.content, textColor, fontSize, isDarkMode));
            break;

          case ContentType.table:
            widgets.add(buildTableWidget(segment.content, textColor, fontSize));
            break;
        }
      }

      // Check if we have any raw LaTeX code that wasn't properly rendered
      bool hasRawLatex = false;
      if (latexRegExp.hasMatch(content)) {
        for (var match in latexRegExp.allMatches(content)) {
          String latexContent = match.group(0)!;
          // Check if this LaTeX content appears as raw text in any of our widgets
          for (var widget in widgets) {
            if (widget is Text && widget.data == latexContent) {
              hasRawLatex = true;
              break;
            }
          }
          if (hasRawLatex) break;
        }
      }

      // Add a retry button if we detected raw LaTeX
      if (hasRawLatex) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Center(
              child: TextButton.icon(
                icon: Icon(Icons.refresh, size: 16, color: textColor),
                label: Text('Retry LaTeX Rendering', style: TextStyle(color: textColor)),
                onPressed: () {
                  // Force a rebuild of the widget
                  setState(() {});
                },
              ),
            ),
          ),
        );
      }

      return Container(
        decoration: BoxDecoration(
          // Ensure white background in light mode for better contrast
          color: isDarkMode ? Colors.transparent : Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: widgets,
        ),
      );
    },
  );
}
