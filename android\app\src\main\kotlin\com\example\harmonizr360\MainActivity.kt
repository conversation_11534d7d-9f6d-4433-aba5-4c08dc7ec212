package com.example.harmonizr360 // THIS IS YOUR PACKAGE NAME

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.content.Intent
import android.net.Uri
import android.media.MediaScannerConnection

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.refactrai.studybuddy/media_scanner" // CHANNEL NAME - MAKE SURE IT MATCHES FLUTTER SIDE

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            when (call.method) {
                "scanFile" -> {
                    val filePath = call.argument<String>("filePath")
                    if (filePath != null) {
                        scanFileForGallery(filePath) { success ->
                            if (success) {
                                result.success(null) // Indicate success to Flutter
                            } else {
                                result.error("SCAN_FAILED", "MediaScannerConnection failed", null)
                            }
                        }
                    } else {
                        result.error("INVALID_ARGUMENT", "File path is null", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun scanFileForGallery(filePath: String, callback: (Boolean) -> Unit) {
        MediaScannerConnection.scanFile(
            applicationContext,
            arrayOf(filePath),
            null // Mime types, can be null to use media-type from file extension.
        ) { path, uri ->
            if (uri != null) {
                println("MediaScannerConnection completed for path: $path, URI: $uri")
                callback(true)
            } else {
                println("MediaScannerConnection failed for path: $path")
                callback(false)
            }
        }
    }
}