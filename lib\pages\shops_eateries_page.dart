import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'shop_eatery_detail_page.dart';
import 'login_page.dart';

class ShopsEateriesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedShopsEateries;
  final bool isFromDetailPage;

  const ShopsEateriesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedShopsEateries,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ShopsEateriesPageState createState() => _ShopsEateriesPageState();
}

class _ShopsEateriesPageState extends State<ShopsEateriesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('shops_eateries_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _shopsEateries = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _selectedFilter = 'All';
  List<String> _filterOptions = ['All', 'Shop', 'Eatery'];
  List<String> _typeOptions = [];

  @override
  void initState() {
    super.initState();
    print("ShopsEateriesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedShopsEateries != null &&
        widget.preloadedShopsEateries!.isNotEmpty) {
      setState(() {
        _shopsEateries = List.from(widget.preloadedShopsEateries!);
        _isLoading = false;
        _updateTypeOptions();
      });
    } else {
      _loadShopsEateriesFromDatabase();
    }
  }

  void _updateTypeOptions() {
    Set<String> types = {};
    for (var item in _shopsEateries) {
      if (item['type'] != null && (item['type'] as String).isNotEmpty) {
        types.add(item['type'] as String);
      }
    }
    setState(() {
      _typeOptions = types.toList()..sort();
    });
  }

  void _setupRealtime() {
    final shopsEateriesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shopsoreateries';
    _realtimeChannel = Supabase.instance.client
        .channel('shops_eateries_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: shopsEateriesTableName,
      callback: (payload) async {
        print("Realtime update received for shops & eateries: ${payload.eventType}");
        _loadShopsEateriesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadShopsEateriesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _shopsEateries = [];
    });

    await _loadMoreShopsEateries();
  }

  Future<void> _loadMoreShopsEateries() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final shopsEateriesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shopsoreateries';
      var query = Supabase.instance.client
          .from(shopsEateriesTableName)
          .select('*');
      
      if (_selectedFilter == 'Shop') {
        query = query.eq('type', 'Shop');
      } else if (_selectedFilter == 'Eatery') {
        query = query.eq('type', 'Eatery');
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _shopsEateries.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
        _updateTypeOptions();
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading shops & eateries: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading shops & eateries: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreShopsEateries();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Shops & Eateries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Filter dropdown
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Filter: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface,
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.colorScheme.surface,
                        items: _filterOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != _selectedFilter) {
                            setState(() {
                              _selectedFilter = newValue;
                              _page = 0;
                              _shopsEateries = [];
                              _hasMore = true;
                            });
                            _loadMoreShopsEateries();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Shops & Eateries list
          Expanded(
            child: VisibilityDetector(
              key: const Key('shops_eateries_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _shopsEateries.isEmpty && !_isLoading) {
                  _loadShopsEateriesFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadShopsEateriesFromDatabase,
                child: _shopsEateries.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No shops or eateries found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _shopsEateries.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _shopsEateries.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildShopEateryCard(
                            _shopsEateries[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildShopEateryCard(
    Map<String, dynamic> shopEatery,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = shopEatery['fullname'] ?? 'Unknown';
    final String type = shopEatery['type'] ?? '';
    final String building = shopEatery['building'] ?? '';
    final String room = shopEatery['room'] ?? '';
    final String hours = shopEatery['hours'] ?? '';
    final String payment = shopEatery['payment'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ShopEateryDetailPage(
                shopEatery: shopEatery,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  type == 'Shop' ? Icons.shopping_bag : Icons.restaurant,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (type.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          type,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
