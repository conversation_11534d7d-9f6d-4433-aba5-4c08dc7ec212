// makerspaces_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'makerspace_detail_page.dart';

class MakerspacesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedMakerspaces;
  final bool isFromDetailPage;

  const MakerspacesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedMakerspaces,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<MakerspacesPage> createState() => _MakerspacesPageState();
}

class _MakerspacesPageState extends State<MakerspacesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('makerspaces_list');
  List<Map<String, dynamic>> _makerspaces = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("MakerspacesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant MakerspacesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("MakerspacesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("MakerspacesPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("MakerspacesPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedMakerspaces != null && widget.preloadedMakerspaces!.isNotEmpty) {
      print("Preloaded makerspaces found, using them.");
      setState(() {
        _makerspaces = List<Map<String, dynamic>>.from(widget.preloadedMakerspaces!);
        _makerspaces.forEach((makerspace) {
          makerspace['_isImageLoading'] = false;
        });
        _makerspaces.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedMakerspaces!.length == _pageSize;
      });
      _loadMakerspacesFromSupabase(initialLoad: false);
    } else {
      print("No preloaded makerspaces or empty list, loading from database.");
      _loadMakerspacesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadMakerspacesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadMakerspacesFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final makerspacesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_makerspaces';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(makerspacesTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedMakerspaces =
          await _updateMakerspaceImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _makerspaces = updatedMakerspaces;
        } else {
          _makerspaces.addAll(updatedMakerspaces);
        }
        _makerspaces.forEach((makerspace) {
          makerspace['_isImageLoading'] = false;
        });
        _makerspaces.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the makerspaces
      _cacheMakerspaces(_makerspaces);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching makerspaces: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateMakerspaceImageUrls(
      List<Map<String, dynamic>> makerspaces) async {
    List<Future<void>> futures = [];
    for (final makerspace in makerspaces) {
      if (makerspace['image_url'] == null ||
          makerspace['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(makerspace));
      }
    }
    await Future.wait(futures);
    return makerspaces;
  }

  Future<void> _cacheMakerspaces(List<Map<String, dynamic>> makerspaces) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String makerspacesJson = jsonEncode(makerspaces);
      await prefs.setString(
          'makerspaces_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          makerspacesJson);
    } catch (e) {
      print('Error caching makerspaces: $e');
    }
  }
  
  void _setupRealtime() {
    final makerspacesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_makerspaces';
    _realtimeChannel = Supabase.instance.client
        .channel('makerspaces')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: makerspacesTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newMakerspaceId = payload.newRecord['id'];
          final newMakerspaceResponse = await Supabase.instance.client
              .from(makerspacesTableName)
              .select('*')
              .eq('id', newMakerspaceId)
              .single();
          if (mounted) {
            Map<String, dynamic> newMakerspace = Map.from(newMakerspaceResponse);
            final updatedMakerspace = await _updateMakerspaceImageUrls([newMakerspace]);
            setState(() {
              _makerspaces = [..._makerspaces, updatedMakerspace.first];
              updatedMakerspace.first['_isImageLoading'] = false;
              _makerspaces.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedMakerspaceId = payload.newRecord['id'];
          final updatedMakerspaceResponse = await Supabase.instance.client
              .from(makerspacesTableName)
              .select('*')
              .eq('id', updatedMakerspaceId)
              .single();
          if (mounted) {
            final updatedMakerspace = Map<String, dynamic>.from(updatedMakerspaceResponse);
            setState(() {
              _makerspaces = _makerspaces.map((makerspace) {
                return makerspace['id'] == updatedMakerspace['id'] ? updatedMakerspace : makerspace;
              }).toList();
              _makerspaces.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedMakerspaceId = payload.oldRecord['id'];
          setState(() {
            _makerspaces.removeWhere((makerspace) => makerspace['id'] == deletedMakerspaceId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreMakerspaces();
    }
  }

  Future<void> _loadMoreMakerspaces() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadMakerspacesFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> makerspace) async {
    if (makerspace['_isImageLoading'] == true) {
      print('Image loading already in progress for ${makerspace['fullname']}, skipping.');
      return;
    }
    if (makerspace['image_url'] != null &&
        makerspace['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${makerspace['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      makerspace['_isImageLoading'] = true;
    });

    final fullname = makerspace['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeMakerspaceBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/makerspaces';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeMakerspaceBucket');
    print('Image URL before fetch: ${makerspace['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeMakerspaceBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeMakerspaceBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        makerspace['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        makerspace['_isImageLoading'] = false;
        print('Setting image_url for ${makerspace['fullname']} to: ${makerspace['image_url']}');
      });
    } else {
      makerspace['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> makerspace) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MakerspaceDetailPage(
            makerspace: makerspace,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("MakerspacesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Makerspaces',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _makerspaces.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadMakerspacesFromSupabase(initialLoad: true),
              child: _makerspaces.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No makerspaces available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _makerspaces.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _makerspaces.length) {
                          final makerspace = _makerspaces[index];
                          final phone = makerspace['phone'] as String? ?? '';
                          final email = makerspace['email'] as String? ?? '';
                          final whatsapp = makerspace['whatsapp'] as String? ?? '';
                          final building = makerspace['building'] as String? ?? '';
                          final room = makerspace['room'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('makerspace_${makerspace['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (makerspace['image_url'] == null ||
                                      makerspace['image_url'] == 'assets/placeholder_image.png') &&
                                  !makerspace['_isImageLoading']) {
                                _fetchImageUrl(makerspace);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: makerspace['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      makerspace['fullname'] ?? 'Unnamed Makerspace',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        if (building.isNotEmpty || room.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.only(bottom: 4),
                                            child: Text(
                                              [
                                                if (building.isNotEmpty) building,
                                                if (room.isNotEmpty) 'Room $room',
                                              ].join(', '),
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: theme.colorScheme.secondary,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        Text(
                                          makerspace['about'] ?? '',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: theme.colorScheme.secondary,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                      ],
                                    ),
                                    onTap: () => _navigateToDetail(context, makerspace),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                          if (whatsapp.isNotEmpty)
                                            IconButton(
                                              icon: FaIcon(
                                                FontAwesomeIcons.whatsapp,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchWhatsapp(whatsapp),
                                              tooltip: 'WhatsApp',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
