import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'emergency_contact_detail_page.dart';
import 'tertiary_safety_page.dart';

class EmergencyContactsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EmergencyContactsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EmergencyContactsPage> createState() => _EmergencyContactsPageState();
}

class _EmergencyContactsPageState extends State<EmergencyContactsPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<EmergencyContact> _contacts = [];
  List<EmergencyContact> _filteredContacts = [];
  TextEditingController _searchController = TextEditingController();
  String _selectedDepartment = 'All';
  List<String> _departments = ['All'];
  bool _showMap = false;
  LatLng? _mapCenter;

  @override
  void initState() {
    super.initState();
    _fetchEmergencyContacts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchEmergencyContacts() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_emergencycontacts';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final List<EmergencyContact> contacts = List<Map<String, dynamic>>.from(response)
          .map((json) => EmergencyContact.fromJson(json))
          .toList();
      
      // Extract unique departments for filters
      final Set<String> departments = {'All'};
      for (var contact in contacts) {
        if (contact.department.isNotEmpty) {
          departments.add(contact.department);
        }
      }

      // Find map center (use first contact with location or default to a central point)
      LatLng? mapCenter;
      for (var contact in contacts) {
        if (contact.hasLocation()) {
          mapCenter = LatLng(contact.latitude!, contact.longitude!);
          break;
        }
      }

      setState(() {
        _contacts = contacts;
        _filteredContacts = List.from(contacts);
        _departments = departments.toList();
        _mapCenter = mapCenter;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading emergency contacts: $e';
      });
      print('Error fetching emergency contacts: $e');
    }
  }

  void _filterContacts() {
    final String searchQuery = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredContacts = _contacts.where((contact) {
        // Filter by department
        if (_selectedDepartment != 'All' && contact.department != _selectedDepartment) {
          return false;
        }
        
        // Filter by search query
        if (searchQuery.isNotEmpty) {
          return contact.fullname.toLowerCase().contains(searchQuery) ||
                 contact.title.toLowerCase().contains(searchQuery) ||
                 contact.department.toLowerCase().contains(searchQuery) ||
                 contact.about.toLowerCase().contains(searchQuery) ||
                 contact.services.toLowerCase().contains(searchQuery);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Emergency Contacts',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showMap ? Icons.list : Icons.map,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _showMap = !_showMap;
              });
            },
            tooltip: _showMap ? 'Show List' : 'Show Map',
          ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchEmergencyContacts,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search emergency contacts...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _filterContacts();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    _filterContacts();
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Department filter
                if (_departments.length > 1) ...[
                  Text(
                    'Department:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _departments.map((department) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(department),
                            selected: _selectedDepartment == department,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedDepartment = department;
                                  _filterContacts();
                                });
                              }
                            },
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: Colors.white,
                            labelStyle: TextStyle(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                            side: BorderSide(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Emergency contacts list or map
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchEmergencyContacts,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredContacts.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.contact_emergency,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _contacts.isEmpty
                                      ? 'No emergency contacts available'
                                      : 'No emergency contacts match your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _showMap && _mapCenter != null
                            ? _buildMap()
                            : _buildList(),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        onPressed: () {
          // Call emergency number
          _launchUrl('tel:911');
        },
        child: const Icon(Icons.call),
        tooltip: 'Call Emergency Services',
      ),
    );
  }

  Widget _buildList() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    return ListView.builder(
      itemCount: _filteredContacts.length,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemBuilder: (context, index) {
        final contact = _filteredContacts[index];
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12.0),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16.0),
            leading: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
              child: Icon(
                Icons.contact_emergency,
                color: theme.colorScheme.primary,
              ),
            ),
            title: Text(
              contact.fullname,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: theme.colorScheme.onSurface,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (contact.title.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
                    child: Text(
                      contact.title,
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),
                if (contact.department.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Icon(Icons.business, size: 16, color: theme.colorScheme.primary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(contact.department),
                        ),
                      ],
                    ),
                  ),
                if (contact.phone.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: theme.colorScheme.primary),
                        const SizedBox(width: 4),
                        Text(contact.getFormattedPhone()),
                      ],
                    ),
                  ),
              ],
            ),
            trailing: contact.phone.isNotEmpty
                ? IconButton(
                    icon: Icon(
                      Icons.call,
                      color: theme.colorScheme.primary,
                    ),
                    onPressed: () => _launchUrl('tel:${contact.phone}'),
                  )
                : Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EmergencyContactDetailPage(
                    contact: contact,
                    institutionName: widget.institutionName,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMap() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Filter contacts with valid locations
    final contactsWithLocation = _filteredContacts.where((contact) => contact.hasLocation()).toList();
    
    if (contactsWithLocation.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No emergency contacts with location data available',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _showMap = false;
                });
              },
              child: const Text('Show List View'),
            ),
          ],
        ),
      );
    }
    
    return FlutterMap(
      options: MapOptions(
        initialCenter: _mapCenter!,
        initialZoom: 16.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.harmonizr.app',
        ),
        MarkerLayer(
          markers: contactsWithLocation.map((contact) {
            return Marker(
              point: LatLng(contact.latitude!, contact.longitude!),
              width: 40,
              height: 40,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EmergencyContactDetailPage(
                        contact: contact,
                        institutionName: widget.institutionName,
                        isDarkMode: currentIsDarkMode,
                        toggleTheme: widget.toggleTheme,
                      ),
                    ),
                  );
                },
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.contact_emergency,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      child: Text(
                        contact.fullname,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
