// television_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'television_detail_page.dart';

class TelevisionPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedTelevisionStations;
  final bool isFromDetailPage;

  const TelevisionPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedTelevisionStations,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TelevisionPage> createState() => _TelevisionPageState();
}

class _TelevisionPageState extends State<TelevisionPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('television_list');
  List<Map<String, dynamic>> _televisionStations = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("TelevisionPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant TelevisionPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("TelevisionPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("TelevisionPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("TelevisionPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedTelevisionStations != null && widget.preloadedTelevisionStations!.isNotEmpty) {
      print("Preloaded television stations found, using them.");
      setState(() {
        _televisionStations = List<Map<String, dynamic>>.from(widget.preloadedTelevisionStations!);
        _televisionStations.forEach((station) {
          station['_isImageLoading'] = false;
        });
        _televisionStations.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedTelevisionStations!.length == _pageSize;
      });
      _loadTelevisionStationsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded television stations or empty list, loading from database.");
      _loadTelevisionStationsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadTelevisionStationsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadTelevisionStationsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final televisionTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_television';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(televisionTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedTelevisionStations =
          await _updateTelevisionStationImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _televisionStations = updatedTelevisionStations;
        } else {
          _televisionStations.addAll(updatedTelevisionStations);
        }
        _televisionStations.forEach((station) {
          station['_isImageLoading'] = false;
        });
        _televisionStations.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the television stations
      _cacheTelevisionStations(_televisionStations);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching television stations: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateTelevisionStationImageUrls(
      List<Map<String, dynamic>> televisionStations) async {
    List<Future<void>> futures = [];
    for (final station in televisionStations) {
      if (station['image_url'] == null ||
          station['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(station));
      }
    }
    await Future.wait(futures);
    return televisionStations;
  }

  Future<void> _cacheTelevisionStations(List<Map<String, dynamic>> televisionStations) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String televisionStationsJson = jsonEncode(televisionStations);
      await prefs.setString(
          'television_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          televisionStationsJson);
    } catch (e) {
      print('Error caching television stations: $e');
    }
  }
  
  void _setupRealtime() {
    final televisionTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_television';
    _realtimeChannel = Supabase.instance.client
        .channel('television')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: televisionTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStationId = payload.newRecord['id'];
          final newStationResponse = await Supabase.instance.client
              .from(televisionTableName)
              .select('*')
              .eq('id', newStationId)
              .single();
          if (mounted) {
            Map<String, dynamic> newStation = Map.from(newStationResponse);
            final updatedStation = await _updateTelevisionStationImageUrls([newStation]);
            setState(() {
              _televisionStations = [..._televisionStations, updatedStation.first];
              updatedStation.first['_isImageLoading'] = false;
              _televisionStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStationId = payload.newRecord['id'];
          final updatedStationResponse = await Supabase.instance.client
              .from(televisionTableName)
              .select('*')
              .eq('id', updatedStationId)
              .single();
          if (mounted) {
            final updatedStation = Map<String, dynamic>.from(updatedStationResponse);
            setState(() {
              _televisionStations = _televisionStations.map((station) {
                return station['id'] == updatedStation['id'] ? updatedStation : station;
              }).toList();
              _televisionStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStationId = payload.oldRecord['id'];
          setState(() {
            _televisionStations.removeWhere((station) => station['id'] == deletedStationId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreTelevisionStations();
    }
  }

  Future<void> _loadMoreTelevisionStations() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadTelevisionStationsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> station) async {
    if (station['_isImageLoading'] == true) {
      print('Image loading already in progress for ${station['fullname']}, skipping.');
      return;
    }
    if (station['image_url'] != null &&
        station['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${station['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      station['_isImageLoading'] = true;
    });

    final fullname = station['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeTelevisionBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/television';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeTelevisionBucket');
    print('Image URL before fetch: ${station['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeTelevisionBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeTelevisionBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        station['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        station['_isImageLoading'] = false;
        print('Setting image_url for ${station['fullname']} to: ${station['image_url']}');
      });
    } else {
      station['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> station) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TelevisionDetailPage(
            televisionStation: station,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("TelevisionPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Television Stations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _televisionStations.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadTelevisionStationsFromSupabase(initialLoad: true),
              child: _televisionStations.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No television stations available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _televisionStations.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _televisionStations.length) {
                          final station = _televisionStations[index];
                          final phone = station['phone'] as String? ?? '';
                          final email = station['email'] as String? ?? '';
                          final whatsapp = station['whatsapp'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('television_${station['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (station['image_url'] == null ||
                                      station['image_url'] == 'assets/placeholder_image.png') &&
                                  !station['_isImageLoading']) {
                                _fetchImageUrl(station);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: station['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      station['fullname'] ?? 'Unnamed Television Station',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        station['about'] ?? '',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: theme.colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                    onTap: () => _navigateToDetail(context, station),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                          if (whatsapp.isNotEmpty)
                                            IconButton(
                                              icon: FaIcon(
                                                FontAwesomeIcons.whatsapp,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchWhatsapp(whatsapp),
                                              tooltip: 'WhatsApp',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
