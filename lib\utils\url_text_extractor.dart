import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

class UrlTextExtractor {
  /// Extracts text content from a URL
  static Future<String> extractTextFromUrl(String url) async {
    try {
      // Validate URL format
      final Uri uri = Uri.parse(url);
      if (!uri.hasScheme || !uri.hasAuthority) {
        throw Exception('Invalid URL format');
      }

      // Add http:// if missing
      String validUrl = url;
      if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {
        validUrl = 'https://$validUrl';
      }

      // Fetch the HTML content
      final response = await http.get(Uri.parse(validUrl));
      if (response.statusCode != 200) {
        throw Exception('Failed to load page: ${response.statusCode}');
      }

      // Parse the HTML
      final document = html_parser.parse(response.body);
      
      // Extract text from the document
      final String extractedText = _extractTextFromDocument(document);
      
      return extractedText;
    } catch (e) {
      throw Exception('Error extracting text from URL: $e');
    }
  }

  /// Helper method to extract text from HTML document
  static String _extractTextFromDocument(dom.Document document) {
    // Remove script and style elements
    document.querySelectorAll('script, style, noscript, iframe, svg').forEach((element) {
      element.remove();
    });

    // Get the body content
    final body = document.querySelector('body');
    if (body == null) {
      return '';
    }

    // Extract article content if available (for better results with news articles)
    final article = document.querySelector('article');
    final mainContent = document.querySelector('main');
    final contentDiv = document.querySelector('div[id*="content"], div[class*="content"]');
    
    final contentElement = article ?? mainContent ?? contentDiv ?? body;
    
    // Extract text from paragraphs and headings
    final paragraphs = contentElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6');
    final StringBuffer textBuffer = StringBuffer();
    
    // If we found paragraphs, use them
    if (paragraphs.isNotEmpty) {
      for (var paragraph in paragraphs) {
        final text = paragraph.text.trim();
        if (text.isNotEmpty) {
          textBuffer.writeln(text);
          textBuffer.writeln();
        }
      }
    } else {
      // Fallback to all text content
      textBuffer.write(contentElement.text.trim());
    }
    
    // Clean up the text
    String text = textBuffer.toString();
    
    // Remove excessive whitespace
    text = text.replaceAll(RegExp(r'\s{2,}'), ' ');
    
    // Remove excessive newlines
    text = text.replaceAll(RegExp(r'\n{3,}'), '\n\n');
    
    return text.trim();
  }

  /// Checks if a string is likely a URL
  static bool isUrl(String text) {
    text = text.trim().toLowerCase();
    
    // Simple URL pattern check
    final urlPattern = RegExp(
      r'^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
      caseSensitive: false,
    );
    
    return urlPattern.hasMatch(text);
  }
}
