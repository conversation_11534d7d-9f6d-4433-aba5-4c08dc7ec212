import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_math_fork/flutter_math.dart';

// A class to render LaTeX as images for PDF export
class LatexImageRenderer {
  // Render LaTeX as an image
  static Future<Uint8List?> renderLatexAsImage(String latexExpression, double fontSize, bool isDarkMode) async {
    // Create a GlobalKey to capture the rendered widget
    final GlobalKey repaintBoundaryKey = GlobalKey();

    // Create a widget with the LaTeX content
    final Widget latexWidget = RepaintBoundary(
      key: repaintBoundaryKey,
      child: Material(
        color: Colors.transparent,
        child: _buildLatexWidget(latexExpression, fontSize, isDarkMode),
      ),
    );

    // Create a BuildContext to render the widget
    final BuildContext context = await _createBuildContext(latexWidget);

    // Capture the rendered widget as an image
    final RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    final ui.Image image = await boundary.toImage(pixelRatio: 2.0);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData?.buffer.asUint8List();
  }

  // Build the LaTeX widget
  static Widget _buildLatexWidget(String latexExpression, double fontSize, bool isDarkMode) {
    final Color textColor = isDarkMode ? Colors.white : Colors.black;
    final Color errorColor = isDarkMode ? Colors.red.shade300 : Colors.red.shade800;

    // Handle different LaTeX delimiters
    String texString = latexExpression.trim();
    bool isDisplayMode = false;

    if (texString.startsWith(r'$$') && texString.endsWith(r'$$')) {
      texString = texString.substring(2, texString.length - 2).trim();
      isDisplayMode = true;
    } else if (texString.startsWith(r'$') && texString.endsWith(r'$')) {
      texString = texString.substring(1, texString.length - 1).trim();
    } else if (texString.startsWith(r'\[') && texString.endsWith(r'\]')) {
      texString = texString.substring(2, texString.length - 2).trim();
      isDisplayMode = true;
    } else if (texString.startsWith(r'\(') && texString.endsWith(r'\)')) {
      texString = texString.substring(2, texString.length - 2).trim();
    }

    // Fix common LaTeX errors
    texString = _fixCommonLatexErrors(texString);

    return Container(
      padding: EdgeInsets.symmetric(vertical: isDisplayMode ? 8.0 : 4.0),
      child: Math.tex(
        texString,
        textStyle: TextStyle(
          fontSize: isDisplayMode ? fontSize * 1.2 : fontSize,
          color: textColor,
        ),
        mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
        onErrorFallback: (error) {
          print('LaTeX parsing error in image renderer: $error for input: $texString');
          // Return a simple text widget with the error
          return Text(
            latexExpression,  // Show original LaTeX code on error
            style: TextStyle(color: errorColor, fontSize: fontSize * 0.9, fontFamily: 'monospace'),
          );
        },
      ),
    );
  }

  // Helper method to fix common LaTeX errors
  static String _fixCommonLatexErrors(String texString) {
    // Replace common LaTeX errors
    String fixed = texString;

    // Fix missing backslashes before common LaTeX commands
    final commonCommands = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
                           'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'rho', 'sigma',
                           'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega', 'sum', 'prod', 'int',
                           'frac', 'sqrt', 'times', 'div', 'pm', 'leq', 'geq', 'neq', 'approx'];

    for (final cmd in commonCommands) {
      // Only add backslash if it's not already there
      fixed = fixed.replaceAllMapped(RegExp('(?<!\\\\)$cmd'), (match) => '\\$cmd');
    }

    // Fix unbalanced braces
    int openBraces = 0;
    int closeBraces = 0;
    for (int i = 0; i < fixed.length; i++) {
      if (fixed[i] == '{') openBraces++;
      if (fixed[i] == '}') closeBraces++;
    }

    // Add missing closing braces
    if (openBraces > closeBraces) {
      fixed += '}' * (openBraces - closeBraces);
    }

    // Fix missing closing brackets in \frac{}{} commands
    fixed = fixed.replaceAllMapped(
      RegExp(r'\\frac\{([^{}]*)\}\{([^{}]*)(?!\})'),
      (match) => '\\frac{${match.group(1)}}{${match.group(2)}}'
    );

    return fixed;
  }

  // Create a BuildContext to render the widget
  static Future<BuildContext> _createBuildContext(Widget widget) async {
    final completer = Completer<BuildContext>();

    // Create a temporary overlay entry to get a BuildContext
    final overlayEntry = OverlayEntry(
      builder: (context) {
        completer.complete(context);
        return Container();
      },
    );

    // Add the overlay entry to the overlay
    final navigator = Navigator.of(navigatorKey.currentContext!);
    navigator.overlay!.insert(overlayEntry);

    // Wait for the BuildContext to be available
    final context = await completer.future;

    // Remove the overlay entry
    overlayEntry.remove();

    return context;
  }
}

// A GlobalKey for the navigator
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
