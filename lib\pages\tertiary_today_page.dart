// tertiary_today_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:async'; // Import dart:async for Timer
import 'dart:io'; // Import dart:io for SocketException

import 'login_page.dart';
import 'tertiary_today_detail_page.dart';

class TertiaryTodayPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final List<Map<String, dynamic>>? preloadedEvents; // Add preloadedEvents parameter
  final bool isFromDetailPage;

  const TertiaryTodayPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    this.preloadedEvents, // Make preloadedEvents optional
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _TertiaryTodayPageState createState() => _TertiaryTodayPageState();
}

class _TertiaryTodayPageState extends State<TertiaryTodayPage> {
  List<Map<String, dynamic>> _events = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  bool _isDisposed = false;
  Timer? _expiryTimer; // Timer for checking event expiry

  @override
  void initState() {
    super.initState();
    if (widget.preloadedEvents != null && widget.preloadedEvents!.isNotEmpty) {
      // Use preloaded events if available
      _events = widget.preloadedEvents!;
      _isLoading = false; // No loading needed as data is preloaded
      _removeExpiredEvents(); // Remove expired events from preloaded data initially
    } else {
      _loadTodaysEvents(); // Load events from Supabase if not preloaded
    }
    _setupRealtime();
    _startExpiryTimer(); // Start timer to periodically check for expired events
  }

  @override
  void dispose() {
    _isDisposed = true;
    _realtimeChannel.unsubscribe();
    _stopExpiryTimer(); // Stop the timer when the widget is disposed
    super.dispose();
  }

  void _startExpiryTimer() {
    _expiryTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _removeExpiredEvents();
    });
  }

  void _stopExpiryTimer() {
    _expiryTimer?.cancel();
    _expiryTimer = null;
  }


  void _removeExpiredEvents() {
    if (_isLoading || _isDisposed) return; // Avoid removing events if loading or disposed

    final now = DateTime.now();
    List<Map<String, dynamic>> stillValidEvents = [];
    for (var event in _events) {
      if (!_isEventOver(event, now)) {
        stillValidEvents.add(event);
      }
    }
    if (stillValidEvents.length != _events.length) { // Only setState if there's a change
      setState(() {
        _events = stillValidEvents;
      });
      print("Expired events removed. Updated event count: ${_events.length}");
    }
  }


  Future<void> _loadTodaysEvents() async {
    if (widget.preloadedEvents != null && widget.preloadedEvents!.isNotEmpty) {
      // Do not reload if preloaded events are already set
      return;
    }
    if (_isDisposed) return;
    setState(() {
      _isLoading = true;
      _events = [];
    });

    final now = DateTime.now();

    final eventsTableName = '${widget.collegeData['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';
    print("Events Table Name: $eventsTableName");

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .order('starttime', ascending: true); // Removed date filters

      print("Supabase Response: $response");

      if (response == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load events.'), backgroundColor: Colors.redAccent),
        );
        setState(() {
          _isLoading = false;
        });
        return;
      }

      List<Map<String, dynamic>> eventsData = List<Map<String, dynamic>>
          .from(response as List<dynamic>);

      List<Map<String, dynamic>> filteredEvents = [];
      for (var event in eventsData) {
        print("Checking Event: ${event['fullname']}");
        if (_isTodayWithinEventDates(event, now) && !_isEventOver(event, now)) { // Added date range check
          filteredEvents.add(event);
          print("Event IS for today and NOT Over: ${event['fullname']}");
        } else {
          print("Event NOT for today OR OVER: ${event['fullname']}");
        }
      }

      List<Future<void>> futures = [];
      for (final event in filteredEvents) {
        futures.add(_fetchEventImageUrl(event));
      }
      await Future.wait(futures);

      filteredEvents.sort((a, b) {
        final startTimeA = _parseTimeOfDay(a['starttime'] ?? '00:00') ?? TimeOfDay.now();
        final startTimeB = _parseTimeOfDay(b['starttime'] ?? '00:00') ?? TimeOfDay.now();
        final endTimeA = _parseTimeOfDay(a['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event A
        final endTimeB = _parseTimeOfDay(b['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event B

        if (startTimeA.hour != startTimeB.hour || startTimeA.minute != startTimeB.minute) {
          // Primary sort by start time
          if (startTimeA.hour != startTimeB.hour) {
            return startTimeA.hour.compareTo(startTimeB.hour);
          }
          return startTimeA.minute.compareTo(startTimeB.minute);
        } else {
          // Secondary sort by end time if start times are the same
          if (endTimeA.hour != endTimeB.hour) {
            return endTimeA.hour.compareTo(endTimeB.hour);
          }
          return endTimeA.minute.compareTo(endTimeB.minute);
        }
      });


      setState(() {
        _events = filteredEvents;
        _isLoading = false;
      });
    } catch (error) {
      if (!_isDisposed) {
        setState(() {
          _isLoading = false;
        });
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Nothing is scheduled yet for today";
        } else {
          errorMsg = 'Error fetching events: $error';
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
        print("Error fetching events: $errorMsg");
      }
    }
  }

  bool _isTodayWithinEventDates(Map<String, dynamic> event, DateTime now) {
    try {
      DateTime startDate = _parseEventStartDate(event);
      DateTime endDate = _parseEventEndDate(event);
      DateTime today = DateTime(now.year, now.month, now.day);

      return (today.isAfter(startDate.subtract(const Duration(days: 1))) &&
          today.isBefore(endDate.add(const Duration(days: 1))));
    } catch (e) {
      print("Error checking date range: $e");
      return false;
    }
  }

  String _monthNameToNumber(String monthName) {
    const monthMap = {
      'january': '01', 'february': '02', 'march': '03', 'april': '04',
      'may': '05', 'june': '06', 'july': '07', 'august': '08',
      'september': '09', 'october': '10', 'november': '11', 'december': '12',
      'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
      'jun': '06', 'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12',
    };
    final lowerMonthName = monthName.toLowerCase();
    return monthMap[lowerMonthName] ?? monthName; // Return original if not found
  }


  DateTime _parseEventStartDate(Map<String, dynamic> event) {
    int startDay = int.tryParse(event['startday'] ?? '') ?? 0;
    String startMonthStr = _monthNameToNumber(event['startmonth'] ?? ''); // Convert month name to number
    int startYear = int.tryParse(event['startyear'] ?? '') ?? 0;

    int month = int.tryParse(startMonthStr) ?? 0; // Parse month as number
    return DateTime(startYear, month, startDay);
  }

  DateTime _parseEventEndDate(Map<String, dynamic> event) {
    int endDay = int.tryParse(event['endday'] ?? '') ?? 0;
    String endMonthStr = _monthNameToNumber(event['endmonth'] ?? ''); // Convert month name to number
    int endYear = int.tryParse(event['endyear'] ?? '') ?? 0;

    if (endDay == 0 || endMonthStr.isEmpty || endYear == 0) {
      return _parseEventStartDate(event); // Fallback to start date
    }

    int month = int.tryParse(endMonthStr) ?? 0; // Parse month as number
    return DateTime(endYear, month, endDay);
  }

  bool _isEventOver(Map<String, dynamic> event, DateTime now) {
    try {
      TimeOfDay? endTimeOfDay = _parseTimeOfDay(event['endtime'] ?? '23:59');
      if (endTimeOfDay == null) {
        return false; // If end time parsing fails, consider it not over
      }

      DateTime todayEventEndDateTime = DateTime(now.year, now.month, now.day, endTimeOfDay.hour, endTimeOfDay.minute);

      print("Current time (now): $now"); // Print current DateTime
      print("Event end TimeOfDay: $endTimeOfDay"); // Print parsed TimeOfDay
      print("Today's Event End DateTime: $todayEventEndDateTime"); // Print combined DateTime

      bool isOver = now.isAfter(todayEventEndDateTime);
      print("Is Event Over: $isOver"); // Print the result of comparison
      return isOver;
    } catch (e) {
      print("Error comparing event end time: $e");
      return true; // On error, assume event is over to be safe
    }
  }

  DateTime _parseEventEndDateTime(Map<String, dynamic> event) { // Keep this as is for overall end date
    int endDay = int.tryParse(event['endday'] ?? '') ?? 0;
    String endMonthStr = _monthNameToNumber(event['endmonth'] ?? ''); // Convert month name to number
    int endYear = int.tryParse(event['endyear'] ?? '') ?? 0;
    String endTimeStr = event['endtime'] ?? '23:59';

    // Fallback to start date if end date is missing
    if (endDay == 0 || endMonthStr.isEmpty || endYear == 0) {
      DateTime startDate = _parseEventStartDate(event);
      endDay = startDate.day;
      endMonthStr = startDate.month.toString(); // Use start month number
      endYear = startDate.year;
    }

    int month = int.tryParse(endMonthStr) ?? 0; // Parse month as number
    TimeOfDay endTime = _parseTimeOfDay(endTimeStr) ?? const TimeOfDay(hour: 23, minute: 59);

    return DateTime(endYear, month, endDay, endTime.hour, endTime.minute);
  }


  TimeOfDay? _parseTimeOfDay(String timeString) {
    try {
      // Try parsing with AM/PM format first
      DateTime parsedTime12H = DateFormat('h:mm a').parse(timeString.trim());
      return TimeOfDay.fromDateTime(parsedTime12H);
    } catch (e1) {
      try {
        // If AM/PM parsing fails, try 24-hour format
        DateTime parsedTime24H = DateFormat('HH:mm').parse(timeString.trim());
        return TimeOfDay.fromDateTime(parsedTime24H);
      } catch (e2) {
        print("Error parsing time string: $timeString, errors: 12H: $e1, 24H: $e2");
        return null;
      }
    }
  }


  Future<void> _fetchEventImageUrl(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) {
      return;
    }
    if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') {
      return;
    }

    setState(() {
      event['_isImageLoading'] = true;
    });

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventsBucket = '${widget.collegeData['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}/events';

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventsBucket)
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventsBucket)
            .getPublicUrl(imageNameWebp);
      }
    } catch (e) {
    }

    if (mounted) {
      setState(() {
        event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        event['_isImageLoading'] = false;
      });
    } else {
      event['_isImageLoading'] = false;
    }
  }

  void _setupRealtime() {
    final eventsTableName = '${widget.collegeData['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';
    _realtimeChannel = Supabase.instance.client
        .channel('today_events')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: eventsTableName,
      callback: (payload) async {
        print("Realtime event received: ${payload.eventType}");

        if (_isDisposed) {
          print("Realtime callback ignored because widget is disposed.");
          return;
        }

        final now = DateTime.now();


        bool isTodayEvent(Map<String, dynamic> record) {
          return _isTodayWithinEventDates(record, now);
        }

        if (payload.eventType == PostgresChangeEvent.insert) {
          print("Realtime INSERT event detected.");

          final newEventId = payload.newRecord['id'];
          if (isTodayEvent(payload.newRecord)) {
            print("New event IS for today.");
            final newEventResponse = await Supabase.instance.client
                .from(eventsTableName)
                .select('*')
                .eq('id', newEventId)
                .single();
            if (mounted && newEventResponse != null) {
              Map<String, dynamic> newEvent = Map.from(newEventResponse);
              if (!_isEventOver(newEvent, now)) {
                print("New event is NOT over.");
                await _fetchEventImageUrl(newEvent);
                setState(() {
                  _events = [..._events, newEvent];
                  newEvent['_isImageLoading'] = false;
                  _events.sort((a, b) {
                    final startTimeA = _parseTimeOfDay(a['starttime'] ?? '00:00') ?? TimeOfDay.now();
                    final startTimeB = _parseTimeOfDay(b['starttime'] ?? '00:00') ?? TimeOfDay.now();
                    final endTimeA = _parseTimeOfDay(a['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event A
                    final endTimeB = _parseTimeOfDay(b['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event B

                    if (startTimeA.hour != startTimeB.hour || startTimeA.minute != startTimeB.minute) {
                      // Primary sort by start time
                      if (startTimeA.hour != startTimeB.hour) {
                        return startTimeA.hour.compareTo(startTimeB.hour);
                      }
                      return startTimeA.minute.compareTo(startTimeB.minute);
                    } else {
                      // Secondary sort by end time if start times are the same
                      if (endTimeA.hour != endTimeB.hour) {
                        return endTimeA.hour.compareTo(endTimeB.hour);
                      }
                      return endTimeA.minute.compareTo(endTimeB.minute);
                    }
                  });
                  print("setState called to update UI with new event: ${newEvent['fullname']}");
                });
              } else {
                print("New event IS over, not adding to list.");
              }
            } else {
              print("newEventResponse is NULL for new event ID: $newEventId");
            }
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          print("Realtime UPDATE event detected.");
          final updatedEventId = payload.newRecord['id'];
          if (isTodayEvent(payload.newRecord)) {
            print("Updated event IS for today.");
            final updatedEventResponse = await Supabase.instance.client
                .from(eventsTableName)
                .select('*')
                .eq('id', updatedEventId)
                .single();
            if (mounted && updatedEventResponse != null) {
              final updatedEvent = Map<String, dynamic>.from(updatedEventResponse);
              await _fetchEventImageUrl(updatedEvent); // Fetch image again in case something changed

              if (!_isEventOver(updatedEvent, now)) {
                print("Updated event is NOT over.");
                int existingIndex = _events.indexWhere((event) => event['id'] == updatedEvent['id']);
                setState(() {
                  if (existingIndex != -1) {
                    // Event already exists, update it
                    _events[existingIndex] = updatedEvent;
                  } else {
                    // Event was not in the list (possibly removed before), add it back
                    _events = [..._events, updatedEvent];
                  }
                  _events.sort((a, b) {
                    final startTimeA = _parseTimeOfDay(a['starttime'] ?? '00:00') ?? TimeOfDay.now();
                    final startTimeB = _parseTimeOfDay(b['starttime'] ?? '00:00') ?? TimeOfDay.now();
                    final endTimeA = _parseTimeOfDay(a['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event A
                    final endTimeB = _parseTimeOfDay(b['endtime'] ?? '23:59') ?? TimeOfDay.now(); // Parse end time for event B

                    if (startTimeA.hour != startTimeB.hour || startTimeA.minute != startTimeB.minute) {
                      // Primary sort by start time
                      if (startTimeA.hour != startTimeB.hour) {
                        return startTimeA.hour.compareTo(startTimeB.hour);
                      }
                      return startTimeA.minute.compareTo(startTimeB.minute);
                    } else {
                      // Secondary sort by end time if start times are the same
                      if (endTimeA.hour != endTimeB.hour) {
                        return endTimeA.hour.compareTo(endTimeB.hour);
                      }
                      return endTimeA.minute.compareTo(endTimeB.minute);
                    }
                  });
                  print("setState called to update UI with updated event: ${updatedEvent['fullname']}");
                });
              } else {
                print("Updated event IS over, removing from list.");
                setState(() {
                  _events.removeWhere((event) => event['id'] == updatedEventId);
                });
              }
            } else {
              print("updatedEventResponse is NULL for updated event ID: $updatedEventId");
            }
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          print("Realtime DELETE event detected.");
          final deletedEventId = payload.oldRecord['id'];
          setState(() {
            _events.removeWhere((event) => event['id'] == deletedEventId);
            print("setState called to update UI after delete event ID: $deletedEventId");
          });
        }
      },
    ).subscribe();
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Determine the current theme dynamically.
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Today',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _loadTodaysEvents,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _events.isEmpty
            ? LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: SizedBox(
                height: constraints.maxHeight,
                child: const Center(
                  child: Text('No events scheduled for today.'),
                 ),
               ),
             );
           },
         )
             : ListView.builder(
           padding: const EdgeInsets.only(top: 16), // ADDED TOP PADDING HERE
           itemCount: _events.length,
           itemBuilder: (context, index) {
             final event = _events[index];
             return VisibilityDetector(
               key: Key('event_${event['id']}'),
               onVisibilityChanged: (VisibilityInfo info) {
                 if (info.visibleFraction > 0.1 && (event['image_url'] == null ||
                     event['image_url'] == 'assets/placeholder_image.png') &&
                     !event['_isImageLoading']) {
                   _fetchEventImageUrl(event);
                 }
               },
               child: _buildEventListItem(theme, event, event),
             );
           },
         ),
       ),
       bottomNavigationBar: Container(
         color: theme.colorScheme.surface,
         child: SafeArea(
           child: Padding(
             padding: const EdgeInsets.symmetric(vertical: 8),
             child: Row(
               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
               children: [
                 IconButton(
                   icon: Icon(
                     Icons.home_outlined,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: () {
                     Navigator.of(context).popUntil((route) => route.isFirst);
                   },
                 ),
                 const SizedBox(width: 24),
                 IconButton(
                   icon: Icon(
                     currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: widget.toggleTheme,
                 ),
                 const SizedBox(width: 24),
                 IconButton(
                   icon: Icon(
                     Icons.person_outline,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: () {
                     Navigator.push(
                       context,
                       MaterialPageRoute(
                         builder: (context) => LoginPage(
                           isDarkMode: widget.isDarkMode,
                           toggleTheme: widget.toggleTheme,
                         ),
                       ),
                     );
                   },
                 ),
               ],
             ),
           ),
         ),
       ),
     );
   }

   Widget _buildEventListItem(ThemeData theme, Map<String, dynamic> event, Map<String, dynamic> fullEventData) {
     String startTime = event['starttime'] ?? '00:00';
     String endTime = event['endtime'] ?? '';

     TimeOfDay? startTimeOfDay = _parseTimeOfDay(startTime);
     TimeOfDay? endTimeOfDay = _parseTimeOfDay(endTime);

     String formattedStartTime = startTimeOfDay != null ? DateFormat('HH:mm').format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, startTimeOfDay.hour, startTimeOfDay.minute)) : startTime;
     String formattedEndTime = endTimeOfDay != null ? DateFormat('HH:mm').format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, endTimeOfDay.hour, endTimeOfDay.minute)) : endTime;


     String startDay = event['startday'] ?? '';
     String startMonth = event['startmonth'] ?? '';
     String startYear = event['startyear'] ?? '';
     String endDay = event['endday'] ?? '';
     String endMonth = event['endmonth'] ?? '';
     String endYear = event['endyear'] ?? '';

     if (startDay.isEmpty || startMonth.isEmpty || startYear.isEmpty) {
       return const SizedBox.shrink(); // Or handle as you see fit, like logging an error
     }

     String formattedStartDay = startDay.padLeft(2, '0');
     String formattedStartMonth = _monthNameToNumber(startMonth).padLeft(2, '0'); // Format month number after conversion
     String startDateString = '$formattedStartDay/$formattedStartMonth/$startYear';

     String endDateString = '';
     if (endDay.isNotEmpty && endMonth.isNotEmpty && endYear.isNotEmpty) {
       String formattedEndDay = endDay.padLeft(2, '0');
       String formattedEndMonth = _monthNameToNumber(endMonth).padLeft(2, '0'); // Format month number after conversion
       endDateString = '-$formattedEndDay/$formattedEndMonth/$endYear';
     }


     return Card(
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       margin: const EdgeInsets.only(bottom: 16),
       child: InkWell(
         onTap: () {
           Navigator.push(
             context,
             MaterialPageRoute(
               builder: (context) => TertiaryTodayDetailPage( // Corrected class name here
                 event: fullEventData,
                 isDarkMode: widget.isDarkMode,
                 toggleTheme: widget.toggleTheme,
                 collegeNameForBucket: widget.collegeData['fullname'],
                 startTime: event['starttime'] as String?,
                 endTime: event['endtime'] as String?,
                 startDay: event['startday'] as String?,
                 startMonth: event['startmonth'] as String?,
                 startYear: event['startyear'] as String?,
                 endDay: event['endday'] as String?,
                 endMonth: event['endmonth'] as String?,
                 endYear: event['endyear'] as String?,
                 latitude: event['latitude'] != null ? double.tryParse(event['latitude'].toString()) : null, // Convert to double
                 longitude: event['longitude'] != null ? double.tryParse(event['longitude'].toString()) : null, // Convert to double
                 isFromDetailPage: widget.isFromDetailPage,
               ),
             ),
           );
         },
         child: ListTile(
           contentPadding: const EdgeInsets.all(16),
           leading: ClipOval(
             child: SizedBox(
               width: 40,
               height: 40,
               child: CachedNetworkImage(
                 imageUrl: event['image_url'] ?? 'assets/placeholder_image.png',
                 errorWidget: (context, url, error) => Image.asset('assets/placeholder_image.png'),
                 fit: BoxFit.cover,
               ),
             ),
           ),
           title: Text(
             event['fullname'] ?? 'Event Title',
             style: TextStyle(
               fontWeight: FontWeight.bold,
               fontSize: 16,
               color: theme.colorScheme.onSurface,
             ),
             overflow: TextOverflow.ellipsis,
             maxLines: 2,
           ),
           subtitle: Column(
             crossAxisAlignment: CrossAxisAlignment.start,
             children: [
               Padding(
                 padding: const EdgeInsets.only(top: 4),
                 child: Text(
                   '$formattedStartTime - $formattedEndTime', // Display formatted time
                   style: TextStyle(color: theme.colorScheme.secondary),
                 ),
               ),
               Padding(
                 padding: const EdgeInsets.only(top: 4),
                 child: Text(
                   event['location'] ?? 'Location',
                   style: TextStyle(color: theme.colorScheme.secondary),
                   overflow: TextOverflow.ellipsis,
                   maxLines: 1,
                 ),
               ),
               if (event['about'] != null && (event['about'] as String).isNotEmpty) ...[
                 Padding(
                   padding: const EdgeInsets.only(top: 8),
                   child: Text(
                     event['about'] ?? '',
                     style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                     maxLines: 1,
                     overflow: TextOverflow.ellipsis,
                   ),
                 ),
               ],
               Padding(
                 padding: const EdgeInsets.only(top: 8),
                 child: Text(
                   '$startDateString$endDateString', // Display date range
                   style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                 ),
               ),
             ],
           ),
         ),
       ),
     );
   }
 }