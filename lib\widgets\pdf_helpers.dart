import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter_math_fork/flutter_math.dart';
import 'dart:ui';

// Helper function to convert LaTeX to PDF widgets
List<pw.Widget> convertLatexToPdfWidgets(
    String content,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font italicFont,
    List<pw.Font> fallbackFonts) {

  // Regular expressions for different elements
  final codeBlockRegExp = RegExp(r'```(?:.*?)\n(.*?)```', dotAll: true);
  final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\\\\[.*?\\\\\\]|\\\\\\(.*?\\\\\\))', dotAll: true);
  final tableRegExp = RegExp(r'(\|.*\|\n\|[-:\s|]*\|\n(?:\|.*\|\n)+)', dotAll: true);

  // Process the content in stages
  List<_ContentSegment> segments = [];

  // First, identify code blocks (they take precedence)
  _splitByRegex(content, codeBlockRegExp, segments, _ContentType.codeBlock);

  // Then process tables in the non-code segments
  List<_ContentSegment> afterTableSegments = [];
  for (var segment in segments) {
    if (segment.type == _ContentType.codeBlock) {
      afterTableSegments.add(segment);
    } else {
      _splitByRegex(segment.content, tableRegExp, afterTableSegments, _ContentType.table);
    }
  }

  // Finally, process LaTeX in the non-code, non-table segments
  List<_ContentSegment> finalSegments = [];
  for (var segment in afterTableSegments) {
    if (segment.type == _ContentType.codeBlock || segment.type == _ContentType.table) {
      finalSegments.add(segment);
    } else {
      _splitByRegex(segment.content, latexRegExp, finalSegments, _ContentType.latex);
    }
  }

  // Build PDF widgets from the segments
  List<pw.Widget> widgets = [];
  for (var segment in finalSegments) {
    switch (segment.type) {
      case _ContentType.text:
        widgets.add(_buildPdfMarkdownWidget(segment.content, regularFont, boldFont, italicFont, fallbackFonts));
        break;

      case _ContentType.latex:
        widgets.add(_buildPdfLatexWidget(segment.content, regularFont, fallbackFonts));
        break;

      case _ContentType.codeBlock:
        widgets.add(_buildPdfCodeBlockWidget(segment.content, regularFont, fallbackFonts));
        break;

      case _ContentType.table:
        widgets.add(_buildPdfTableWidget(segment.content, regularFont, boldFont, fallbackFonts));
        break;
    }
  }

  return widgets;
}

// Helper method to split content by regex and add to segments
void _splitByRegex(String content, RegExp regex, List<_ContentSegment> segments, _ContentType matchType) {
  int lastEnd = 0;
  final matches = regex.allMatches(content);

  for (final match in matches) {
    if (match.start > lastEnd) {
      segments.add(_ContentSegment(
        content: content.substring(lastEnd, match.start),
        type: _ContentType.text,
      ));
    }

    segments.add(_ContentSegment(
      content: match.group(0)!,
      type: matchType,
    ));

    lastEnd = match.end;
  }

  if (lastEnd < content.length) {
    segments.add(_ContentSegment(
      content: content.substring(lastEnd),
      type: _ContentType.text,
    ));
  }
}

// Helper class for content segments
class _ContentSegment {
  final String content;
  final _ContentType type;

  _ContentSegment({required this.content, required this.type});
}

// Enum for content types
enum _ContentType { text, latex, codeBlock, table }

// Build a PDF Markdown widget
pw.Widget _buildPdfMarkdownWidget(String content, pw.Font regularFont, pw.Font boldFont, pw.Font italicFont, List<pw.Font> fallbackFonts) {
  // Process markdown formatting
  // Handle headers
  final headerRegExp = RegExp(r'^(#{1,6})\s+(.+)$', multiLine: true);
  content = content.replaceAllMapped(headerRegExp, (match) {
    final level = match.group(1)!.length;
    final text = match.group(2)!;
    return '<h$level>$text</h$level>';
  });

  // Handle bold
  content = content.replaceAllMapped(
    RegExp(r'\*\*(.*?)\*\*'),
    (match) => '<b>${match.group(1)}</b>',
  );

  // Handle italic
  content = content.replaceAllMapped(
    RegExp(r'\*(.*?)\*'),
    (match) => '<i>${match.group(1)}</i>',
  );

  // Parse the HTML-like content into PDF widgets
  return pw.RichText(
    text: pw.TextSpan(
      children: _parseHtmlToPdf(content, regularFont, boldFont, italicFont, fallbackFonts),
      style: pw.TextStyle(
        fontSize: 12,
        font: regularFont,
        fontFallback: fallbackFonts,
      ),
    ),
  );
}

// Parse simple HTML tags into PDF TextSpans
List<pw.TextSpan> _parseHtmlToPdf(String content, pw.Font regularFont, pw.Font boldFont, pw.Font italicFont, List<pw.Font> fallbackFonts) {
  final List<pw.TextSpan> spans = [];
  final RegExp tagRegExp = RegExp(r'<(/?)([^>]+)>([^<]*)');
  int lastEnd = 0;

  // Stack to track nested tags
  final List<String> tagStack = [];
  final Map<String, pw.TextStyle> styles = {
    'b': pw.TextStyle(font: boldFont, fontFallback: fallbackFonts),
    'i': pw.TextStyle(font: italicFont, fontFallback: fallbackFonts),
    'h1': pw.TextStyle(fontSize: 24, font: boldFont, fontFallback: fallbackFonts),
    'h2': pw.TextStyle(fontSize: 20, font: boldFont, fontFallback: fallbackFonts),
    'h3': pw.TextStyle(fontSize: 18, font: boldFont, fontFallback: fallbackFonts),
    'h4': pw.TextStyle(fontSize: 16, font: boldFont, fontFallback: fallbackFonts),
    'h5': pw.TextStyle(fontSize: 14, font: boldFont, fontFallback: fallbackFonts),
    'h6': pw.TextStyle(fontSize: 12, font: boldFont, fontFallback: fallbackFonts),
  };

  for (final match in tagRegExp.allMatches(content)) {
    if (match.start > lastEnd) {
      spans.add(pw.TextSpan(text: content.substring(lastEnd, match.start)));
    }

    final isClosingTag = match.group(1) == '/';
    final tagName = match.group(2)!;
    final text = match.group(3)!;

    if (isClosingTag) {
      // Closing tag
      if (tagStack.isNotEmpty && tagStack.last == tagName) {
        tagStack.removeLast();
      }
    } else {
      // Opening tag
      if (styles.containsKey(tagName)) {
        tagStack.add(tagName);
      }
    }

    if (text.isNotEmpty) {
      pw.TextStyle? currentStyle;
      for (final tag in tagStack) {
        final style = styles[tag];
        if (style != null) {
          currentStyle = currentStyle?.merge(style) ?? style;
        }
      }
      spans.add(pw.TextSpan(text: text, style: currentStyle));
    }

    lastEnd = match.end;
  }

  if (lastEnd < content.length) {
    spans.add(pw.TextSpan(text: content.substring(lastEnd)));
  }

  return spans;
}

// Build a PDF LaTeX widget
pw.Widget _buildPdfLatexWidget(String latexExpr, pw.Font regularFont, List<pw.Font> fallbackFonts) {
  final isDisplayMode = (latexExpr.startsWith(r'$$') && latexExpr.endsWith(r'$$')) ||
                       (latexExpr.startsWith(r'\[') && latexExpr.endsWith(r'\]'));

  // Remove the delimiters based on the format
  String processedLatex;
  if (latexExpr.startsWith(r'$$') && latexExpr.endsWith(r'$$')) {
    processedLatex = latexExpr.substring(2, latexExpr.length - 2);
  } else if (latexExpr.startsWith(r'$') && latexExpr.endsWith(r'$')) {
    processedLatex = latexExpr.substring(1, latexExpr.length - 1);
  } else if (latexExpr.startsWith(r'\[') && latexExpr.endsWith(r'\]')) {
    processedLatex = latexExpr.substring(2, latexExpr.length - 2);
  } else if (latexExpr.startsWith(r'\(') && latexExpr.endsWith(r'\)')) {
    processedLatex = latexExpr.substring(2, latexExpr.length - 2);
  } else {
    processedLatex = latexExpr;
  }

  // Format the LaTeX for better PDF rendering
  // Replace common LaTeX symbols with Unicode equivalents where possible
  processedLatex = processedLatex
      .replaceAll(r'\alpha', 'α')
      .replaceAll(r'\beta', 'β')
      .replaceAll(r'\gamma', 'γ')
      .replaceAll(r'\delta', 'δ')
      .replaceAll(r'\epsilon', 'ε')
      .replaceAll(r'\zeta', 'ζ')
      .replaceAll(r'\eta', 'η')
      .replaceAll(r'\theta', 'θ')
      .replaceAll(r'\iota', 'ι')
      .replaceAll(r'\kappa', 'κ')
      .replaceAll(r'\lambda', 'λ')
      .replaceAll(r'\mu', 'μ')
      .replaceAll(r'\nu', 'ν')
      .replaceAll(r'\xi', 'ξ')
      .replaceAll(r'\pi', 'π')
      .replaceAll(r'\rho', 'ρ')
      .replaceAll(r'\sigma', 'σ')
      .replaceAll(r'\tau', 'τ')
      .replaceAll(r'\upsilon', 'υ')
      .replaceAll(r'\phi', 'φ')
      .replaceAll(r'\chi', 'χ')
      .replaceAll(r'\psi', 'ψ')
      .replaceAll(r'\omega', 'ω')
      .replaceAll(r'\Gamma', 'Γ')
      .replaceAll(r'\Delta', 'Δ')
      .replaceAll(r'\Theta', 'Θ')
      .replaceAll(r'\Lambda', 'Λ')
      .replaceAll(r'\Xi', 'Ξ')
      .replaceAll(r'\Pi', 'Π')
      .replaceAll(r'\Sigma', 'Σ')
      .replaceAll(r'\Phi', 'Φ')
      .replaceAll(r'\Psi', 'Ψ')
      .replaceAll(r'\Omega', 'Ω')
      .replaceAll(r'\infty', '∞')
      .replaceAll(r'\pm', '±')
      .replaceAll(r'\times', '×')
      .replaceAll(r'\div', '÷')
      .replaceAll(r'\leq', '≤')
      .replaceAll(r'\geq', '≥')
      .replaceAll(r'\neq', '≠')
      .replaceAll(r'\approx', '≈')
      .replaceAll(r'\cdot', '·')
      .replaceAll(r'\ldots', '…')
      .replaceAll(r'\rightarrow', '→')
      .replaceAll(r'\leftarrow', '←')
      .replaceAll(r'\Rightarrow', '⇒')
      .replaceAll(r'\Leftarrow', '⇐')
      .replaceAll(r'\sum', '∑')
      .replaceAll(r'\prod', '∏')
      .replaceAll(r'\int', '∫')
      .replaceAll(r'\partial', '∂')
      .replaceAll(r'\nabla', '∇')
      .replaceAll(r'\forall', '∀')
      .replaceAll(r'\exists', '∃')
      .replaceAll(r'\in', '∈')
      .replaceAll(r'\notin', '∉')
      .replaceAll(r'\subset', '⊂')
      .replaceAll(r'\supset', '⊃')
      .replaceAll(r'\cup', '∪')
      .replaceAll(r'\cap', '∩')
      .replaceAll(r'\emptyset', '∅');

  // Handle superscripts and subscripts
  final superscriptRegex = RegExp(r'\^\{([^}]+)\}|\^([0-9a-zA-Z])');
  processedLatex = processedLatex.replaceAllMapped(superscriptRegex, (match) {
    final content = match.group(1) ?? match.group(2) ?? '';
    return _convertToSuperscript(content);
  });

  final subscriptRegex = RegExp(r'_\{([^}]+)\}|_([0-9a-zA-Z])');
  processedLatex = processedLatex.replaceAllMapped(subscriptRegex, (match) {
    final content = match.group(1) ?? match.group(2) ?? '';
    return _convertToSubscript(content);
  });

  // For PDF, we'll use the processed LaTeX with special formatting
  return pw.Container(
    width: isDisplayMode ? double.infinity : null,
    padding: isDisplayMode ? const pw.EdgeInsets.symmetric(vertical: 8) : null,
    alignment: isDisplayMode ? pw.Alignment.center : null,
    child: pw.Text(
      processedLatex,
      style: pw.TextStyle(
        font: fallbackFonts[0], // Use stixTwoMathRegular as primary font for LaTeX
        fontFallback: fallbackFonts,
        fontSize: isDisplayMode ? 14 : 12,
      ),
    ),
  );
}

// Helper function to convert text to superscript Unicode characters
String _convertToSuperscript(String text) {
  final Map<String, String> superscriptMap = {
    '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
    '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
    'n': 'ⁿ', 'i': 'ⁱ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i];
    result += superscriptMap[char] ?? char;
  }
  return result;
}

// Helper function to convert text to subscript Unicode characters
String _convertToSubscript(String text) {
  final Map<String, String> subscriptMap = {
    '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
    '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
    '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
    'a': 'ₐ', 'e': 'ₑ', 'o': 'ₒ', 'x': 'ₓ', 'h': 'ₕ',
    'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'p': 'ₚ',
    's': 'ₛ', 't': 'ₜ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i];
    result += subscriptMap[char] ?? char;
  }
  return result;
}

// Build a PDF code block widget
pw.Widget _buildPdfCodeBlockWidget(String codeBlock, pw.Font codeFont, List<pw.Font> fallbackFonts) {
  // Extract the code and language
  final match = RegExp(r'```(?:(.*?)\n)?(.*?)```', dotAll: true).firstMatch(codeBlock);
  if (match == null) return pw.Container();

  final language = match.group(1)?.trim() ?? '';
  final code = match.group(2) ?? '';

  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(8),
    margin: const pw.EdgeInsets.symmetric(vertical: 8),
    decoration: pw.BoxDecoration(
      color: PdfColors.grey200,
      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
    ),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        if (language.isNotEmpty)
          pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 4),
            child: pw.Text(
              language,
              style: pw.TextStyle(
                font: codeFont,
                fontFallback: fallbackFonts,
                fontSize: 10,
                color: PdfColors.grey700,
              ),
            ),
          ),
        pw.Text(
          code,
          style: pw.TextStyle(
            font: codeFont,
            fontFallback: fallbackFonts,
            fontSize: 10,
          ),
        ),
      ],
    ),
  );
}

// Build a PDF table widget
pw.Widget _buildPdfTableWidget(String tableContent, pw.Font regularFont, pw.Font boldFont, List<pw.Font> fallbackFonts) {
  final lines = tableContent.trim().split('\n');
  if (lines.length < 3) return pw.Container(); // Need at least header, separator, and one row

  // Parse the table
  final List<List<String>> rows = [];
  for (var line in lines) {
    if (line.trim().isEmpty) continue;
    if (!line.startsWith('|') || !line.endsWith('|')) continue;

    // Remove first and last pipe
    line = line.substring(1, line.length - 1);

    // Split by pipe but handle escaped pipes
    final cells = line.split('|').map((cell) => cell.trim()).toList();
    rows.add(cells);
  }

  if (rows.isEmpty) return pw.Container();

  // Check if second row is separator
  final isSeparator = rows.length > 1 &&
                     rows[1].every((cell) => cell.isEmpty || RegExp(r'^[-:\s]+$').hasMatch(cell));

  final headerRow = rows[0];
  final dataRows = isSeparator ? rows.sublist(2) : rows.sublist(1);

  // Ensure all rows have the same number of columns
  final columnCount = headerRow.length;
  for (var row in dataRows) {
    while (row.length < columnCount) {
      row.add(''); // Add empty cells if needed
    }
    if (row.length > columnCount) {
      row = row.sublist(0, columnCount); // Trim extra cells
    }
  }

  // Create table
  return pw.Container(
    margin: const pw.EdgeInsets.symmetric(vertical: 8),
    decoration: pw.BoxDecoration(
      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
    ),
    child: pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 0.5),
      columnWidths: Map.fromIterable(
        List.generate(columnCount, (index) => index),
        key: (index) => index,
        value: (_) => const pw.FlexColumnWidth(1),
      ),
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: headerRow.map((cell) => pw.Container(
            padding: const pw.EdgeInsets.all(4),
            child: pw.Text(
              cell,
              style: pw.TextStyle(
                font: boldFont,
                fontFallback: fallbackFonts,
                fontSize: 10,
              ),
              textAlign: pw.TextAlign.center,
            ),
          )).toList(),
        ),
        // Data rows
        ...dataRows.map((row) => pw.TableRow(
          decoration: const pw.BoxDecoration(
            border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300, width: 0.5)),
          ),
          children: row.map((cell) => pw.Container(
            padding: const pw.EdgeInsets.all(4),
            child: pw.Text(
              cell,
              style: pw.TextStyle(
                font: regularFont,
                fontFallback: fallbackFonts,
                fontSize: 10,
              ),
              textAlign: pw.TextAlign.center,
            ),
          )).toList(),
        )).toList(),
      ],
    ),
  );
}
