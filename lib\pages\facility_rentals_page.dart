import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'rental_detail_page.dart';
import 'tertiary_rentals_page.dart';

class FacilityRentalsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const FacilityRentalsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<FacilityRentalsPage> createState() => _FacilityRentalsPageState();
}

class _FacilityRentalsPageState extends State<FacilityRentalsPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Rental> _facilityRentals = [];
  List<Rental> _filteredRentals = [];
  TextEditingController _searchController = TextEditingController();
  String _selectedDepartment = 'All';
  List<String> _departments = ['All'];
  RangeValues _capacityRange = const RangeValues(0, 1000);
  int _minCapacity = 0;
  int _maxCapacity = 1000;

  @override
  void initState() {
    super.initState();
    _fetchFacilityRentals();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchFacilityRentals() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rentals';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .eq('facilityrental', true)
          .order('fullname', ascending: true);

      final List<Rental> rentals = List<Map<String, dynamic>>.from(response)
          .map((json) => Rental.fromJson(json))
          .toList();
      
      // Extract unique departments for filters
      final Set<String> departments = {'All'};
      for (var rental in rentals) {
        if (rental.department.isNotEmpty) {
          departments.add(rental.department);
        }
      }
      
      // Find min and max capacity for the range slider
      if (rentals.isNotEmpty) {
        _minCapacity = rentals.map((e) => e.capacity).reduce((a, b) => a < b ? a : b);
        _maxCapacity = rentals.map((e) => e.capacity).reduce((a, b) => a > b ? a : b);
        // Add some padding to max capacity for better UI
        _maxCapacity = (_maxCapacity * 1.2).round();
        _capacityRange = RangeValues(_minCapacity.toDouble(), _maxCapacity.toDouble());
      }

      setState(() {
        _facilityRentals = rentals;
        _filteredRentals = List.from(rentals);
        _departments = departments.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading facility rentals: $e';
      });
      print('Error fetching facility rentals: $e');
    }
  }

  void _filterRentals() {
    final String searchQuery = _searchController.text.toLowerCase();
    final int minCapacity = _capacityRange.start.round();
    final int maxCapacity = _capacityRange.end.round();
    
    setState(() {
      _filteredRentals = _facilityRentals.where((rental) {
        // Filter by department
        if (_selectedDepartment != 'All' && rental.department != _selectedDepartment) {
          return false;
        }
        
        // Filter by capacity range
        if (rental.capacity < minCapacity || rental.capacity > maxCapacity) {
          return false;
        }
        
        // Filter by search query
        if (searchQuery.isNotEmpty) {
          return rental.fullname.toLowerCase().contains(searchQuery) ||
                 rental.about.toLowerCase().contains(searchQuery) ||
                 rental.department.toLowerCase().contains(searchQuery) ||
                 rental.dimensions.toLowerCase().contains(searchQuery) ||
                 rental.payment.toLowerCase().contains(searchQuery) ||
                 rental.pricing.toLowerCase().contains(searchQuery);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Facility Rentals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchFacilityRentals,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search facilities...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _filterRentals();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    _filterRentals();
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Department filter
                if (_departments.length > 1) ...[
                  Text(
                    'Department:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _departments.map((department) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(department),
                            selected: _selectedDepartment == department,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedDepartment = department;
                                  _filterRentals();
                                });
                              }
                            },
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: Colors.white,
                            labelStyle: TextStyle(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                            side: BorderSide(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
                
                // Capacity range slider
                if (_facilityRentals.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Capacity Range:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '${_capacityRange.start.round()} - ${_capacityRange.end.round()} people',
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  RangeSlider(
                    values: _capacityRange,
                    min: _minCapacity.toDouble(),
                    max: _maxCapacity.toDouble(),
                    divisions: (_maxCapacity - _minCapacity) > 100 ? 100 : (_maxCapacity - _minCapacity),
                    labels: RangeLabels(
                      _capacityRange.start.round().toString(),
                      _capacityRange.end.round().toString(),
                    ),
                    onChanged: (RangeValues values) {
                      setState(() {
                        _capacityRange = values;
                        _filterRentals();
                      });
                    },
                  ),
                ],
              ],
            ),
          ),
          
          // Facility rentals list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchFacilityRentals,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredRentals.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.home_work,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _facilityRentals.isEmpty
                                      ? 'No facility rentals available'
                                      : 'No facilities match your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredRentals.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            itemBuilder: (context, index) {
                              final rental = _filteredRentals[index];
                              return Card(
                                margin: const EdgeInsets.only(bottom: 12.0),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.all(16.0),
                                  title: Text(
                                    rental.fullname,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 8),
                                      if (rental.dimensions.isNotEmpty && rental.dimensions != 'Not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.straighten, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Dimensions: ${rental.dimensions}'),
                                            ],
                                          ),
                                        ),
                                      if (rental.capacity > 0)
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.people, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Capacity: ${rental.capacity} people'),
                                            ],
                                          ),
                                        ),
                                      if (rental.pricing.isNotEmpty && rental.pricing != 'Not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.attach_money, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Pricing: ${rental.pricing}'),
                                            ],
                                          ),
                                        ),
                                      if (rental.department.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.business, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Department: ${rental.department}'),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                  trailing: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16,
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => RentalDetailPage(
                                          rental: rental,
                                          institutionName: widget.institutionName,
                                          isDarkMode: currentIsDarkMode,
                                          toggleTheme: widget.toggleTheme,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
