import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
// Ensure this import is present if SpeechRecognitionError is used directly without stt. prefix
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async'; // For TimeoutException, StreamSubscription
import 'dart:io';    // For SocketException
import 'package:collection/collection.dart'; // For firstWhereOrNull, sortedBy

// --- Helper Classes (Moved Outside State Class) ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping;
  ChatMessage({required this.text, required this.isUser, this.isTyping = false});

  @override bool operator ==(Object other) => identical(this, other) || other is ChatMessage && runtimeType == other.runtimeType && text == other.text && isUser == other.isUser && isTyping == other.isTyping;
  @override int get hashCode => text.hashCode ^ isUser.hashCode ^ isTyping.hashCode;
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override Widget build(BuildContext context) {
    if (message.isTyping) {
       return Align( alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration( color: theme.colorScheme.surfaceVariant.withOpacity(0.8), borderRadius: const BorderRadius.only( topLeft: Radius.circular(4.0), topRight: Radius.circular(18.0), bottomLeft: Radius.circular(18.0), bottomRight: Radius.circular(18.0) ) ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                 Text( message.text, style: TextStyle( color: theme.colorScheme.onSurfaceVariant, fontStyle: FontStyle.italic, fontSize: 14 ) ),
              ],
            ),
          ),
       );
    }
    final Color userBubbleColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final Color aiBubbleColor = theme.colorScheme.surfaceVariant;
    final Color userTextColor = theme.colorScheme.onSurface;
    final Color aiTextColor = theme.colorScheme.onSurfaceVariant;
    final bubbleColor = message.isUser ? userBubbleColor : aiBubbleColor;
    final textColor = message.isUser ? userTextColor : aiTextColor;
    final borderRadius = BorderRadius.only( topLeft: Radius.circular(message.isUser ? 18.0 : 4.0), topRight: Radius.circular(message.isUser ? 4.0 : 18.0), bottomLeft: const Radius.circular(18.0), bottomRight: const Radius.circular(18.0) );

    return Align( alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints: BoxConstraints( maxWidth: MediaQuery.of(context).size.width * 0.8 ),
        decoration: BoxDecoration( color: bubbleColor, borderRadius: borderRadius, boxShadow: [ BoxShadow( color: Colors.black.withOpacity(0.06), blurRadius: 3, offset: const Offset(1, 2) ) ] ),
        child: SelectableText( message.text, style: TextStyle( color: textColor, fontSize: 15, height: 1.35 ) ),
      ),
    );
  }
}

// --- Extensions (Moved Outside State Class) ---
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return split(' ')
       .map((word) {
         if (word.isEmpty) return "";
         if (word.length > 1 && word == word.toUpperCase()) return word; // Preserve acronyms
         return word[0].toUpperCase() + (word.length > 1 ? word.substring(1).toLowerCase() : "");
       })
       .join(' ');
  }

  String readableFieldName() {
    if (isEmpty) return "";
    String spaced = replaceAll('_', ' ');
    spaced = spaced.replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}');
    spaced = spaced.replaceAllMapped(RegExp(r'([A-Z])([A-Z][a-z])'), (match) => '${match.group(1)} ${match.group(2)}');
    return spaced.capitalize();
  }
}


// --- Main Widget ---
class AiAgentPage extends StatefulWidget {
 final bool isDarkMode;
 final VoidCallback toggleTheme;
 final Map<String, dynamic>? collegeData;

 const AiAgentPage({
   Key? key,
   required this.isDarkMode,
   required this.toggleTheme,
   this.collegeData,
 }) : super(key: key);

 @override
 _AiAgentPageState createState() => _AiAgentPageState();
}

enum ProcessingStep { idle, processing }

class _AiAgentPageState extends State<AiAgentPage> {
 final TextEditingController _messageController = TextEditingController();
 final List<ChatMessage> _messages = [];
 ProcessingStep _processingStep = ProcessingStep.idle;
 final ScrollController _scrollController = ScrollController();

 bool _isListening = false;

 final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // IMPORTANT: Replace with your actual API Key
 final String _model = 'gemini-2.0-flash-lite';

 // Language selection
 String? _outputLanguage = 'English';

 int _dailyTokenCount = 0;
 final int _maxDailyTokens = 500000;
 int _lastPromptTokenCount = 0;
 int _lastApiResponseTokenCount = 0;

  // Pricing for Gemini gemini-2.0-flash-lite (adjust if using a different model or if prices change)
  // Assuming $0.075 per 1M input tokens and $0.30 per 1M output tokens for gemini-2.0-flash-lite (USD, check latest pricing)
  static const double _inputCostPer1MTokens_FlashLatest = 0.075; // Example pricing
  static const double _outputCostPer1MTokens_FlashLatest = 0.30; // Example pricing

  final double _inputCostPer1kTokens = _inputCostPer1MTokens_FlashLatest / 1000.0;
  final double _outputCostPer1kTokens = _outputCostPer1MTokens_FlashLatest / 1000.0;

 final double _exchangeRate = 2000.0; // Example, set your local currency exchange rate to USD if needed

 static const double APPROX_CHARS_PER_TOKEN = 4.0;
 static const int APPROX_TABLE_SELECTION_RESPONSE_TOKENS = 50;
 static const int APPROX_FINAL_RESPONSE_TOKENS = 300;

  final List<String> _alwaysConsiderTables = [
      'people', 'services', 'departments', 'schools', 'centers', 'helpdesks', 'faq'
  ];


 late stt.SpeechToText _speechToText;
 late FlutterTts _flutterTts;
 bool _isMuted = true; // MODIFIED: Default to muted

 List<Map<String, String>> _availableVoices = [];
 Map<String, String>? _selectedVoice;
 bool _voicesLoaded = false;

// Keywords and Manifests - Ensure these are comprehensive
 final Map<String, List<String>> _tableKeywords = {
      'helpdesks': ['help desk', 'it support', 'tech support', 'library help', 'student services desk', 'assistance', 'computer problem', 'wifi issue', 'password reset', 'student support', 'get help', 'contact helpdesk'],
      'accessibility': ['accessibility', 'disability', 'disabled', 'ada', 'special needs', 'accomodation', 'access', 'mobility', 'learning support'],
      'faq': ['faq', 'frequently asked', 'questions', 'common questions', 'q&a', 'ask', 'wondering', 'general help', 'information', 'how do i', 'what about'],
      'links': ['link', 'website', 'url', 'resource', 'webpage', 'portal', 'online form', 'find'],
      'construction': ['construction', 'building work', 'renovation', 'campus updates', 'project', 'noise', 'closure'],
      'printing': ['print', 'printer', 'printing', 'copies', 'photocopy', 'scan', 'cost to print'],
      'daycares': ['daycare', 'childcare', 'nursery', 'kids', 'child care', 'preschool'],
      'sustainability': ['sustainability', 'green', 'environment', 'eco', 'recycling', 'conservation', 'solar'],
      'notices': ['notice', 'alert', 'announcement', 'update', 'important info', 'news', 'bulletin'],
      'socialmediafeeds': ['social media', 'twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube channel'],
      'admissionsprocess': ['admission', 'admissions', 'apply', 'application', 'how to apply', 'enroll', 'acceptance', 'get in', 'prospectus', 'entry requirements'],
      'registrationprocess': ['registration', 'register', 'enrollment', 'course selection', 'sign up', 'add drop class', 'choose courses', 'academic planning'],
      'selection': ['selection criteria', 'admission profile', 'student demographics', 'acceptance rate', 'average gpa', 'sat score', 'act score'],
      'costsorrates': ['cost', 'fee', 'fees', 'tuition', 'rate', 'price', 'how much', 'payment plan', 'expense', 'school fees'],
      'scholarships': [
          'scholarship', 'scholarships', 'grant', 'grants', 'financial aid',
          'bursary', 'funding', 'award', 'loan', 'fafsa', 'student aid',
          'tuition assistance', 'financial support', 'fellowship', 'list scholarships', 'all scholarships'
      ],
      'payments': ['payment', 'pay', 'billing', 'invoice', 'tuition payment', 'how to pay', 'deadline', 'finance office'],
      'orientations': ['orientation', 'welcome week', 'new student', 'campus tour', 'introduction', 'onboarding'],
      'symposiums': ['symposium', 'conference', 'seminar', 'lecture series', 'guest speaker', 'academic event'],
      'graduation': ['graduation', 'commencement', 'ceremony', 'degree', 'graduate', 'diploma', 'finish school'],
      'people': ['faculty', 'staff', 'directory', 'contact', 'professor', 'teacher', 'employee list', 'instructor', 'advisor', 'dean', 'department head', 'phone number for', 'email for', 'list people', 'all staff'],
      'currentstudents': ['current student', 'student portal', 'student id', 'student life', 'enrolled student'],
      'housing': ['housing', 'residence', 'dorm', 'dormitory', 'accommodation', 'living on campus', 'room assignment', 'ra', 'res life', 'list housing', 'all dorms', 'where to live', 'housing options'],
      'locallodging': ['hotel', 'lodging', 'bnb', 'accomodation', 'off-campus stay', 'nearby hotel', 'place to stay', 'visitor housing'],
      'shopsoreateries': ['store', 'shop', 'cafe', 'dining', 'eatery', 'food', 'restaurant', 'canteen', 'bookstore', 'campus store', 'merchandise', 'eat', 'list stores', 'where to eat'],
      'mealplans': ['meal plan', 'dining plan', 'food points', 'swipes', 'how much is meal plan'],
      'localareadining': ['nearby restaurant', 'off-campus food', 'places to eat near campus', 'town dining'],
      'studentdiscounts': ['discount', 'student deal', 'coupon', 'offer', 'save money'],
      'inventory': ['bookstore inventory', 'item stock', 'merchandise', 'textbook', 'supplies'],
      'menus': ['menu', 'dining hall menu', 'whats for lunch', 'food options', 'cafeteria food', 'nutrition'],
      'campusshuttle': ['shuttle', 'bus', 'transport', 'campus bus', 'route', 'schedule', 'getting around'],
      'parkingspaces': ['parking', 'permit', 'lot', 'where to park', 'car park', 'vehicle registration', 'ticket'],
      'localtransport': ['public transport', 'local bus', 'train', 'metro', 'getting here', 'travel to campus'],
      'schools': ['school of', 'college of', 'division', 'faculty', 'academic school', 'list schools', 'all schools'],
      'departments': ['department', 'academic department', 'program contact', 'major department', 'minor department', 'list departments', 'all departments'],
      'centers': ['research center', 'institute', 'lab', 'facility', 'program center', 'list centers', 'all centers'],
      'documents': ['form', 'report', 'pdf', 'download', 'handbook', 'policy document', 'transcript request', 'application form', 'list documents'],
      'majors': ['major', 'degree program', 'field of study', 'course of study', 'what majors', 'list of majors', 'all majors', 'available majors'],
      'minors': ['minor', 'concentration', 'certificate program', 'list of minors', 'all minors', 'available minors'],
      'funding': ['research funding', 'grant opportunity', 'project support', 'financial support for research'],
      'coursecatalog': ['course catalog', 'course list', 'class description', 'module details', 'subject list', 'curriculum', 'all courses', 'list courses'],
      'enrollmentexercise': ['mock registration', 'practice enrollment', 'course signup simulation'],
      'academicresources': ['tutoring', 'academic support', 'writing center', 'library resources', 'study help', 'advisor', 'mentor', 'struggling academically'],
      'academichonors': ['honors', 'dean\'s list', 'gpa requirement', 'academic distinction', 'cum laude'],
      'academicprizes': ['prize', 'award', 'competition', 'student award', 'scholarship award'],
      'academicdress': ['graduation gown', 'regalia', 'cap and gown', 'hood', 'academic attire'],
      'entryrequirements': ['admission requirements', 'prerequisites', 'how to get in', 'application criteria', 'gpa needed', 'test scores', 'apply'],
      'gradingscale': ['grading system', 'gpa scale', 'how grades work', 'marks', 'pass fail'],
      'programs': ['student program', 'special program', 'initiative', 'extracurricular', 'leadership program', 'list programs'],
      'signatureevents': ['homecoming', 'annual event', 'campus tradition', 'key ceremony', 'founder day'],
      'traditions': ['campus traditions', 'rituals', 'history', 'customs'],
      'partnershipopportunities': ['partnership', 'collaboration', 'industry link', 'community engagement'],
      'athletics': ['athletics', 'sports', 'team', 'game schedule', 'varsity', 'intramural', 'coach', 'stadium', 'gym', 'list sports teams'],
      'orgsorclubs': ['clubs', 'student organizations', 'student group', 'society', 'join a club', 'extracurricular activities', 'list clubs', 'all clubs'],
      'researchgroups': ['research group', 'lab group', 'research team', 'project group'],
      'committees': ['committee', 'governance', 'board', 'student government', 'faculty senate'],
      'news': ['news', 'latest news', 'press release', 'campus updates', 'announcements'],
      'periodicals': ['campus newspaper', 'magazine', 'journal', 'student publication'],
      'radio': ['campus radio', 'radio station', 'broadcast'],
      'television': ['campus tv', 'tv station', 'student broadcast'],
      'photos': ['photos', 'gallery', 'images', 'pictures', 'campus scenery'],
      'videos': ['videos', 'youtube', 'promotional video', 'recordings'],
      'accelerators': ['startup accelerator', 'incubator', 'entrepreneurship program', 'business support'],
      'makerspaces': ['makerspace', 'fab lab', 'diy space', '3d printer', 'workshop'],
      'startupfunds': ['seed fund', 'venture capital', 'student startup funding', 'pitch competition'],
      'startups': ['student startup', 'campus venture', 'spin-off company'],
      'researchprojects': ['research project', 'faculty research', 'student research', 'study'],
      'theses': ['thesis', 'dissertation', 'capstone project', 'final paper', 'senior project'],
      'books': ['faculty books', 'alumni books', 'library books', 'textbooks'],
      'articles': ['journal article', 'research paper', 'faculty publication'],
      'patents': ['patent', 'invention', 'intellectual property', 'ip'],
      'building': ['building map', 'building directory', 'specific building', 'hall name', 'facility location', 'list buildings'],
      'rooms': ['room schedule', 'classroom location', 'lab number', 'book a room', 'study space', 'lecture hall'],
      'roomequipment': ['projector', 'smartboard', 'av equipment', 'classroom tech', 'computer lab'],
      'roomassignments': ['dorm assignment', 'room key', 'housing placement', 'move-in'],
      'publicart': ['campus art', 'sculpture', 'mural', 'art installation', 'gallery exhibit'],
      'emergencyequipment': ['aed location', 'fire extinguisher', 'safety equipment', 'first aid kit'],
      'classschedules': ['class schedule', 'my schedule', 'course times', 'timetable', 'find class location'],
      'weeklyschedule': ['weekly events', 'this week schedule', 'regular meetings'],
      'events': ['events', 'calendar', 'upcoming events', 'activity', 'workshop', 'register for event', 'what happening', 'list events', 'all events'],
      'academiccalendar': ['academic calendar', 'term dates', 'semester dates', 'important dates', 'deadline', 'holiday', 'break'],
      'feedback': ['feedback', 'suggestion', 'complaint', 'survey', 'course evaluation'],
      'historicaltimeline': ['history', 'college history', 'timeline', 'milestones', 'founding'],
      'rentals': ['equipment rental', 'space rental', 'book equipment', 'reserve room'],
      'rentalequipmentcalendar': ['equipment availability', 'rental booking', 'gear schedule'],
      'jobs': ['job', 'campus job', 'student employment', 'work study', 'career services', 'vacancy', 'hiring', 'list jobs', 'all jobs'],
      'services': ['student services', 'support services', 'health services', 'it services', 'counseling services', 'career services', 'what services offered', 'helpdesk services', 'list of services', 'all services', 'available services'],
      'atms': ['atm', 'cash machine', 'bank machine', 'withdraw cash'],
      'clinicsorhospitals': ['health center', 'clinic', 'hospital', 'doctor', 'nurse', 'medical appointment', 'sick'],
      'counselingservices': ['counseling', 'mental health', 'therapist', 'psychologist', 'support group', 'wellness center', 'stress', 'anxiety'],
      'emergencycontacts': ['emergency number', 'campus security', 'campus police', 'report incident', 'hotline'],
      'safetyprocedures': ['safety plan', 'emergency procedure', 'evacuation', 'lockdown', 'fire safety'],
      'connectivity': ['wifi', 'internet access', 'network', 'eduroam', 'connect to wifi', 'internet down'],
      'giving': ['donate', 'donation', 'support', 'fundraising', 'alumni giving', 'gift'],
  };
 final Map<String, String> _tableManifest = {
      'helpdesks': 'Campus help desk locations and services (IT, library, student services, etc.) and general student support. Contains details about specific help desks like IT Help Desk, including contact info, hours, and a general scope of their support areas. Useful for finding helpdesk contact or location.',
      'accessibility': 'Disability support services and campus accessibility resources.',
      'faq': 'Frequently asked questions about various campus topics including admissions, academics, and campus life. Good for general queries.',
      'links': 'Important website links for departments, applications, and resources.',
      'construction': 'Current/pending campus construction projects with locations and timelines.',
      'printing': 'Printing service locations, costs, and availability.',
      'daycares': 'On-campus childcare facilities and registration information.',
      'sustainability': 'Environmental initiatives and green campus programs.',
      'notices': 'Campus-wide announcements and time-sensitive alerts.',
      'socialmediafeeds': 'Official college social media accounts and links.',
      'admissionsprocess': 'Step-by-step application procedures, admission requirements, and related information.',
      'registrationprocess': 'Course enrollment steps and academic planning.',
      'selection': 'Demographic/academic profiles of admitted students, acceptance rates, and selection criteria.',
      'costsorrates': 'Tuition fees, housing costs, school fees, and other financial rates.',
      'scholarships': 'Information on available grants, awards, scholarships, bursaries, loans, and other financial aid opportunities for students. Provides a list of scholarships.',
      'payments': 'Payment methods, portals, and billing information for tuition and fees.',
      'orientations': 'New student orientation programs and schedules.',
      'symposiums': 'Academic conference details and participation info.',
      'graduation': 'Commencement ceremony logistics and graduate data.',
      'people': 'Faculty/staff directories with contact info, roles, and departments. Useful for finding specific individuals or people in a department. Contains a list of people.',
      'currentstudents': 'Profiles of enrolled students (majors, housing, etc.).',
      'housing': 'Residence hall details, policies, and living arrangements on campus. Lists available dormitories/residences and their features. Use this table for queries about housing options, dorms, or on-campus living.',
      'locallodging': 'Off-campus hotels/B&Bs near the college for visitors.',
      'shopsoreateries': 'On-campus stores, cafes, and dining options. Lists places to eat or shop on campus.',
      'mealplans': 'Dining plan options and associated costs for students.',
      'localareadining': 'Nearby off-campus restaurants and food discounts.',
      'studentdiscounts': 'Local business offers for students.',
      'inventory': 'Campus store products, merchandise, and pricing.',
      'menus': 'Daily dining hall meal offerings and nutritional info.',
      'campusshuttle': 'Transportation routes and schedules for the campus bus/shuttle.',
      'parkingspaces': 'Parking lot locations, permits, and regulations.',
      'localtransport': 'Public transit options and regional travel to/from campus.',
      'schools': 'Academic divisions (e.g., School of Arts), their deans, and departments within them. Provides a list of schools.',
      'departments': 'Academic department info, faculty contacts, and associated school. Provides a list of departments.',
      'centers': 'Research centers, institutes, and special program facilities, often with their focus area. Provides a list of centers.',
      'documents': 'Official forms, reports, and policy PDFs for download. Provides a list of available documents.',
      'majors': 'Undergraduate degree programs, requirements, and descriptions. Provides a list of all available majors.',
      'minors': 'Minor programs, concentration details, and certification information. Provides a list of all available minors.',
      'funding': 'General research grants and project funding opportunities (distinct from student scholarships).',
      'coursecatalog': 'Course descriptions, prerequisites, credits, and class details for all academic offerings. Provides a list of courses.',
      'enrollmentexercise': 'Registration practice simulations.',
      'academicresources': 'Tutoring, libraries, and study support services.',
      'academichonors': 'Dean’s list, honors programs, and GPA requirements for academic distinction.',
      'academicprizes': 'Student achievement awards and competitions.',
      'academicdress': 'Graduation regalia info and ordering.',
      'entryrequirements': 'Detailed admission criteria, prerequisites, and application guidelines. Often related to `admissionsprocess`.',
      'gradingscale': 'Letter grade definitions and GPA calculations.',
      'programs': 'Special academic initiatives, student programs, and partnerships. Provides a list of programs.',
      'signatureevents': 'Major annual campus traditions/ceremonies.',
      'traditions': 'Historical campus customs and rituals.',
      'partnershipopportunities': 'Community/corporate collaboration programs.',
      'athletics': 'Sports teams, schedules, and athlete resources. Lists sports teams.',
      'orgsorclubs': 'Student organizations, club listings, and contact information. Provides a list of clubs.',
      'researchgroups': 'Active academic research teams/projects.',
      'committees': 'Campus governance groups and their functions.',
      'news': 'College news articles and press releases.',
      'periodicals': 'Student-run publications and magazines.',
      'radio': 'Campus radio station programming and staff.',
      'television': 'Student-produced TV shows and content.',
      'photos': 'Campus photo archives and event galleries.',
      'videos': 'Official college videos and student projects.',
      'accelerators': 'Entrepreneurship programs and startup support.',
      'makerspaces': 'Creative labs with equipment/tech resources.',
      'startupfunds': 'Funding opportunities for student ventures.',
      'startups': 'Student-run businesses and their profiles.',
      'researchprojects': 'Ongoing faculty/student research studies.',
      'theses': 'Senior capstone projects and research papers.',
      'books': 'Publications by faculty/alumni.',
      'articles': 'Academic papers and journal contributions.',
      'patents': 'Innovations/IP created at the college.',
      'building': 'Campus building info, maps, and facilities directories. Lists campus buildings.',
      'rooms': 'Classroom/lab specifications and reservations.',
      'roomequipment': 'AV/tech gear available in spaces.',
      'roomassignments': 'Student housing placements.',
      'publicart': 'Campus art installations and exhibits.',
      'emergencyequipment': 'Safety devices and their locations.',
      'classschedules': 'Course times, locations, and instructors.',
      'weeklyschedule': 'Recurring events and meetings.',
      'events': 'Campus activities calendar, event details, and RSVP info. Provides a list of upcoming events.',
      'academiccalendar': 'Term dates, holidays, and academic deadlines.',
      'feedback': 'Student surveys and feedback forms.',
      'historicaltimeline': 'Key moments in college history.',
      'rentals': 'Equipment/space rental options and policies.',
      'rentalequipmentcalendar': 'Reservation schedule for gear.',
      'jobs': 'Campus employment, career opportunities, and job listings. Provides a list of available jobs.',
      'services': 'Overview of student support services, including specific services offered by IT, health, counseling, career centers, etc. This table can list individual services (e.g., "Password Reset", "Wifi Support", "Resume Review") and the department or helpdesk providing them (e.g., "IT Helpdesk", "Career Services"). Useful for "what services does X offer?" type questions or listing all services.',
      'atms': 'On-campus cash machine locations.',
      'clinicsorhospitals': 'Health center services and hours.',
      'counselingservices': 'Mental health resources and appointments.',
      'emergencycontacts': 'Critical phone numbers and protocols.',
      'safetyprocedures': 'Emergency response guidelines.',
      'connectivity': 'WiFi, tech resources, and IT support.',
      'giving': 'Donation opportunities and alumni fundraising.',
  };
 final Map<String, List<String>> _collegeFieldKeywords = {
      'about': ['about', 'overview', 'information', 'general info', 'tell me about the college', 'history', 'background'],
      'address': ['address', 'location', 'located', 'where is', 'find you', 'campus address', 'physical address'],
      'daysnhours': ['hours', 'opening hours', 'closing time', 'open', 'close', 'days open', 'schedule', 'operating hours', 'business hours'],
      'postaladdress': ['postal address', 'mailing address', 'zip code', 'postcode', 'mail to'],
      'mission': ['mission', 'mission statement', 'purpose', 'college aim', 'institutional mission', 'our mission'],
      'vision': ['vision', 'vision statement', 'college aspiration', 'future goals', 'institutional vision', 'our vision'],
      'corevalues': ['values', 'core values', 'principles', 'ethics', 'guiding principles'],
      'motto': ['motto', 'tagline', 'slogan', 'college motto'],
      'goals': ['goals', 'objectives', 'targets', 'aims', 'strategic goals'],
      'mandate': ['mandate', 'authority', 'charge', 'official purpose'],
      'founded': ['founded', 'established', 'since', 'when was it founded', 'history start', 'year founded'],
      'accreditation': ['accreditation', 'accredited', 'certified', 'recognized', 'licensing'],
      'freewifi': ['wifi', 'internet', 'wireless', 'free wifi', 'connect to internet'],
      'objectives': ['objectives', 'aims', 'goals', 'key objectives'],
      'aims': ['aims', 'objectives', 'goals'],
      'pledge': ['pledge', 'commitment', 'promise', 'dedication', 'our pledge'],
      'statementoffaith': ['faith', 'belief', 'statement of faith', 'religious statement', 'creed'],
      'religiousaffiliation': ['religious', 'faith', 'denomination', 'affiliation', 'church associated'],
      'whychooseus': ['why choose', 'why us', 'advantages', 'benefits', 'choose us', 'selling points', 'why attend here', 'unique features'],
      'institutiontype': ['type', 'public', 'private', 'institution type', 'kind of school', 'college type', 'university type'],
      'campussetting': ['campus setting', 'setting', 'urban', 'rural', 'suburban', 'campus environment', 'location type'],
      'highestqualificationoffered': ['qualification', 'degree', 'certificate', 'highest degree', 'level of study', 'programs offered', 'diploma offered'],
      'studentpopulation': ['population', 'students', 'enrollment', 'how many students', 'student body size', 'number of students'],
      'academicyearcalendar': ['academic calendar', 'terms', 'semesters', 'academic year', 'school year schedule', 'term dates', 'session dates'],
      'website': ['website', 'site', 'url', 'web address', 'online', 'homepage', 'official website'],
      'city': ['city', 'town', 'located in which city'],
      'state': ['state', 'region', 'province', 'located in which state'],
      'fullname': ['name', 'full name', 'official name', 'college name'],
      'phone': ['phone', 'number', 'contact number', 'call', 'telephone', 'main phone'],
      'email': ['email', 'email address', 'contact email', 'mail address', 'main email'],
  };

 @override
 void initState() {
   super.initState();
   _speechToText = stt.SpeechToText();
   _flutterTts = FlutterTts();
   WidgetsBinding.instance.addPostFrameCallback((_) {
     _initializeAndLoadData();
     _addInitialGreeting();
   });
   _messageController.addListener(() {
      if(mounted) setState(() {});
   });
 }

 @override
 void dispose() {
   _messageController.removeListener(() { if(mounted) setState(() {}); });
   _messageController.dispose();
   _scrollController.dispose();
   _speechToText.stop();
   _speechToText.cancel();
   _flutterTts.stop();
   super.dispose();
 }

 Future<void> _initializeAndLoadData() async {
    await _initSpeech();
    await _configureTts();
    await _loadTtsVoices();
 }

 Future<void> _initSpeech() async {
   try {
     _speechToText.statusListener = _onSpeechStatusUpdate;
     _speechToText.errorListener = _onSpeechErrorUpdate;

     bool available = await _speechToText.initialize(
       onStatus: (status) {
         // print('Speech System Status (from initialize call): $status');
       },
       onError: (errorNotification) {
         print('Error during Speech System INITIALIZATION: ${errorNotification.errorMsg}');
         if (mounted) {
           _showError("Failed to initialize speech recognition: ${errorNotification.errorMsg}");
         }
       }
     );

     if (available) {
       print("Speech recognition initialized and listeners assigned.");
     } else if (mounted) {
       print("Speech recognition not available on this device after initialize.");
       _showError("Speech recognition is not available on this device.");
     }
   } catch (e) {
     print("Exception calling speech.initialize: $e");
     if (mounted) {
       _showError("Failed to initialize speech recognition service due to an exception.");
     }
   }
 }

 void _onSpeechStatusUpdate(String status) {
    // print('Speech Listen Status (from statusListener): $status');
    if (!mounted) return;

    bool newListeningState;
    if (status == stt.SpeechToText.listeningStatus) {
      newListeningState = true;
    } else if (status == stt.SpeechToText.notListeningStatus || status == stt.SpeechToText.doneStatus) {
      newListeningState = false;
      // print("STT session ended or stopped (via statusListener). Status: $status.");
    } else {
      // print("Received intermediate STT status (via statusListener): $status. Current listening state: $_isListening");
      return;
    }

    if (_isListening != newListeningState) {
      if (mounted) {
        setState(() { _isListening = newListeningState; });
      }
    }
 }

 void _onSpeechErrorUpdate(SpeechRecognitionError errorNotification) {
    print('Speech Listen Error (from errorListener): ${errorNotification.errorMsg}, permanent: ${errorNotification.permanent}');
    if (mounted) {
      if (_isListening) {
          setState(() => _isListening = false);
      }
      _showError("Speech recognition error: ${errorNotification.errorMsg}");
    }
 }


 Future<void> _configureTts() async {
     await _flutterTts.awaitSpeakCompletion(true); // This is important for how _speakText will behave
     await _flutterTts.setVolume(1.0);
     await _flutterTts.setSpeechRate(0.5);
     await _flutterTts.setPitch(1.0);
     if (_selectedVoice != null && mounted) {
       try {
         List<dynamic>? voices = await _flutterTts.getVoices;
         bool voiceExists = voices?.any((v) => v is Map && v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale']) ?? false;

         if (voiceExists) {
           await _flutterTts.setVoice(_selectedVoice!);
           // print("TTS voice set: ${_selectedVoice!['name']}");
         } else {
           print("Selected voice ' ${_selectedVoice!['name']}' not found or incompatible, attempting to set by language 'en-US'.");
           if(mounted) setState(() => _selectedVoice = null);
           await _flutterTts.setLanguage("en-US");
         }
       } catch (e) {
         print("Error setting TTS voice: $e. Using default 'en-US'.");
         if (mounted) setState(() => _selectedVoice = null);
         await _flutterTts.setLanguage("en-US");
       }
     } else {
       await _flutterTts.setLanguage("en-US");
       // print("Using default US English TTS voice.");
     }
  }

 Future<void> _loadTtsVoices() async {
     if (!mounted) return;
     try {
       var voices = await _flutterTts.getVoices;
       if (voices != null && voices is List && mounted) {
         List<Map<String, String>> englishVoices = voices
               .map((v) => Map<String, String>.from(v as Map))
               .where((v) => v['locale']?.startsWith('en-') ?? false)
               .sortedBy<String>((v) => v['name'] ?? '')
               .toList();

         setState(() {
           _availableVoices = englishVoices;
           _voicesLoaded = true;
           if (_selectedVoice == null || !_availableVoices.any((v) => v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale'])) {
             _selectedVoice = _availableVoices.firstWhereOrNull( (v) => v['locale'] == 'en-US' && (v['name']?.toLowerCase().contains('female') ?? false))
                            ?? _availableVoices.firstWhereOrNull((v) => v['locale'] == 'en-US')
                            ?? _availableVoices.firstOrNull;
           }
         });
         await _configureTts(); // Re-configure with potentially new selected voice
         // print("Loaded ${_availableVoices.length} English voices. Selected: ${_selectedVoice?['name']}");
       }
     } catch (e) {
       print("Error getting TTS voices: $e");
       if (mounted) setState(() => _voicesLoaded = false);
     }
   }

  Future<void> _speakText(String textToSpeak) async {
    if (_isMuted || !mounted || textToSpeak.isEmpty) return;
    try {
      await _flutterTts.stop(); // Stop any ongoing speech before starting new
      await _configureTts();    // Ensure voice and other params are set (important if changed via dropdown)
      // A small delay can sometimes help ensure stop/configure take effect before speak
      // await Future.delayed(const Duration(milliseconds: 50));
      if (!_isMuted && mounted) { // Re-check mute status as it could change
        await _flutterTts.speak(textToSpeak);
      }
    } catch (e) {
      print("Error during TTS speak operation: $e");
    }
  }


 void _toggleListening() async {
    if (_processingStep != ProcessingStep.idle) return;

    if (_speechToText.isListening) {
      await _speechToText.stop();
      // _isListening state will be updated by _onSpeechStatusUpdate
    } else {
      if (!await _speechToText.hasPermission) {
        _showError("Speech permission required. Please grant microphone access in settings.");
        return;
      }

      if (!_speechToText.isAvailable) {
          print("STT service not available. Attempting re-initialization.");
          await _initSpeech(); // This calls initialize which has its own error handling
          if (!_speechToText.isAvailable && mounted) { // Check again after re-init
             _showError("Could not start speech recognition: service unavailable after re-attempt.");
             return;
          }
      }
      // If still not available after re-init, the above `_showError` handles it.
      // If it becomes available, proceed to listen.
      if(mounted && _speechToText.isAvailable){
         _startSingleListenSession();
      }
    }
 }

 Future<void> _startSingleListenSession() async {
    // Ensure not already listening, not processing, and component is mounted
    if (_speechToText.isListening || _processingStep != ProcessingStep.idle || !mounted) {
       return;
    }
    await _flutterTts.stop(); // Stop any TTS before listening

    _speechToText.listen(
      listenFor: const Duration(seconds: 30), // Listen for up to 30 seconds
      pauseFor: const Duration(seconds: 4),  // Stop if user pauses for 4 seconds
      partialResults: true, // Get intermediate results
      onResult: (result) {
        if (!mounted) return;
        // Update text field with recognized words
        _messageController.text = result.recognizedWords;
        // Move cursor to the end of the text
        _messageController.selection = TextSelection.fromPosition(TextPosition(offset: _messageController.text.length));
      },
      listenMode: stt.ListenMode.dictation, // Suitable for free-form speech
    ).catchError((error, stackTrace) {
       // This catchError is for the Future returned by listen() itself, e.g., if platform call fails
       print("Error calling STT listen() method: $error\n$stackTrace");
       if (mounted) {
          _showError("Failed to start speech recognition listening session.");
          if (_isListening) setState(() => _isListening = false); // Ensure UI reflects listening stopped
       }
    });
    // _isListening state will be updated by _onSpeechStatusUpdate
 }

 void _handleManualInput(String value) {
     // If user types while STT is active, stop STT
     if (_speechToText.isListening && _messageController.text.isNotEmpty && mounted) {
        _speechToText.stop(); // _isListening will be updated by status listener
     }
 }

 Future<void> _sendMessage(String message) async {
   final String userMessageText = message.trim();
   if (userMessageText.isEmpty || _processingStep != ProcessingStep.idle) return;

   if (_speechToText.isListening) { // Ensure STT is stopped
       await _speechToText.stop();
   }

   String? quickAnswer = _getQuickAnswer(userMessageText);
   if (quickAnswer != null) {
     print("Quick Answer triggered for: '$userMessageText'");
     final approxUserTokens = _calculateApproxTokens(userMessageText);
     final approxResponseTokens = _calculateApproxTokens(quickAnswer);

     if (_dailyTokenCount + approxUserTokens + approxResponseTokens > _maxDailyTokens) {
       _showError("Daily token limit reached. Cannot send message.");
       return;
     }
     if (mounted) _messageController.clear();
     setState(() {
       _messages.add(ChatMessage(text: userMessageText, isUser: true));
       _messages.add(ChatMessage(text: quickAnswer, isUser: false));
       _dailyTokenCount += approxUserTokens + approxResponseTokens;
       _lastPromptTokenCount = approxUserTokens;
       _lastApiResponseTokenCount = approxResponseTokens;
       // No change to _processingStep, it remains idle for quick answers
     });
     _scrollToBottom();
     _speakText(quickAnswer); // MODIFIED: Use helper for TTS
     return;
   }

   int approxUserMessageTokens = _calculateApproxTokens(userMessageText);

   final userChatMessage = ChatMessage(text: userMessageText, isUser: true);
   setState(() {
     _processingStep = ProcessingStep.processing; // Set to processing
     _isListening = false; // Ensure listening is off
     _messages.add(userChatMessage);
     _lastPromptTokenCount = approxUserMessageTokens;
     _lastApiResponseTokenCount = 0; // Reset for new response
   });
   _scrollToBottom();
   if (mounted) _messageController.clear();

   int actualPromptTokensForTableSelection = 0;
   int actualResponseTokensForTableSelection = 0;
   String tableSelectionResponseText = "";

   int actualPromptTokensForFinalResponse = 0;
   int actualResponseTokensForFinalResponse = 0;
   String finalApiResponseText = "";

   List<String> tablesToFetchInitially = [];
   String contextData = "";

   try {
     _addOrUpdateTypingIndicator("Analyzing query...");
     _scrollToBottom();

     final tableSelectionPrompt = _buildTableSelectionPrompt(userMessageText);
     int approxTableSelectionPromptTokens = _calculateApproxTokens(tableSelectionPrompt);
     if (_dailyTokenCount + approxTableSelectionPromptTokens + APPROX_TABLE_SELECTION_RESPONSE_TOKENS > _maxDailyTokens) {
       throw Exception("Estimated daily token limit would be exceeded by table selection request.");
     }
     actualPromptTokensForTableSelection = await _countTokensApi(tableSelectionPrompt, _model);
     if(mounted) setState(() => _lastPromptTokenCount = actualPromptTokensForTableSelection);
     if (_dailyTokenCount + actualPromptTokensForTableSelection + APPROX_TABLE_SELECTION_RESPONSE_TOKENS > _maxDailyTokens) {
        throw Exception("Daily token limit would be exceeded by table selection request (actual count).");
     }


     _addOrUpdateTypingIndicator("Selecting relevant tables...");
     tableSelectionResponseText = await _callGeminiApi(tableSelectionPrompt, isContextBuilding: true);
     if (!mounted) return; // Early exit if widget is disposed

     actualResponseTokensForTableSelection = await _countTokensApi(tableSelectionResponseText, _model);
     if(mounted) setState(() => _lastApiResponseTokenCount = actualResponseTokensForTableSelection);

     tablesToFetchInitially = tableSelectionResponseText
         .split(',')
         .map((t) => t.trim().toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'),''))
         .where((t) => t.isNotEmpty && t != 'none' && _tableManifest.containsKey(t))
         .toSet().toList();

     _addOrUpdateTypingIndicator("Fetching relevant details...");
     _scrollToBottom();
     contextData = await _getCollegeDataForPrompt(userMessageText, tablesToFetchInitially);
     if (!mounted) return;
     if (contextData.startsWith('No specific college data')) throw Exception("Error loading essential college information.");
     if (contextData == 'Component unmounted') return;

     _addOrUpdateTypingIndicator("Generating response...");
     final finalPrompt = _buildFinalPrompt(userMessageText, contextData, _buildConversationHistory());
     int approxFinalPromptTokens = _calculateApproxTokens(finalPrompt);
     if (_dailyTokenCount + actualPromptTokensForTableSelection + actualResponseTokensForTableSelection + approxFinalPromptTokens + APPROX_FINAL_RESPONSE_TOKENS > _maxDailyTokens) {
        throw Exception("Estimated daily token limit would be exceeded by final response generation.");
     }
     actualPromptTokensForFinalResponse = await _countTokensApi(finalPrompt, _model);
      if (_dailyTokenCount + actualPromptTokensForTableSelection + actualResponseTokensForTableSelection + actualPromptTokensForFinalResponse + APPROX_FINAL_RESPONSE_TOKENS > _maxDailyTokens) {
        throw Exception("Daily token limit would be exceeded by final response generation request (actual count).");
     }
     if(mounted) setState(() => _lastPromptTokenCount = actualPromptTokensForFinalResponse);

     _addOrUpdateTypingIndicator("Generating final response...");
     finalApiResponseText = await _callGeminiApi(finalPrompt);
     if (!mounted) return;

     actualResponseTokensForFinalResponse = await _countTokensApi(finalApiResponseText, _model);
     if(mounted) setState(() => _lastApiResponseTokenCount = actualResponseTokensForFinalResponse);

     // --- Response Handling and UI Update ---
     _removeTypingIndicator(); // Remove "Typing..." indicator

     final aiResponseMessage = ChatMessage(
         text: finalApiResponseText.isEmpty ? "Sorry, I couldn't generate a response based on the available information." : finalApiResponseText,
         isUser: false
     );

     int totalTokensConsumedThisTurn =
         actualPromptTokensForTableSelection +
         actualResponseTokensForTableSelection +
         actualPromptTokensForFinalResponse +
         actualResponseTokensForFinalResponse;

     if (mounted) {
        setState(() {
            // Add AI message
            _messages.add(aiResponseMessage);

            // Update token counts and check limits
            if (_dailyTokenCount + totalTokensConsumedThisTurn > _maxDailyTokens && _dailyTokenCount < _maxDailyTokens) {
                print("Warning: Daily token limit was exceeded upon final count. Displaying response.");
            }
            _dailyTokenCount += totalTokensConsumedThisTurn;
            if (_dailyTokenCount > _maxDailyTokens) {
                print("Warning: Daily token count exceeded threshold. Current: $_dailyTokenCount");
            }
            _processingStep = ProcessingStep.idle; // MODIFIED: Set to idle BEFORE TTS
        });
     }
     _scrollToBottom(); // Scroll to the new message

     // Speak the response (if not muted) AFTER UI is updated and processing is idle
     _speakText(finalApiResponseText); // MODIFIED: Use helper for TTS

   } catch (e, stacktrace) {
     print('Error in multi-step pipeline: $e\n$stacktrace');
     _removeTypingIndicator(); // Ensure typing indicator is removed on error

     int tokensConsumedBeforeError = 0;
     if (actualPromptTokensForTableSelection > 0) tokensConsumedBeforeError += actualPromptTokensForTableSelection;
     if (actualResponseTokensForTableSelection > 0) tokensConsumedBeforeError += actualResponseTokensForTableSelection;
     if (actualPromptTokensForFinalResponse > 0) tokensConsumedBeforeError += actualPromptTokensForFinalResponse;

     if (mounted && tokensConsumedBeforeError > 0) {
       setState(() { // Update daily count for tokens consumed before error
         _dailyTokenCount += tokensConsumedBeforeError;
         if (_dailyTokenCount > _maxDailyTokens) print("Warning: Daily token count exceeded on error. Current: $_dailyTokenCount");
       });
     }
     String errorMessageText = e.toString().replaceFirst("Exception: ", "");
     _showError('Sorry, an error occurred: $errorMessageText'); // _showError also handles TTS for the error

     // Ensure processingStep is set to idle in the catch block too,
     // as the finally block might run too late if _showError does async work
     if (mounted && _processingStep != ProcessingStep.idle) {
        setState(() { _processingStep = ProcessingStep.idle; });
     }

   } finally {
     // This finally block ensures that, no matter what, we try to reset the state.
     // It's a safeguard.
     if (mounted) {
       _removeTypingIndicator(); // Call again, harmless if already removed
       if (_processingStep != ProcessingStep.idle) { // If not set idle by success/error paths
         setState(() { _processingStep = ProcessingStep.idle; });
       }
       _scrollToBottom(); // Ensure scroll
     }
   }
 }

 String _buildFinalPrompt(String userQuery, String contextData, String history) {
    final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    final mainPhoneNumber = widget.collegeData?['phone'] ?? 'the main college phone number';

    // Add language instruction if a non-English language is selected
    String languageInstruction = '';
    if (_outputLanguage != null && _outputLanguage != 'English') {
      languageInstruction = "- IMPORTANT: Respond ONLY in ${_outputLanguage!} language. All your text must be in ${_outputLanguage!}.\n";
    }

    return '''SYSTEM: You are a specialized AI assistant for $collegeName. Answer the user's query based ONLY on the information provided in the 'Provided Context' section below.
Instructions:
$languageInstruction- Use the 'Provided Context' which contains 'General Info' about the college and 'Relevant Specific Information' fetched based on the query (this may include data from tables like people, services, departments, schools, centers, helpdesks, faq, and other tables specifically selected for the query like majors, housing, etc.).
- **Format your response for easy readability using clear paragraphs and complete sentences.
- When presenting information that comes from distinct fields about a single entity (e.g., name, phone, email for a department), weave them into a natural sentence or paragraph. For example, instead of 'Name: IT Helpdesk, Phone: 123-456, Services: Password reset, Wifi support', say 'The IT Helpdesk can be reached at 123-456 and offers services such as password resets and wifi support.'
- For lists of multiple distinct items (e.g., a list of several different scholarships, a list of various campus clubs, a list of majors, a list of housing options), use an asterisk (*) at the beginning of each item, with each item on a new line. **If the user asks for "all" or "list" of something (e.g., "what majors are offered?", "list all dorms", "what services are at the IT helpdesk?"), and the relevant data is present in the context, present all available items from the context as a list.**
- Avoid using any raw markdown formatting such as '##' for headers or '**' for bolding. Plain text output is required. If emphasis is needed, rephrase the sentence or state that something is important.**
- **Critically analyze the user's query. If answering it requires combining information from different data entries or tables provided in the 'Provided Context', please synthesize this information. For example, if the user asks about 'services offered at the IT helpdesk', look for 'IT Helpdesk' in the 'helpdesks' data and then cross-reference with the 'services' data, looking for services where the provider is 'IT Helpdesk' or similar. Similarly, connect people to their departments, departments to their schools, etc., if the query requires it and the information is present.**
- **If the user's query is very broad (e.g., "tell me everything about admissions"), politely ask them to be more specific about what aspect of admissions they are interested in to provide a more focused and helpful answer. For example: "Admissions is a broad topic. Could you please specify what you'd like to know? For example, are you interested in the application process, entry requirements, or deadlines?"**
- **If the context provides a very extensive list of items (e.g., hundreds of courses) for a query that didn't explicitly ask for "all" of them, you can summarize the type of information available and offer to list more details or specific items if the user asks. For example, "The course catalog contains many courses across various departments. Are you interested in courses from a particular department or level?" However, if the user explicitly asks for "all majors" or "all housing", and the context contains this data, you should list them all, even if it's a moderately long list.**
- If the information needed to answer the query accurately is NOT present in the 'Provided Context' (e.g., the data for 'majors' was empty or not fetched, or an item was truncated within its table section due to internal budget limits for that section), explicitly state that the specific detail is unavailable or potentially incomplete in the current data and politely suggest the user call $mainPhoneNumber for further assistance, or consult the college website.
- Do NOT use external knowledge or make assumptions beyond the provided context. Be concise and directly answer the question unless a summary or clarification request is more appropriate as described above.
--- Provided Context ---
$contextData
--- End Provided Context ---
--- Recent Conversation History ---
$history
--- End Conversation History ---
user: $userQuery
model:''';
  }


 String _buildTableSelectionPrompt(String userQuery) {
   final manifestFormatted = _tableManifest.entries
       .map((e) => '- ${e.key}: ${e.value}')
       .join('\n');
   return '''SYSTEM: You are an AI assistant helping to select relevant database tables to answer a user's query about a college.
User Query: "$userQuery"
Available Data Tables (Manifest):
Each line has the format: table_name: description
---
$manifestFormatted
---
Instruction: Based *only* on the User Query and the table descriptions in the manifest, list the short table names (e.g., 'admissionsprocess', 'housing', 'costs') from the manifest that are MOST likely to contain the information needed to answer the query.
- List ONLY the relevant table_names, separated by commas (e.g., housing,costs,mealplans).
- Prioritize tables that directly match keywords in the query and their descriptions. Consider synonyms and related concepts (e.g., 'financial aid' might relate to 'scholarships'; 'IT helpdesk services' might relate to both 'helpdesks' and 'services' tables if their descriptions are relevant).
- If the query asks for a list of items (e.g., "list all majors", "what departments are there?", "show housing options", "what services does the IT helpdesk offer?"), ensure you select the table(s) that contain(s) that list (e.g., 'majors', 'departments', 'housing', 'services', 'helpdesks').
- If the query is about a specific entity (e.g., 'IT helpdesk') and asks for details about it (e.g., 'services offered'), ensure you select tables that describe the entity itself (e.g., 'helpdesks' for IT helpdesk details) AND tables that might list the details requested (e.g., 'services' table if it lists services provided by various departments including IT).
- If the query is very general (e.g., "tell me about the college"), list core tables like 'faq', 'links', and potentially 'people', 'departments', 'schools' for a broad overview.
- If multiple tables seem relevant, list them all.
- If no specific table seems relevant based on the query and descriptions, respond with the single word 'NONE'.
- Do NOT add any explanation, preamble, or concluding text. Just provide the comma-separated list or 'NONE'.
model:''';
 }

 String? _getQuickAnswer(String query) {
     if (widget.collegeData == null) return null;
     final lowerQuery = query.toLowerCase();
     String? answer;

     if (RegExp(r'\b(website|site|url|web address|homepage)\b').hasMatch(lowerQuery)) {
         answer = widget.collegeData!['website'] != null
             ? "The college website is: ${widget.collegeData!['website']}"
             : null;
     }
     else if (RegExp(r'\b(phone|contact number|call|telephone)\b').hasMatch(lowerQuery) &&
         !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff") && !lowerQuery.contains("helpdesk")) {
         answer = widget.collegeData!['phone'] != null
             ? "The main phone number for the college is: ${widget.collegeData!['phone']}"
             : null;
     }
     else if (RegExp(r'\b(email|email address|contact email)\b').hasMatch(lowerQuery) &&
          !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff") && !lowerQuery.contains("helpdesk")) {
         answer = widget.collegeData!['email'] != null
             ? "The main contact email for the college is: ${widget.collegeData!['email']}"
             : null;
     }
     else if (RegExp(r'\b(address|location|located|where is|find you|campus address)\b').hasMatch(lowerQuery)) {
         String? address = widget.collegeData!['address']?.toString();
         String? city = widget.collegeData!['city']?.toString();
         String? state = widget.collegeData!['state']?.toString();
         List<String?> parts = [address, city, state];
         List<String> validParts = parts.whereNotNull().where((s) => s.trim().isNotEmpty).toList();
         answer = validParts.isNotEmpty
             ? "The college address is: ${validParts.join(', ')}"
             : null;
     }

     // If we have an answer and a non-English language is selected, add a translation note
     if (answer != null && _outputLanguage != null && _outputLanguage != 'English') {
       // For quick answers, we'll just add a note that it would be in the selected language
       // In a real implementation, you might want to use a translation service here
       answer = "$answer\n\n(Note: This would be translated to $_outputLanguage in a full implementation)";
     }

     return answer;
 }

 Future<String> _getCollegeDataForPrompt(String userQuery, List<String> geminiSuggestedTables) async {
    if (!mounted) return 'Component unmounted';
    if (widget.collegeData == null || widget.collegeData!['fullname']?.isEmpty == true) {
      print("Error: _getCollegeDataForPrompt called with null or empty collegeData.");
      return 'No specific college data available.';
    }

    String collegeName = widget.collegeData!['fullname']!;
    String collegeTablePrefix = widget.collegeData!['tableprefix']?.toString().trim() ?? '';
    if (collegeTablePrefix.isEmpty) {
       collegeTablePrefix = collegeName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');
    }
    if (collegeTablePrefix.isEmpty) collegeTablePrefix = "college";

    final sb = StringBuffer();
    int currentContextChars = 0;
    // Max context characters for Gemini 1.5 Flash can be very high (e.g., 1M tokens = ~4M chars)
    // But for practical API calls and cost, we keep it reasonably constrained.
    // This maxContextChars is for the *data blob* we construct, not the model's absolute limit.
    const int maxContextChars = 7500; // Slightly increased from 6500

    // --- General College Info ---
    sb.writeln('## General Info ($collegeName):');
    bool baseDataAdded = false;
    List<String> essentialFields = [ /* ... same as before ... */
      'fullname', 'about', 'mission', 'vision', 'motto', 'institutiontype',
      'accreditation', 'founded', 'website', 'phone', 'email', 'address',
      'city', 'state', 'postaladdress', 'daysnhours', 'studentpopulation',
      'campussetting', 'academicyearcalendar', 'highestqualificationoffered',
      'corevalues', 'goals', 'mandate', 'freewifi', 'objectives', 'aims'
    ];
    Set<String> addedFields = {};
    for (String field in essentialFields) {
      if (currentContextChars > maxContextChars * 0.25) break;
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        if (value.isNotEmpty && value.toLowerCase() != 'n/a' && value.toLowerCase() != 'null') {
          String displayValue = value;
          int maxLength = (['about', 'mission', 'vision'].contains(field)) ? 250 : 120; // Slightly more for descriptive
          if (displayValue.length > maxLength) displayValue = displayValue.substring(0, maxLength) + '...';
          String entry = '- ${field.readableFieldName()}: $displayValue\n';
          if(currentContextChars + entry.length <= maxContextChars * 0.30) {
            sb.write(entry); currentContextChars += entry.length; baseDataAdded = true; addedFields.add(field);
          } else { break; }
        }
      }
    }
    final relevantDynamicFields = _getRelevantCollegeFields(userQuery, max: 3);
    for (var field in relevantDynamicFields) {
      if (currentContextChars > maxContextChars * 0.30) break;
      if (addedFields.contains(field)) continue;
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        if (value.isNotEmpty && value.toLowerCase() != 'n/a' && value.toLowerCase() != 'null') {
          String displayValue = value; int maxLength = 100; // Slightly more
          if (displayValue.length > maxLength) displayValue = displayValue.substring(0, maxLength) + '...';
          String entry = '- ${field.readableFieldName()}: $displayValue\n';
           if(currentContextChars + entry.length <= maxContextChars * 0.32) {
            sb.write(entry); currentContextChars += entry.length; baseDataAdded = true; addedFields.add(field);
          } else { break; }
        }
      }
    }
    if (!baseDataAdded) sb.writeln("(No relevant general details found for this query)");
    sb.writeln('---');
    currentContextChars = sb.length;

    // --- Specific Table Data Fetching ---
    Set<String> finalTablesToFetchSet = Set<String>.from(geminiSuggestedTables);
    finalTablesToFetchSet.addAll(_alwaysConsiderTables.where((t) => _tableManifest.containsKey(t)));
    List<String> finalTablesToFetchList = finalTablesToFetchSet.toList();

    List<String> actuallyFetchedTableNames = [];
    if (finalTablesToFetchList.isNotEmpty) {
       sb.writeln('\n## Relevant Specific Information (Data fetched based on query analysis and key areas):');
       currentContextChars = sb.length;
       List<Future<Map<String, String>>> fetchFutures = [];

       double remainingBudgetForTables = maxContextChars * 0.90 - currentContextChars;
       int totalTablesToFetch = finalTablesToFetchList.length;

       // Key tables that often contain lists and might need more budget if user asks for "all"
       const List<String> listHeavyTables = [
           'housing', 'majors', 'scholarships', 'coursecatalog', 'services',
           'events', 'jobs', 'orgsorclubs', 'people', 'departments',
           'schools', 'centers', 'documents', 'minors', 'programs', 'athletics'
       ];

       for (String tableNameShort in finalTablesToFetchList) {
           if (currentContextChars >= maxContextChars * 0.90) {
               break;
           }
           String fullTableName = "${collegeTablePrefix}_$tableNameShort";

           bool wasGeminiSuggested = geminiSuggestedTables.contains(tableNameShort);
           double budgetFactor = (wasGeminiSuggested || listHeavyTables.contains(tableNameShort)) ? 1.5 : 0.8; // Adjusted factor

           // MODIFIED: Budget allocation for tables
           int maxCharsForThisTable = 2000; // Default max per table
           if (wasGeminiSuggested && listHeavyTables.contains(tableNameShort)) {
                 // If Gemini suggested a list-heavy table, it's likely the user wants a list from it.
                 maxCharsForThisTable = 6000; // Increased max character budget for these specific tables
           }

           int budgetForThisTable = totalTablesToFetch > 0
                                    ? ((remainingBudgetForTables / totalTablesToFetch) * budgetFactor).clamp(300, maxCharsForThisTable).toInt()
                                    : 300;

           fetchFutures.add(
              _fetchSupabaseDataForRow(fullTableName, maxLengthBudget: budgetForThisTable)
                 .then((data) => {'tableName': tableNameShort, 'data': data})
                 .catchError((e) {
                    print("Error fetching data for $tableNameShort ($fullTableName): $e");
                    return {'tableName': tableNameShort, 'data': "Error fetching data for ${tableNameShort.readableFieldName()}: Could not retrieve details.\n"};
                 })
           );
       }

       final results = await Future.wait(fetchFutures);
       if (!mounted) return 'Component unmounted';

       bool specificDataAdded = false;
       for (var result_item in results) { // Iterating over each fetched result
         final name = result_item['tableName']!;
         final data = result_item['data']!;

         if (data.isNotEmpty) {
           final sectionTitle = name.readableFieldName();
           // Check if data indicates an error message from fetch
           bool isErrorData = data.toLowerCase().contains("error fetching data for");
           String entry = '\n### From $sectionTitle Data:\n$data\n---\n';

           if (currentContextChars + entry.length <= maxContextChars * 0.95 || isErrorData) { // Allow error messages even if slightly over
             sb.write(entry);
             currentContextChars += entry.length;
             if (!isErrorData) specificDataAdded = true; // Only count non-error data as "added"
             actuallyFetchedTableNames.add(name);
           } else {
              // Optionally log if a non-error table was skipped
              if(!isErrorData) print("Skipping data from $name due to character limit. Current: $currentContextChars, Entry: ${entry.length}");
           }
         }
       }
        if (!specificDataAdded &&
            finalTablesToFetchList.isNotEmpty &&
            !actuallyFetchedTableNames.any((tableNameFromList) {
              // Find the original result for this tableNameFromList from the 'results' list
              final correspondingResult = results.firstWhereOrNull(
                (res) => res['tableName'] == tableNameFromList
              );
              // If correspondingResult or its data is null, it's not an "error fetching data for" string.
              if (correspondingResult == null || correspondingResult['data'] == null) {
                return false;
              }
              return correspondingResult['data']!.toLowerCase().contains("error fetching data for");
            })) {
            sb.writeln("(No specific data retrieved or data was empty/filtered for tables: ${finalTablesToFetchList.join(', ')}, or budget was exceeded).");
        }
        currentContextChars = sb.length;
    } else {
        sb.writeln('\n## Relevant Specific Information (Fetched):');
        sb.writeln("(No specific tables were identified or proactively added for fetching additional data).");
        currentContextChars = sb.length;
    }

    // --- Manifest (Very brief, if space) ---
    String manifestHeader = '\n## Available Data Tables (Manifest for Reference Only - AI should prioritize fetched data above):\n';
    if (currentContextChars + manifestHeader.length < maxContextChars * 0.98) {
        sb.write(manifestHeader);
        currentContextChars += manifestHeader.length;
        int manifestEntryCount = 0;
        _tableManifest.forEach((tableName, description) {
          if (manifestEntryCount >= 7 && currentContextChars > maxContextChars * 0.9) return; // Show a few more manifest entries
          String entry = '- ${tableName.readableFieldName()}: ${description.substring(0, description.length > 60 ? 60 : description.length)}...\n'; // Slightly longer desc
          if (currentContextChars + entry.length <= maxContextChars) {
            sb.write(entry);
            currentContextChars += entry.length;
            manifestEntryCount++;
          } else {
            return;
          }
        });
    }
    print("Context built. Chars: ${sb.length}. Approx Tokens: ${_calculateApproxTokens(sb.toString())}");
    return sb.toString();
  }

Future<String> _fetchSupabaseDataForRow(String tableName, {required int maxLengthBudget}) async {
    if (maxLengthBudget <= 0 || !mounted) return "";
    try {
        final response = await Supabase.instance.client
            .from(tableName)
            .select()
            .timeout(const Duration(seconds: 25)); // Increased timeout

        if (!mounted) return "";

        if (response is List && response.isNotEmpty) {
            final sb = StringBuffer();
            // No "Entries from..." here, _getCollegeDataForPrompt adds the "### From TableName Data:" header
            int itemsProcessed = 0;
            for (int i = 0; i < response.length; i++) {
                if (sb.length >= maxLengthBudget * 0.98) {
                    sb.writeln("- ... (additional items from ${tableName.readableFieldName()} truncated due to character budget for this section)");
                    break;
                }
                final row = response[i] as Map<String, dynamic>;
                List<String> parts = [];
                row.forEach((key, value) {
                    if (value != null &&
                        key != 'id' && key != 'created_at' && key != 'updated_at' &&
                        key != 'uuid' && !key.endsWith("_id") && // Keep general foreign keys if they aren't just 'id'
                        value.toString().trim().isNotEmpty) {

                        String valStr = value.toString().trim();
                        if (valStr.isNotEmpty && valStr.toLowerCase() != 'n/a' && valStr.toLowerCase() != 'null') {
                            int maxLen = 100; // Default max length for a field value
                            if (key.toLowerCase().contains('description') ||
                                key.toLowerCase().contains('details') ||
                                key.toLowerCase().contains('about') ||
                                key.toLowerCase().contains('name') ||
                                key.toLowerCase().contains('title') ||
                                key.toLowerCase().contains('summary') ||
                                key.toLowerCase().contains('content') ||
                                key.toLowerCase().contains('requirements') ||
                                key.toLowerCase().contains('features')
                                ) {
                               maxLen = 180; // More space for descriptive or key fields
                            }
                            if (valStr.length > maxLen) valStr = valStr.substring(0, maxLen) + '...';
                            parts.add('${key.readableFieldName()}: $valStr');
                        }
                    }
                });
                if (parts.isNotEmpty) {
                    String rowSummary = parts.join('; ');
                    String entry = '- $rowSummary\n';
                    if (sb.length + entry.length <= maxLengthBudget) {
                        sb.write(entry);
                        itemsProcessed++;
                    } else {
                        sb.writeln("- ... (additional items from ${tableName.readableFieldName()} truncated due to character budget for this section)");
                        break;
                    }
                }
            }
            if (itemsProcessed == 0 && response.isNotEmpty) {
                 return "- (No displayable details found for items in ${tableName.readableFieldName()} after filtering, or budget too small for the first item).\n";
            }
            if (itemsProcessed == 0) return "";

            return sb.toString().trim();
        } else {
            return ''; // No data or not a list
        }
    } on PostgrestException catch (e) {
        if (e.code == '42P01') { // Table not found
             // print("Info: Table '$tableName' not found (Supabase code 42P01). This might be expected.");
             return "Note: The data source '${tableName.readableFieldName()}' appears to be missing or is not currently available.\n";
        } else {
            print("Supabase error fetching '$tableName': ${e.message} (Code: ${e.code})");
        }
        return "Error fetching data for ${tableName.readableFieldName()}: Table might be inaccessible (${e.code}).\n";
    } catch (e) {
        print("General error fetching '$tableName': $e");
        return "Error fetching data for ${tableName.readableFieldName()}: $e\n";
    }
}


  Future<String> _callGeminiApi(String promptText, {bool isContextBuilding = false}) async {
    if (_apiKey == 'YOUR_API_KEY_HERE' || _apiKey.length < 20) {
      print("ERROR: API Key is a placeholder or missing. Please update it in the code.");
      throw Exception("Configuration error: Invalid API key. Please update it in the code.");
    }
    final modelToUse = _model.startsWith('models/') ? _model.split('/').last : _model;
    // Ensure the model name used in the URL is just the model ID, not "models/" prefixed.
    // For Gemini 1.5 Flash, the endpoint is usually `v1beta/models/gemini-1.5-flash-latest:generateContent`
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$modelToUse:generateContent?key=$_apiKey');


    final generationConfig = {
      'temperature': isContextBuilding ? 0.15 : 0.55, // Slightly adjusted
      'topP': 0.95,
      'topK': 40, // Can be adjusted, sometimes higher (e.g., 64) for more diverse but less predictable
      'maxOutputTokens': isContextBuilding ? 256 : 2048, // Max for table selection is fine, main response can be up to 8192 for flash
    };
    final body = jsonEncode({
      'contents': [{'role': 'user', 'parts': [{'text': promptText}]}],
      'generationConfig': generationConfig,
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'}
      ]
    });

    try {
       final response = await http.post(url, headers: {'Content-Type': 'application/json'}, body: body)
           .timeout(const Duration(seconds: 120)); // Increased timeout for potentially larger responses

       final decoded = jsonDecode(response.body);

       if (response.statusCode == 200) {
          if (decoded['promptFeedback'] != null && decoded['promptFeedback']['blockReason'] != null) {
            final reason = decoded['promptFeedback']['blockReason'];
            final ratings = decoded['promptFeedback']['safetyRatings'];
            print("Prompt blocked by Gemini: $reason. Ratings: $ratings");
            throw Exception("Request blocked by AI due to input content (Reason: $reason). Please rephrase your query.");
          }

          final candidates = decoded['candidates'];
          if (candidates != null && candidates.isNotEmpty) {
             final candidate = candidates[0];
             final finishReason = candidate['finishReason'];

             // Handle SAFETY for candidate
             if (finishReason == 'SAFETY') {
                 final ratings = candidate['safetyRatings'];
                 print("Warning: Gemini response blocked for safety. Reason: $finishReason. Ratings: $ratings");
                 throw Exception("Response blocked by AI due to output content (Safety).");
             }

             if (finishReason != null && finishReason != 'STOP' && finishReason != 'MAX_TOKENS') {
               if (finishReason == 'RECITATION') {
                  print("Warning: Gemini response flagged for recitation. Reason: $finishReason.");
                  // Allow recitation for now, but log it. Could throw exception if strict.
               } else if (finishReason == 'OTHER') {
                  print("Warning: Gemini response incomplete. Reason: $finishReason (OTHER).");
                  // This might mean the model couldn't generate a response for other reasons.
                  // Could be an issue if it happens often.
               } else if (finishReason != 'STOP' && finishReason != 'MAX_TOKENS') { // Other unexpected reasons
                 print("Warning: Gemini response potentially incomplete. Finish Reason: $finishReason");
               }
             }

             final content = candidate['content'];
             if (content != null && content['parts'] != null && (content['parts'] as List).isNotEmpty) {
               String resultText = content['parts'][0]['text']?.trim() ?? '';
               return resultText;
             }
             // If content or parts are missing, but not blocked by safety for candidate
             print("Warning: No content parts in successful response candidate, but not a safety block for candidate. Candidate: $candidate. Decoded: $decoded");
             return ''; // Or throw, if this should not happen.
          }
          // If no candidates, but also no promptFeedback block
          print("Warning: No candidates returned by API and no explicit promptFeedback block. Full response: $decoded");
          throw Exception("No valid response generated by AI (no candidates and no explicit block).");
       } else { // Non-200 status code
          String errorMessage = 'AI API request failed (Status: ${response.statusCode}).';
          final errorDetails = decoded['error'] as Map<String, dynamic>?;
          if (errorDetails != null && errorDetails['message'] != null) {
            errorMessage += ' Error: ${errorDetails['message']}';
          } else if (response.body.isNotEmpty) {
            errorMessage += ' Response Body: ${response.body.length > 300 ? response.body.substring(0, 300) + "..." : response.body}';
          }
          print(errorMessage);
          // Specific error handling for common issues like API key
          if (response.statusCode == 400 && (errorDetails?['message']?.toString().contains('API key not valid') ?? false)) {
              throw Exception("AI API Key is not valid. Please check your API key configuration.");
          }
          if (response.statusCode == 403) { // Permission denied
              throw Exception("AI API request denied (403). Check API key permissions or project setup.");
          }
          if (response.statusCode == 429) { // Quota exceeded
              throw Exception("AI API quota exceeded (429). Please check your usage limits or billing.");
          }
          throw Exception(errorMessage);
       }
    } on TimeoutException catch (_) {
        print("Request to AI service timed out.");
        throw Exception("Request to AI service timed out. Please try again.");
    } on SocketException catch (e) {
        print("Network error connecting to AI service: $e");
        throw Exception("Network error: Could not connect to AI service. Check your internet connection.");
    } on http.ClientException catch (e) {
        print("Network client error contacting AI service: $e");
        throw Exception("Network error: Problem contacting AI service.");
    }
    catch (e) { // Catch any other exceptions, including those re-thrown
      print("Unhandled error during Gemini API call: $e");
      if (e is Exception) throw e; // Re-throw if already an Exception
      throw Exception("An unexpected error occurred while communicating with the AI: ${e.toString()}");
    }
  }

  Future<int> _countTokensApi(String textToCount, String modelName) async {
    if (textToCount.isEmpty) return 0;
    if (_apiKey == 'YOUR_API_KEY_HERE' || _apiKey.length < 20) {
      return _calculateApproxTokens(textToCount);
    }

    final modelId = modelName.startsWith('models/') ? modelName.split('/').last : modelName;
    final uri = Uri.https(
      'generativelanguage.googleapis.com',
      '/v1beta/models/$modelId:countTokens', // Ensure this matches the model version (e.g., v1beta)
      {'key': _apiKey},
    );

    try {
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
            // For gemini-1.5-flash, 'model' field is often expected inside the request for countTokens
            // 'model': 'models/$modelId', // This might be needed depending on API version
            'contents': [{'role': 'user', 'parts': [{'text': textToCount}]}]
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final tokenCount = data['totalTokens'] as int?;
        if (tokenCount != null) {
          return tokenCount;
        } else {
          print("Token count ('totalTokens') not found in API response. Body: ${response.body}. Using approximation.");
          return _calculateApproxTokens(textToCount);
        }
      } else {
        String responseBody = response.body;
        String errorMessage = 'Unknown error';
        try {
            final errorBody = jsonDecode(responseBody);
            errorMessage = errorBody['error']?['message'] ?? responseBody;
        } catch (e) {
            errorMessage = responseBody.length > 100 ? responseBody.substring(0,100) + "..." : responseBody;
        }
        print('Token count API failed (${response.statusCode}): $errorMessage. Using approximation.');
        if (response.statusCode == 400 && errorMessage.contains("model parameter is not set")) {
             print("Consider adding 'model': 'models/$modelId' to the countTokens request body.");
        }
        return _calculateApproxTokens(textToCount);
      }
    } on TimeoutException catch (_) {
      return _calculateApproxTokens(textToCount);
    } on SocketException catch (_) {
      return _calculateApproxTokens(textToCount);
    } on http.ClientException catch (_) {
      return _calculateApproxTokens(textToCount);
    } catch (e) {
      return _calculateApproxTokens(textToCount);
    }
  }


 @override
 Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    String appBarTitle = 'College AI Assistant';
    if (widget.collegeData?['fullname']?.isNotEmpty == true) {
      appBarTitle = "${widget.collegeData!['fullname']} AI";
    }
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    // Interaction is possible if not processing OR if token limit not reached.
    // The send button itself will be disabled if text is empty.
    final bool canInteractGenerally = _processingStep == ProcessingStep.idle && !tokenLimitReached;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent, // Or theme.colorScheme.surface for a slight tint
        elevation: 1.0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: iconColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          // Language selection dropdown
          Padding(
            padding: const EdgeInsets.only(right: 0.0),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _outputLanguage,
                hint: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Icon(Icons.language, color: iconColor.withOpacity(0.7), size: 20)
                ),
                icon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)
                ),
                selectedItemBuilder: (context) => [
                  'English', 'Chichewa', 'Chitumbuka', 'Swahili', 'Shona', 'Zulu'
                ].map((_) => Center(
                  child: Tooltip(
                    message: "Output Language: $_outputLanguage",
                    child: Icon(Icons.language, color: iconColor.withOpacity(0.7), size: 20)
                  )
                )).toList(),
                items: [
                  DropdownMenuItem(value: 'English', child: Text('English')),
                  DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')),
                  DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                  DropdownMenuItem(value: 'Swahili', child: Text('Swahili')),
                  DropdownMenuItem(value: 'Shona', child: Text('Shona')),
                  DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
                ],
                onChanged: (_processingStep == ProcessingStep.idle)
                  ? (String? newValue) {
                      if (newValue != null && mounted) {
                        setState(() => _outputLanguage = newValue);
                      }
                    }
                  : null,
                style: TextStyle(color: theme.colorScheme.onSurface),
                dropdownColor: theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
                elevation: 4,
              ),
            ),
          ),
          // Voice selection dropdown
          if (_voicesLoaded && _availableVoices.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Map<String, String>>(
                  value: _selectedVoice,
                  hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)),
                  icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                  selectedItemBuilder: (context) => _availableVoices
                      .map((_) => Center(
                            child: Tooltip(
                                message: _selectedVoice != null ? "${_selectedVoice!['name']} (${_selectedVoice!['locale']})" : "Select Voice",
                                child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)
                             )
                          )).toList(),
                  items: _availableVoices
                      .map((voice) => DropdownMenuItem<Map<String, String>>(
                          value: voice,
                          child: Tooltip(
                              message: "${voice['name']} (${voice['locale']})",
                              child: SizedBox(
                                width: 180, // Adjust width as needed
                                child: Text("${voice['name']} (${voice['locale']})",
                                    style: TextStyle(fontSize: 12, color: theme.colorScheme.onSurfaceVariant),
                                    overflow: TextOverflow.ellipsis)
                              )
                          )
                       )).toList(),
                  onChanged: (_processingStep == ProcessingStep.idle) // Allow changing voice even if token limit reached
                      ? (Map<String, String>? newValue) {
                          if (newValue != null && mounted) {
                            setState(() => _selectedVoice = newValue);
                            _configureTts(); // Apply new voice
                          }
                        }
                      : null,
                  style: TextStyle(color: theme.colorScheme.onSurface),
                  dropdownColor: theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                  elevation: 4,
                ),
              ),
            ),
          IconButton(
            icon: Icon(Icons.refresh, color: (_processingStep == ProcessingStep.idle) ? iconColor : theme.disabledColor),
            tooltip: "Start New Conversation",
            // Allow refresh even if token limit reached, as it resets local state
            onPressed: (_processingStep == ProcessingStep.idle) ? _startNewConversation : null,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _messages.length,
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 8.0),
                itemBuilder: (context, index) {
                  final msg = _messages[index];
                  return ChatBubble(
                    // Using a more robust key
                    key: ValueKey("msg-${msg.hashCode}-${index}-${msg.isUser}-${msg.isTyping}-${msg.text.length > 10 ? msg.text.substring(0,10) : msg.text}"),
                    message: msg,
                    isDarkMode: isDark,
                    theme: theme,
                  );
                },
              ),
            ),
            _buildInputAreaReverted(theme),
          ],
        ),
      ),
    );
 }

 Widget _buildInputAreaReverted(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    final sendProgressColor = isDark ? Colors.white : Colors.black; // Or theme.colorScheme.primary
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    // Can send or listen if NOT processing AND token limit is NOT reached.
    final bool canSendOrListen = _processingStep == ProcessingStep.idle && !tokenLimitReached;
    // General interaction with input field (typing) is allowed even if processing (e.g. to stop STT)
    // but actual send/listen actions are gated by `canSendOrListen`.
    final bool inputFieldEnabled = _processingStep == ProcessingStep.idle || _isListening;


    String hintText = 'Ask a question...';
    if (widget.collegeData?['fullname'] != null && widget.collegeData!['fullname']!.isNotEmpty) {
      hintText = 'Ask about ${widget.collegeData!['fullname']}...';
    }

    if (_processingStep != ProcessingStep.idle) {
       final typingMsg = _messages.lastWhereOrNull((m) => m.isTyping);
       hintText = typingMsg?.text ?? "Processing...";
    } else if (_isListening) {
       hintText = 'Listening... Speak now';
    } else if (tokenLimitReached){
       hintText = 'Daily token limit reached';
    }

    final threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = (threadMetrics['totalCost'] ?? 0.0) * _exchangeRate;

    return Container(
      padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 10.0),
      margin: const EdgeInsets.only(bottom: 4.0), // Add some margin if keyboard might overlap
      decoration: BoxDecoration(
        color: theme.colorScheme.surface, // Use surface color from theme
        boxShadow: [
          BoxShadow(
              offset: const Offset(0, -1),
              blurRadius: 3,
              color: Colors.black.withOpacity(0.08)) // Softer shadow
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Token/Cost information - only show if relevant data exists
          if (_messageController.text.isNotEmpty ||
              (threadMetrics['totalTokens'] ?? 0) > 0 ||
              _lastPromptTokenCount > 0 ||
              _dailyTokenCount > 0 ||
              _lastApiResponseTokenCount > 0)
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 8.0, right: 8.0),
              child: DefaultTextStyle(
                 style: TextStyle(
                     color: theme.colorScheme.onSurface.withOpacity(0.7), // Theme-aware text color
                     fontSize: 10.5, // Slightly smaller for less emphasis
                 ),
                 child: Wrap( // Wrap allows items to flow to next line if space is tight
                    spacing: 8.0, // Horizontal spacing
                    runSpacing: 2.0, // Vertical spacing if wrapped
                    children: [
                       ValueListenableBuilder<TextEditingValue>(
                          valueListenable: _messageController,
                          builder: (context, value, child) {
                             int approxTokens = _calculateApproxTokens(value.text);
                             // Show current message stats only if there's text
                             if (approxTokens > 0) {
                               double cost = (approxTokens / 1000.0) * _inputCostPer1kTokens;
                               double costMkw = cost * _exchangeRate;
                               return Text("Msg (est): ~${approxTokens}t (\$${cost.toStringAsFixed(5)}/MKW${costMkw.toStringAsFixed(3)})");
                             }
                             return const SizedBox.shrink(); // Don't show if no text
                          },
                       ),
                       if (_lastPromptTokenCount > 0) Text("Last Prompt: ${_lastPromptTokenCount}t"),
                       if (_lastApiResponseTokenCount > 0) Text("Last Resp: ${_lastApiResponseTokenCount}t"),
                       if ((threadMetrics['totalTokens'] ?? 0) > 0) Text("Thread (est): ~${threadMetrics['totalTokens']}t (\$${threadMetrics['totalCost'].toStringAsFixed(5)}/MKW${threadCostMkw.toStringAsFixed(3)})"),
                       Text(
                          "Daily Tokens: ${_dailyTokenCount}/${_maxDailyTokens}",
                          style: TextStyle(
                             color: tokenLimitReached ? Colors.redAccent : theme.colorScheme.onSurface.withOpacity(0.7),
                             fontWeight: tokenLimitReached ? FontWeight.bold : FontWeight.normal,
                          ),
                       ),
                    ],
                 ),
              ),
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center, // Vertically align items in the row
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  onChanged: _handleManualInput,
                  onTap: () {
                     // If user taps input field while listening (and eligible to listen), stop listening
                     if (_isListening && mounted && canSendOrListen) {
                        _speechToText.stop();
                        // _isListening state will be updated by the status listener
                     }
                  },
                  maxLines: 1, // Single line input
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.send,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(
                      color: (!canSendOrListen && _processingStep == ProcessingStep.idle) // Hint color when disabled
                          ? theme.disabledColor.withOpacity(0.6)
                          : (_isListening // Hint color when listening
                             ? iconColor.withOpacity(0.9)
                             : theme.hintColor.withOpacity(0.6)), // Default hint color
                    ),
                    border: InputBorder.none, // No border for a cleaner look
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0), // Padding inside TextField
                  ),
                  style: TextStyle(
                    color: inputFieldEnabled // Text color based on enabled state
                        ? theme.colorScheme.onSurface
                        : theme.disabledColor,
                    fontSize: 15,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: (value) {
                    if (canSendOrListen && value.trim().isNotEmpty) {
                      _sendMessage(value);
                    }
                  },
                  enabled: inputFieldEnabled, // Enable/disable TextField
                  cursorColor: inputFieldEnabled ? theme.colorScheme.primary : Colors.transparent, // Cursor color
                ),
              ),
              // Microphone button
              IconButton(
                 icon: Icon(
                   _isListening ? Icons.mic : Icons.mic_none, // Change icon based on listening state
                   color: canSendOrListen ? iconColor : theme.disabledColor,
                 ),
                 tooltip: _isListening ? "Stop Listening" : (canSendOrListen ? "Start Listening" : "Listening disabled"),
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 onPressed: canSendOrListen ? _toggleListening : null, // Enable/disable based on state
              ),
              // Send button or Progress indicator
              (_processingStep != ProcessingStep.idle)
                  ? Padding( // Show progress indicator when processing
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        width: 24, height: 24,
                        child: CircularProgressIndicator( strokeWidth: 2.5, color: sendProgressColor, ),
                      ),
                    )
                  : IconButton( // Show send button when idle
                      icon: Icon(
                        Icons.send,
                        color: (_messageController.text.trim().isEmpty || !canSendOrListen)
                            ? theme.disabledColor // Disabled color if no text or cannot send
                            : sendProgressColor, // Active color
                      ),
                      tooltip: "Send Message",
                      visualDensity: VisualDensity.compact,
                      splashRadius: 20,
                      onPressed: (_messageController.text.trim().isEmpty || !canSendOrListen)
                          ? null // Disable if no text or cannot send
                          : () => _sendMessage(_messageController.text),
                    ),
              // Mute/Unmute TTS button
              IconButton(
                 icon: Icon(
                    _isMuted ? Icons.volume_off_outlined : Icons.volume_up_outlined,
                    color: (_processingStep == ProcessingStep.idle || tokenLimitReached) ? iconColor : theme.disabledColor, // Allow mute toggle unless actively processing mid-API call
                    size: 22,
                 ),
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 tooltip: _isMuted ? "Unmute TTS" : "Mute TTS",
                 // Allow toggling mute unless in the middle of a multi-step API call sequence
                 onPressed: (_processingStep == ProcessingStep.idle || _processingStep == ProcessingStep.processing && !_messages.any((m)=>m.isTyping)) // More refined condition
                 ? () {
                    if (mounted) {
                       setState(() => _isMuted = !_isMuted);
                       if (_isMuted) _flutterTts.stop(); // Stop TTS if muted
                    }
                 } : null,
              ),
            ],
          ),
        ],
      ),
    );
 }


 Map<String, dynamic> _calculateThreadMetrics() {
     int currentInputTokensApproximation = 0;
     int currentOutputTokensApproximation = 0;

     for (int i = 0; i < _messages.length; i++) {
       final msg = _messages[i];
       if (!msg.isTyping && msg.text.trim().isNotEmpty) {
         int approxMsgTokens = _calculateApproxTokens(msg.text);
         bool isInitialGreeting = i == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");

         if (msg.isUser) {
           currentInputTokensApproximation += approxMsgTokens;
         } else if (!isInitialGreeting) { // Do not count initial greeting in cost/output tokens
           currentOutputTokensApproximation += approxMsgTokens;
         }
       }
     }

     double inCost = (currentInputTokensApproximation / 1000.0) * _inputCostPer1kTokens;
     double outCost = (currentOutputTokensApproximation / 1000.0) * _outputCostPer1kTokens;

     return {
       'inputTokens': currentInputTokensApproximation,
       'outputTokens': currentOutputTokensApproximation,
       'totalTokens': currentInputTokensApproximation + currentOutputTokensApproximation,
       'inputCost': inCost,
       'outputCost': outCost,
       'totalCost': inCost + outCost,
     };
 }

 void _addOrUpdateTypingIndicator(String text) {
   if (!mounted) return;
   // Remove any existing typing indicator before adding a new one
   _messages.removeWhere((m) => m.isTyping);
   final newIndicator = ChatMessage(text: text, isUser: false, isTyping: true);
   _messages.add(newIndicator);
   setState(() {}); // Update UI
   _scrollToBottom(); // Scroll to show indicator
 }

 void _removeTypingIndicator() {
   if (!mounted) return;
   final initialLength = _messages.length;
   _messages.removeWhere((m) => m.isTyping);
   if (_messages.length < initialLength) { // Only call setState if an item was actually removed
     setState(() {});
   }
 }

 String _buildConversationHistory() {
    List<String> orderedHistorySegments = [];
    int currentHistoryTokens = 0;
    const int maxHistoryTokens = 1800; // Max tokens for history (Gemini 1.5 Flash can handle larger contexts)
    const int maxHistoryMessages = 12; // Consider last 6 pairs of messages

    List<ChatMessage> eligibleMessages = _messages
        .where((m) => !m.isTyping && m.text.trim().isNotEmpty)
        .toList();

    // Take the last 'maxHistoryMessages' from eligible messages
    if (eligibleMessages.length > maxHistoryMessages) {
      eligibleMessages = eligibleMessages.sublist(eligibleMessages.length - maxHistoryMessages);
    }

    // Iterate in chronological order (oldest to newest of the selected slice)
    for (final msg in eligibleMessages) {
      // Exclude the initial AI greeting from history
      bool isInitialGreeting = _messages.indexOf(msg) == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");

      // Exclude the current user message that is being processed
      bool isCurrentUserMessageBeingProcessed = msg.isUser &&
                                              _messages.lastWhereOrNull((m) => m.isUser && !m.isTyping) == msg &&
                                              _processingStep == ProcessingStep.processing;

      if (!isInitialGreeting && !isCurrentUserMessageBeingProcessed) {
        String role = msg.isUser ? 'user' : 'model';
        String formattedMsg = '$role: ${msg.text}\n'; // Newline separated
        int msgTokens = _calculateApproxTokens(formattedMsg);

        if (currentHistoryTokens + msgTokens <= maxHistoryTokens) {
          orderedHistorySegments.add(formattedMsg);
          currentHistoryTokens += msgTokens;
        } else {
          // If adding this message exceeds the token limit, stop adding more.
          // print("Conversation history truncated due to token limit (~$maxHistoryTokens tokens). Oldest messages dropped if limit hit.");
          break;
        }
      }
    }
    return orderedHistorySegments.join(); // Join with no extra separator, as newlines are included
 }


 void _addInitialGreeting() {
     String collegeName = widget.collegeData?['fullname']?.toString().isNotEmpty == true
         ? widget.collegeData!['fullname']!
         : 'the selected institution';

     String greeting = "Hi! I'm the AI Assistant for $collegeName. How can I help you today?";

     // Add language note if non-English is selected
     if (_outputLanguage != null && _outputLanguage != 'English') {
       greeting += "\n\nI'll respond in $_outputLanguage language.";
     }

     final initialMessage = ChatMessage(text: greeting, isUser: false);
     if(mounted){
       setState(() {
           _messages.add(initialMessage);
        });
       _speakText(greeting); // MODIFIED: Use helper for TTS
     }
 }

 void _startNewConversation() {
     if(_processingStep != ProcessingStep.idle && mounted) { // Should already be handled by button's onPressed
        ScaffoldMessenger.of(context).showSnackBar(
           const SnackBar(content: Text("Please wait for the current response to complete."), duration: Duration(seconds: 2))
        );
        return;
     }
     if (mounted) {
       if (_speechToText.isListening) {
           _speechToText.stop(); // Stop STT if active
       }
       _flutterTts.stop(); // Stop any ongoing TTS

       setState(() {
           _messages.clear();
           _lastPromptTokenCount = 0;
           _lastApiResponseTokenCount = 0;
           // _dailyTokenCount is not reset here, it's a daily limit.
           // _outputLanguage is not reset here, preserve language selection
           _processingStep = ProcessingStep.idle; // Ensure it's idle
           _isListening = false; // Ensure listening is off
       });
       _addInitialGreeting();
     }
 }

 void _showError(String errorMessageText) {
     final errorMsg = ChatMessage(text: "Error: $errorMessageText", isUser: false);
     if (mounted) {
       _removeTypingIndicator(); // Remove typing indicator if present
       setState(() {
            // Avoid adding duplicate error messages
            if (_messages.isEmpty || _messages.last.text != errorMsg.text) {
                _messages.add(errorMsg);
            }
            // Ensure processing step is idle after showing error
            if (_processingStep != ProcessingStep.idle) {
                _processingStep = ProcessingStep.idle;
            }
       });
       _scrollToBottom();
       _speakText(errorMessageText); // MODIFIED: Use helper for TTS
     }
 }

 void _scrollToBottom() {
     // Ensure this runs after the build phase
     WidgetsBinding.instance.addPostFrameCallback((_) {
         if (_scrollController.hasClients && mounted && _scrollController.position.hasContentDimensions) {
             _scrollController.animateTo(
                 _scrollController.position.maxScrollExtent,
                 duration: const Duration(milliseconds: 300), // Smooth scroll
                 curve: Curves.easeOut);
         }
     });
 }

 List<String> _getRelevantCollegeFields(String query, {int max = 7}) {
      final lowerQuery = query.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), '');
      final scores = <String, int>{};

      // Base scores for generally important fields
      scores['fullname'] = 100; // Always highly relevant
      scores['about'] = 50;
      scores['website'] = 50;
      scores['address'] = 40; scores['city'] = 40; scores['state'] = 40;
      scores['phone'] = 40; scores['email'] = 40;
      scores['mission'] = 30; scores['vision'] = 30; scores['motto'] = 25;
      scores['daysnhours'] = 25;
      scores['institutiontype'] = 20;
      scores['accreditation'] = 20;
      scores['founded'] = 15;
      scores['studentpopulation'] = 15;
      scores['campussetting'] = 10;
      scores['corevalues'] = 20;
      scores['goals'] = 20;
      scores['mandate'] = 15;
      scores['freewifi'] = 18;
      scores['objectives'] = 20;
      scores['aims'] = 20;
      scores['pledge'] = 10;
      scores['statementoffaith'] = 10;
      scores['religiousaffiliation'] = 10;
      scores['whychooseus'] = 25;
      scores['academicyearcalendar'] = 15;
      scores['highestqualificationoffered'] = 15;
      scores['postaladdress'] = 10;


      _collegeFieldKeywords.forEach((field, keywords) {
        int currentScore = scores[field] ?? 0; // Start with base score or 0
        for (var kw in keywords) {
          if (lowerQuery.contains(kw)) {
             currentScore += (kw.contains(' ') ? 4 : 2); // Higher score for multi-word keywords
             if (RegExp(r'\b' + RegExp.escape(kw) + r'\b').hasMatch(lowerQuery)) { // Exact word match
                currentScore += 6;
             }
          }
        }
        // Bonus if the field name itself (or part of it) is in the query
        if (RegExp(r'\b' + RegExp.escape(field.toLowerCase()) + r'\b', caseSensitive: false).hasMatch(lowerQuery)) {
           currentScore += 12;
        }
        scores[field] = currentScore;
      });

      // Filter out fields with zero score and sort by score descending
      final scoredFields = scores.keys.where((key) => scores[key]! > 0).toList();
      scoredFields.sort((a, b) => scores[b]!.compareTo(scores[a]!));

      // Take top 'max' fields
      List<String> finalFields = scoredFields.take(max).toList();

      // Ensure 'fullname' is always included if it has a score and list is not full, or bumps out lowest if full
      if (scores['fullname']! > 0 && !finalFields.contains('fullname')) {
          if (finalFields.length >= max && max > 0) finalFields.removeLast(); // Make space if full
          if (finalFields.length < max ) finalFields.insert(0, 'fullname'); // Add if space, at the top
      } else if (finalFields.contains('fullname')) { // If already present, ensure it's at the top
          finalFields.remove('fullname');
          finalFields.insert(0, 'fullname');
      }

      // Prioritize other essential fields if they made the cut
      List<String> priorityOrder = ['about', 'website', 'phone', 'email', 'address'];
      for (String priorityField in priorityOrder) {
        if (finalFields.contains(priorityField) && finalFields.indexOf(priorityField) > (finalFields.contains('fullname') ? 0 : -1) ) { // if not already fullname or first
            finalFields.remove(priorityField);
            // Insert after fullname if present, otherwise at start, then after previously inserted priority fields
            int insertAtIndex = 0;
            while(insertAtIndex < finalFields.length && (finalFields[insertAtIndex] == 'fullname' || priorityOrder.contains(finalFields[insertAtIndex]))) {
                insertAtIndex++;
            }
            finalFields.insert(insertAtIndex.clamp(0, finalFields.length), priorityField);
        }
      }

      return finalFields.toSet().toList(); // Return unique fields
 }

  int _calculateApproxTokens(String text) {
    if (text.isEmpty) return 0;
    // A common approximation: 1 token ~ 4 chars in English.
    // This is a rough estimate and can vary.
    return (text.trim().length / APPROX_CHARS_PER_TOKEN).ceil();
  }
}
