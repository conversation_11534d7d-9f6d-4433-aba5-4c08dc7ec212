import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';

class EventDetailPage extends StatelessWidget {
  final Map<String, dynamic> eventData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EventDetailPage({
    Key? key,
    required this.eventData,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Extract event details
    final String eventName = eventData['fullname'] ?? 'Unnamed Event';
    final String location = eventData['location'] ?? 'Not specified';
    final String building = eventData['building'] ?? '';
    final String room = eventData['room'] ?? '';
    final String startTime = eventData['starttime'] ?? 'Not specified';
    final String endTime = eventData['endtime'] ?? 'Not specified';
    final String about = eventData['about'] ?? 'No description available';
    final int capacity = eventData['capacity'] ?? 0;
    final String payment = eventData['payment'] ?? 'Not specified';
    final String ticketsLink = eventData['ticketslink'] ?? '';
    
    // Format date
    String formattedDate = 'Date not specified';
    if (eventData['startday'] != null && 
        eventData['startmonth'] != null && 
        eventData['startyear'] != null) {
      
      final startDate = DateTime(
        eventData['startyear'],
        eventData['startmonth'],
        eventData['startday'],
      );
      
      if (eventData['endday'] != null && 
          eventData['endmonth'] != null && 
          eventData['endyear'] != null) {
        
        final endDate = DateTime(
          eventData['endyear'],
          eventData['endmonth'],
          eventData['endday'],
        );
        
        if (startDate == endDate) {
          formattedDate = DateFormat('EEEE, MMMM d, yyyy').format(startDate);
        } else {
          formattedDate = '${DateFormat('MMM d, yyyy').format(startDate)} - ${DateFormat('MMM d, yyyy').format(endDate)}';
        }
      } else {
        formattedDate = DateFormat('EEEE, MMMM d, yyyy').format(startDate);
      }
    }
    
    // Event type
    List<String> eventTypes = [];
    if (eventData['admissionskeydate'] == true) eventTypes.add('Admissions Key Date');
    if (eventData['paymentkeydate'] == true) eventTypes.add('Payment Key Date');
    if (eventData['orientationevent'] == true) eventTypes.add('Orientation');
    if (eventData['graduationevent'] == true) eventTypes.add('Graduation Event');
    if (eventData['graduationkeydate'] == true) eventTypes.add('Graduation Key Date');
    if (eventData['alumnievent'] == true) eventTypes.add('Alumni Event');
    if (eventData['communityrentalevent'] == true) eventTypes.add('Community Rental');
    
    // Determine event icon
    IconData eventIcon = Icons.event;
    if (eventData['graduationevent'] == true || eventData['graduationkeydate'] == true) {
      eventIcon = Icons.school;
    } else if (eventData['alumnievent'] == true) {
      eventIcon = Icons.people;
    } else if (eventData['communityrentalevent'] == true) {
      eventIcon = Icons.business;
    } else if (eventData['admissionskeydate'] == true || 
              eventData['paymentkeydate'] == true ||
              eventData['orientationevent'] == true) {
      eventIcon = Icons.calendar_today;
    }
    
    // Contact information
    final String phone = eventData['phone'] ?? '';
    final String email = eventData['email'] ?? '';
    final String whatsapp = eventData['whatsapp'] ?? '';
    final String teamOrOrg = eventData['teamororg'] ?? '';
    final String department = eventData['department'] ?? '';
    
    // Location coordinates
    final double? latitude = eventData['latitude'];
    final double? longitude = eventData['longitude'];
    final bool hasLocation = latitude != null && longitude != null && 
                            latitude != 0.0 && longitude != 0.0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          eventName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: theme.colorScheme.primary.withOpacity(0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(eventIcon, size: 32, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          eventName,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (eventTypes.isNotEmpty)
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: eventTypes.map((type) => Chip(
                        label: Text(type),
                        backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                        labelStyle: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 12,
                        ),
                      )).toList(),
                    ),
                ],
              ),
            ),
            
            // Event details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Event Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.calendar_today, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  formattedDate,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.access_time, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Text(
                                '$startTime - $endTime',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.location_on, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  [location, building, room]
                                      .where((s) => s.isNotEmpty)
                                      .join(', '),
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (capacity > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                children: [
                                  Icon(Icons.people, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Capacity: $capacity people',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (payment.isNotEmpty && payment != 'Not specified')
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                children: [
                                  Icon(Icons.payment, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Payment: $payment',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (ticketsLink.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 16.0),
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.confirmation_number),
                                label: const Text('Get Tickets'),
                                onPressed: () => _launchUrl(ticketsLink),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: theme.colorScheme.onPrimary,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Organizer information
            if (teamOrOrg.isNotEmpty || department.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Organizer',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (teamOrOrg.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(
                                  children: [
                                    Icon(Icons.group, 
                                      color: theme.colorScheme.primary),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Team/Organization: $teamOrOrg',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            if (department.isNotEmpty)
                              Row(
                                children: [
                                  Icon(Icons.business, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Department: $department',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Contact information
            if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            if (phone.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.phone, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  phone,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('tel:$phone'),
                              ),
                            if (email.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.email, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  email,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('mailto:$email'),
                              ),
                            if (whatsapp.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.message, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  'WhatsApp: $whatsapp',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('https://wa.me/$whatsapp'),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // About/Description
            if (about.isNotEmpty && about != 'No description available')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About This Event',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Map
            if (hasLocation)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: FlutterMap(
                          options: MapOptions(
                            initialCenter: LatLng(latitude!, longitude!),
                            initialZoom: 15.0,
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.harmonizr.app',
                            ),
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: LatLng(latitude, longitude),
                                  width: 40,
                                  height: 40,
                                  child: Icon(
                                    Icons.location_on,
                                    color: theme.colorScheme.primary,
                                    size: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
