// book_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class BookDetailPage extends StatefulWidget {
  final Map<String, dynamic> book;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BookDetailPage({
    Key? key,
    required this.book,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<BookDetailPage> createState() => _BookDetailPageState();
}

class _BookDetailPageState extends State<BookDetailPage> {
  late RealtimeChannel _bookRealtimeChannel; // Realtime channel for book updates

  @override
  void initState() {
    super.initState();
    _setupBookRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _bookRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupBookRealtimeListener() {
    final booksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
    _bookRealtimeChannel = Supabase.instance.client
        .channel('book_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: booksTableName,
          callback: (payload) {
            // Check if this change is for the current book
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.book['id']) {
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedBookData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the book is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedBookData() async {
    try {
      final booksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
      final response = await Supabase.instance.client
          .from(booksTableName)
          .select('*')
          .eq('id', widget.book['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedBook = Map.from(response);
        // Update the widget.book with the new data
        setState(() {
          widget.book.clear(); // Clear old data
          widget.book.addAll(updatedBook); // Add updated data
          print("Book data updated in detail page for ${widget.book['fullname']}");
          _updateBooksCache(updatedBook); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated book data: $e');
    }
  }

  Future<void> _updateBooksCache(Map<String, dynamic> updatedBook) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'books_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedBooks = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific book in the cache
        bool found = false;
        for (var book in cachedBooks) {
          if (book['id'] == updatedBook['id']) {
            updatedCache.add(updatedBook);
            found = true;
          } else {
            updatedCache.add(Map<String, dynamic>.from(book));
          }
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
      }
    } catch (e) {
      print('Error updating books cache: $e');
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = widget.book['fullname'] as String? ?? 'Book';
    final String year = widget.book['year']?.toString() ?? 'N/A';
    final String author = widget.book['author'] as String? ?? '';
    final String author2 = widget.book['author2'] as String? ?? '';
    final String author3 = widget.book['author3'] as String? ?? '';
    final bool facultyOrStaffBook = widget.book['facultyorstaffbook'] as bool? ?? false;
    final bool studentBook = widget.book['studentbook'] as bool? ?? false;
    final String publisher = widget.book['publisher'] as String? ?? '';
    final String about = widget.book['about'] as String? ?? '';
    final String link = widget.book['link'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Book Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Year: $year',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Author(s):',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (author.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (author2.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author2,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (author3.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author3,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      if (publisher.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Publisher: $publisher',
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          if (facultyOrStaffBook)
                            Chip(
                              label: const Text('Faculty/Staff Book'),
                              backgroundColor: theme.colorScheme.primaryContainer,
                            ),
                          if (studentBook)
                            Chip(
                              label: const Text('Student Book'),
                              backgroundColor: theme.colorScheme.secondaryContainer,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (about.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'About',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (link.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Link',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.primary,
                                  decoration: TextDecoration.underline,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _copyToClipboard(link),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _launchURL(link),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
