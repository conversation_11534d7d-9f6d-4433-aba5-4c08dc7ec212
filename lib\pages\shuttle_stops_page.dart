import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

class ShuttleStopsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedShuttleStops;
  final bool isFromDetailPage;

  const ShuttleStopsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedShuttleStops,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ShuttleStopsPageState createState() => _ShuttleStopsPageState();
}

class _ShuttleStopsPageState extends State<ShuttleStopsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('shuttle_stops_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _shuttleStops = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  bool _mapViewEnabled = false;

  @override
  void initState() {
    super.initState();
    print("ShuttleStopsPage initState called");
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() {
    if (widget.preloadedShuttleStops != null &&
        widget.preloadedShuttleStops!.isNotEmpty) {
      setState(() {
        _shuttleStops = List.from(widget.preloadedShuttleStops!);
        _isLoading = false;
      });
    } else {
      _loadShuttleStopsFromDatabase();
    }
  }

  void _setupRealtime() {
    final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
    _realtimeChannel = Supabase.instance.client
        .channel('shuttle_stops_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: shuttleStopsTableName,
      callback: (payload) async {
        print("Realtime update received for shuttle stops: ${payload.eventType}");
        _loadShuttleStopsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadShuttleStopsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _shuttleStops = [];
    });

    try {
      final shuttleStopsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_shuttlestops';
      
      // Check if the table exists
      final tableExists = await _checkIfTableExists(shuttleStopsTableName);
      
      if (tableExists) {
        final response = await Supabase.instance.client
            .from(shuttleStopsTableName)
            .select('*')
            .order('fullname', ascending: true);

        if (_isDisposed) return;

        setState(() {
          _shuttleStops = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      } else {
        // If the table doesn't exist, try to get stops from campusshuttle table
        final campusShuttleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
        final response = await Supabase.instance.client
            .from(campusShuttleTableName)
            .select('*')
            .not('latitude', 'is', null)
            .not('longitude', 'is', null)
            .order('fullname', ascending: true);

        if (_isDisposed) return;

        setState(() {
          _shuttleStops = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading shuttle stops: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading shuttle stops: $e')),
      );
    }
  }

  Future<bool> _checkIfTableExists(String tableName) async {
    try {
      // Try to get a single row from the table
      await Supabase.instance.client
          .from(tableName)
          .select('id')
          .limit(1);
      return true;
    } catch (e) {
      print("Table $tableName might not exist: $e");
      return false;
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Bus/Shuttle Stops',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _mapViewEnabled ? Icons.list : Icons.map,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _mapViewEnabled = !_mapViewEnabled;
              });
            },
            tooltip: _mapViewEnabled ? 'List View' : 'Map View',
          ),
        ],
      ),
      body: VisibilityDetector(
        key: const Key('shuttle_stops_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _shuttleStops.isEmpty && !_isLoading) {
            _loadShuttleStopsFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadShuttleStopsFromDatabase,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _shuttleStops.isEmpty
                  ? Center(
                      child: Text(
                        'No shuttle stops found',
                        style: TextStyle(color: theme.colorScheme.onSurface),
                      ),
                    )
                  : _mapViewEnabled
                      ? _buildMapView(theme, currentIsDarkMode)
                      : _buildListView(theme, currentIsDarkMode),
        ),
      ),
    );
  }

  Widget _buildListView(ThemeData theme, bool isDarkMode) {
    return ListView.builder(
      key: _listKey,
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _shuttleStops.length,
      itemBuilder: (context, index) {
        return _buildShuttleStopCard(
          _shuttleStops[index],
          theme,
          isDarkMode,
        );
      },
    );
  }

  Widget _buildMapView(ThemeData theme, bool isDarkMode) {
    // In a real implementation, this would use a map widget like Google Maps or MapBox
    // For now, we'll just show a message and a list of stops with navigation buttons
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Map view is not available in this version. Please use the list view or navigate to stops using the buttons below.',
            style: TextStyle(color: theme.colorScheme.onSurface),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _shuttleStops.length,
            itemBuilder: (context, index) {
              final stop = _shuttleStops[index];
              final String fullname = stop['fullname'] ?? 'Unknown';
              final dynamic latitude = stop['latitude'];
              final dynamic longitude = stop['longitude'];
              
              // Check if navigation is available
              final bool isNavigationAvailable = latitude != null && longitude != null;
              
              if (!isNavigationAvailable) {
                return const SizedBox.shrink();
              }
              
              return ListTile(
                title: Text(
                  fullname,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                trailing: IconButton(
                  icon: Icon(
                    Icons.navigation,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () => _launchNavigation(latitude, longitude),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildShuttleStopCard(
    Map<String, dynamic> stop,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = stop['fullname'] ?? 'Unknown';
    final String about = stop['about'] ?? '';
    final dynamic latitude = stop['latitude'];
    final dynamic longitude = stop['longitude'];
    
    // Check if navigation is available
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: isDarkMode 
                  ? Colors.white.withOpacity(0.1) 
                  : Colors.black.withOpacity(0.1),
              child: Icon(
                Icons.place,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fullname,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (about.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        about,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            if (isNavigationAvailable)
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: theme.colorScheme.primary,
                ),
                onPressed: () => _launchNavigation(latitude, longitude),
                tooltip: 'Navigate',
              ),
          ],
        ),
      ),
    );
  }
}
