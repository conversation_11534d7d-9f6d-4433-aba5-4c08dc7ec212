import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'login_page.dart'; // Assuming you have a login_page.dart
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:intl/intl.dart'; // Import intl for date formatting

class ListPage extends StatelessWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ListPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Navigate to the specific app page
          if (title == 'Todo List') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => TodoApp(toggleTheme: toggleTheme, isDarkMode: isDarkMode)),
            );
          } else if (title == 'Shopping List') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => ShoppingApp(toggleTheme: toggleTheme, isDarkMode: isDarkMode)),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Lists',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Todo List', Icons.list, theme),
                _buildGridItem(context, 'Shopping List', Icons.shopping_cart_outlined, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    // Action for Home button
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    // Use the local isDarkMode variable here
                    isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          // And pass the local isDarkMode and toggleTheme
                          isDarkMode: isDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ------ Individual Apps with Local Storage ------

// Abstract class for reusable local storage logic
abstract class LocalStorageApp<T> extends StatefulWidget {
  const LocalStorageApp({Key? key}) : super(key: key);

  String get storageKey;
  Future<List<T>> loadData();
  Future<void> saveData(List<T> data);
  Widget buildBody(BuildContext context, List<T> data, Function(T) addItem);

  @override
  State<StatefulWidget> createState(); // Add this line
}

abstract class _LocalStorageAppState<T, W extends LocalStorageApp<T>> extends State<W> {
  List<T> data = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    data = await widget.loadData();
    setState(() {});
  }

  Future<void> _saveData() async {
    await widget.saveData(data);
  }

  void addItem(T item) {
    setState(() {
      data.add(item);
    });
    _saveData();
  }

  void deleteItem(int index) {
    setState(() {
      data.removeAt(index);
    });
    _saveData();
  }

  void updateItem(int index, T newValue) {
    setState(() {
      data[index] = newValue;
    });
    _saveData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.storageKey),
      ),
      body: buildBody(context, data, addItem),
    );
  }

  Widget buildBody(BuildContext context, List<T> data, Function(T) addItem);
}

// ------ Todo App ------

class TodoItem {
  String text;
  DateTime createdAt;
  bool isDone;

  TodoItem({required this.text, required this.createdAt, this.isDone = false});

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'createdAt': createdAt.toIso8601String(),
      'isDone': isDone,
    };
  }

  factory TodoItem.fromJson(Map<String, dynamic> json) {
    return TodoItem(
      text: json['text'],
      createdAt: DateTime.parse(json['createdAt']),
      isDone: json['isDone'] ?? false,
    );
  }
}

class TodoApp extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const TodoApp({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<TodoApp> createState() => _TodoAppState();
}

class _TodoAppState extends State<TodoApp> {
  List<TodoItem> _todos = [];
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTodos();
  }

  Future<void> _loadTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedTodos = prefs.getStringList('todos');
    if (encodedTodos != null) {
      setState(() {
        _todos = encodedTodos.map((e) => TodoItem.fromJson(jsonDecode(e))).toList();
      });
    }
  }

  Future<void> _saveTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedTodos = _todos.map((todo) => jsonEncode(todo.toJson())).toList();
    await prefs.setStringList('todos', encodedTodos);
  }

  void _addTodo(String todo) {
    if (todo.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a todo item.')),
      );
      return;
    }
    setState(() {
      _todos.add(TodoItem(text: todo, createdAt: DateTime.now()));
      _controller.clear();
      _saveTodos();
    });
  }

  void _deleteTodo(int index) {
    setState(() {
      _todos.removeAt(index);
      _saveTodos();
    });
  }

  void _updateTodo(int index, String newValue, bool isDone) {
    setState(() {
      _todos[index] = TodoItem(text: newValue, createdAt: _todos[index].createdAt, isDone: isDone);
      _saveTodos();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Todo List',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(isDarkMode ? 0.1 : 0.2),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Enter a todo',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            _addTodo(value);
                          }
                        },
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.add, color: isDarkMode ? Colors.white : Colors.black),
                      onPressed: () {
                        _addTodo(_controller.text);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.separated(
              itemCount: _todos.length,
              separatorBuilder: (context, index) => Divider(
                height: 0.5,
                thickness: 0.5,
                color: isDarkMode ? Colors.grey.shade600 : const Color(0xFFC0C0C0), // Silver color
              ),
              itemBuilder: (context, index) {
                final todoItem = _todos[index];
                return Dismissible(
                  key: Key(todoItem.text),
                  background: Container(color: Colors.red),
                  onDismissed: (direction) {
                    _deleteTodo(index);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('${todoItem.text} deleted')),
                    );
                  },
                  child: Container(
                    color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.list),
                      title: Text(
                        todoItem.text,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          decoration: todoItem.isDone ? TextDecoration.lineThrough : null,
                        ),
                      ),
                      subtitle: Text(
                        'Created on: ${DateFormat('yyyy-MM-dd – kk:mm').format(todoItem.createdAt)}',
                        style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(Icons.edit, color: isDarkMode ? Colors.white : Colors.black),
                            onPressed: () => _showEditTodoDialog(context, index, todoItem),
                          ),
                          IconButton(
                            icon: Icon(Icons.delete, color: theme.colorScheme.onSurface),
                            onPressed: () => _deleteTodo(index),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showEditTodoDialog(BuildContext context, int index, TodoItem oldTodo) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final editController = TextEditingController(text: oldTodo.text);
    bool isDone = oldTodo.isDone; // Keep track of the done state locally

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder( // Use StatefulBuilder to manage the checkbox state
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              backgroundColor: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              title: Text('Edit Todo', style: TextStyle(color: theme.colorScheme.onSurface)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: editController,
                    autofocus: true,
                    style: TextStyle(
                        color: theme.colorScheme.onSurface,
                        decoration: isDone ? TextDecoration.lineThrough : null),
                    decoration: InputDecoration(
                      labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                      enabledBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.grey)),
                      focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.blue)),
                    ),
                    onChanged: (newValue) {
                      // Optionally update the strike-through in the dialog as well
                      setState(() {});
                    },
                    onSubmitted: (newValue) {
                      if (newValue.isNotEmpty) {
                        _updateTodo(index, newValue, isDone);
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: isDone,
                        onChanged: (bool? value) {
                          setState(() {
                            isDone = value ?? false;
                          });
                        },
                      ),
                      Text('Done', style: TextStyle(color: theme.colorScheme.onSurface)),
                    ],
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text('Save', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
                  onPressed: () {
                    final newValue = editController.text;
                    if (newValue.isNotEmpty) {
                      _updateTodo(index, newValue, isDone);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}

// ------ Shopping App ------

class ShoppingItem {
  String name;
  double price;
  int quantity;
  String? imagePath;

  ShoppingItem({required this.name, required this.price, required this.quantity, this.imagePath});

  Map<String, dynamic> toJson() => {
        'name': name,
        'price': price,
        'quantity': quantity,
        'imagePath': imagePath,
      };

  factory ShoppingItem.fromJson(Map<String, dynamic> json) => ShoppingItem(
        name: json['name'],
        price: json['price'],
        quantity: json['quantity'] ?? 1, // Default to 1 if not present
        imagePath: json['imagePath'],
      );

  @override
  String toString() {
    return '$name - Quantity: $quantity - ${(price * quantity).toStringAsFixed(2)}';
  }
}

class ShoppingApp extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const ShoppingApp({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<ShoppingApp> createState() => _ShoppingAppState();
}

class _ShoppingAppState extends State<ShoppingApp> {
  List<ShoppingItem> _shoppingItems = [];
  final TextEditingController _itemController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController(text: '1');
  double _budget = 0.0; // You can implement budget functionality if needed
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadShoppingItems();
  }

  Future<void> _loadShoppingItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedItems = prefs.getStringList('shopping_items');
    if (encodedItems != null) {
      setState(() {
        _shoppingItems = encodedItems.map((item) => ShoppingItem.fromJson(jsonDecode(item))).toList();
      });
    }
  }

  Future<void> _saveShoppingItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedItems = _shoppingItems.map((item) => jsonEncode(item.toJson())).toList();
    await prefs.setStringList('shopping_items', encodedItems);
  }

  Future<void> _pickImage(ShoppingItem item) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      final index = _shoppingItems.indexOf(item);
      if (index != -1) {
        setState(() {
          _shoppingItems[index] = ShoppingItem(
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            imagePath: image.path,
          );
        });
        _saveShoppingItems();
      }
    }
  }

  void _addShoppingItem(String name, double price, int quantity, String? imagePath) {
    setState(() {
      _shoppingItems.add(ShoppingItem(name: name, price: price, quantity: quantity, imagePath: imagePath));
      _itemController.clear();
      _priceController.clear();
      _quantityController.text = '1';
      _saveShoppingItems();
    });
  }

  void _deleteShoppingItem(int index) {
    setState(() {
      _shoppingItems.removeAt(index);
      _saveShoppingItems();
    });
  }

  void _updateShoppingItem(int index, String newName, double newPrice, int newQuantity, String? newImagePath) {
    setState(() {
      _shoppingItems[index] = ShoppingItem(name: newName, price: newPrice, quantity: newQuantity, imagePath: newImagePath);
      _saveShoppingItems();
    });
  }

  double _calculateTotal() {
    return _shoppingItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Shopping List',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(isDarkMode ? 0.1 : 0.2),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _itemController,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Enter item',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 80,
                      child: TextField(
                        controller: _quantityController,
                        keyboardType: TextInputType.number,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Qty',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 100,
                      child: TextField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Price',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                        onSubmitted: (_) => _addItemAction(context),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.add_shopping_cart, color: isDarkMode ? Colors.white : Colors.black),
                      onPressed: () => _addItemAction(context),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.separated(
              itemCount: _shoppingItems.length,
              separatorBuilder: (context, index) => Divider(
                height: 0.5,
                thickness: 0.5,
                color: isDarkMode ? Colors.grey.shade600 : const Color(0xFFC0C0C0),
              ),
              itemBuilder: (context, index) {
                final item = _shoppingItems[index];
                return Dismissible(
                  key: Key(item.name),
                  background: Container(color: Colors.red),
                  onDismissed: (direction) {
                    _deleteShoppingItem(index);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('${item.name} deleted')),
                    );
                  },
                  child: Container(
                    color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                    child: ListTile(
                      leading: GestureDetector(
                        onTap: () => _pickImage(item),
                        child: item.imagePath != null
                            ? Image.file(File(item.imagePath!), width: 40, height: 40)
                            : const Icon(Icons.camera_alt),
                      ),
                      title: Text(
                        item.name,
                        style: TextStyle(color: theme.colorScheme.onSurface, fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Price: ${item.price.toStringAsFixed(2)}',
                            style: TextStyle(color: theme.hintColor),
                          ),
                          Text(
                            'Quantity: ${item.quantity}',
                            style: TextStyle(color: theme.hintColor),
                          ),
                          Text(
                            'Total: ${(item.price * item.quantity).toStringAsFixed(2)}',
                            style: TextStyle(color: theme.hintColor, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(Icons.edit, color: isDarkMode ? Colors.white : Colors.black),
                            onPressed: () => _showEditShoppingItemDialog(context, index, item),
                          ),
                          IconButton(
                            icon: Icon(Icons.delete, color: theme.colorScheme.onSurface),
                            onPressed: () => _deleteShoppingItem(index),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Total:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
                  Text(
                    _calculateTotal().toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addItemAction(BuildContext context) {
    final itemName = _itemController.text.trim();
    final itemPrice = double.tryParse(_priceController.text.trim()) ?? 0.0;
    final itemQuantity = int.tryParse(_quantityController.text.trim()) ?? 0;
    if (itemName.isNotEmpty && itemPrice > 0 && itemQuantity > 0) {
      _addShoppingItem(itemName, itemPrice, itemQuantity, null);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter item, valid price, and quantity.')),
      );
    }
  }

  void _showEditShoppingItemDialog(BuildContext context, int index, ShoppingItem oldItem) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final nameController = TextEditingController(text: oldItem.name);
    final priceController = TextEditingController(text: oldItem.price.toString());
    final quantityController = TextEditingController(text: oldItem.quantity.toString());

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            String? _imagePath = oldItem.imagePath;
            return AlertDialog(
              backgroundColor: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              title: Text('Edit Shopping Item', style: TextStyle(color: theme.colorScheme.onSurface)),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        final XFile? image = await _picker.pickImage(source: ImageSource.camera);
                        if (image != null) {
                          setState(() {
                            _imagePath = image.path;
                          });
                        }
                      },
                      child: _imagePath != null
                          ? Image.file(File(_imagePath!), width: 80, height: 80)
                          : const Icon(Icons.camera_alt, size: 60),
                    ),
                    TextField(
                      controller: nameController,
                      autofocus: true,
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Item Name',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.grey)),
                          focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.blue))),
                    ),
                    TextField(
                      controller: quantityController,
                      keyboardType: TextInputType.number,
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Quantity',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.grey)),
                          focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.blue))),
                      onChanged: (value) {
                        setState(() {}); // Trigger rebuild for instant update
                      },
                    ),
                    TextField(
                      controller: priceController,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Price',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.grey)),
                          focusedBorder: const UnderlineInputBorder(borderSide: BorderSide(color: Colors.blue))),
                      onChanged: (value) {
                        setState(() {}); // Trigger rebuild for instant update
                      },
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text('Save', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
                  onPressed: () {
                    final newName = nameController.text.trim();
                    final newPrice = double.tryParse(priceController.text.trim()) ?? 0.0;
                    final newQuantity = int.tryParse(quantityController.text.trim()) ?? 0;
                    if (newName.isNotEmpty && newPrice > 0 && newQuantity > 0) {
                      _updateShoppingItem(index, newName, newPrice, newQuantity, _imagePath);
                      Navigator.of(context).pop();
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Please enter item, valid price, and quantity.')),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}