import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_dress_detail_page.dart';
import 'login_page.dart';

class AcademicDressPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicDress;
  final bool isFromDetailPage;

  const AcademicDressPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicDress,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicDressPageState createState() => _AcademicDressPageState();
}

class _AcademicDressPageState extends State<AcademicDressPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_dress_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicDress = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All Categories';
  List<String> _categories = ['All Categories'];

  @override
  void initState() {
    super.initState();
    print("AcademicDressPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedAcademicDress != null &&
        widget.preloadedAcademicDress!.isNotEmpty) {
      setState(() {
        _academicDress = List.from(widget.preloadedAcademicDress!);
        _isLoading = false;
        _extractCategories();
      });
    } else {
      _loadAcademicDressFromDatabase();
    }
  }

  void _extractCategories() {
    Set<String> categoriesSet = {'All Categories'};

    for (var dress in _academicDress) {
      if (dress['category'] != null && dress['category'].toString().isNotEmpty) {
        categoriesSet.add(dress['category']);
      }
    }

    setState(() {
      _categories = categoriesSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final academicDressTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicdress';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_dress_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicDressTableName,
      callback: (payload) async {
        print("Realtime update received for academic dress: ${payload.eventType}");
        _loadAcademicDressFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadAcademicDressFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _academicDress = [];
    });

    await _loadMoreAcademicDress();
  }

  Future<void> _loadMoreAcademicDress() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final academicDressTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicdress';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply category filter
      if (_selectedCategory != 'All Categories') {
        conditions.add("category.eq.${_selectedCategory}");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(academicDressTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _academicDress.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractCategories();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading academic dress: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading academic dress: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreAcademicDress();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadAcademicDressFromDatabase();
  }

  void _filterByCategory(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _loadAcademicDressFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Dress',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search academic dress...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          if (_categories.length > 1)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                value: _selectedCategory,
                items: _categories.map((String category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(
                      category,
                      style: TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    _filterByCategory(newValue);
                  }
                },
              ),
            ),

          const SizedBox(height: 16),

          // Academic dress list
          Expanded(
            child: VisibilityDetector(
              key: const Key('academic_dress_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _academicDress.isEmpty && !_isLoading) {
                  _loadAcademicDressFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadAcademicDressFromDatabase,
                child: _academicDress.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No academic dress found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _academicDress.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _academicDress.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildAcademicDressCard(
                            _academicDress[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildAcademicDressCard(
    Map<String, dynamic> dress,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = dress['fullname'] ?? 'Unknown';
    final String category = dress['category'] ?? '';
    final String style = dress['style'] ?? '';
    final String color = dress['color'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AcademicDressDetailPage(
                dress: dress,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school_outlined,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (category.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Category: $category',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (style.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Style: $style',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (color.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Color: $color',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
