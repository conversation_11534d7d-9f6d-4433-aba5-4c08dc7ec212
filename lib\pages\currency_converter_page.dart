// currency_converter_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CurrencyConverterPage extends StatefulWidget {
  const CurrencyConverterPage({Key? key}) : super(key: key);

  @override
  _CurrencyConverterPageState createState() => _CurrencyConverterPageState();
}

class _CurrencyConverterPageState extends State<CurrencyConverterPage> {
  final Map<String, double> _currencyRates = {
    'MWK': 1.0, // Malawi Kwacha - Base currency, default rate (relative to itself)
    'AED': 0.0, // UAE Dirham - Default placeholder
    'CNY': 0.0, // Chinese Yuan - Default placeholder
    'INR': 0.0, // Indian Rupee - Default placeholder
    'USD': 0.0, // USD - Default placeholder
    'EUR': 0.0, // EUR - Default placeholder
    'GBP': 0.0, // GBP - Default placeholder
    'JPY': 0.0, // JPY - Default placeholder
    'CAD': 0.0, // CAD - Default placeholder
    'AUD': 0.0, // AUD - Default placeholder
  };

  List<String> _currencyCodes = ['MWK', 'AED', 'CNY', 'INR', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];

  String _fromCurrency = 'MWK'; // Default From Currency is Malawi Kwacha
  String _toCurrency = 'USD'; // Default To Currency is USD
  double _amount = 0.0;
  double _result = 0.0;
  double _manualRate = 0.0; //  _manualRate now represents MWK value of 1 _toCurrency (e.g., MWK per 1 USD)
  TextEditingController _rateController = TextEditingController(); // Controller for rate input

  @override
  void dispose() {
    _rateController.dispose(); // Dispose the controller when the widget is disposed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Currency Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Amount to Convert',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _amount = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _fromCurrency,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _currencyCodes.map((String currency) {
                          return DropdownMenuItem<String>(
                            value: currency,
                            child: Text(currency, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromCurrency = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<String>(
                        value: _toCurrency,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _currencyCodes.map((String currency) {
                          return DropdownMenuItem<String>(
                            value: currency,
                            child: Text(currency, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toCurrency = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    controller: _rateController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Exchange Rate (MWK to $_toCurrency): Enter MWK value of 1 $_toCurrency', // Updated Label
                      hintText: 'Enter MWK per 1 $_toCurrency', // Updated Hint
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _manualRate = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertCurrency();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_result $_toCurrency',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                  if (_manualRate > 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        'Rate Used: 1 $_toCurrency = $_manualRate MWK', // Updated Rate Display
                        style: TextStyle(fontSize: 14, color: theme.colorScheme.onSurfaceVariant),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertCurrency() {
    if (_manualRate > 0) {
      setState(() {
        _result = _amount / _manualRate; // Corrected Calculation: Division
      });
    } else {
      setState(() {
        _result = 0.0;
      });
      _showErrorSnackBar('Please enter a valid exchange rate.');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}