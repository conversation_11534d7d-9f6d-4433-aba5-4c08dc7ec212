// tertiary_virtual_tour_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert'; // For JSON parsing
import 'login_page.dart';

// Conditionally import the web implementation if dart:html is available.
// On non-web platforms the stub will be imported.
import 'web_panorama_stub.dart'
    if (dart.library.html) 'web_panorama.dart';

class TertiaryVirtualTourPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryVirtualTourPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryVirtualTourPage> createState() =>
      _TertiaryVirtualTourPageState();
}

class _TertiaryVirtualTourPageState extends State<TertiaryVirtualTourPage> {
  int _currentSceneIndex = 0;
  late FlutterTts flutterTts;
  bool isPlaying = false;

  // Cache panorama widgets to prevent unnecessary reloads.
  final Map<String, Widget> _panoramaCache = {};

  // Example latitude and longitude, replace with actual values
  final double _latitude = 34.0522; // Example: Los Angeles latitude
  final double _longitude = -118.2437; // Example: Los Angeles longitude

  final List<String> _scenes = [
    'scene1',
    'scene2',
    'scene3',
    'scene4',
    'scene5', // Example 180 scene
  ];

  final Map<String, String> _sceneConfig = {
    'scene1': jsonEncode({
      "title": "Campus Entrance",
      "author": "Your College",
      "autoLoad": true,
      "type": "equirectangular",
      "panorama": "https://i.imgur.com/ggt9dUI.jpeg",
      "haov": 360,  // Full 360° horizontal field of view
      "vaov": 130, // Keeping vertical field of view to show black areas
      "pitch": 0, // Start with a level view
      "minPitch": 0,
      "maxPitch": 0,
      "panoramaType": "360",
      "hotSpots": [],
    }),
    'scene2': jsonEncode({
      "title": "Library Plaza (360)",
      "author": "Your College",
      "autoLoad": true,
      "type": "equirectangular",
      "panorama": "https://i.imgur.com/XDJXvwJ.jpeg",
      "haov": 360,
      "vaov": 130,
      "pitch": 0,
      "minPitch": 0,
      "maxPitch": 0,
      "panoramaType": "360",
      "hotSpots": [],
    }),
    'scene3': jsonEncode({
      "title": "Science Building (360)",
      "author": "Your College",
      "autoLoad": true,
      "type": "equirectangular",
      "panorama": "https://i.imgur.com/CnCMZLm.jpeg",
      "haov": 360,
      "vaov": 130,
      "pitch": 0,
      "minPitch": 0,
      "maxPitch": 0,
      "panoramaType": "360",
      "hotSpots": [],
    }),
    'scene4': jsonEncode({
      "title": "Student Union (360)",
      "author": "Your College",
      "autoLoad": true,
      "type": "equirectangular",
      "panorama": "https://i.imgur.com/lAHTGib.jpeg",
      "haov": 360,
      "vaov": 130,
      "pitch": 0,
      "minPitch": 0,
      "maxPitch": 0,
      "panoramaType": "360",
      "hotSpots": [],
    }),
    'scene5': jsonEncode({
      "title": "Lecture Hall (180)",
      "author": "Your College",
      "autoLoad": true,
      "type": "equirectangular",
      "panorama":
          "https://cdn.create.vista.com/api/media/small/189335348/stock-photo-cantacuzino-castle-carpathian-mountains-caraiman-peak-ultrawide-panoramic-view-sunny",
      "pitch": 0,
      "yaw": 0,
      "hfov": 90,
      "panoramaType": "360",
      "hotSpots": [],
    }),
  };

  final Map<String, String> _sceneDescriptions = {
    'scene1':
        "Welcome to the grand Campus Entrance of Your College! (360 Panorama)",
    'scene2':
        "Welcome to the serene Library Plaza, a central and vibrant hub for students at Your College. (360 Panorama)",
    'scene3':
        "Step inside the impressive Science Building, a cornerstone of innovation and discovery at Your College. (360 Panorama)",
    'scene4':
        "Welcome to the bustling Student Union, the vibrant heart of campus life at Your College. (360 Panorama)",
    'scene5':
        "This is a 180-degree panorama example, showing a lecture hall. Notice how the view is limited to 180 degrees.",
  };

  String get _currentSceneName => _scenes[_currentSceneIndex];
  String get _currentSceneDescription =>
      _sceneDescriptions[_currentSceneName] ?? 'Description not available';

  /// Extracts the scene title from its configuration.
  String _getSceneTitle(String sceneName) {
    final config = _sceneConfig[sceneName];
    if (config != null) {
      try {
        final decoded = jsonDecode(config);
        return decoded['title'] ?? sceneName;
      } catch (e) {
        return sceneName;
      }
    }
    return sceneName;
  }

  /// Builds the full HTML content for the Pannellum viewer.
  /// The "author" property is removed so it is not displayed.
  String _buildHtmlContent(String pannellumConfigJson) {
    var config = jsonDecode(pannellumConfigJson);
    config.remove("author");

    // Check for panoramaType and adjust config for 180 panoramas
    String panoramaType = config['panoramaType'] ?? '360';
    if (panoramaType == '180') {
      config['hfov'] = 180;
      config['minYaw'] = -90;
      config['maxYaw'] = 90;
      config.remove('panoramaType');
    } else {
      config.remove('panoramaType');
    }

    String modifiedConfigJson = jsonEncode(config);
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pannellum Virtual Tour</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/pannellum@2.3.2/build/pannellum.css"/>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/pannellum@2.3.2/build/pannellum.js"></script>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100%;
        }
        #panorama-container {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
<div id="panorama-container"></div>
<script>
    pannellum.viewer('panorama-container', $modifiedConfigJson);
</script>
</body>
</html>
    ''';
  }

  /// Auto-plays the audio for the current scene.
  Future<void> _startSpeaking() async {
    await flutterTts.speak(_currentSceneDescription);
  }

  /// Pauses the audio.
  Future<void> _pauseSpeaking() async {
    await flutterTts.pause();
  }

  /// Stops the audio.
  Future<void> _stopSpeaking() async {
    await flutterTts.stop();
  }

  /// Returns a cached panorama widget for a given scene.
  Widget _getPanoramaWidget(String sceneName) {
    if (_panoramaCache.containsKey(sceneName)) {
      return _panoramaCache[sceneName]!;
    }
    String htmlContent = _buildHtmlContent(_sceneConfig[sceneName]!);
    Widget widget;
    if (kIsWeb) {
      widget = Container(
        key: ValueKey(sceneName),
        child: buildWebPanorama(htmlContent),
      );
    } else {
      final controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadHtmlString(htmlContent);
      widget = WebViewWidget(
        key: ValueKey(sceneName),
        controller: controller,
      );
    }
    _panoramaCache[sceneName] = widget;
    return widget;
  }

  /// Builds the panorama view using an Offstage approach.
  /// The current scene is visible while preloaded scenes remain offstage.
  Widget _buildPanorama() {
    // Ensure the current scene is cached.
    _getPanoramaWidget(_currentSceneName);

    // Preload the next scene in background.
    int nextIndex = (_currentSceneIndex + 1) % _scenes.length;
    String nextScene = _scenes[nextIndex];
    _getPanoramaWidget(nextScene);

    // Build a list of all cached panoramas wrapped in Offstage.
    List<Widget> panoramaWidgets = [];
    _panoramaCache.forEach((scene, widget) {
      bool isActive = (scene == _currentSceneName);
      panoramaWidgets.add(Offstage(
        offstage: !isActive,
        child: widget,
      ));
    });

    return Stack(children: panoramaWidgets);
  }

  /// Shows a modal with the current scene description.
  void _showDescriptionModal(BuildContext context) {
    final sceneTitle = _getSceneTitle(_currentSceneName);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      barrierColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
      builder: (BuildContext bc) {
        return Container(
          padding: EdgeInsets.only(
              top: 24,
              left: 24,
              right: 24,
              bottom: MediaQuery.of(context).viewInsets.bottom + 24),
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    sceneTitle,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close,
                        color: Theme.of(context).colorScheme.onSurface),
                    onPressed: () => Navigator.pop(context),
                    iconSize: 30,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    _currentSceneDescription,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.onSurface),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    flutterTts = FlutterTts();
    flutterTts.setCompletionHandler(() {
      setState(() {
        isPlaying = false;
      });
    });
    // Auto-play audio when the tour is opened.
    isPlaying = true;
    _startSpeaking();

    // Preload the next scene for a seamless experience.
    if (_scenes.length > 1) {
      _getPanoramaWidget(_scenes[1]);
    }
  }

  /// Auto-play audio on scene change.
  Future<void> _nextScene() async {
    await _stopSpeaking();
    setState(() {
      _currentSceneIndex = (_currentSceneIndex + 1) % _scenes.length;
      isPlaying = true;
    });
    _startSpeaking();
  }

  Future<void> _previousScene() async {
    // Do not allow wrapping from first to last scene.
    if (_currentSceneIndex == 0) return;
    await _stopSpeaking();
    setState(() {
      _currentSceneIndex = _currentSceneIndex - 1;
      isPlaying = true;
    });
    _startSpeaking();
  }

  void _togglePlayPause() {
    setState(() {
      if (isPlaying) {
        _pauseSpeaking();
        isPlaying = false;
      } else {
        _startSpeaking();
        isPlaying = true;
      }
    });
  }

  Future<void> _launchNavigation() async {
    final Uri url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$_latitude,$_longitude');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  void dispose() {
    _stopSpeaking();
    flutterTts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white : Colors.black;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Virtual Tour',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: _buildPanorama(),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.info_outline, color: iconColor),
                  onPressed: () => _showDescriptionModal(context),
                ),
                // Disable left arrow on first scene.
                IconButton(
                  icon: Icon(
                    Icons.chevron_left,
                    color: _currentSceneIndex == 0
                        ? iconColor.withOpacity(0.5)
                        : iconColor,
                  ),
                  onPressed: _currentSceneIndex == 0 ? null : _previousScene,
                ),
                IconButton(
                  icon: Icon(
                      isPlaying ? Icons.pause : Icons.play_arrow,
                      color: iconColor),
                  onPressed: _togglePlayPause,
                ),
                IconButton(
                  icon: Icon(Icons.chevron_right, color: iconColor),
                  onPressed: _nextScene,
                ),
                IconButton(
                  icon: Icon(Icons.navigation, color: iconColor),
                  onPressed: _launchNavigation,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
