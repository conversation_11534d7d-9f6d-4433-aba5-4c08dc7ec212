import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

// Map imports
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'login_page.dart';

class AccessibilityDetailPage extends StatefulWidget {
  final Map<String, dynamic> accessibilityFeature;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const AccessibilityDetailPage({
    Key? key,
    required this.accessibilityFeature,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<AccessibilityDetailPage> createState() =>
      _AccessibilityDetailPageState();
}

class _AccessibilityDetailPageState extends State<AccessibilityDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloaded();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloaded() {
    setState(() {
      _imageUrl = widget.accessibilityFeature['image_url']
              as String? ??
          'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupRealtimeListener() {
    final table =
        '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_accessibility';
    _realtimeChannel = Supabase.instance.client
        .channel('accessibility_detail_channel')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: table,
          callback: (payload) {
            if (payload.newRecord['id'] ==
                widget.accessibilityFeature['id']) {
              _fetchUpdatedFeature();
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedFeature() async {
    final table =
        '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_accessibility';
    try {
      final updated = await Supabase.instance.client
          .from(table)
          .select('*')
          .eq('id', widget.accessibilityFeature['id'])
          .single();
      if (mounted && updated != null) {
        setState(() {
          widget.accessibilityFeature
            ..clear()
            ..addAll(Map<String, dynamic>.from(updated));
          _loadImageFromPreloaded();
        });
        _updateCache(updated);
      }
    } catch (e) {
      print('Error fetching updated accessibility: $e');
    }
  }

  Future<void> _updateCache(Map<String, dynamic> updated) async {
    final prefs = await SharedPreferences.getInstance();
    final key =
        'accessibility_${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}';
    final jsonStr = prefs.getString(key);
    if (jsonStr != null) {
      final list = (jsonDecode(jsonStr) as List)
          .cast<Map<String, dynamic>>();
      for (var i = 0; i < list.length; i++) {
        if (list[i]['id'] == updated['id']) {
          list[i] = updated;
          break;
        }
      }
      await prefs.setString(key, jsonEncode(list));
    }
  }

  Future<void> _launchDialer(String number) async {
    final uri = Uri(scheme: 'tel', path: number);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showSnackbar('Could not place a call.');
    }
  }

  Future<void> _launchNavigation(double lat, double lng) async {
    final uri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showSnackbar('Could not launch navigation.');
    }
  }

  Future<void> _launchWhatsapp(String num) async {
    final clean = num.replaceAll(RegExp(r'[+\s]'), '');
    final uri = Uri.parse('https://wa.me/$clean');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showSnackbar('Could not launch WhatsApp.');
    }
  }

  void _showSnackbar(String msg) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(msg)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final feat = widget.accessibilityFeature;
    final phone = feat['phone']?.toString() ?? '';
    final whatsapp = feat['whatsapp']?.toString() ?? '';
    final lat = double.tryParse(feat['latitude']?.toString() ?? '');
    final lng = double.tryParse(feat['longitude']?.toString() ?? '');

    final hasPhone = phone.isNotEmpty;
    final hasWhats = whatsapp.isNotEmpty;
    final hasMap = lat != null && lng != null;

    String? location;
    final bld = feat['building'] as String? ?? '';
    final room = feat['room'] as String? ?? '';
    if (bld.isNotEmpty && room.isNotEmpty) {
      location = '$bld, Room $room';
    } else if (bld.isNotEmpty) {
      location = bld;
    } else if (room.isNotEmpty) {
      location = 'Room $room';
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          feat['fullname'] as String? ?? 'Accessibility Feature',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header image
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: const Center(
                        child: CircularProgressIndicator()),
                  )
                : (_imageUrl.isNotEmpty &&
                        _imageUrl !=
                            'assets/placeholder_image.png')
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (_, __) => Container(
                          height: 200,
                          color: theme
                              .colorScheme.surfaceVariant,
                          child: const Center(
                              child:
                                  CircularProgressIndicator()),
                        ),
                        errorWidget: (_, __, ___) =>
                            Container(
                          height: 200,
                          color: theme
                              .colorScheme.surfaceVariant,
                          child: Center(
                            child: Icon(Icons.help_center,
                                size: 50,
                                color: theme.colorScheme
                                    .onSurface),
                          ),
                        ),
                      )
                    : Container(
                        height: 200,
                        color: theme.colorScheme.surfaceVariant,
                        child: Center(
                          child: Icon(Icons.help_center,
                              size: 50,
                              color: theme.colorScheme
                                  .onSurface),
                        ),
                      ),

            // Details card
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment:
                        CrossAxisAlignment.start,
                    children: [
                      // Avatar + title + location
                      Row(
                        crossAxisAlignment:
                            CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: isDark
                                ? Colors.white
                                    .withOpacity(0.1)
                                : Colors.black
                                    .withOpacity(0.1),
                            backgroundImage: (_imageUrl
                                        .isNotEmpty &&
                                    _imageUrl !=
                                        'assets/placeholder_image.png')
                                ? NetworkImage(_imageUrl)
                                : null,
                            child:
                                (_imageUrl.isEmpty ||
                                        _imageUrl ==
                                            'assets/placeholder_image.png')
                                    ? Icon(
                                        Icons.help_center,
                                        size: 30,
                                        color: theme.colorScheme
                                            .onSurface,
                                      )
                                    : null,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment:
                                  CrossAxisAlignment.start,
                              children: [
                                Text(
                                  feat['fullname']
                                      as String? ??
                                      'Unknown',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight:
                                        FontWeight.bold,
                                    color: theme.colorScheme
                                        .onSurface,
                                  ),
                                ),
                                if (location != null)
                                  Padding(
                                    padding:
                                        const EdgeInsets.only(
                                            top: 4),
                                    child: Text(
                                      location!,
                                      style: TextStyle(
                                        color: theme
                                            .colorScheme
                                            .onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Hours
                      if (feat['hours'] != null &&
                          (feat['hours'] as String)
                              .isNotEmpty)
                        _buildDetailRow(
                            theme,
                            Icons.access_time,
                            'Hours',
                            feat['hours'] as String,
                            canCopy: false),

                      // Phone
                      if (hasPhone)
                        _buildDetailRow(
                          theme,
                          Icons.phone,
                          'Phone',
                          phone,
                          onTap: () => _launchDialer(phone),
                          canCopy: true,
                        ),

                      // Email
                      if (feat['email'] != null &&
                          (feat['email'] as String)
                              .isNotEmpty)
                        _buildDetailRow(
                          theme,
                          Icons.email,
                          'Email',
                          feat['email'] as String,
                          onTap: () => launchUrl(
                            Uri(
                                scheme: 'mailto',
                                path: feat['email']
                                    as String),
                          ),
                          canCopy: true,
                        ),

                      // WhatsApp
                      if (hasWhats)
                        _buildDetailRow(
                          theme,
                          FontAwesomeIcons.whatsapp,
                          'WhatsApp',
                          whatsapp,
                          onTap: () =>
                              _launchWhatsapp(whatsapp),
                          canCopy: false,
                        ),

                      // About
                      if (feat['about'] != null &&
                          (feat['about'] as String)
                              .isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildSectionTitle(
                            theme,
                            Icons.info_outline,
                            'About'),
                        const SizedBox(height: 8),
                        Text(
                          feat['about'] as String,
                          style: TextStyle(
                              color: theme.colorScheme
                                  .onSurfaceVariant),
                        ),
                      ],

                      // Map
                      if (hasMap) ...[
                        const SizedBox(height: 24),
                        _buildSectionTitle(
                            theme,
                            Icons.map_outlined,
                            'Location'),
                        const SizedBox(height: 8),
                        SizedBox(
                          height: 200,
                          child: ClipRRect(
                            borderRadius:
                                BorderRadius.circular(8),
                            child: FlutterMap(
                              options: MapOptions(
                                initialCenter:
                                    LatLng(lat!, lng!),
                                initialZoom: 16.0,
                              ),
                              children: [
                                TileLayer(
                                  urlTemplate:
                                      'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                                  userAgentPackageName:
                                      'com.example.yourapp',
                                ),
                                MarkerLayer(
                                  markers: [
                                    Marker(
                                      point: LatLng(lat, lng),
                                      width: 40,
                                      height: 40,
                                      child: Icon(
                                        Icons.location_on,
                                        color: Colors.red,
                                        size: 40,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom nav bar
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Row(
            mainAxisAlignment:
                MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(
                  Icons.call,
                  color: hasPhone
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface
                          .withOpacity(0.5),
                ),
                onPressed: hasPhone
                    ? () => _launchDialer(phone)
                    : null,
                tooltip: hasPhone
                    ? 'Call $phone'
                    : 'Phone not available',
              ),
              IconButton(
                icon: Icon(
                  Icons.navigation,
                  color: hasMap
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface
                          .withOpacity(0.5),
                ),
                onPressed: hasMap
                    ? () => _launchNavigation(lat!, lng!)
                    : null,
                tooltip: hasMap
                    ? 'Navigate'
                    : 'Navigation not available',
              ),
              IconButton(
                icon: FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: hasWhats
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface
                          .withOpacity(0.5),
                ),
                onPressed: hasWhats
                    ? () => _launchWhatsapp(whatsapp)
                    : null,
                tooltip: hasWhats
                    ? 'WhatsApp $whatsapp'
                    : 'WhatsApp not available',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme,
    IconData icon,
    String label,
    String value, {
    VoidCallback? onTap,
    bool canCopy = false,
  }) {
    final clickable = onTap != null;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: clickable
          ? InkWell(
              onTap: onTap,
              child: _rowContent(
                  theme, icon, label, value, canCopy, clickable),
            )
          : _rowContent(
              theme, icon, label, value, canCopy, clickable),
    );
  }

  Widget _rowContent(
    ThemeData theme,
    IconData icon,
    String label,
    String value,
    bool canCopy,
    bool clickable,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: theme.colorScheme.onSurface, size: 20),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label,
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface)),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: TextStyle(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  if (canCopy)
                    IconButton(
                      icon: Icon(Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurface),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: value));
                        _showSnackbar('$label copied to clipboard');
                      },
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(
      ThemeData theme, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.onSurface, size: 20),
          const SizedBox(width: 8),
          Text(text,
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface)),
        ],
      ),
    );
  }
}
