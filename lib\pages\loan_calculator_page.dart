import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math; // Make sure this import is present

class LoanCalculatorPage extends StatefulWidget {
  const LoanCalculatorPage({Key? key}) : super(key: key);

  @override
  _LoanCalculatorPageState createState() => _LoanCalculatorPageState();
}

class _LoanCalculatorPageState extends State<LoanCalculatorPage> {
  double _loanAmount = 0.0;
  double _interestRate = 0.0;
  int _loanTermYears = 0;
  String _monthlyPayment = '';
  String _totalPayment = '';
  String _totalInterest = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Loan Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Loan Amount',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _loanAmount = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Annual Interest Rate (%)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _interestRate = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Loan Term (Years)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _loanTermYears = int.tryParse(value) ?? 0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateLoan();
                    },
                    child: const Text('Calculate Loan'),
                  ),
                  const SizedBox(height: 20),
                  Text('Monthly Payment: $_monthlyPayment', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Total Payment: $_totalPayment', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Total Interest: $_totalInterest', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateLoan() {
    if (_loanAmount <= 0 || _interestRate <= 0 || _loanTermYears <= 0) {
      setState(() {
        _monthlyPayment = 'Invalid Input';
        _totalPayment = 'Invalid Input';
        _totalInterest = 'Invalid Input';
      });
      return;
    }

    double monthlyInterestRate = _interestRate / 100 / 12;
    int numberOfPayments = _loanTermYears * 12;

    double monthlyPaymentValue = (_loanAmount * monthlyInterestRate) / (1 - math.pow(1 + monthlyInterestRate, -numberOfPayments));
    double totalPaymentValue = monthlyPaymentValue * numberOfPayments;
    double totalInterestValue = totalPaymentValue - _loanAmount;

    setState(() {
      _monthlyPayment = '\$${monthlyPaymentValue.toStringAsFixed(2)}';
      _totalPayment = '\$${totalPaymentValue.toStringAsFixed(2)}';
      _totalInterest = '\$${totalInterestValue.toStringAsFixed(2)}';
    });
  }
}