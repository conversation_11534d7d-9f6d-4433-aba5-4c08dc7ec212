import 'dart:io';
import 'dart:convert';
import 'dart:html' as html;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  double _uploadProgress = 0.0;
  String? _geminiOutput;
  PlatformFile? _pickedFile;
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s';
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _initSpeech();
    _setupSupabaseListeners();
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
      .from('user_activities')
      .stream(primaryKey: ['id'])
      .listen((List<Map<String, dynamic>> snapshot) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
          );
        }
      });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'txt', 'doc', 'docx'],
        withData: true,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        if (mounted) {
          setState(() {
            _pickedFile = result.files.first;
            _isUploading = true;
            _uploadProgress = 0.0;
            _geminiOutput = null;
            _flashcards = [];
            _quizQuestions = [];
          });
        }

        for (int i = 0; i <= 100; i += 10) {
          await Future.delayed(const Duration(milliseconds: 50));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('File ready: ${_pickedFile!.name}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final extractor = sf_pdf.PdfTextExtractor(document);
      final text = extractor.extractText();
      document.dispose();
      return text;
    } catch (e) {
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<void> _processFile() async {
    if (_pickedFile == null) return;

    if (mounted) {
      setState(() {
        _isProcessing = true;
        _geminiOutput = null;
        _flashcards = [];
        _quizQuestions = [];
      });
    }

    try {
      String fileContent = '';
      final fileName = _pickedFile!.name.toLowerCase();

      if (fileName.endsWith('.pdf')) {
        if (_pickedFile!.bytes != null) {
          fileContent = await _extractTextFromPdf(_pickedFile!.bytes!) ?? '';
        } else if (_pickedFile!.path != null) {
          final bytes = await File(_pickedFile!.path!).readAsBytes();
          fileContent = await _extractTextFromPdf(bytes) ?? '';
        }
      } else if (fileName.endsWith('.txt') || 
                 fileName.endsWith('.doc') || 
                 fileName.endsWith('.docx')) {
        fileContent = utf8.decode(_pickedFile!.bytes!);
      }

      if (fileContent.isEmpty) throw Exception('Content extraction failed');

      if (mounted) {
        setState(() => _fileContent = fileContent);
      }

      final prompt = _buildPrompt(fileContent);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        _handleResponse(response.text!);
      } else {
        throw Exception('AI response empty');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Processing error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  String _buildPrompt(String content) {
    final prompts = {
      'notes': '''Generate structured notes with:
- Clear headings
- Bullet points
- Key terms in bold
- Examples in italic

Content: $content''',

      'cheatsheet': '''Create cheatsheet with:
# Topic
- Key formulas
- Definitions
- Examples

From: $content''',

      'flashcards': '''Generate flashcards (Q: question / A: answer):

Q: [Question]
A: [Answer]

Content: $content''',

      'quiz': '''Generate quiz format:
1. Question
A) Option 1
B) Option 2
C) Option 3
D) Option 4
Answer: X

Content: $content''',

      'transcript': '''Create transcript:
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',

      'chat': '''Use as context:
$content
Provide summary and discussion question'''
    };

    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        break;
      case 'chat':
        _startChatSession(response);
        break;
      default:
        if (mounted) {
          setState(() => _geminiOutput = response);
        }
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final lines = response.split('\n');
    String? currentQuestion;

    for (String line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('Q:')) {
        currentQuestion = trimmedLine.substring(2).trim();
      } else if (trimmedLine.startsWith('A:') && currentQuestion != null) {
        flashcards.add(Flashcard(
          question: currentQuestion,
          answer: trimmedLine.substring(2).trim(),
        ));
        currentQuestion = null;
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final questions = <QuizQuestion>[];
    final blocks = response.split(RegExp(r'\n\s*\n'));

    for (String block in blocks) {
      final lines = block.split('\n');
      if (lines.length < 5) continue;

      try {
        final questionLine = lines.firstWhere((l) => l.startsWith(RegExp(r'^\d+\.')));
        final question = questionLine.replaceAll(RegExp(r'^\d+[\.\)]\s*'), '').trim();
        
        final options = lines
            .where((l) => l.startsWith(RegExp(r'^[A-D][\.\)]')))
            .map((l) => l.replaceFirst(RegExp(r'^[A-D][\.\)]\s*'), '').trim())
            .toList();

        final answerLine = lines.firstWhere((l) => l.toLowerCase().startsWith('answer:'));
        final answer = answerLine.split(':').last.trim().toUpperCase();
        final correctIndex = answer.codeUnitAt(0) - 'A'.codeUnitAt(0);

        if (question.isNotEmpty && options.length == 4 && correctIndex >= 0) {
          questions.add(QuizQuestion(
            question: question,
            options: options,
            correctAnswerIndex: correctIndex,
          ));
        }
      } catch (e) {
        print('Quiz parsing error: $e');
      }
    }

    if (mounted) {
      setState(() {
        _quizQuestions = questions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
      });
    }
  }

  void _startChatSession(String response) {
    if (mounted) {
      setState(() {
        _chatMessages = [
          ChatMessage("Document Content:\n$_fileContent", false),
          ChatMessage(response, false),
        ];
      });
    }
  }

  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Header(
                  level: 0,
                  text: 'AI Generated Content',
                  textStyle: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 20),
                pw.Text(_geminiOutput!, style: pw.TextStyle(fontSize: 14)),
              ],
            );
          },
        ),
      );

      final bytes = await pdf.save();

      if (kIsWeb) {
        final blob = html.Blob([bytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.AnchorElement(href: url)
          ..download = 'learning_materials.pdf'
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        final dir = await getApplicationDocumentsDirectory();
        final path = '${dir.path}/learning_materials.pdf';
        await File(path).writeAsBytes(bytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to $path')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF export failed: $e')),
        );
      }
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages.add(
            ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }

  void _submitQuiz() {
    if (_currentQuestionIndex < _quizQuestions.length - 1) {
      if (mounted) {
        setState(() => _currentQuestionIndex++);
      }
      return;
    }

    int score = 0;
    final results = <QuizResult>[];

    for (int i = 0; i < _quizQuestions.length; i++) {
      final correctIndex = _quizQuestions[i].correctAnswerIndex;
      final userAnswer = _userAnswers[i];
      final isCorrect = userAnswer == correctIndex;

      if (isCorrect) score++;

      results.add(QuizResult(
        question: _quizQuestions[i].question,
        userAnswer: userAnswer != null && userAnswer < _quizQuestions[i].options.length
            ? _quizQuestions[i].options[userAnswer]
            : 'No answer',
        correctAnswer: _quizQuestions[i].options[correctIndex ?? 0],
        isCorrect: isCorrect,
      ));
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => QuizResultsDialog(
          score: score,
          total: _quizQuestions.length,
          results: results,
          textColor: widget.isDarkMode ? Colors.white : Colors.black,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textColor = widget.isDarkMode ? Colors.white : Colors.black;

    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Learning Dashboard'),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, textColor),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, textColor),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, textColor),
                ],
              ),
            ),
    );
  }

  Widget _buildFileSection(ThemeData theme, Color textColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.upload, color: theme.colorScheme.onPrimary),
              label: Text('Select Learning Material',
                  style: GoogleFonts.roboto(
                      color: theme.colorScheme.onPrimary)),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              ),
              onPressed: _isUploading ? null : _pickFile,
            ),
            if (_pickedFile != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text('Selected: ${_pickedFile!.name}',
                    style: GoogleFonts.roboto(
                        color: textColor.withOpacity(0.8))),
              ),
            if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _uploadProgress),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingControls(ThemeData theme, Color textColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.roboto(color: textColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.roboto(color: textColor),
              items: const [
                DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                DropdownMenuItem(value: 'chat', child: Text('Chat with Content')),
              ],
              onChanged: _isProcessing
                  ? null
                  : (value) => setState(() => _processType = value!),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.auto_awesome, color: theme.colorScheme.onPrimary),
              label: Text(_isProcessing ? 'Processing...' : 'Generate Content',
                  style: GoogleFonts.roboto(
                      color: theme.colorScheme.onPrimary)),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: _isProcessing ? null : _processFile,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color textColor) {
    if (_processType == 'flashcards') {
      return _buildFlashcardsView(theme, textColor);
    } else if (_processType == 'quiz') {
      return _buildQuizView(theme, textColor);
    } else if (_processType == 'chat') {
      return _buildChatView(theme, textColor);
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Generated Content',
                    style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: textColor)),
                IconButton(
                  icon: Icon(Icons.download, color: textColor),
                  onPressed: _exportToPdf,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: MarkdownBody(
                data: _geminiOutput ?? 'No content generated',
                styleSheet: MarkdownStyleSheet(
                  h1: GoogleFonts.roboto(
                      fontSize: 24, fontWeight: FontWeight.bold, color: textColor),
                  p: GoogleFonts.roboto(fontSize: 16, color: textColor),
                  code: GoogleFonts.robotoMono(
                      fontSize: 14, backgroundColor: theme.cardColor, color: textColor),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlashcardsView(ThemeData theme, Color textColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: textColor,
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: Icon(Icons.download, color: theme.colorScheme.onPrimary),
              label: Text('Export Flashcards',
                  style: GoogleFonts.roboto(
                      color: theme.colorScheme.onPrimary)),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: _exportToPdf,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizView(ThemeData theme, Color textColor) {
    if (_quizQuestions.isEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('No quiz questions generated',
              style: GoogleFonts.roboto(color: textColor)),
        ),
      );
    }

    final question = _quizQuestions[_currentQuestionIndex];

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
              style: GoogleFonts.roboto(
                  fontSize: 18, fontWeight: FontWeight.bold, color: textColor)),
            const SizedBox(height: 16),
            Text(question.question,
                style: GoogleFonts.roboto(fontSize: 16, color: textColor)),
            const SizedBox(height: 20),
            ...question.options.asMap().entries.map((entry) => RadioListTile<int>(
                  title: Text(entry.value,
                      style: GoogleFonts.roboto(color: textColor)),
                  value: entry.key,
                  groupValue: _userAnswers[_currentQuestionIndex],
                  onChanged: (value) => setState(
                      () => _userAnswers[_currentQuestionIndex] = value),
                  activeColor: theme.colorScheme.primary,
                )),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _submitQuiz,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                _currentQuestionIndex < _quizQuestions.length - 1
                    ? 'Next Question'
                    : 'Finish Quiz',
                style: GoogleFonts.roboto(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatView(ThemeData theme, Color textColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.roboto(color: textColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: theme.colorScheme.primary),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    super.key,
    required this.flashcard,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return FlipCard(
      front: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.question,
                style: GoogleFonts.roboto(fontSize: 20, color: textColor)),
          ),
        ),
      ),
      back: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.answer,
                style: GoogleFonts.roboto(fontSize: 18, color: textColor)),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({super.key, required this.message, required this.isDarkMode});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(message.text,
            style: GoogleFonts.roboto(
                color: isDarkMode ? Colors.white : Colors.black)),
      ),
    );
  }
}

class QuizResultsDialog extends StatelessWidget {
  final int score;
  final int total;
  final List<QuizResult> results;
  final Color textColor;

  const QuizResultsDialog({
    super.key,
    required this.score,
    required this.total,
    required this.results,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Quiz Results', style: GoogleFonts.roboto(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          children: [
            Text('Score: $score/$total',
                style: GoogleFonts.roboto(
                    fontSize: 20, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...results.map((result) => ListTile(
                  title: Text(result.question,
                      style: GoogleFonts.roboto(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Your answer: ${result.userAnswer}',
                          style: GoogleFonts.roboto(
                              color: result.isCorrect
                                  ? Colors.green
                                  : Colors.red)),
                      Text('Correct answer: ${result.correctAnswer}',
                          style: GoogleFonts.roboto()),
                      const Divider(),
                    ],
                  ),
                )),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.roboto()),
        ),
      ],
    );
  }
}