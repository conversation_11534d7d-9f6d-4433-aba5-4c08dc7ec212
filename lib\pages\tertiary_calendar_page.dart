import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'class_schedule_page.dart';
import 'training_schedule_page.dart';
import 'events_page.dart';
import 'academic_calendar_page.dart';

class TertiaryCalendarPage extends StatelessWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryCalendarPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    final bool currentIsDarkMode = theme.brightness == Brightness.dark;

    return Card(
      key: Key('calendar_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          switch (title) {
            case 'Class Schedules':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ClassSchedulePage(
                    institutionName: institutionName,
                    collegeData: collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: toggleTheme,
                  ),
                ),
              );
              break;
            case 'Training Schedules':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TrainingSchedulePage(
                    institutionName: institutionName,
                    collegeData: collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: toggleTheme,
                  ),
                ),
              );
              break;
            case 'Events':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EventsPage(
                    institutionName: institutionName,
                    collegeData: collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: toggleTheme,
                  ),
                ),
              );
              break;
            case 'Academic Calendar':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AcademicCalendarPage(
                    institutionName: institutionName,
                    collegeData: collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: toggleTheme,
                  ),
                ),
              );
              break;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = this.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Class Schedules', 'icon': Icons.schedule},
      {'title': 'Training Schedules', 'icon': Icons.fitness_center},
      {'title': 'Events', 'icon': Icons.event},
      {'title': 'Academic Calendar', 'icon': Icons.calendar_today},
    ];

    // Filter out 'Events' and 'Academic Calendar' when not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage && item['title'] == 'none') {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Calendar',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}