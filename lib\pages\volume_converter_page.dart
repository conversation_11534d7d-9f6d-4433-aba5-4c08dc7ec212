// volume_converter_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VolumeConverterPage extends StatefulWidget {
  const VolumeConverterPage({Key? key}) : super(key: key);

  @override
  _VolumeConverterPageState createState() => _VolumeConverterPageState();
}

class _VolumeConverterPageState extends State<VolumeConverterPage> {
  final Map<String, double> _volumeUnits = {
    'Cubic Meters': 1.0,
    'Liters': 0.001,
    'Milliliters': 0.000001,
    'Gallons (US)': 0.00378541,
    'Quarts (US)': 0.000946353,
    'Pints (US)': 0.000473176,
    'Cups (US)': 0.000236588,
    'Fluid Ounces (US)': 2.95735e-5,
    'Cubic Feet': 0.0283168,
    'Cubic Inches': 1.63871e-5,
  };

  String _fromUnit = 'Cubic Meters';
  String _toUnit = 'Liters';
  double _inputValue = 0.0;
  double _outputValue = 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Volume Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Value to Convert',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _inputValue = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _fromUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _volumeUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromUnit = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<String>(
                        value: _toUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _volumeUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toUnit = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertVolume();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_outputValue $_toUnit',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertVolume() {
    if (_volumeUnits[_fromUnit] != null && _volumeUnits[_toUnit] != null) {
      setState(() {
        _outputValue = _inputValue * (_volumeUnits[_fromUnit]! / _volumeUnits[_toUnit]!);
      });
    } else {
      setState(() {
        _outputValue = 0.0;
      });
      // Handle error: unit not found
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Volume unit not found.')),
      );
    }
  }
}