import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'research_projects_page.dart';
import 'theses_page.dart';
import 'books_page.dart';
import 'articles_page.dart';
import 'patents_page.dart';

class TertiaryProjectsPublicationsPage extends StatefulWidget {
  final Map<String, dynamic>? collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryProjectsPublicationsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryProjectsPublicationsPage> createState() => _TertiaryProjectsPublicationsPageState();



}

class _TertiaryProjectsPublicationsPageState extends State<TertiaryProjectsPublicationsPage> {
  // Data for each section
  List<Map<String, dynamic>> _researchProjects = [];
  List<Map<String, dynamic>> _theses = [];
  List<Map<String, dynamic>> _books = [];
  List<Map<String, dynamic>> _articles = [];
  List<Map<String, dynamic>> _patents = [];

  // Loading states
  bool _isLoadingResearchProjects = false;
  bool _isLoadingTheses = false;
  bool _isLoadingBooks = false;
  bool _isLoadingArticles = false;
  bool _isLoadingPatents = false;

  // Realtime channels
  late final RealtimeChannel _researchProjectsChannel;
  late final RealtimeChannel _thesesChannel;
  late final RealtimeChannel _booksChannel;
  late final RealtimeChannel _articlesChannel;
  late final RealtimeChannel _patentsChannel;

  @override
  void initState() {
    super.initState();
    _preloadAndCacheData();
    _setupRealtimeChannels();
  }

  @override
  void dispose() {
    _researchProjectsChannel.unsubscribe();
    _thesesChannel.unsubscribe();
    _booksChannel.unsubscribe();
    _articlesChannel.unsubscribe();
    _patentsChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _preloadAndCacheData() async {
    await Future.wait([
      _preloadResearchProjects(),
      _preloadTheses(),
      _preloadBooks(),
      _preloadArticles(),
      _preloadPatents(),
    ]);
  }

  Future<void> _preloadResearchProjects() async {
    if (_isLoadingResearchProjects) return;
    setState(() => _isLoadingResearchProjects = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('researchprojects');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _researchProjects = cachedData;
          _isLoadingResearchProjects = false;
        });
      }

      // Then fetch from Supabase
      final researchProjectsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_researchprojects';
      final response = await Supabase.instance.client
          .from(researchProjectsTableName)
          .select('*')
          .order('year', ascending: false)
          .limit(20);

      final researchProjects = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('researchprojects', researchProjects);

      setState(() {
        _researchProjects = researchProjects;
        _isLoadingResearchProjects = false;
      });
    } catch (e) {
      print('Error preloading research projects: $e');
      setState(() => _isLoadingResearchProjects = false);
    }
  }

  Future<void> _preloadTheses() async {
    if (_isLoadingTheses) return;
    setState(() => _isLoadingTheses = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('theses');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _theses = cachedData;
          _isLoadingTheses = false;
        });
      }

      // Then fetch from Supabase
      final thesesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_theses';
      final response = await Supabase.instance.client
          .from(thesesTableName)
          .select('*')
          .order('year', ascending: false)
          .limit(20);

      final theses = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('theses', theses);

      setState(() {
        _theses = theses;
        _isLoadingTheses = false;
      });
    } catch (e) {
      print('Error preloading theses: $e');
      setState(() => _isLoadingTheses = false);
    }
  }

  Future<void> _preloadBooks() async {
    if (_isLoadingBooks) return;
    setState(() => _isLoadingBooks = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('books');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _books = cachedData;
          _isLoadingBooks = false;
        });
      }

      // Then fetch from Supabase
      final booksTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_books';
      final response = await Supabase.instance.client
          .from(booksTableName)
          .select('*')
          .order('year', ascending: false)
          .limit(20);

      final books = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('books', books);

      setState(() {
        _books = books;
        _isLoadingBooks = false;
      });
    } catch (e) {
      print('Error preloading books: $e');
      setState(() => _isLoadingBooks = false);
    }
  }

  Future<void> _preloadArticles() async {
    if (_isLoadingArticles) return;
    setState(() => _isLoadingArticles = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('articles');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _articles = cachedData;
          _isLoadingArticles = false;
        });
      }

      // Then fetch from Supabase
      final articlesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_articles';
      final response = await Supabase.instance.client
          .from(articlesTableName)
          .select('*')
          .order('year', ascending: false)
          .limit(20);

      final articles = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('articles', articles);

      setState(() {
        _articles = articles;
        _isLoadingArticles = false;
      });
    } catch (e) {
      print('Error preloading articles: $e');
      setState(() => _isLoadingArticles = false);
    }
  }

  Future<void> _preloadPatents() async {
    if (_isLoadingPatents) return;
    setState(() => _isLoadingPatents = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('patents');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _patents = cachedData;
          _isLoadingPatents = false;
        });
      }

      // Then fetch from Supabase
      final patentsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_patents';
      final response = await Supabase.instance.client
          .from(patentsTableName)
          .select('*')
          .order('year', ascending: false)
          .limit(20);

      final patents = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('patents', patents);

      setState(() {
        _patents = patents;
        _isLoadingPatents = false;
      });
    } catch (e) {
      print('Error preloading patents: $e');
      setState(() => _isLoadingPatents = false);
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache(String tableName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading $tableName from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(String tableName, List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving $tableName to cache: $e');
    }
  }

  void _setupRealtimeChannels() {
    _setupResearchProjectsRealtimeChannel();
    _setupThesesRealtimeChannel();
    _setupBooksRealtimeChannel();
    _setupArticlesRealtimeChannel();
    _setupPatentsRealtimeChannel();
  }

  void _setupResearchProjectsRealtimeChannel() {
    final researchProjectsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_researchprojects';
    _researchProjectsChannel = Supabase.instance.client
        .channel('researchprojects_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: researchProjectsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadResearchProjects();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadResearchProjects();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadResearchProjects();
            }
          },
        )
        .subscribe();
  }

  void _setupThesesRealtimeChannel() {
    final thesesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_theses';
    _thesesChannel = Supabase.instance.client
        .channel('theses_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: thesesTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadTheses();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadTheses();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadTheses();
            }
          },
        )
        .subscribe();
  }

  void _setupBooksRealtimeChannel() {
    final booksTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_books';
    _booksChannel = Supabase.instance.client
        .channel('books_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: booksTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadBooks();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadBooks();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadBooks();
            }
          },
        )
        .subscribe();
  }

  void _setupArticlesRealtimeChannel() {
    final articlesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_articles';
    _articlesChannel = Supabase.instance.client
        .channel('articles_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: articlesTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadArticles();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadArticles();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadArticles();
            }
          },
        )
        .subscribe();
  }

  void _setupPatentsRealtimeChannel() {
    final patentsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_patents';
    _patentsChannel = Supabase.instance.client
        .channel('patents_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: patentsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadPatents();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadPatents();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadPatents();
            }
          },
        )
        .subscribe();
  }

  Widget _buildGridItem(
    BuildContext context,
    String title,
    IconData icon,
    ThemeData theme,
    bool isFromDetailPage,
  ) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    void navigateToPage() {
      if (title == 'Research Projects') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ResearchProjectsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedResearchProjects: _researchProjects,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Theses/Dissertations') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ThesesPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedTheses: _theses,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Books') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BooksPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedBooks: _books,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Articles') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ArticlesPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedArticles: _articles,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Patents') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PatentsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPatents: _patents,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: navigateToPage,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Research Projects', 'icon': Icons.science_outlined},
      {'title': 'Theses/Dissertations', 'icon': Icons.description},
      {'title': 'Books', 'icon': Icons.book},
      {'title': 'Articles', 'icon': Icons.article},
      {'title': 'Patents', 'icon': Icons.verified_outlined},
    ];

    // Filter out 'Theses/Dissertations' if not coming from a detail page.
    final filteredGridItems = gridItems
        .where((item) => isFromDetailPage || item['title'] != 'Theses/Dissertations')
        .toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Projects & Publications',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
