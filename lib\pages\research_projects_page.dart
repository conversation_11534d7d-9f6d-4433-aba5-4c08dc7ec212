// research_projects_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'research_project_detail_page.dart';

class ResearchProjectsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedResearchProjects;
  final bool isFromDetailPage;

  const ResearchProjectsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedResearchProjects,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ResearchProjectsPage> createState() => _ResearchProjectsPageState();
}

class _ResearchProjectsPageState extends State<ResearchProjectsPage> {
  List<Map<String, dynamic>> _researchProjects = [];
  List<Map<String, dynamic>> _filteredResearchProjects = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _researchProjectsChannel;

  @override
  void initState() {
    super.initState();
    _loadResearchProjects();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _researchProjectsChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterResearchProjects();
    });
  }

  void _filterResearchProjects() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredResearchProjects = List.from(_researchProjects);
      return;
    }

    _filteredResearchProjects = _researchProjects.where((project) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          project['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (project['researcher']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (project['researcher2']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (project['researcher3']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (project['department']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (project['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Faculty/Staff') {
          matchesFilter = project['facultyorstaffresearch'] == true;
        } else if (_selectedFilter == 'Student') {
          matchesFilter = project['studentresearch'] == true;
        } else if (_selectedFilter == 'Department') {
          matchesFilter = project['department'] == _selectedFilter;
        } else if (_selectedFilter == 'Year') {
          matchesFilter = project['year'].toString() == _selectedFilter;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadResearchProjects() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedResearchProjects.isNotEmpty) {
        setState(() {
          _researchProjects = widget.preloadedResearchProjects;
          _filteredResearchProjects = widget.preloadedResearchProjects;
          _isLoading = false;
        });
        print('Using preloaded research projects data for ${widget.collegeNameForTable}');
        // Still fetch in background to refresh cache
        _fetchResearchProjectsFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _researchProjects = cachedData;
          _filteredResearchProjects = cachedData;
          _isLoading = false;
        });
        print('Loaded research projects from cache for ${widget.collegeNameForTable}');
      }

      // Then fetch from Supabase
      await _fetchResearchProjectsFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading research projects: $e';
      });
      print('Error in _loadResearchProjects: $e');
    }
  }

  Future<void> _fetchResearchProjectsFromSupabase() async {
    try {
      // Make sure to use the exact same format as in tertiary_start_page.dart
      final researchProjectsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
      print('Fetching from table: $researchProjectsTableName');

      final response = await Supabase.instance.client
          .from(researchProjectsTableName)
          .select('*')
          .order('year', ascending: false);

      final researchProjects = List<Map<String, dynamic>>.from(response);
      print('Fetched ${researchProjects.length} research projects from Supabase for ${widget.collegeNameForTable}');

      // Cache the data
      await _saveToCache(researchProjects);

      if (mounted) {
        setState(() {
          _researchProjects = researchProjects;
          _filteredResearchProjects = researchProjects;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching research projects from Supabase: $e');
      print('Table name attempted: ${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_researchProjects.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading research projects: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'researchprojects_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} research projects in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached research projects found');
      }
    } catch (e) {
      print('Error loading research projects from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'researchprojects_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} research projects to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved research projects to cache');
    } catch (e) {
      print('Error saving research projects to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final researchProjectsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
    print('Setting up realtime listener for table: $researchProjectsTableName');
    _researchProjectsChannel = Supabase.instance.client
        .channel('researchprojects_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: researchProjectsTableName,
          callback: (payload) {
            print('Realtime update received for research projects');
            _fetchResearchProjectsFromSupabase();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> departments = {};
    final Set<String> years = {};

    for (final project in _researchProjects) {
      if (project['department'] != null && project['department'].toString().isNotEmpty) {
        departments.add(project['department'].toString());
      }
      if (project['year'] != null) {
        years.add(project['year'].toString());
      }
    }

    final List<String> filters = ['All', 'Faculty/Staff', 'Student'];
    filters.addAll(departments);
    filters.addAll(years);

    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Research Projects',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('research-projects-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _researchProjects.isEmpty && !_isLoading) {
            _loadResearchProjects();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search research projects...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterResearchProjects();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadResearchProjects,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredResearchProjects.isEmpty
                          ? const Center(
                              child: Text('No research projects found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredResearchProjects.length,
                              itemBuilder: (context, index) {
                                final project = _filteredResearchProjects[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      project['fullname'] ?? 'Untitled Project',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Year: ${project['year'] ?? 'N/A'}'),
                                        Text('Researchers: ${_formatResearchers(project)}'),
                                        Text('Department: ${project['department'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ResearchProjectDetailPage(
                                            researchProject: project,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatResearchers(Map<String, dynamic> project) {
    final List<String> researchers = [];

    if (project['researcher'] != null && project['researcher'].toString().isNotEmpty) {
      researchers.add(project['researcher'].toString());
    }

    if (project['researcher2'] != null && project['researcher2'].toString().isNotEmpty) {
      researchers.add(project['researcher2'].toString());
    }

    if (project['researcher3'] != null && project['researcher3'].toString().isNotEmpty) {
      researchers.add(project['researcher3'].toString());
    }

    if (researchers.isEmpty) {
      return 'N/A';
    }

    return researchers.join(', ');
  }
}
