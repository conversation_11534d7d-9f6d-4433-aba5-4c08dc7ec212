import 'package:flutter/material.dart';
import 'dart:async';

class StopwatchScreen extends StatefulWidget {
  const StopwatchScreen({Key? key}) : super(key: key);

  @override
  _StopwatchScreenState createState() => _StopwatchScreenState();
}

class _StopwatchScreenState extends State<StopwatchScreen> with WidgetsBindingObserver {
  final _stopwatch = Stopwatch();
  Timer? _timer;
  List<String> _laps = [];
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _timer?.cancel();
    } else if (state == AppLifecycleState.resumed && _stopwatch.isRunning) {
      _startTimer();
    }
  }

  void _startTimer() {
    setState(() {
      _isRunning = true;
    });
    _timer = Timer.periodic(const Duration(milliseconds: 10), (timer) {
      setState(() {});
    });
    _stopwatch.start();
  }

  void _stopTimer() {
    setState(() {
      _isRunning = false;
    });
    _timer?.cancel();
    _stopwatch.stop();
  }

  void _resetTimer() {
    _stopTimer();
    setState(() {
      _stopwatch.reset();
      _laps.clear();
    });
  }

  void _lap() {
    setState(() {
      _laps.insert(0, _formatMilliseconds(_stopwatch.elapsedMilliseconds));
    });
  }

  String _formatMilliseconds(int milliseconds) {
    final hundreds = (milliseconds % 1000 ~/ 10).toString().padLeft(2, '0');
    final seconds = (milliseconds ~/ 1000 % 60).toString().padLeft(2, '0');
    final minutes = (milliseconds ~/ 1000 ~/ 60).toString().padLeft(2, '0');
    return '$minutes:$seconds.$hundreds';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        // surfaceTintColor: Colors.transparent, // Removed surfaceTintColor
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Stopwatch',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
          color: theme.colorScheme.surface,
          // surfaceTintColor: Colors.transparent, // Removed surfaceTintColor
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    _formatMilliseconds(_stopwatch.elapsedMilliseconds),
                    style: TextStyle(fontSize: 60, color: theme.colorScheme.onSurface),
                  ),
                ),
                const SizedBox(height: 30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    _buildCircleButton(
                      heroTag: 'stopwatch_lap',
                      icon: Icons.flag_outlined,
                      onPressed: _stopwatch.isRunning ? _lap : null,
                      backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
                    ),
                    _buildCircleButton(
                      heroTag: 'stopwatch_start_stop',
                      icon: _stopwatch.isRunning ? Icons.pause : Icons.play_arrow,
                      onPressed: _stopwatch.isRunning ? _stopTimer : _startTimer,
                      backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
                    ),
                    _buildCircleButton(
                      heroTag: 'stopwatch_reset',
                      icon: Icons.refresh,
                      onPressed: _stopwatch.isRunning ? null : _resetTimer,
                      backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: _laps.isNotEmpty
          ? FloatingActionButton(
              heroTag: "stopwatch_laps_fab",
              onPressed: () {
                // No specific action needed for the FAB in this case,
                // it just indicates the presence of laps.
              },
              tooltip: 'Laps',
              backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.onSurface,
              shape: const CircleBorder(),
              child: Text('${_laps.length}', style: TextStyle(color: theme.colorScheme.onSurface)),
            )
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      bottomSheet: _laps.isNotEmpty
          ? BottomSheet(
              backgroundColor: theme.colorScheme.surface,
              // surfaceTintColor: Colors.transparent, // Removed surfaceTintColor
              onClosing: () {},
              builder: (context) {
                return SizedBox(
                  height: 200.0, // Adjust the height as needed
                  child: ListView.builder(
                    itemCount: _laps.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        tileColor: theme.colorScheme.surface,
                        title: Text('Lap ${index + 1}', style: TextStyle(color: theme.colorScheme.onSurface)),
                        trailing: Text(_laps[index], style: TextStyle(color: theme.colorScheme.onSurface)),
                      );
                    },
                  ),
                );
              },
            )
          : null,
    );
  }

  Widget _buildCircleButton({required String heroTag, required IconData icon, VoidCallback? onPressed, Color? backgroundColor}) {
    final theme = Theme.of(context);
    return FloatingActionButton.small(
      heroTag: heroTag,
      onPressed: onPressed,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      shape: const CircleBorder(),
      child: Icon(icon),
    );
  }
}