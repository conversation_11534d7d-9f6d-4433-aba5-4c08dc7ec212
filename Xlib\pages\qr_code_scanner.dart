// bible_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:flutter_tts/flutter_tts.dart';

class BiblePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BiblePage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  _BiblePageState createState() => _BiblePageState();
}

class _BiblePageState extends State<BiblePage> {
  final FlutterTts flutterTts = FlutterTts();
  Map<String, dynamic>? _bibleData;
  String? _selectedBook;
  int _selectedChapter = 1;
  List<String> _books = [];
  bool _isSpeaking = false;

  @override
  void initState() {
    super.initState();
    _loadBibleData();
    _initTts();
  }

  Future<void> _initTts() async {
    await flutterTts.setLanguage("en-US");
    await flutterTts.setSpeechRate(0.4);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.0);

    flutterTts.setStartHandler(() {
      setState(() {
        _isSpeaking = true;
      });
    });

    flutterTts.setCompletionHandler(() {
      setState(() {
        _isSpeaking = false;
      });
    });

    flutterTts.setErrorHandler((message) {
      setState(() {
        _isSpeaking = false;
      });
    });
  }

  Future<void> _loadBibleData() async {
    try {
      print('Loading Bible data...');
      String jsonString = await rootBundle.loadString('assets/kjv.json');
      _bibleData = jsonDecode(jsonString);
      _books = _bibleData?.keys.toList() ?? [];
      if (_books.isNotEmpty) {
        _selectedBook = _books.first;
      }
      setState(() {
        print('Bible data loaded successfully.');
      });
    } catch (e) {
      print('Error loading Bible data: $e');
      setState(() {});
    }
  }

  List<int> _getChapters() {
    if (_bibleData == null || _selectedBook == null || !_bibleData!.containsKey(_selectedBook)) {
      return [];
    }
    return List<int>.generate(_bibleData![_selectedBook]!.length, (i) => i + 1);
  }

  List<String> _getVerses() {
    if (_bibleData == null || _selectedBook == null || !_bibleData!.containsKey(_selectedBook)) {
      return [];
    }
    final bookData = _bibleData![_selectedBook];
    if (bookData == null || !bookData.containsKey(_selectedChapter.toString())) {
      return [];
    }
    return (bookData[_selectedChapter.toString()] as List<dynamic>)
        .map((verse) => verse.toString())
        .toList();
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty) {
      await flutterTts.stop();
      await flutterTts.speak(text);
    }
  }

  Future<void> _stop() async {
    await flutterTts.stop();
    setState(() {
      _isSpeaking = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text('Bible (KJV)', style: TextStyle(color: theme.colorScheme.onSurface)),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                color: theme.colorScheme.onSurface),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _bibleData == null
          ? Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _selectedBook,
                        dropdownColor: theme.colorScheme.surface,
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedBook = newValue;
                            _selectedChapter = 1;
                          });
                        },
                        items: _books.map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                      ),
                      DropdownButton<int>(
                        value: _selectedChapter,
                        dropdownColor: theme.colorScheme.surface,
                        onChanged: (int? newValue) {
                          setState(() {
                            _selectedChapter = newValue!;
                          });
                        },
                        items: _getChapters().map<DropdownMenuItem<int>>((int value) {
                          return DropdownMenuItem<int>(
                            value: value,
                            child: Text(value.toString(), style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: _getVerses().length,
                    itemBuilder: (context, index) {
                      final verse = _getVerses()[index];
                      return ListTile(
                        title: Text('${index + 1}. $verse',
                            style: TextStyle(color: theme.colorScheme.onSurface)),
                        trailing: IconButton(
                          icon: Icon(Icons.volume_up, color: theme.colorScheme.onSurface),
                          onPressed: () => _speak(verse),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
      floatingActionButton: _isSpeaking
          ? FloatingActionButton(
              onPressed: _stop,
              tooltip: 'Stop Reading',
              backgroundColor: Colors.red,
              child: Icon(Icons.stop),
            )
          : null,
    );
  }
}