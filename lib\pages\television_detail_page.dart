// television_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class TelevisionDetailPage extends StatefulWidget {
  final Map<String, dynamic> televisionStation;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const TelevisionDetailPage({
    Key? key,
    required this.televisionStation,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<TelevisionDetailPage> createState() => _TelevisionDetailPageState();
}

class _TelevisionDetailPageState extends State<TelevisionDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _televisionRealtimeChannel; // Realtime channel for television station updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupTelevisionRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _televisionRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.televisionStation['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupTelevisionRealtimeListener() {
    final televisionTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_television';
    _televisionRealtimeChannel = Supabase.instance.client
        .channel('television_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: televisionTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current television station's ID
        if (payload.newRecord['id'] == widget.televisionStation['id']) {
          print("Realtime UPDATE event received for THIS television station (manual filter applied): ${widget.televisionStation['fullname']}");
          _fetchUpdatedTelevisionData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER television station, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedTelevisionData() async {
    final televisionTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_television';
    try {
      final updatedTelevisionResponse = await Supabase.instance.client
          .from(televisionTableName)
          .select('*')
          .eq('id', widget.televisionStation['id'])
          .single();

      if (mounted && updatedTelevisionResponse != null) {
        Map<String, dynamic> updatedTelevision = Map.from(updatedTelevisionResponse);
        // Update the widget.televisionStation with the new data
        setState(() {
          widget.televisionStation.clear(); // Clear old data
          widget.televisionStation.addAll(updatedTelevision); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Television station data updated in detail page for ${widget.televisionStation['fullname']}");
          _updateTelevisionCache(updatedTelevision); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated television station data: $error");
    }
  }

  Future<void> _updateTelevisionCache(Map<String, dynamic> updatedTelevision) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'television_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedTelevisionJson = prefs.getString(cacheKey);

    if (cachedTelevisionJson != null) {
      List<Map<String, dynamic>> cachedTelevision = (jsonDecode(cachedTelevisionJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the television station in the cached list
      for (int i = 0; i < cachedTelevision.length; i++) {
        if (cachedTelevision[i]['id'] == updatedTelevision['id']) {
          cachedTelevision[i] = updatedTelevision;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedTelevision));
      print("Television stations cache updated with realtime change for ${updatedTelevision['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.televisionStation['phone'] as String? ?? '';
    final whatsappNumber = widget.televisionStation['whatsapp'] as String? ?? '';
    final email = widget.televisionStation['email'] as String? ?? '';
    final about = widget.televisionStation['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.televisionStation['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                  if (email.isNotEmpty)
                    _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                  if (phone.isNotEmpty)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                  if (whatsappNumber.isNotEmpty)
                    _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isWhatsappAvailable ? 1.0 : 0.5,
                    child: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email' || title == 'WhatsApp';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
