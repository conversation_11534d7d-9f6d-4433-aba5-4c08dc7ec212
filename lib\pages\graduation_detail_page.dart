// graduation_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'tertiary_map_page.dart';

class GraduationDetailPage extends StatefulWidget {
  final Map<String, dynamic> graduation;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const GraduationDetailPage({
    Key? key,
    required this.graduation,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<GraduationDetailPage> createState() => _GraduationDetailPageState();
}

class _GraduationDetailPageState extends State<GraduationDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _graduationRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupGraduationRealtimeListener();
  }

  @override
  void dispose() {
    _graduationRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.graduation['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupGraduationRealtimeListener() {
    final graduationTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_graduation';
    _graduationRealtimeChannel = Supabase.instance.client
        .channel('graduation_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: graduationTableName,
      callback: (payload) async {
        if (payload.newRecord['id'] == widget.graduation['id']) {
          print("Realtime UPDATE event received for THIS graduation: ${widget.graduation['venue']}");
          _fetchUpdatedGraduationData();
        } else {
          print("Realtime UPDATE event received for OTHER graduation, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedGraduationData() async {
    final graduationTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_graduation';
    try {
      final updatedGraduationResponse = await Supabase.instance.client
          .from(graduationTableName)
          .select('*')
          .eq('id', widget.graduation['id'])
          .single();

      if (mounted && updatedGraduationResponse != null) {
        Map<String, dynamic> updatedGraduation = Map.from(updatedGraduationResponse);
        setState(() {
          widget.graduation.clear();
          widget.graduation.addAll(updatedGraduation);
          _loadImageFromPreloadedData();
          print("Graduation data updated in detail page for ${widget.graduation['venue']}");
          _updateGraduationCache(updatedGraduation);
        });
      }
    } catch (error) {
      print("Error fetching updated graduation data: $error");
    }
  }

  Future<void> _updateGraduationCache(Map<String, dynamic> updatedGraduation) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'graduation_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedGraduationJson = prefs.getString(cacheKey);

    if (cachedGraduationJson != null) {
      List<Map<String, dynamic>> cachedGraduation = (jsonDecode(cachedGraduationJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedGraduation.length; i++) {
        if (cachedGraduation[i]['id'] == updatedGraduation['id']) {
          cachedGraduation[i] = updatedGraduation;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedGraduation));
      print("Graduation cache updated with realtime change for ${updatedGraduation['venue']}");
    }
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$label copied to clipboard')),
    );
  }

  void _openMap() {
    final double? latitude = widget.graduation['latitude'];
    final double? longitude = widget.graduation['longitude'];

    if (latitude != null && longitude != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TertiaryMapPage(
            institutionName: widget.collegeNameForBucket,
            collegeData: {'fullname': widget.collegeNameForBucket},
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location coordinates not available')),
      );
    }
  }

  String _formatDate() {
    final int? day = widget.graduation['day'];
    final int? month = widget.graduation['month'];
    final int? year = widget.graduation['year'];

    if (day != null && month != null && year != null) {
      final DateTime date = DateTime(year, month, day);
      return DateFormat('MMMM d, y').format(date);
    }
    return 'Date not specified';
  }

  String _formatTime() {
    final String startTime = widget.graduation['starttime'] ?? '';
    final String endTime = widget.graduation['endtime'] ?? '';

    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      return '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      return 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      return 'Ends at $endTime';
    }
    return 'Time not specified';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool currentIsDarkMode = widget.isDarkMode;
    final venue = widget.graduation['venue'] as String? ?? '';
    final alternateLocation = widget.graduation['alternatelocation'] as String? ?? '';
    final speaker = widget.graduation['speaker'] as String? ?? '';
    final about = widget.graduation['about'] as String? ?? '';

    final bool hasAlternateLocation = alternateLocation.isNotEmpty;
    final bool hasSpeaker = speaker.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Graduation Ceremony',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section - always display
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: Center(
                            child: Icon(
                              Icons.school,
                              size: 50,
                              color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      )
                    : Container(
                        height: 200,
                        color: theme.colorScheme.surfaceVariant,
                        child: Center(
                          child: Icon(
                            Icons.school,
                            size: 50,
                            color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Graduation details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                backgroundImage: (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                                    ? NetworkImage(_imageUrl) as ImageProvider
                                    : null,
                                child: (_imageUrl.isEmpty || _imageUrl == 'assets/placeholder_image.png')
                                    ? Icon(
                                        Icons.school,
                                        size: 30,
                                        color: currentIsDarkMode ? Colors.white : Colors.black,
                                      )
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      venue,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.calendar_today,
                                          size: 16,
                                          color: theme.colorScheme.primary,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          _formatDate(),
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          size: 16,
                                          color: theme.colorScheme.primary,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          _formatTime(),
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (hasSpeaker) ...[
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.person,
                                            size: 16,
                                            color: theme.colorScheme.primary,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'Speaker: $speaker',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: theme.colorScheme.onSurfaceVariant,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                    if (hasAlternateLocation) ...[
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.location_on_outlined,
                                            size: 16,
                                            color: theme.colorScheme.primary,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'Alternate Location: $alternateLocation',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: theme.colorScheme.onSurfaceVariant,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                          if (about.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              'About',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              about,
                              style: TextStyle(
                                fontSize: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),

                  // Location card
                  if (widget.graduation['latitude'] != null && widget.graduation['longitude'] != null) ...[
                    const SizedBox(height: 16),
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: InkWell(
                        onTap: _openMap,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Location',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  Icon(
                                    Icons.map,
                                    color: theme.colorScheme.primary,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Tap to view on map',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
