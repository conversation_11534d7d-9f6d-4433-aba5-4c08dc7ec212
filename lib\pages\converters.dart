// converters.dart
import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:flutter/services.dart';
import 'currency_converter_page.dart';
import 'length_converter_page.dart';
import 'weight_converter_page.dart';
import 'temperature_converter_page.dart';
import 'area_converter_page.dart';
import 'volume_converter_page.dart';
import 'speed_converter_page.dart';
import 'data_converter_page.dart';
import 'power_converter_page.dart';
import 'pressure_converter_page.dart';
import 'energy_converter_page.dart';
import 'time_converter_page.dart';
import 'angle_converter_page.dart';
import 'fuel_consumption_converter_page.dart';
import 'density_converter_page.dart';
import 'number_system_converter_page.dart';


class ConvertersPage extends StatefulWidget { // Renamed from CalculatePage
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ConvertersPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<ConvertersPage> createState() => _ConvertersPageState(); // Renamed State class
}

class _ConvertersPageState extends State<ConvertersPage> { // Renamed State class

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Converters', // Updated title
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Converter Items
                _buildGridItem(context, 'Currency Converter', Icons.monetization_on_outlined, theme),
                _buildGridItem(context, 'Length Converter', Icons.straighten, theme),
                _buildGridItem(context, 'Weight Converter', Icons.fitness_center, theme),
                _buildGridItem(context, 'Temperature Converter', Icons.thermostat_outlined, theme),
                _buildGridItem(context, 'Area Converter', Icons.texture, theme),
                _buildGridItem(context, 'Volume Converter', Icons.leaderboard, theme),
                _buildGridItem(context, 'Speed Converter', Icons.speed, theme),
                _buildGridItem(context, 'Data Converter', Icons.data_usage, theme),
                _buildGridItem(context, 'Power Converter', Icons.bolt, theme),
                _buildGridItem(context, 'Pressure Converter', Icons.compress, theme),
                _buildGridItem(context, 'Energy Converter', Icons.flash_on, theme),
                _buildGridItem(context, 'Time Converter', Icons.timer_outlined, theme),
                _buildGridItem(context, 'Angle Converter', Icons.compass_calibration, theme),
                _buildGridItem(context, 'Fuel Consumption Converter', Icons.local_gas_station, theme),
                _buildGridItem(context, 'Density Converter', Icons.layers, theme),
                _buildGridItem(context, 'Number System Converter', Icons.numbers, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Theme.of(context).brightness == Brightness.dark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                 ),
               ],
             ),
           ),
         ),
       ),
     );
   }

   Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
     final bool isDarkMode = theme.brightness == Brightness.dark;
     return Card(
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       child: InkWell(
         onTap: () {
           // Navigation Logic - Expand this for all new converters
           if (title == 'Currency Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const CurrencyConverterPage()),
             );
           } else if (title == 'Length Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const LengthConverterPage()),
             );
           } else if (title == 'Weight Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const WeightConverterPage()),
             );
           } else if (title == 'Temperature Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TemperatureConverterPage()),
             );
           } else if (title == 'Area Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AreaConverterPage()),
             );
           } else if (title == 'Volume Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const VolumeConverterPage()),
             );
           }
           // New Converter Navigation Items - Add else if blocks for each new title
           else if (title == 'Speed Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const SpeedConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Data Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DataConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Power Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PowerConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Pressure Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PressureConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Energy Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const EnergyConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Time Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TimeConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Angle Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AngleConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Fuel Consumption Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const FuelConsumptionConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Density Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DensityConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Number System Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const NumberSystemConverterPage()), // Placeholder page
             );
           }
         },
         child: Padding(
           padding: const EdgeInsets.all(8.0),
           child: Column(
             mainAxisAlignment: MainAxisAlignment.center,
             mainAxisSize: MainAxisSize.min,
             children: [
               Icon(
                 icon,
                 size: 32,
                 color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
               ),
               const SizedBox(height: 8),
               Flexible(
                 child: Text(
                   title,
                   textAlign: TextAlign.center,
                   overflow: TextOverflow.ellipsis,
                   maxLines: 2,
                   style: TextStyle(
                     fontSize: 14,
                     fontWeight: FontWeight.bold,
                     color: theme.colorScheme.onSurface,
                   ),
                 ),
               ),
             ],
           ),
         ),
       ),
     );
   }
 }