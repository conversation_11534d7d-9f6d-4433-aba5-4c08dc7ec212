import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'course_detail_page.dart';
import 'login_page.dart';

class CourseCatalogPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedCourseCatalog;
  final bool isFromDetailPage;

  const CourseCatalogPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedCourseCatalog,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _CourseCatalogPageState createState() => _CourseCatalogPageState();
}

class _CourseCatalogPageState extends State<CourseCatalogPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('course_catalog_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _courses = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedSchool = 'All Schools';
  List<String> _schools = ['All Schools'];
  String _selectedDepartment = 'All Departments';
  List<String> _departments = ['All Departments'];
  String _selectedMajor = 'All Majors';
  List<String> _majors = ['All Majors'];
  String _selectedYear = 'All Years';
  List<String> _years = ['All Years', '1', '2', '3', '4', '5'];
  bool _showCoreCoursesOnly = false;
  bool _showElectivesOnly = false;

  @override
  void initState() {
    super.initState();
    print("CourseCatalogPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedCourseCatalog != null &&
        widget.preloadedCourseCatalog!.isNotEmpty) {
      setState(() {
        _courses = List.from(widget.preloadedCourseCatalog!);
        _isLoading = false;
        _extractFilters();
      });
    } else {
      _loadCoursesFromDatabase();
    }
  }

  void _extractFilters() {
    Set<String> schoolsSet = {'All Schools'};
    Set<String> departmentsSet = {'All Departments'};
    Set<String> majorsSet = {'All Majors'};

    for (var course in _courses) {
      // Extract schools
      if (course['school'] != null && course['school'].toString().isNotEmpty) {
        schoolsSet.add(course['school']);
      }
      if (course['school2'] != null && course['school2'].toString().isNotEmpty) {
        schoolsSet.add(course['school2']);
      }
      if (course['school3'] != null && course['school3'].toString().isNotEmpty) {
        schoolsSet.add(course['school3']);
      }

      // Extract departments
      if (course['department'] != null && course['department'].toString().isNotEmpty) {
        departmentsSet.add(course['department']);
      }
      if (course['department2'] != null && course['department2'].toString().isNotEmpty) {
        departmentsSet.add(course['department2']);
      }
      if (course['department3'] != null && course['department3'].toString().isNotEmpty) {
        departmentsSet.add(course['department3']);
      }

      // Extract majors
      if (course['major'] != null && course['major'].toString().isNotEmpty) {
        majorsSet.add(course['major']);
      }
    }

    setState(() {
      _schools = schoolsSet.toList()..sort();
      _departments = departmentsSet.toList()..sort();
      _majors = majorsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final courseCatalogTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_coursecatalog';
    _realtimeChannel = Supabase.instance.client
        .channel('course_catalog_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: courseCatalogTableName,
      callback: (payload) async {
        print("Realtime update received for course catalog: ${payload.eventType}");
        _loadCoursesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadCoursesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _courses = [];
    });

    await _loadMoreCourses();
  }

  Future<void> _loadMoreCourses() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final courseCatalogTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_coursecatalog';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("or(modulecode.ilike.%${_searchQuery}%,modulename.ilike.%${_searchQuery}%)");
      }

      // Apply school filter
      if (_selectedSchool != 'All Schools') {
        conditions.add("or(school.eq.${_selectedSchool},school2.eq.${_selectedSchool},school3.eq.${_selectedSchool})");
      }

      // Apply department filter
      if (_selectedDepartment != 'All Departments') {
        conditions.add("or(department.eq.${_selectedDepartment},department2.eq.${_selectedDepartment},department3.eq.${_selectedDepartment})");
      }

      // Apply major filter
      if (_selectedMajor != 'All Majors') {
        conditions.add("major.eq.${_selectedMajor}");
      }

      // Apply year filter
      if (_selectedYear != 'All Years') {
        conditions.add("year.eq.${int.parse(_selectedYear)}");
      }

      // Apply core courses filter
      if (_showCoreCoursesOnly) {
        conditions.add("corecourse.eq.true");
      }

      // Apply electives filter
      if (_showElectivesOnly) {
        conditions.add("electivecourse.eq.true");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(courseCatalogTableName)
          .select('*')
          .or(conditions.join(','))
          .order('modulecode', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _courses.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractFilters();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading courses: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading courses: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreCourses();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadCoursesFromDatabase();
  }

  void _filterBySchool(String school) {
    setState(() {
      _selectedSchool = school;
    });
    _loadCoursesFromDatabase();
  }

  void _filterByDepartment(String department) {
    setState(() {
      _selectedDepartment = department;
    });
    _loadCoursesFromDatabase();
  }

  void _filterByMajor(String major) {
    setState(() {
      _selectedMajor = major;
    });
    _loadCoursesFromDatabase();
  }

  void _filterByYear(String year) {
    setState(() {
      _selectedYear = year;
    });
    _loadCoursesFromDatabase();
  }

  void _toggleCoreCoursesFilter(bool value) {
    setState(() {
      _showCoreCoursesOnly = value;
      if (value) {
        _showElectivesOnly = false;
      }
    });
    _loadCoursesFromDatabase();
  }

  void _toggleElectivesFilter(bool value) {
    setState(() {
      _showElectivesOnly = value;
      if (value) {
        _showCoreCoursesOnly = false;
      }
    });
    _loadCoursesFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Course Catalog',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by course code or name...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'School',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                        ),
                        value: _selectedSchool,
                        items: _schools.map((String school) {
                          return DropdownMenuItem<String>(
                            value: school,
                            child: Text(
                              school,
                              style: TextStyle(fontSize: 14),
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            _filterBySchool(newValue);
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Department',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                        ),
                        value: _selectedDepartment,
                        items: _departments.map((String department) {
                          return DropdownMenuItem<String>(
                            value: department,
                            child: Text(
                              department,
                              style: TextStyle(fontSize: 14),
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            _filterByDepartment(newValue);
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Major',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                        ),
                        value: _selectedMajor,
                        items: _majors.map((String major) {
                          return DropdownMenuItem<String>(
                            value: major,
                            child: Text(
                              major,
                              style: TextStyle(fontSize: 14),
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            _filterByMajor(newValue);
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Year',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          filled: true,
                          fillColor: theme.colorScheme.surface,
                        ),
                        value: _selectedYear,
                        items: _years.map((String year) {
                          return DropdownMenuItem<String>(
                            value: year,
                            child: Text(
                              year,
                              style: TextStyle(fontSize: 14),
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            _filterByYear(newValue);
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: CheckboxListTile(
                        title: Text(
                          'Core Courses Only',
                          style: TextStyle(fontSize: 14),
                        ),
                        value: _showCoreCoursesOnly,
                        onChanged: (bool? value) {
                          if (value != null) {
                            _toggleCoreCoursesFilter(value);
                          }
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        title: Text(
                          'Electives Only',
                          style: TextStyle(fontSize: 14),
                        ),
                        value: _showElectivesOnly,
                        onChanged: (bool? value) {
                          if (value != null) {
                            _toggleElectivesFilter(value);
                          }
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Courses list
          Expanded(
            child: VisibilityDetector(
              key: const Key('course_catalog_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _courses.isEmpty && !_isLoading) {
                  _loadCoursesFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadCoursesFromDatabase,
                child: _courses.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No courses found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _courses.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _courses.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildCourseCard(
                            _courses[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildCourseCard(
    Map<String, dynamic> course,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String moduleCode = course['modulecode'] ?? '';
    final String moduleName = course['modulename'] ?? 'Unknown';
    final String department = course['department'] ?? '';
    final int? year = course['year'];
    final String term = course['term'] ?? '';
    final int? credits = course['credits'];
    final bool isCore = course['corecourse'] == true;
    final bool isElective = course['electivecourse'] == true;
    final bool isOffered = course['offered'] == true;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CourseDetailPage(
                course: course,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.book,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            moduleCode.isNotEmpty ? '$moduleCode: $moduleName' : moduleName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (!isOffered)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Not Offered',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          if (year != null)
                            Text(
                              'Year $year',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (year != null && term.isNotEmpty)
                            Text(
                              ' • ',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (term.isNotEmpty)
                            Text(
                              term,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if ((year != null || term.isNotEmpty) && credits != null)
                            Text(
                              ' • ',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          if (credits != null)
                            Text(
                              '$credits credits',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Wrap(
                        spacing: 8,
                        children: [
                          if (isCore)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primaryContainer,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Core',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                              ),
                            ),
                          if (isElective)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.secondaryContainer,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Elective',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSecondaryContainer,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
