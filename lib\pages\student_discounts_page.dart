import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'student_discount_detail_page.dart';
import 'login_page.dart';

class StudentDiscountsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedStudentDiscounts;
  final bool isFromDetailPage;

  const StudentDiscountsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedStudentDiscounts,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _StudentDiscountsPageState createState() => _StudentDiscountsPageState();
}

class _StudentDiscountsPageState extends State<StudentDiscountsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('student_discounts_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _studentDiscounts = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("StudentDiscountsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedStudentDiscounts != null &&
        widget.preloadedStudentDiscounts!.isNotEmpty) {
      setState(() {
        _studentDiscounts = List.from(widget.preloadedStudentDiscounts!);
        _isLoading = false;
      });
    } else {
      _loadStudentDiscountsFromDatabase();
    }
  }

  void _setupRealtime() {
    final studentDiscountsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_studentdiscounts';
    _realtimeChannel = Supabase.instance.client
        .channel('student_discounts_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: studentDiscountsTableName,
      callback: (payload) async {
        print("Realtime update received for student discounts: ${payload.eventType}");
        _loadStudentDiscountsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadStudentDiscountsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _studentDiscounts = [];
    });

    await _loadMoreStudentDiscounts();
  }

  Future<void> _loadMoreStudentDiscounts() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final studentDiscountsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_studentdiscounts';
      final response = await Supabase.instance.client
          .from(studentDiscountsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _studentDiscounts.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading student discounts: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading student discounts: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreStudentDiscounts();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Student Discounts',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('student_discounts_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _studentDiscounts.isEmpty && !_isLoading) {
            _loadStudentDiscountsFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadStudentDiscountsFromDatabase,
          child: _studentDiscounts.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No student discounts found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _studentDiscounts.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _studentDiscounts.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildStudentDiscountCard(
                      _studentDiscounts[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildStudentDiscountCard(
    Map<String, dynamic> discount,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = discount['fullname'] ?? 'Unknown';
    final String hours = discount['hours'] ?? '';
    final String payment = discount['payment'] ?? '';
    final String about = discount['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StudentDiscountDetailPage(
                studentDiscount: discount,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.loyalty,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
