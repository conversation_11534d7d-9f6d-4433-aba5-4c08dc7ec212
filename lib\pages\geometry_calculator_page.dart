import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GeometryCalculatorPage extends StatefulWidget {
  const GeometryCalculatorPage({Key? key}) : super(key: key);

  @override
  _GeometryCalculatorPageState createState() => _GeometryCalculatorPageState();
}

class _GeometryCalculatorPageState extends State<GeometryCalculatorPage> {
  String _shapeType = 'Circle'; // Default shape
  double _dimension1 = 0.0;
  double _dimension2 = 0.0;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Geometry Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  DropdownButton<String>(
                    value: _shapeType,
                    dropdownColor: theme.colorScheme.surface,
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    items: <String>['Circle', 'Square', 'Rectangle', 'Triangle'].map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, style: TextStyle(color: theme.colorScheme.onSurface)),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        _shapeType = newValue!;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  if (_shapeType == 'Circle')
                    TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'Radius',
                        border: const OutlineInputBorder(),
                        labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                      ),
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      onChanged: (value) {
                        setState(() {
                          _dimension1 = double.tryParse(value) ?? 0.0;
                        });
                      },
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    ),
                  if (_shapeType == 'Square')
                    TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'Side Length',
                        border: const OutlineInputBorder(),
                        labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                      ),
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      onChanged: (value) {
                        setState(() {
                          _dimension1 = double.tryParse(value) ?? 0.0;
                        });
                      },
                      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                    ),
                  if (_shapeType == 'Rectangle')
                    Column(
                      children: [
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Length',
                            border: const OutlineInputBorder(),
                            labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                            enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                            focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                          ),
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          onChanged: (value) {
                            setState(() {
                              _dimension1 = double.tryParse(value) ?? 0.0;
                            });
                          },
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Width',
                            border: const OutlineInputBorder(),
                            labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                            enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                            focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                          ),
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          onChanged: (value) {
                            setState(() {
                              _dimension2 = double.tryParse(value) ?? 0.0;
                            });
                          },
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        ),
                      ],
                    ),
                  if (_shapeType == 'Triangle')
                    Column(
                      children: [
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Base',
                            border: const OutlineInputBorder(),
                            labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                            enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                            focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                          ),
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          onChanged: (value) {
                            setState(() {
                              _dimension1 = double.tryParse(value) ?? 0.0;
                            });
                          },
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Height',
                            border: const OutlineInputBorder(),
                            labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                            enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                            focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                          ),
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          onChanged: (value) {
                            setState(() {
                              _dimension2 = double.tryParse(value) ?? 0.0;
                            });
                          },
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        ),
                      ],
                    ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      setState(() {
                        if (_shapeType == 'Circle') {
                          _result = 'Area: ${(3.14159 * _dimension1 * _dimension1).toStringAsFixed(2)}';
                        } else if (_shapeType == 'Square') {
                          _result = 'Area: ${(_dimension1 * _dimension1).toStringAsFixed(2)}';
                        } else if (_shapeType == 'Rectangle') {
                          _result = 'Area: ${(_dimension1 * _dimension2).toStringAsFixed(2)}';
                        } else if (_shapeType == 'Triangle') {
                          _result = 'Area: ${(0.5 * _dimension1 * _dimension2).toStringAsFixed(2)}';
                        }
                      });
                    },
                    child: const Text('Calculate Area'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_result',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}