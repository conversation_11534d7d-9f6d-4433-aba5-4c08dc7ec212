// tradition_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class TraditionDetailPage extends StatefulWidget {
  final Map<String, dynamic> tradition;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const TraditionDetailPage({
    Key? key,
    required this.tradition,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<TraditionDetailPage> createState() => _TraditionDetailPageState();
}

class _TraditionDetailPageState extends State<TraditionDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _traditionRealtimeChannel; // Realtime channel for tradition updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupTraditionRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _traditionRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.tradition['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupTraditionRealtimeListener() {
    final traditionsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_traditions';
    _traditionRealtimeChannel = Supabase.instance.client
        .channel('tradition_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: traditionsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current tradition's ID
        if (payload.newRecord['id'] == widget.tradition['id']) {
          print("Realtime UPDATE event received for THIS tradition (manual filter applied): ${widget.tradition['fullname']}");
          _fetchUpdatedTraditionData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER tradition, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedTraditionData() async {
    final traditionsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_traditions';
    try {
      final updatedTraditionResponse = await Supabase.instance.client
          .from(traditionsTableName)
          .select('*')
          .eq('id', widget.tradition['id'])
          .single();

      if (mounted && updatedTraditionResponse != null) {
        Map<String, dynamic> updatedTradition = Map.from(updatedTraditionResponse);
        // Update the widget.tradition with the new data
        setState(() {
          widget.tradition.clear(); // Clear old data
          widget.tradition.addAll(updatedTradition); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Tradition data updated in detail page for ${widget.tradition['fullname']}");
          _updateTraditionsCache(updatedTradition); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated tradition data: $error");
    }
  }

  Future<void> _updateTraditionsCache(Map<String, dynamic> updatedTradition) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'traditions_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedTraditionsJson = prefs.getString(cacheKey);

    if (cachedTraditionsJson != null) {
      List<Map<String, dynamic>> cachedTraditions = (jsonDecode(cachedTraditionsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the tradition in the cached list
      for (int i = 0; i < cachedTraditions.length; i++) {
        if (cachedTraditions[i]['id'] == updatedTradition['id']) {
          cachedTraditions[i] = updatedTradition;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedTraditions));
      print("Traditions cache updated with realtime change for ${updatedTradition['fullname']}");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final about = widget.tradition['about'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.tradition['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
