import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart'; // Import the package

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _fetchColleges();
    _setupRealtime();
  }

  Future<void> _fetchColleges() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('colleges')
          .select()
          .order('fullname', ascending: true);

      await _updateCollegeImageUrls(response);

      setState(() {
        _colleges = response;
        _isLoading = false;
      });
    } catch (error) {
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching colleges: $error')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateCollegeImageUrls(List<Map<String, dynamic>> colleges) async {
    for (final college in colleges) {
      final fullname = college['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = '';

      try {
        await Supabase.instance.client.storage.from('colleges').download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from('colleges').download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
          } catch (e) {
            // Image not found, use placeholder
          }
        }
      }
      college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
    }
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'colleges',
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              final newCollege = payload.newRecord;
              await _updateCollegeImageUrls([newCollege]);
              setState(() {
                _colleges = [..._colleges, newCollege];
                _colleges.sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
              });
            } else if (payload.eventType == PostgresChangeEvent.update) {
              final updatedCollege = payload.newRecord;
              await _updateCollegeImageUrls([updatedCollege]);
              setState(() {
                _colleges = _colleges.map((college) {
                  return college['id'] == updatedCollege['id'] ? updatedCollege : college;
                }).toList();
                _colleges.sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
              });
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              final deletedCollegeId = payload.oldRecord['id'];
              setState(() {
                _colleges.removeWhere((college) => college['id'] == deletedCollegeId);
              });
            }
          },
        )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed) {
      setState(() {});
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, String schoolName) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TertiaryDetailPage(
            schoolName: schoolName,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Tertiary',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ListView.builder(
                key: _listKey,
                controller: _scrollController,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                itemCount: _colleges.length,
                itemBuilder: (context, index) {
                  final university = _colleges[index];
                  return Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: CircleAvatar(
                        backgroundColor: theme.colorScheme.secondary.withOpacity(0.1),
                        backgroundImage: university['image_url'] == 'assets/placeholder_image.png'
                            ? AssetImage(university['image_url']) as ImageProvider
                            : CachedNetworkImageProvider( // Use CachedNetworkImageProvider
                                university['image_url'],
                              ) as ImageProvider,
                      ),
                      title: Text(
                        university['fullname'] ?? 'Unnamed College',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      subtitle: Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          '${university['city'] ?? ''}${university['state'] != null && university['city'] != null ? ', ' : ''}${university['state'] ?? ''}',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.secondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      onTap: () => _navigateToDetail(context, university['fullname'] ?? ''),
                    ),
                  );
                },
              ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}