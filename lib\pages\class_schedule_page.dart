import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'class_detail_page.dart';

class ClassSchedulePage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ClassSchedulePage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ClassSchedulePage> createState() => _ClassSchedulePageState();
}

class _ClassSchedulePageState extends State<ClassSchedulePage> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<dynamic>> _events;
  late List<dynamic> _selectedEvents;
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _classSchedules = [];
  String _selectedFilter = 'All';
  List<String> _departments = ['All'];
  List<String> _majors = ['All'];
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = [];
    _fetchClassSchedules();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchClassSchedules() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_classschedules';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final classSchedules = List<Map<String, dynamic>>.from(response);
      
      // Extract unique departments and majors for filters
      final Set<String> departments = {'All'};
      final Set<String> majors = {'All'};
      
      for (var schedule in classSchedules) {
        if (schedule['department'] != null && schedule['department'].toString().isNotEmpty) {
          departments.add(schedule['department']);
        }
        if (schedule['major'] != null && schedule['major'].toString().isNotEmpty) {
          majors.add(schedule['major']);
        }
      }

      // Convert class schedules to events
      final Map<DateTime, List<dynamic>> events = {};
      
      for (var schedule in classSchedules) {
        // Check if the class has start and end dates
        if (schedule['startday'] != null && 
            schedule['startmonth'] != null && 
            schedule['startyear'] != null &&
            schedule['endday'] != null && 
            schedule['endmonth'] != null && 
            schedule['endyear'] != null) {
          
          final startDate = DateTime(
            schedule['startyear'],
            schedule['startmonth'],
            schedule['startday'],
          );
          
          final endDate = DateTime(
            schedule['endyear'],
            schedule['endmonth'],
            schedule['endday'],
          );
          
          // Generate all dates between start and end date
          DateTime currentDate = startDate;
          while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
            // Check if the class occurs on this day of the week
            bool shouldAdd = false;
            
            switch (currentDate.weekday) {
              case DateTime.monday:
                shouldAdd = schedule['_mon'] == true;
                break;
              case DateTime.tuesday:
                shouldAdd = schedule['_tue'] == true;
                break;
              case DateTime.wednesday:
                shouldAdd = schedule['_wed'] == true;
                break;
              case DateTime.thursday:
                shouldAdd = schedule['_thur'] == true;
                break;
              case DateTime.friday:
                shouldAdd = schedule['_fri'] == true;
                break;
              case DateTime.saturday:
                shouldAdd = schedule['_sat'] == true;
                break;
              case DateTime.sunday:
                shouldAdd = schedule['_sun'] == true;
                break;
            }
            
            if (shouldAdd) {
              final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
              if (events[key] == null) {
                events[key] = [];
              }
              events[key]!.add(schedule);
            }
            
            // Move to next day
            currentDate = currentDate.add(const Duration(days: 1));
          }
        }
      }

      setState(() {
        _classSchedules = classSchedules;
        _departments = departments.toList();
        _majors = majors.toList();
        _events = events;
        _selectedEvents = _getEventsForDay(_selectedDay);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading class schedules: $e';
      });
      print('Error fetching class schedules: $e');
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  void _filterClasses(String filter) {
    setState(() {
      _selectedFilter = filter;
      // Re-apply filters and update events
      _updateFilteredEvents();
    });
  }

  void _updateFilteredEvents() {
    final Map<DateTime, List<dynamic>> filteredEvents = {};
    
    for (var schedule in _classSchedules) {
      // Apply department filter
      if (_selectedFilter != 'All' && schedule['department'] != _selectedFilter) {
        continue;
      }
      
      // Apply search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        final name = schedule['fullname']?.toString().toLowerCase() ?? '';
        final instructor = schedule['instructor']?.toString().toLowerCase() ?? '';
        final department = schedule['department']?.toString().toLowerCase() ?? '';
        final major = schedule['major']?.toString().toLowerCase() ?? '';
        
        if (!name.contains(searchTerm) && 
            !instructor.contains(searchTerm) && 
            !department.contains(searchTerm) &&
            !major.contains(searchTerm)) {
          continue;
        }
      }
      
      // Check if the class has start and end dates
      if (schedule['startday'] != null && 
          schedule['startmonth'] != null && 
          schedule['startyear'] != null &&
          schedule['endday'] != null && 
          schedule['endmonth'] != null && 
          schedule['endyear'] != null) {
        
        final startDate = DateTime(
          schedule['startyear'],
          schedule['startmonth'],
          schedule['startday'],
        );
        
        final endDate = DateTime(
          schedule['endyear'],
          schedule['endmonth'],
          schedule['endday'],
        );
        
        // Generate all dates between start and end date
        DateTime currentDate = startDate;
        while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
          // Check if the class occurs on this day of the week
          bool shouldAdd = false;
          
          switch (currentDate.weekday) {
            case DateTime.monday:
              shouldAdd = schedule['_mon'] == true;
              break;
            case DateTime.tuesday:
              shouldAdd = schedule['_tue'] == true;
              break;
            case DateTime.wednesday:
              shouldAdd = schedule['_wed'] == true;
              break;
            case DateTime.thursday:
              shouldAdd = schedule['_thur'] == true;
              break;
            case DateTime.friday:
              shouldAdd = schedule['_fri'] == true;
              break;
            case DateTime.saturday:
              shouldAdd = schedule['_sat'] == true;
              break;
            case DateTime.sunday:
              shouldAdd = schedule['_sun'] == true;
              break;
          }
          
          if (shouldAdd) {
            final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
            if (filteredEvents[key] == null) {
              filteredEvents[key] = [];
            }
            filteredEvents[key]!.add(schedule);
          }
          
          // Move to next day
          currentDate = currentDate.add(const Duration(days: 1));
        }
      }
    }
    
    setState(() {
      _events = filteredEvents;
      _selectedEvents = _getEventsForDay(_selectedDay);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Class Schedules',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchClassSchedules,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search classes...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _searchController.clear();
                                      _updateFilteredEvents();
                                    });
                                  },
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          _updateFilteredEvents();
                        },
                      ),
                    ),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        children: _departments.map((department) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: FilterChip(
                              label: Text(department),
                              selected: _selectedFilter == department,
                              onSelected: (selected) {
                                if (selected) {
                                  _filterClasses(department);
                                }
                              },
                              backgroundColor: theme.colorScheme.surface,
                              selectedColor: Colors.white,
                              labelStyle: TextStyle(
                                color: _selectedFilter == department
                                    ? Colors.black
                                    : (currentIsDarkMode ? Colors.white : Colors.black),
                              ),
                              side: BorderSide(
                                color: _selectedFilter == department
                                    ? Colors.black
                                    : Colors.grey.shade300,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TableCalendar(
                      firstDay: DateTime.utc(2020, 1, 1),
                      lastDay: DateTime.utc(2030, 12, 31),
                      focusedDay: _focusedDay,
                      calendarFormat: _calendarFormat,
                      eventLoader: _getEventsForDay,
                      selectedDayPredicate: (day) {
                        return isSameDay(_selectedDay, day);
                      },
                      onDaySelected: _onDaySelected,
                      onFormatChanged: (format) {
                        setState(() {
                          _calendarFormat = format;
                        });
                      },
                      onPageChanged: (focusedDay) {
                        _focusedDay = focusedDay;
                      },
                      calendarStyle: CalendarStyle(
                        markersMaxCount: 3,
                        markerDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        todayDecoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        selectedDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      headerStyle: HeaderStyle(
                        formatButtonVisible: true,
                        titleCentered: true,
                        formatButtonShowsNext: false,
                        formatButtonDecoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        formatButtonTextStyle: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _selectedEvents.isEmpty
                          ? Center(
                              child: Text(
                                'No classes scheduled for this day',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _selectedEvents.length,
                              itemBuilder: (context, index) {
                                final event = _selectedEvents[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 4.0,
                                  ),
                                  child: ListTile(
                                    title: Text(
                                      event['fullname'] ?? 'Unnamed Class',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        if (event['instructor'] != null)
                                          Text('Instructor: ${event['instructor']}'),
                                        if (event['starttime'] != null && event['endtime'] != null)
                                          Text('Time: ${event['starttime']} - ${event['endtime']}'),
                                        if (event['building'] != null || event['room'] != null)
                                          Text(
                                            'Location: ${[
                                              if (event['building'] != null) event['building'],
                                              if (event['room'] != null) event['room'],
                                            ].join(', ')}',
                                          ),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ClassDetailPage(
                                            classData: event,
                                            institutionName: widget.institutionName,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
