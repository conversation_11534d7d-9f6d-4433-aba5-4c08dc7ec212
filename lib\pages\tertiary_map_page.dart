import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_cancellable_tile_provider/flutter_map_cancellable_tile_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'building_detail_page.dart';

class TertiaryMapPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryMapPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryMapPage> createState() => _TertiaryMapPageState();
}

class _TertiaryMapPageState extends State<TertiaryMapPage> {
  final TextEditingController _searchController = TextEditingController();
  final MapController _mapController = MapController();
  final FocusNode _searchFocusNode = FocusNode();
  String _selectedFilter = 'All';
  bool _isSatelliteView = false;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _buildings = [];
  List<Map<String, dynamic>> _housing = [];
  List<Map<String, dynamic>> _allLocations = [];
  List<Map<String, dynamic>> _filteredLocations = [];
  List<Map<String, dynamic>> _locationsWithCoordinates = [];
  List<Map<String, dynamic>> _searchSuggestions = [];
  bool _showSuggestions = false;
  Map<String, dynamic>? _selectedLocation;
  LatLng _center = LatLng(-13.9626, 33.7741); // Default center, will be updated
  LatLng? _userLocation;
  bool _isLoadingLocation = false;
  bool _locationPermissionDenied = false;
  late final RealtimeChannel _buildingsChannel;
  late final RealtimeChannel _housingChannel;
  bool _mapInitialized = false;
  List<String> _availableFilters = ['All'];
  List<LatLng> _pathPoints = [];

  final List<String> _filters = [
    'All',
    'Academic Buildings',
    'Residences',
    'Dining',
    'Athletics',
    'Parking',
    'Libraries',
    'Study Spaces',
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
    _setupRealtimeListeners();
    _getUserLocation();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _buildingsChannel.unsubscribe();
    _housingChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _getUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('Location services are disabled');
        setState(() {
          _isLoadingLocation = false;
          _locationPermissionDenied = true;
        });
        return;
      }

      // Check for location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('Location permissions are denied');
          setState(() {
            _isLoadingLocation = false;
            _locationPermissionDenied = true;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('Location permissions are permanently denied');
        setState(() {
          _isLoadingLocation = false;
          _locationPermissionDenied = true;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition();
      print('Got user location: ${position.latitude}, ${position.longitude}');

      setState(() {
        _userLocation = LatLng(position.latitude, position.longitude);
        _isLoadingLocation = false;
      });
    } catch (e) {
      print('Error getting location: $e');
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _loadData() async {
    if (_isLoading && _allLocations.isNotEmpty) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Try to load from cache first
      final cachedBuildings = await _loadFromCache('building');
      final cachedHousing = await _loadFromCache('housing');

      if ((cachedBuildings != null && cachedBuildings.isNotEmpty) ||
          (cachedHousing != null && cachedHousing.isNotEmpty)) {
        // Combine data
        List<Map<String, dynamic>> allCachedLocations = [];

        if (cachedBuildings != null) {
          _buildings = cachedBuildings;
          allCachedLocations.addAll(cachedBuildings);
        }

        if (cachedHousing != null) {
          _housing = cachedHousing;
          allCachedLocations.addAll(cachedHousing);
        }

        // Filter locations with valid coordinates
        final locationsWithCoords = allCachedLocations.where((location) {
          final double lat = location['latitude'] ?? 0.0;
          final double lng = location['longitude'] ?? 0.0;
          return lat != 0.0 && lng != 0.0;
        }).toList();

        setState(() {
          _allLocations = allCachedLocations;
          _filteredLocations = allCachedLocations;
          _locationsWithCoordinates = locationsWithCoords;
          _isLoading = false;

          if (!_mapInitialized && locationsWithCoords.isNotEmpty) {
            _updateMapCenter();
            _mapInitialized = true;
          }
        });
        print('Loaded ${allCachedLocations.length} locations from cache, ${locationsWithCoords.length} with coordinates');
      }

      // Then fetch from Supabase
      await Future.wait([
        _fetchBuildingsFromSupabase(),
        _fetchHousingFromSupabase()
      ]);

      // Combine all data
      _combineLocationData();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = _allLocations.isEmpty; // Only show error if we have no data at all
        _errorMessage = 'Error loading location data: $e';
      });
      print('Error in _loadData: $e');
    }
  }

  void _combineLocationData() {
    // Combine buildings and housing data
    List<Map<String, dynamic>> allLocations = [];
    allLocations.addAll(_buildings);
    allLocations.addAll(_housing);

    // Filter locations with valid coordinates
    final locationsWithCoords = allLocations.where((location) {
      final double lat = location['latitude'] ?? 0.0;
      final double lng = location['longitude'] ?? 0.0;
      return lat != 0.0 && lng != 0.0;
    }).toList();

    // Set fixed filters
    List<String> availableFilters = [
      'All',
      'Academic Buildings',
      'Residences',
      'Dining',
      'Athletics',
      'Parking',
      'Libraries',
      'Study Spaces',
      'Housing'
    ];

    setState(() {
      _allLocations = allLocations;
      _filteredLocations = allLocations;
      _locationsWithCoordinates = locationsWithCoords;
      _availableFilters = availableFilters;

      if (!_mapInitialized && locationsWithCoords.isNotEmpty) {
        _updateMapCenter();
        _mapInitialized = true;
      }
    });

    print('Combined ${_buildings.length} buildings and ${_housing.length} housing entries into ${allLocations.length} locations');
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache(String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${dataType}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      print('Loading $dataType from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} $dataType entries in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached $dataType found');
      }
    } catch (e) {
      print('Error loading $dataType from cache: $e');
    }
    return null;
  }

  Future<void> _fetchBuildingsFromSupabase() async {
    try {
      final buildingsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_building';
      print('Fetching from table: $buildingsTableName');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .order('fullname', ascending: true);

      final buildings = List<Map<String, dynamic>>.from(response);
      print('Fetched ${buildings.length} buildings from Supabase');

      // Add a type field to distinguish buildings from housing
      for (var building in buildings) {
        building['location_type'] = 'building';
      }

      // Save to cache
      await _saveToCache(buildings, 'building');

      if (mounted) {
        setState(() {
          _buildings = buildings;
        });
      }
    } catch (e) {
      print('Error fetching buildings from Supabase: $e');
    }
  }

  Future<void> _fetchHousingFromSupabase() async {
    try {
      final housingTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_housing';
      print('Fetching from table: $housingTableName');

      final response = await Supabase.instance.client
          .from(housingTableName)
          .select('*')
          .order('fullname', ascending: true);

      final housing = List<Map<String, dynamic>>.from(response);
      print('Fetched ${housing.length} housing entries from Supabase');

      // Add a type field to distinguish housing from buildings
      for (var house in housing) {
        house['location_type'] = 'housing';
      }

      // Save to cache
      await _saveToCache(housing, 'housing');

      if (mounted) {
        setState(() {
          _housing = housing;
        });
      }
    } catch (e) {
      print('Error fetching housing from Supabase: $e');
    }
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data, String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${dataType}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Saved ${data.length} $dataType entries to cache');
    } catch (e) {
      print('Error saving $dataType to cache: $e');
    }
  }

  void _setupRealtimeListeners() {
    _setupBuildingRealtimeListener();
    _setupHousingRealtimeListener();
  }

  void _setupBuildingRealtimeListener() {
    final buildingsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_building';
    print('Setting up realtime listener for table: $buildingsTableName');
    _buildingsChannel = Supabase.instance.client
        .channel('buildings_map_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: buildingsTableName,
          callback: (payload) {
            print('Realtime update received for buildings');
            _fetchBuildingsFromSupabase().then((_) => _combineLocationData());
          },
        )
        .subscribe();
  }

  void _setupHousingRealtimeListener() {
    final housingTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_housing';
    print('Setting up realtime listener for table: $housingTableName');
    _housingChannel = Supabase.instance.client
        .channel('housing_map_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: housingTableName,
          callback: (payload) {
            print('Realtime update received for housing');
            _fetchHousingFromSupabase().then((_) => _combineLocationData());
          },
        )
        .subscribe();
  }



  void _updateMapCenter() {
    print('Updating map center...');
    // Find a location with mapreferencepoint = 'yes'
    Map<String, dynamic>? referenceLocation;

    try {
      // First try to find a location with mapreferencepoint = 'yes'
      referenceLocation = _locationsWithCoordinates.firstWhere(
        (location) => location['mapreferencepoint'] == 'yes',
      );
      print('Found reference location: ${referenceLocation['fullname']}');
    } catch (e) {
      print('No location with mapreferencepoint=yes found, using fallback');
      // If no location with mapreferencepoint='yes' is found, use the first location with coordinates
      if (_locationsWithCoordinates.isNotEmpty) {
        referenceLocation = _locationsWithCoordinates.first;
        print('Using first location with coordinates: ${referenceLocation['fullname']}');
      } else {
        print('No locations with coordinates found, using default center');
      }
    }

    if (referenceLocation != null) {
      final double latitude = referenceLocation['latitude'] ?? -13.9626;
      final double longitude = referenceLocation['longitude'] ?? 33.7741;

      if (latitude != 0.0 && longitude != 0.0) {
        setState(() {
          _center = LatLng(latitude, longitude);
        });

        // Move map to the new center
        try {
          _mapController.move(_center, _mapController.camera.zoom);
          print('Map center updated to: $_center');
        } catch (e) {
          print('Error moving map: $e');
        }
      }
    }
  }

  void _filterBuildings(String filter) {
    setState(() {
      _selectedFilter = filter;

      if (filter == 'All') {
        _filteredLocations = List.from(_allLocations);
      } else {
        _filteredLocations = _allLocations.where((location) {
          switch (filter) {
            case 'Academic Buildings':
              return location['school'] == true || location['lab'] == true;
            case 'Residences':
              return location['residence'] == true || location['housing'] == true;
            case 'Dining':
              return location['dining'] == true;
            case 'Athletics':
              return location['fitnessspace'] == true || location['athleticspace'] == true;
            case 'Parking':
              return location['parking'] == true;
            case 'Libraries':
              return location['library'] == true;
            case 'Study Spaces':
              return location['studyspace'] == true;
            case 'Housing':
              return location['location_type'] == 'housing';
            default:
              return true;
          }
        }).toList();
      }
    });
  }

  void _searchBuildings(String query) {
    if (query.isEmpty) {
      setState(() {
        _showSuggestions = false;
        _searchSuggestions = [];
        _selectedLocation = null;
      });
      _filterBuildings(_selectedFilter); // Reset to current filter
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    // Generate search suggestions
    final suggestions = _locationsWithCoordinates.where((location) {
      final name = location['fullname']?.toString().toLowerCase() ?? '';
      final locationText = location['location']?.toString().toLowerCase() ?? '';
      final about = location['about']?.toString().toLowerCase() ?? '';

      return name.contains(lowercaseQuery) ||
             locationText.contains(lowercaseQuery) ||
             about.contains(lowercaseQuery);
    }).toList();

    // Sort suggestions by relevance (exact matches first, then starts with, then contains)
    suggestions.sort((a, b) {
      final aName = a['fullname']?.toString().toLowerCase() ?? '';
      final bName = b['fullname']?.toString().toLowerCase() ?? '';

      // Exact match gets highest priority
      if (aName == lowercaseQuery && bName != lowercaseQuery) return -1;
      if (bName == lowercaseQuery && aName != lowercaseQuery) return 1;

      // Starts with gets second priority
      if (aName.startsWith(lowercaseQuery) && !bName.startsWith(lowercaseQuery)) return -1;
      if (bName.startsWith(lowercaseQuery) && !aName.startsWith(lowercaseQuery)) return 1;

      // Default to alphabetical order
      return aName.compareTo(bName);
    });

    setState(() {
      _searchSuggestions = suggestions;
      _showSuggestions = suggestions.isNotEmpty;

      // Also filter the locations on the map
      _filteredLocations = _allLocations.where((location) {
        final name = location['fullname']?.toString().toLowerCase() ?? '';
        final locationText = location['location']?.toString().toLowerCase() ?? '';
        final about = location['about']?.toString().toLowerCase() ?? '';

        return name.contains(lowercaseQuery) ||
               locationText.contains(lowercaseQuery) ||
               about.contains(lowercaseQuery);
      }).toList();
    });
  }

  void _selectLocation(Map<String, dynamic> location) {
    // Hide suggestions
    setState(() {
      _showSuggestions = false;
      _selectedLocation = location;
      _searchController.text = location['fullname'] ?? '';
    });

    // Get coordinates
    final double latitude = location['latitude'] ?? 0.0;
    final double longitude = location['longitude'] ?? 0.0;

    if (latitude != 0.0 && longitude != 0.0) {
      // Zoom to the location (using a lower zoom level to show context)
      _mapController.move(LatLng(latitude, longitude), 15.0); // Zoom level 15 for better context

      // Clear focus from search field
      _searchFocusNode.unfocus();

      // If we have user location, draw a path to the selected location
      if (_userLocation != null) {
        _drawPathToLocation(latitude, longitude);
      }
    }
  }

  // Draw a path from user location to the selected location
  void _drawPathToLocation(double destinationLat, double destinationLng) {
    if (_userLocation == null) return;

    // Set the path points
    setState(() {
      _pathPoints = [
        _userLocation!,
        LatLng(destinationLat, destinationLng)
      ];
    });

    // Calculate the bounds to fit both points
    final bounds = LatLngBounds.fromPoints(_pathPoints);

    // Zoom out to show both points with some padding
    _mapController.fitCamera(
      CameraFit.bounds(
        bounds: bounds,
        padding: const EdgeInsets.all(50.0),
      ),
    );

    print('Drawing path from ${_userLocation!.latitude},${_userLocation!.longitude} to $destinationLat,$destinationLng');
  }

  String get _mapUrl {
    if (_isSatelliteView) {
      return widget.isDarkMode
          ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
          : 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
    } else {
      return 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Map',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          // Map Type Toggle
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: Icon(
                _isSatelliteView ? Icons.map_outlined : Icons.satellite_alt_outlined,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                setState(() {
                  _isSatelliteView = !_isSatelliteView;
                });
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search Bar with Suggestions
                Column(
                  children: [
                    TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        hintText: 'Search locations...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  setState(() {
                                    _searchController.clear();
                                    _showSuggestions = false;
                                    _searchSuggestions = [];
                                    _selectedLocation = null;
                                    _pathPoints = [];
                                    _filterBuildings(_selectedFilter);
                                  });
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(15),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surface,
                      ),
                      onChanged: (value) {
                        _searchBuildings(value);
                      },
                      onSubmitted: (value) {
                        if (_searchSuggestions.isNotEmpty) {
                          _selectLocation(_searchSuggestions.first);
                        }
                      },
                    ),
                    // Search Suggestions
                    if (_showSuggestions)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        constraints: const BoxConstraints(
                          maxHeight: 200,
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _searchSuggestions.length,
                          itemBuilder: (context, index) {
                            final building = _searchSuggestions[index];
                            final name = building['fullname'] ?? 'Unnamed';
                            final location = building['location'] ?? '';

                            return ListTile(
                              title: Text(name),
                              subtitle: location.isNotEmpty ? Text(location) : null,
                              leading: const Icon(Icons.location_on),
                              dense: true,
                              onTap: () => _selectLocation(building),
                            );
                          },
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                // Filter Chips
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _availableFilters.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: FilterChip(
                          label: Text(
                            _availableFilters[index],
                            style: TextStyle(
                              color: _selectedFilter == _availableFilters[index] ? Colors.black : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                          ),
                          selected: _selectedFilter == _availableFilters[index],
                          showCheckmark: false,
                          backgroundColor: theme.colorScheme.surface,
                          selectedColor: Colors.white,
                          side: BorderSide(
                            color: _selectedFilter == _availableFilters[index]
                                ? Colors.black
                                : Colors.grey.shade300,
                            width: 0.5,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          onSelected: (selected) {
                            if (selected) {
                              _filterBuildings(_availableFilters[index]);
                              // Clear search when changing filters
                              if (_searchController.text.isNotEmpty) {
                                setState(() {
                                  _searchController.clear();
                                  _showSuggestions = false;
                                  _searchSuggestions = [];
                                  _selectedLocation = null;
                                  _pathPoints = [];
                                });
                              }
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Map
          Expanded(
            child: _hasError && _allLocations.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error, size: 48),
                        const SizedBox(height: 16),
                        Text(_errorMessage),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : Stack(
                    children: [
                      if (_isLoading && _allLocations.isEmpty)
                        const Center(child: CircularProgressIndicator()),
                      FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          initialCenter: _center,
                          initialZoom: 13.0,
                          keepAlive: true,
                        ),
                        children: [
                          TileLayer(
                            urlTemplate: _mapUrl,
                            userAgentPackageName: 'com.harmonizr.app',
                            tileProvider: CancellableNetworkTileProvider(),
                            maxZoom: 20,
                            keepBuffer: 5,
                          ),
                          // User location marker if available
                          if (_userLocation != null)
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: _userLocation!,
                                  width: 60,
                                  height: 60,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.red.withOpacity(0.3),
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.my_location,
                                      color: Colors.red,
                                      size: 30,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          MarkerLayer(
                            markers: _filteredLocations
                                .where((location) {
                                  final double lat = location['latitude'] ?? 0.0;
                                  final double lng = location['longitude'] ?? 0.0;
                                  return lat != 0.0 && lng != 0.0;
                                })
                                .map((location) {
                                  final double latitude = location['latitude'] ?? 0.0;
                                  final double longitude = location['longitude'] ?? 0.0;
                                  final String name = location['fullname'] ?? 'Unnamed';
                                  final bool isReferencePoint = location['mapreferencepoint'] == 'yes';
                                  final bool isSelected = _selectedLocation != null &&
                                      _selectedLocation!['id'] == location['id'];

                                  return Marker(
                                    point: LatLng(latitude, longitude),
                                    width: 80,
                                    height: 80,
                                    child: GestureDetector(
                                      onTap: () {
                                        // First select the location to highlight it
                                        _selectLocation(location);

                                        // Then navigate to detail page
                                        Future.delayed(Duration(milliseconds: 300), () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => BuildingDetailPage(
                                                building: location,
                                                collegeNameForTable: widget.institutionName,
                                                isDarkMode: currentIsDarkMode,
                                                toggleTheme: widget.toggleTheme,
                                              ),
                                            ),
                                          );
                                        });
                                      },
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Stack(
                                            children: [
                                              if (isSelected)
                                                Container(
                                                  width: 50,
                                                  height: 50,
                                                  decoration: BoxDecoration(
                                                    color: Colors.red.withOpacity(0.3),
                                                    shape: BoxShape.circle,
                                                  ),
                                                ),
                                              Icon(
                                                isReferencePoint ? Icons.star : Icons.location_on,
                                                color: isSelected
                                                    ? Colors.red
                                                    : isReferencePoint
                                                        ? Colors.amber
                                                        : Colors.red,
                                                size: isSelected
                                                    ? 45
                                                    : isReferencePoint ? 40 : 30,
                                              ),
                                            ],
                                          ),
                                          if (name.isNotEmpty)
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.surface.withOpacity(0.8),
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                name,
                                                style: TextStyle(
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.bold,
                                                  color: currentIsDarkMode ? Colors.white : Colors.black,
                                                ),
                                                textAlign: TextAlign.center,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  );
                                })
                                .toList(),
                          ),
                          // Path polyline
                          if (_pathPoints.length >= 2)
                            PolylineLayer(
                              polylines: [
                                Polyline(
                                  points: _pathPoints,
                                  strokeWidth: 4.0,
                                  color: Colors.red,
                                ),
                              ],
                            ),
                  ],
                ),
                // Zoom Controls
                Positioned(
                  right: 16,
                  bottom: 100,
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Zoom In Button
                            IconButton(
                              icon: Icon(
                                Icons.add,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () {
                                final currentZoom = _mapController.camera.zoom;
                                _mapController.move(
                                  _mapController.camera.center,
                                  currentZoom + 1,
                                );
                              },
                            ),
                            Divider(
                              height: 1,
                              thickness: 1,
                              color: theme.colorScheme.surfaceContainerHighest,
                            ),
                            // Zoom Out Button
                            IconButton(
                              icon: Icon(
                                Icons.remove,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () {
                                final currentZoom = _mapController.camera.zoom;
                                _mapController.move(
                                  _mapController.camera.center,
                                  currentZoom - 1,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // No building count indicator
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}