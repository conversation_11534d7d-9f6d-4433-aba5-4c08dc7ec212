import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class NoteItem {
  String text;
  DateTime createdAt;

  NoteItem({required this.text, required this.createdAt});

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory NoteItem.fromJson(Map<String, dynamic> json) {
    return NoteItem(
      text: json['text'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

class NotesPage extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const NotesPage({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> {
  List<NoteItem> _notes = [];

  @override
  void initState() {
    super.initState();
    _loadNotes();
  }

  Future<void> _loadNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedNotes = prefs.getStringList('notes');
    if (encodedNotes != null) {
      setState(() {
        _notes = encodedNotes.where((e) => e != null).map((e) {
          try {
            return NoteItem.fromJson(jsonDecode(e));
          } catch (error) {
            print('Error decoding note item: $error');
            return null;
          }
        }).whereType<NoteItem>().toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt)); // Sort by most recent
      });
    }
  }

  Future<void> _saveNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedNotes = _notes.map((note) => jsonEncode(note.toJson())).toList();
    await prefs.setStringList('notes', encodedNotes);
  }

  void _addNote(String note) {
    if (note.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a note.')),
      );
      return;
    }
    setState(() {
      _notes.insert(0, NoteItem(text: note, createdAt: DateTime.now())); // Add to the top
      _saveNotes();
    });
  }

  void _deleteNote(int index) {
    setState(() {
      _notes.removeAt(index);
      _saveNotes();
    });
  }

  void _updateNote(int index, String newValue) {
    setState(() {
      _notes[index] = NoteItem(text: newValue, createdAt: _notes[index].createdAt);
      _saveNotes();
    });
  }

  void _showNewNoteDialog(BuildContext context) {
    final isDarkMode = widget.isDarkMode;
    final theme = Theme.of(context);
    final TextEditingController newNoteController = TextEditingController();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('New Note', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: SingleChildScrollView(
            child: TextField(
              controller: newNoteController,
              maxLines: null,
              style: TextStyle(color: theme.colorScheme.onSurface),
              decoration: const InputDecoration(
                hintText: 'Enter your note here',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Save', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
              onPressed: () {
                _addNote(newNoteController.text);
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Notes',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          const SizedBox(height: 1), // Add 1px margin
          Expanded(
            child: ListView.separated(
              itemCount: _notes.length,
              separatorBuilder: (context, index) => Divider(
                height: 0.5,
                thickness: 0.5,
                color: isDarkMode ? Colors.grey.shade600 : const Color(0xFFC0C0C0), // Silver color
              ),
              itemBuilder: (context, index) {
                final noteItem = _notes[index];
                return Container(
                  color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                  child: ListTile(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => NoteDetailView(
                            note: noteItem,
                            index: index, // Pass the index here
                            isDarkMode: isDarkMode,
                            onNoteUpdated: (updatedText) {
                              final noteIndex = _notes.indexOf(noteItem);
                              if (noteIndex != -1) {
                                setState(() {
                                  _notes[noteIndex] = NoteItem(text: updatedText, createdAt: noteItem.createdAt);
                                  _saveNotes();
                                });
                              }
                            },
                            onNoteDeleted: (indexToDelete) {
                              _showDeleteConfirmationDialog(context, indexToDelete);
                            },
                          ),
                        ),
                      );
                    },
                    title: Text(
                      noteItem.text,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: TextStyle(
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    subtitle: Text(
                      'Created on: ${DateFormat('MMM d, yyyy - h:mm a').format(noteItem.createdAt)}',
                      style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showNewNoteDialog(context),
        backgroundColor: isDarkMode ? Colors.black : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black,
        shape: const CircleBorder(),
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, int index) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Confirm Delete', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: Text('Are you sure you want to delete this note?', style: TextStyle(color: theme.colorScheme.onSurface)),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('OK', style: TextStyle(color: theme.colorScheme.error)),
              onPressed: () {
                _deleteNote(index);
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}

class NoteDetailView extends StatefulWidget {
  final NoteItem note;
  final Function(String) onNoteUpdated;
  final Function(int) onNoteDeleted;
  final bool isDarkMode;
  final int index;

  const NoteDetailView({
    Key? key,
    required this.note,
    required this.onNoteUpdated,
    required this.onNoteDeleted,
    required this.isDarkMode,
    required this.index,
  }) : super(key: key);

  @override
  State<NoteDetailView> createState() => _NoteDetailViewState();
}

class _NoteDetailViewState extends State<NoteDetailView> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.note.text);
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(
          widget.note.text.split('\n').first,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              _showEditDialog(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              widget.onNoteDeleted(widget.index);
            },
          ),
        ],
      ),
      body: Container(
        color: widget.isDarkMode ? Colors.grey[850] : Colors.white, // Body background color
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Text(
            widget.note.text,
            style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurface),
          ),
        ),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final isDarkMode = widget.isDarkMode;
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Edit Note', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: SingleChildScrollView(
            child: TextField(
              controller: _textController,
              maxLines: null,
              style: TextStyle(color: theme.colorScheme.onSurface),
              decoration: const InputDecoration(
                hintText: 'Enter your note here',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Save', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
              onPressed: () {
                widget.onNoteUpdated(_textController.text);
                Navigator.of(context).pop();
                setState(() {}); // Update the detail view
              },
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Confirm Delete', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: Text('Are you sure you want to delete this note?', style: TextStyle(color: theme.colorScheme.onSurface)),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('OK', style: TextStyle(color: theme.colorScheme.error)),
              onPressed: () {
                widget.onNoteDeleted(widget.index);
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}