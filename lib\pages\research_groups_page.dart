// research_groups_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'research_group_detail_page.dart';

class ResearchGroupsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedResearchGroups;
  final bool isFromDetailPage;

  const ResearchGroupsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedResearchGroups,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ResearchGroupsPage> createState() => _ResearchGroupsPageState();
}

class _ResearchGroupsPageState extends State<ResearchGroupsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('research_groups_list');
  List<Map<String, dynamic>> _researchGroups = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("ResearchGroupsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ResearchGroupsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ResearchGroupsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ResearchGroupsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ResearchGroupsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedResearchGroups != null && widget.preloadedResearchGroups!.isNotEmpty) {
      print("Preloaded research groups found, using them.");
      setState(() {
        _researchGroups = List<Map<String, dynamic>>.from(widget.preloadedResearchGroups!);
        _researchGroups.forEach((researchGroup) {
          researchGroup['_isImageLoading'] = false;
        });
        _researchGroups.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedResearchGroups!.length == _pageSize;
      });
      _loadResearchGroupsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded research groups or empty list, loading from database.");
      _loadResearchGroupsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadResearchGroupsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadResearchGroupsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final researchGroupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchgroups';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(researchGroupsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedResearchGroups =
          await _updateResearchGroupImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _researchGroups = updatedResearchGroups;
        } else {
          _researchGroups.addAll(updatedResearchGroups);
        }
        _researchGroups.forEach((researchGroup) {
          researchGroup['_isImageLoading'] = false;
        });
        _researchGroups.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the research groups
      _cacheResearchGroups(_researchGroups);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching research groups: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateResearchGroupImageUrls(
      List<Map<String, dynamic>> researchGroups) async {
    List<Future<void>> futures = [];
    for (final researchGroup in researchGroups) {
      if (researchGroup['image_url'] == null ||
          researchGroup['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(researchGroup));
      }
    }
    await Future.wait(futures);
    return researchGroups;
  }

  Future<void> _cacheResearchGroups(List<Map<String, dynamic>> researchGroups) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String researchGroupsJson = jsonEncode(researchGroups);
      await prefs.setString(
          'researchgroups_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          researchGroupsJson);
    } catch (e) {
      print('Error caching research groups: $e');
    }
  }
  
  void _setupRealtime() {
    final researchGroupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchgroups';
    _realtimeChannel = Supabase.instance.client
        .channel('research_groups')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: researchGroupsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newResearchGroupId = payload.newRecord['id'];
          final newResearchGroupResponse = await Supabase.instance.client
              .from(researchGroupsTableName)
              .select('*')
              .eq('id', newResearchGroupId)
              .single();
          if (mounted) {
            Map<String, dynamic> newResearchGroup = Map.from(newResearchGroupResponse);
            final updatedResearchGroup = await _updateResearchGroupImageUrls([newResearchGroup]);
            setState(() {
              _researchGroups = [..._researchGroups, updatedResearchGroup.first];
              updatedResearchGroup.first['_isImageLoading'] = false;
              _researchGroups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedResearchGroupId = payload.newRecord['id'];
          final updatedResearchGroupResponse = await Supabase.instance.client
              .from(researchGroupsTableName)
              .select('*')
              .eq('id', updatedResearchGroupId)
              .single();
          if (mounted) {
            final updatedResearchGroup = Map<String, dynamic>.from(updatedResearchGroupResponse);
            setState(() {
              _researchGroups = _researchGroups.map((researchGroup) {
                return researchGroup['id'] == updatedResearchGroup['id'] ? updatedResearchGroup : researchGroup;
              }).toList();
              _researchGroups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedResearchGroupId = payload.oldRecord['id'];
          setState(() {
            _researchGroups.removeWhere((researchGroup) => researchGroup['id'] == deletedResearchGroupId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreResearchGroups();
    }
  }

  Future<void> _loadMoreResearchGroups() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadResearchGroupsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> researchGroup) async {
    if (researchGroup['_isImageLoading'] == true) {
      print('Image loading already in progress for ${researchGroup['fullname']}, skipping.');
      return;
    }
    if (researchGroup['image_url'] != null &&
        researchGroup['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${researchGroup['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      researchGroup['_isImageLoading'] = true;
    });

    final fullname = researchGroup['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeResearchGroupBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/researchgroups';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeResearchGroupBucket');
    print('Image URL before fetch: ${researchGroup['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeResearchGroupBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeResearchGroupBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        researchGroup['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        researchGroup['_isImageLoading'] = false;
        print('Setting image_url for ${researchGroup['fullname']} to: ${researchGroup['image_url']}');
      });
    } else {
      researchGroup['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> researchGroup) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ResearchGroupDetailPage(
            researchGroup: researchGroup,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("ResearchGroupsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Research Groups',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _researchGroups.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadResearchGroupsFromSupabase(initialLoad: true),
              child: _researchGroups.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No research groups available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _researchGroups.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _researchGroups.length) {
                          final researchGroup = _researchGroups[index];
                          return VisibilityDetector(
                            key: Key('researchgroup_${researchGroup['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (researchGroup['image_url'] == null ||
                                      researchGroup['image_url'] == 'assets/placeholder_image.png') &&
                                  !researchGroup['_isImageLoading']) {
                                _fetchImageUrl(researchGroup);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: researchGroup['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  researchGroup['fullname'] ?? 'Unnamed Research Group',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    researchGroup['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, researchGroup),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
