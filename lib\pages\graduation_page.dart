// graduation_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'graduation_detail_page.dart';

class GraduationPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const GraduationPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _GraduationPageState createState() => _GraduationPageState();
}

class _GraduationPageState extends State<GraduationPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('graduation_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _graduationEvents = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedYearFilter = 'All Years';
  List<String> _yearFilterOptions = ['All Years'];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadGraduationFromCache();
    _loadGraduationFromSupabase();
  }

  void _setupRealtime() {
    final graduationTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_graduation';
    _realtimeChannel = Supabase.instance.client
        .channel('graduation_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: graduationTableName,
      callback: (payload) async {
        print("Realtime update received for graduation: ${payload.eventType}");
        _loadGraduationFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadGraduationFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'graduation_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> graduationEvents = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && graduationEvents.isNotEmpty) {
          setState(() {
            _graduationEvents = graduationEvents;
            _updateYearFilterOptions(graduationEvents);
          });
          print("Loaded ${graduationEvents.length} graduation events from cache");
        }
      }
    } catch (e) {
      print("Error loading graduation events from cache: $e");
    }
  }

  void _updateYearFilterOptions(List<Map<String, dynamic>> graduationEvents) {
    Set<String> years = {'All Years'};
    for (var event in graduationEvents) {
      if (event['year'] != null) {
        years.add(event['year'].toString());
      }
    }
    
    setState(() {
      _yearFilterOptions = years.toList()..sort((a, b) {
        if (a == 'All Years') return -1;
        if (b == 'All Years') return 1;
        return int.parse(b).compareTo(int.parse(a)); // Sort years in descending order
      });
    });
  }

  Future<void> _loadGraduationFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final graduationTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_graduation';
      var query = Supabase.instance.client
          .from(graduationTableName)
          .select('*');
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('venue.ilike.%$_searchQuery%,speaker.ilike.%$_searchQuery%');
      }
      
      // Apply year filter if not "All Years"
      if (_selectedYearFilter != 'All Years') {
        query = query.eq('year', int.parse(_selectedYearFilter));
      }
      
      final response = await query.order('year', ascending: false);

      if (_isDisposed) return;

      setState(() {
        _graduationEvents = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Update year filter options
      _updateYearFilterOptions(_graduationEvents);

      // Cache the data
      _cacheGraduation(_graduationEvents);
      
      // Preload images
      _preloadImages();

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading graduation events: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading graduation events: $e')),
      );
    }
  }

  Future<void> _cacheGraduation(List<Map<String, dynamic>> graduationEvents) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'graduation_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(graduationEvents));
      print("Cached ${graduationEvents.length} graduation events");
    } catch (e) {
      print("Error caching graduation events: $e");
    }
  }

  void _preloadImages() {
    for (var event in _graduationEvents) {
      if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') {
        precacheImage(NetworkImage(event['image_url']), context);
      }
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadGraduationFromSupabase();
  }

  String _formatDate(Map<String, dynamic> graduation) {
    final int? day = graduation['day'];
    final int? month = graduation['month'];
    final int? year = graduation['year'];
    
    if (day != null && month != null && year != null) {
      final DateTime date = DateTime(year, month, day);
      return DateFormat('MMMM d, y').format(date);
    }
    return 'Date not specified';
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Graduation Ceremonies',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search graduation ceremonies...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Year filter
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  'Year:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedYearFilter,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface,
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.colorScheme.surface,
                        items: _yearFilterOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != _selectedYearFilter) {
                            setState(() {
                              _selectedYearFilter = newValue;
                            });
                            _loadGraduationFromSupabase();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Graduation list
          Expanded(
            child: VisibilityDetector(
              key: const Key('graduation_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _graduationEvents.isEmpty && !_isLoading) {
                  _loadGraduationFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadGraduationFromSupabase,
                child: _isLoading && _graduationEvents.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _graduationEvents.isEmpty
                        ? Center(
                            child: Text(
                              'No graduation ceremonies found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _graduationEvents.length,
                            itemBuilder: (context, index) {
                              return _buildGraduationCard(
                                _graduationEvents[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGraduationCard(
    Map<String, dynamic> graduation,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String venue = graduation['venue'] ?? 'Unknown Venue';
    final String speaker = graduation['speaker'] ?? '';
    final String about = graduation['about'] ?? '';
    final String startTime = graduation['starttime'] ?? '';
    final String endTime = graduation['endtime'] ?? '';
    final String imageUrl = graduation['image_url'] ?? 'assets/placeholder_image.png';
    final String formattedDate = _formatDate(graduation);

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => GraduationDetailPage(
                graduation: graduation,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
                collegeNameForBucket: widget.collegeData['fullname'],
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Circular image or icon
              CircleAvatar(
                radius: 30,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                backgroundImage: (imageUrl != 'assets/placeholder_image.png')
                    ? CachedNetworkImageProvider(imageUrl) as ImageProvider
                    : null,
                child: (imageUrl == 'assets/placeholder_image.png')
                    ? Icon(
                        Icons.school,
                        size: 30,
                        color: isDarkMode ? Colors.white : Colors.black,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      venue,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    if (startTime.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            startTime + (endTime.isNotEmpty ? ' - $endTime' : ''),
                            style: TextStyle(
                              fontSize: 14,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (speaker.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              'Speaker: $speaker',
                              style: TextStyle(
                                fontSize: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (about.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        about.length > 80 ? '${about.substring(0, 80)}...' : about,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
