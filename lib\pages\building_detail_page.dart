import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'login_page.dart';
import 'rooms_page.dart';

class BuildingDetailPage extends StatefulWidget {
  final Map<String, dynamic> building;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BuildingDetailPage({
    Key? key,
    required this.building,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<BuildingDetailPage> createState() => _BuildingDetailPageState();
}

class _BuildingDetailPageState extends State<BuildingDetailPage> {
  late RealtimeChannel _buildingRealtimeChannel;
  List<Map<String, dynamic>> _rooms = [];
  bool _isLoadingRooms = false;

  @override
  void initState() {
    super.initState();
    _setupBuildingRealtimeListener();
    _loadRoomsForBuilding();
  }

  @override
  void dispose() {
    _buildingRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupBuildingRealtimeListener() {
    final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_building';
    print('Setting up realtime listener for building detail: $buildingsTableName');
    _buildingRealtimeChannel = Supabase.instance.client
        .channel('building_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: buildingsTableName,
          callback: (payload) {
            // Check if this change is for the current building
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.building['id']) {
              print('Received update for current building: ${widget.building['fullname']}');
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedBuildingData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the building is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedBuildingData() async {
    try {
      final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_building';
      print('Fetching updated data from table: $buildingsTableName for building ID: ${widget.building['id']}');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .eq('id', widget.building['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedBuilding = Map.from(response);
        print('Successfully fetched updated building data');

        // Update the widget.building with the new data
        setState(() {
          widget.building.clear(); // Clear old data
          widget.building.addAll(updatedBuilding); // Add updated data
          print("Building data updated in detail page for ${widget.building['fullname']}");
          _updateBuildingsCache(updatedBuilding); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated building data: $e');
    }
  }

  Future<void> _updateBuildingsCache(Map<String, dynamic> updatedBuilding) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'building_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Updating cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedBuildings = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific building in the cache
        bool found = false;
        for (var building in cachedBuildings) {
          if (building['id'] == updatedBuilding['id']) {
            updatedCache.add(updatedBuilding);
            found = true;
            print('Updated building in cache: ${updatedBuilding['fullname']}');
          } else {
            updatedCache.add(Map<String, dynamic>.from(building));
          }
        }

        if (!found) {
          print('Building not found in cache, adding it');
          updatedCache.add(updatedBuilding);
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
        print('Successfully updated buildings cache');
      } else {
        // If cache doesn't exist, create it with just this building
        print('Cache not found, creating new cache with this building');
        await prefs.setString(cacheKey, jsonEncode([updatedBuilding]));
      }
    } catch (e) {
      print('Error updating buildings cache: $e');
    }
  }

  Future<void> _loadRoomsForBuilding() async {
    if (_isLoadingRooms) return;

    setState(() {
      _isLoadingRooms = true;
    });

    try {
      final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching rooms for building: ${widget.building['fullname']}');

      final response = await Supabase.instance.client
          .from(roomsTableName)
          .select('*')
          .eq('building', widget.building['fullname'])
          .order('fullname', ascending: true);

      final rooms = List<Map<String, dynamic>>.from(response);
      print('Fetched ${rooms.length} rooms for building: ${widget.building['fullname']}');

      setState(() {
        _rooms = rooms;
        _isLoadingRooms = false;
      });
    } catch (e) {
      print('Error fetching rooms for building: $e');
      setState(() {
        _isLoadingRooms = false;
      });
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch URL')),
        );
      }
    }
  }

  Future<void> _launchMaps() async {
    final double latitude = widget.building['latitude'] ?? 0.0;
    final double longitude = widget.building['longitude'] ?? 0.0;

    if (latitude == 0.0 && longitude == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location coordinates not available')),
      );
      return;
    }

    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch maps')),
        );
      }
    }
  }

  Future<void> _launchPhone(String phone) async {
    if (phone.isEmpty) return;

    final Uri url = Uri.parse('tel:$phone');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch phone')),
        );
      }
    }
  }

  void _viewRooms() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RoomsPage(
          collegeNameForTable: widget.collegeNameForTable,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          preloadedRooms: _rooms,
          isFromDetailPage: true,
          buildingFilter: widget.building['fullname'],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.building['fullname'] ?? 'Unnamed Building';
    final String location = widget.building['location'] ?? '';
    final String hours = widget.building['hours'] ?? '';
    final String accessMethod = widget.building['accessmethod'] ?? '';
    final int capacity = widget.building['capacity'] ?? 0;
    final String phone = widget.building['phone'] ?? '';
    final double latitude = widget.building['latitude'] ?? 0.0;
    final double longitude = widget.building['longitude'] ?? 0.0;
    final String mapReferencePoint = widget.building['mapreferencepoint'] ?? '';
    final String about = widget.building['about'] ?? '';

    final bool hasLocation = latitude != 0.0 && longitude != 0.0;
    final bool hasPhone = phone.isNotEmpty;
    final bool hasRooms = _rooms.isNotEmpty;

    // Building type flags
    final List<String> buildingTypes = [];
    if (widget.building['building'] == true) buildingTypes.add('Building');
    if (widget.building['housing'] == true) buildingTypes.add('Housing');
    if (widget.building['parking'] == true) buildingTypes.add('Parking');
    if (widget.building['school'] == true) buildingTypes.add('School');
    if (widget.building['residence'] == true) buildingTypes.add('Residence');
    if (widget.building['library'] == true) buildingTypes.add('Library');
    if (widget.building['lab'] == true) buildingTypes.add('Lab');
    if (widget.building['studio'] == true) buildingTypes.add('Studio');
    if (widget.building['gallery'] == true) buildingTypes.add('Gallery');
    if (widget.building['theater'] == true) buildingTypes.add('Theater');
    if (widget.building['coworkingspace'] == true) buildingTypes.add('Coworking Space');
    if (widget.building['studyspace'] == true) buildingTypes.add('Study Space');
    if (widget.building['fitnessspace'] == true) buildingTypes.add('Fitness Space');
    if (widget.building['funspace'] == true) buildingTypes.add('Fun Space');
    if (widget.building['storagespace'] == true) buildingTypes.add('Storage Space');
    if (widget.building['mailingspace'] == true) buildingTypes.add('Mailing Space');
    if (widget.building['museum'] == true) buildingTypes.add('Museum');
    if (widget.building['sacredspace'] == true) buildingTypes.add('Sacred Space');
    if (widget.building['outdoorspace'] == true) buildingTypes.add('Outdoor Space');
    if (widget.building['researchstation'] == true) buildingTypes.add('Research Station');
    if (widget.building['clinic'] == true) buildingTypes.add('Clinic');
    if (widget.building['laundryspace'] == true) buildingTypes.add('Laundry Space');
    if (widget.building['campusdumpsters'] == true) buildingTypes.add('Campus Dumpsters');
    if (widget.building['watertanks'] == true) buildingTypes.add('Water Tanks');
    if (widget.building['other'] == true) buildingTypes.add('Other');

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasLocation)
              SizedBox(
                height: 200,
                child: FlutterMap(
                  options: MapOptions(
                    initialCenter: LatLng(latitude, longitude),
                    initialZoom: 16.0,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: currentIsDarkMode
                          ? 'https://tile.jawg.io/jawg-dark/{z}/{x}/{y}{r}.png?access-token=your-access-token'
                          : 'https://tile.jawg.io/jawg-light/{z}/{x}/{y}{r}.png?access-token=your-access-token',
                      subdomains: const ['a', 'b', 'c'],
                    ),
                    MarkerLayer(
                      markers: [
                        Marker(
                          width: 40.0,
                          height: 40.0,
                          point: LatLng(latitude, longitude),
                          child: const Icon(
                            Icons.location_on,
                            color: Colors.red,
                            size: 40.0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (buildingTypes.isNotEmpty) ...[
                    const Text(
                      'Building Type:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: buildingTypes
                          .map((type) => Chip(
                                label: Text(type),
                                backgroundColor: theme.colorScheme.primaryContainer,
                                labelStyle: TextStyle(
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                              ))
                          .toList(),
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (location.isNotEmpty) ...[
                    const Text(
                      'Location:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(location),
                    const SizedBox(height: 16),
                  ],
                  if (hours.isNotEmpty) ...[
                    const Text(
                      'Hours:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(hours),
                    const SizedBox(height: 16),
                  ],
                  if (accessMethod.isNotEmpty) ...[
                    const Text(
                      'Access Method:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(accessMethod),
                    const SizedBox(height: 16),
                  ],
                  if (capacity > 0) ...[
                    const Text(
                      'Capacity:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('$capacity'),
                    const SizedBox(height: 16),
                  ],
                  if (phone.isNotEmpty) ...[
                    const Text(
                      'Phone:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(phone),
                        IconButton(
                          icon: const Icon(Icons.phone),
                          onPressed: () => _launchPhone(phone),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (about.isNotEmpty) ...[
                    const Text(
                      'About:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(about),
                    const SizedBox(height: 16),
                  ],
                  if (hasRooms) ...[
                    const Text(
                      'Rooms:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('This building has ${_rooms.length} rooms'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _viewRooms,
                      child: const Text('View Rooms'),
                    ),
                    const SizedBox(height: 16),
                  ] else if (_isLoadingRooms) ...[
                    const Text(
                      'Rooms:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Center(child: CircularProgressIndicator()),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                if (hasLocation)
                  IconButton(
                    icon: Icon(
                      Icons.directions,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchMaps,
                  ),
                if (hasPhone)
                  IconButton(
                    icon: Icon(
                      Icons.phone,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () => _launchPhone(phone),
                  ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
