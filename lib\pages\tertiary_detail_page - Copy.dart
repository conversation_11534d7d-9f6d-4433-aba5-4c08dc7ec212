import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart'; // Import the Google Mobile Ads package
import 'tertiary_start_page.dart';
import 'tertiary_updates_page.dart';
import 'tertiary_path_page.dart';
import 'tertiary_people_page.dart';
import 'tertiary_lodging_page.dart';
import 'tertiary_shop_eat_page.dart';
import 'tertiary_transport_page.dart';
import 'tertiary_core_page.dart';
import 'tertiary_academics_page.dart';
import 'tertiary_programs_page.dart';
import 'tertiary_athletics_groups_page.dart';
import 'tertiary_media_page.dart';
import 'tertiary_startups_page.dart';
import 'tertiary_projects_publications_page.dart';
import 'tertiary_buildings_spaces_page.dart';
import 'tertiary_calendar_page.dart';
import 'tertiary_statistics_page.dart';
import 'tertiary_map_page.dart';
import 'tertiary_feedback_page.dart';
import 'tertiary_timeline_page.dart';
import 'tertiary_connectivity_page.dart';
import 'tertiary_giving_page.dart';
import 'tertiary_virtual_tour_page.dart';
import 'tertiary_today_page.dart';
import 'tertiary_info_page.dart';

class TertiaryDetailPage extends StatefulWidget {
  final Map<String, dynamic> college; // Receive college data
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryDetailPage({
    Key? key,
    required this.college, // Now requires college data
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryDetailPage> createState() => _TertiaryDetailPageState();
}

class _TertiaryDetailPageState extends State<TertiaryDetailPage> {
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;

  // **Replace with your actual AdMob Rewarded Ad unit ID (Test ID for now)**
  final String _adUnitId = 'ca-app-pub-3940256099942544/5224354917';

  @override
  void initState() {
    super.initState();
    _loadRewardedAd();
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (RewardedAd ad) {
              // Dispose the ad once it's dismissed
              ad.dispose();
              _loadRewardedAd(); // Load a new ad for the next time
            },
            onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
              print('Failed to show rewarded ad: $error');
              ad.dispose();
            },
          );
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
        },
      ),
    );
  }

  void _showRewardedAd() {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('User earned reward: ${reward.amount} ${reward.type}');
          // You can add any logic here if you want to track rewards
        },
      );
    } else {
      print('Rewarded ad is not ready yet.');
      Navigator.pop(context); // Still navigate back if the ad isn't ready
    }
  }

  // Updated to use data from widget.college
  Future<void> _launchDialer() async {
    final String phoneNumber = widget.college['phone'] ?? ''; // Get from college data
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchWhatsapp() async {
    final String whatsappNumber = widget.college['whatsapp'] ?? ''; // Get from college data
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchNavigation() async {
    final double latitude = widget.college['latitude'] ?? 0.0; // Get from college data
    final double longitude = widget.college['longitude'] ?? 0.0; // Get from college data
    final Uri url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return WillPopScope(
      onWillPop: () async {
        _showRewardedAd();
        return true; // Allow navigation after showing the ad (or immediately if not loaded)
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd();
            },
          ),
          title: Text(
            widget.college['fullname'] ?? '', // Use college fullname
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          actions: [
            IconButton(
              icon: Icon(
                Icons.support_agent,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryConnectivityPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.wifi,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryConnectivityPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.volunteer_activism,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryGivingPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.view_in_ar,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryVirtualTourPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = 2;
              double aspectRatio = 1.3;

              if (constraints.maxWidth > 1200) {
                crossAxisCount = 6;
                aspectRatio = 1.4;
              } else if (constraints.maxWidth > 900) {
                crossAxisCount = 4;
                aspectRatio = 1.3;
              } else if (constraints.maxWidth > 600) {
                crossAxisCount = 3;
                aspectRatio = 1.2;
              }

              return GridView.count(
                crossAxisCount: crossAxisCount,
                padding: const EdgeInsets.all(16),
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: aspectRatio,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildGridItem(context, 'Start', Icons.play_arrow, theme),
                  _buildGridItem(context, 'Updates', Icons.update, theme),
                  _buildGridItem(context, 'Path', Icons.timeline, theme),
                  _buildGridItem(context, 'People', Icons.people, theme),
                  _buildGridItem(context, 'Lodging', Icons.hotel, theme),
                  _buildGridItem(
                      context, 'Shop & Eat', Icons.restaurant_menu, theme),
                  _buildGridItem(
                      context, 'Transport', Icons.directions_bus, theme),
                  _buildGridItem(context, 'Core', Icons.stars, theme),
                  _buildGridItem(context, 'Academics', Icons.school, theme),
                  _buildGridItem(context, 'Programs', Icons.list_alt, theme),
                  _buildGridItem(
                      context, 'Athletics & Groups', Icons.sports_soccer, theme),
                  _buildGridItem(context, 'Media', Icons.perm_media, theme),
                  _buildGridItem(context, 'Startups', Icons.rocket_launch, theme),
                  _buildGridItem(context, 'Projects & Publications',
                      Icons.article, theme),
                  _buildGridItem(
                      context, 'Buildings & Spaces', Icons.apartment, theme),
                  _buildGridItem(context, 'Statistics', Icons.bar_chart, theme),
                  _buildGridItem(
                      context, 'Calendar', Icons.calendar_month, theme),
                  _buildGridItem(context, 'Map', Icons.map, theme),
                  _buildGridItem(context, 'Feedback', Icons.feedback, theme),
                  _buildGridItem(
                      context, 'Historical Timeline', Icons.history_edu, theme),
                  _buildGridItem(context, 'Rentals', Icons.swap_horiz, theme),
                  _buildGridItem(context, 'Jobs', Icons.work, theme),
                  _buildGridItem(
                      context, 'Services', Icons.miscellaneous_services, theme),
                  _buildGridItem(context, 'Money', Icons.payments, theme),
                  _buildGridItem(context, 'Health', Icons.local_hospital, theme),
                  _buildGridItem(context, 'Weather', Icons.cloud_queue, theme),
                  _buildGridItem(context, 'Safety', Icons.security, theme),
                  _buildGridItem(context, 'Nearby', Icons.near_me, theme),
                ],
              );
            },
          ),
        ),
        bottomNavigationBar: Container(
          color: theme.colorScheme.surface,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchDialer,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.navigation,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchNavigation,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.watch,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryTodayPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                          ),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchWhatsapp,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.info,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryInfoPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            schoolName: widget.college['fullname'] ?? '',
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Start') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStartPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Updates') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryUpdatesPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Path') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryPathPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'People') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryPeoplePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Lodging') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryLodgingPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Shop & Eat') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryShopEatPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Transport') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryTransportPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Core') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryCorePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Academics') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryAcademicsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Programs') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryProgramsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Athletics & Groups') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryAthleticsGroupsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Media') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryMediaPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Startups') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStartupsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Projects & Publications') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryProjectsPublicationsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Buildings & Spaces') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryBuildingsSpacesPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Calendar') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryCalendarPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Statistics') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStatisticsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Map') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryMapPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Feedback') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryFeedbackPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Historical Timeline') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryTimelinePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    super.dispose();
  }
}