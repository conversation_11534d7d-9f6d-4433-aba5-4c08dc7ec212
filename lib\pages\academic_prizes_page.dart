import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'academic_prize_detail_page.dart';
import 'login_page.dart';

class AcademicPrizesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAcademicPrizes;
  final bool isFromDetailPage;

  const AcademicPrizesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAcademicPrizes,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AcademicPrizesPageState createState() => _AcademicPrizesPageState();
}

class _AcademicPrizesPageState extends State<AcademicPrizesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('academic_prizes_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _academicPrizes = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    print("AcademicPrizesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedAcademicPrizes != null &&
        widget.preloadedAcademicPrizes!.isNotEmpty) {
      setState(() {
        _academicPrizes = List.from(widget.preloadedAcademicPrizes!);
        _isLoading = false;
      });
    } else {
      _loadAcademicPrizesFromDatabase();
    }
  }

  void _setupRealtime() {
    final academicPrizesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicprizes';
    _realtimeChannel = Supabase.instance.client
        .channel('academic_prizes_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicPrizesTableName,
      callback: (payload) async {
        print("Realtime update received for academic prizes: ${payload.eventType}");
        _loadAcademicPrizesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadAcademicPrizesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _academicPrizes = [];
    });

    await _loadMoreAcademicPrizes();
  }

  Future<void> _loadMoreAcademicPrizes() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final academicPrizesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_academicprizes';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(academicPrizesTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _academicPrizes.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading academic prizes: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading academic prizes: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreAcademicPrizes();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadAcademicPrizesFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Prizes',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search academic prizes...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Academic prizes list
          Expanded(
            child: VisibilityDetector(
              key: const Key('academic_prizes_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _academicPrizes.isEmpty && !_isLoading) {
                  _loadAcademicPrizesFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadAcademicPrizesFromDatabase,
                child: _academicPrizes.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No academic prizes found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _academicPrizes.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _academicPrizes.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildAcademicPrizeCard(
                            _academicPrizes[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildAcademicPrizeCard(
    Map<String, dynamic> prize,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = prize['fullname'] ?? 'Unknown';
    final String about = prize['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AcademicPrizeDetailPage(
                prize: prize,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.emoji_events,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
