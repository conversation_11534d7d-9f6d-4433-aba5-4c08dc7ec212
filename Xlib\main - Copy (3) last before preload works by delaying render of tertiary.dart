import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform;
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_messaging_service.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
//import 'package:flutter_dotenv/flutter_dotenv.dart'; // Import flutter_dotenv

import 'pages/tertiary_page.dart';
import 'pages/secondary_page.dart';
import 'pages/primary_page.dart';
import 'pages/pre_primary_page.dart';
import 'pages/login_page.dart';
import 'pages/whiteboard_page.dart';
import 'pages/wellness_page.dart';
import 'pages/utilities_page.dart';
import 'pages/calculate_page.dart';
import 'package:google_fonts/google_fonts.dart';

// Custom scroll behavior that always shows scrollbar on desktop/web
class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.linux ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        thickness: 12.0,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFF8c8c8c)
            : const Color.fromRGBO(158, 158, 158, .6),
        trackColor: Theme.of(context).brightness == Brightness.light
            ? Colors.white
            : null,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase FIRST
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await FirebaseMessagingService().initialize();

  if (!kIsWeb) {
    await MobileAds.instance.initialize();
  } else {
    MobileAds.instance.initialize();
  }

  // Load environment variables from .env file
  //await dotenv.load(fileName: '.env');

  // Initialize Supabase AFTER Firebase
  await Supabase.initialize(
    url: 'https://qyhhhvqmbahyknltcbqw.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5aGhodnFtYmFoeWtubHRjYnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYxNDkzMzQsImV4cCI6MjA1MTcyNTMzNH0.gEAdk-Efly7wec5AoQrErtL2kHLuEjzMVmCBCKrse7g',
  );

  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  runApp(MyApp(initialIsDarkMode: isDarkMode));
}

class MyApp extends StatefulWidget {
  final bool initialIsDarkMode;
  // Static variable to store the preloaded colleges
  static List<Map<String, dynamic>>? preloadedColleges;

  const MyApp({Key? key, required this.initialIsDarkMode}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late bool isDarkMode;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    isDarkMode = widget.initialIsDarkMode;
    _preloadColleges(); // Call the preloading function
  }

  // Function to preload colleges from Supabase
  Future<void> _preloadColleges() async {
    try {
      final response = await Supabase.instance.client
          .from('colleges')
          .select('id, fullname, city, state'); // Select specific fields

      if (response is List) {
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(response);
        print('Colleges preloaded successfully: ${MyApp.preloadedColleges?.length} colleges');
      } else {
        print('Error preloading colleges: Response is not a List');
      }
    } catch (e) {
      print('Error preloading colleges: $e');
    }
  }

  Future<void> _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('darkMode', isDark);
  }

  void toggleTheme() {
    setState(() {
      isDarkMode = !isDarkMode;
      _saveThemePreference(isDarkMode);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      scrollBehavior: CustomScrollBehavior(),
      title: 'Phygital Access',
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      theme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFFEEEEEE),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: Colors.white,
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: ColorScheme.light(
          surface: Colors.white,
          onSurface: Colors.black,
          secondary: Colors.black.withOpacity(0.6),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      darkTheme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFF090909),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF202020),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: const Color(0xFF202020),
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: const ColorScheme.dark(
          surface: Color(0xFF202020),
          onSurface: Colors.white,
          secondary: Colors.white70,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      home: HomeScreen(
        isDarkMode: isDarkMode,
        toggleTheme: toggleTheme,
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HomeScreen({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _adTimer;
  List<String> _adUrls = []; // Store URLs from Supabase
  int _currentAdIndex = 0;
  late bool _isDarkMode;
  VoidCallback? _toggleTheme;
  VideoPlayerController? _adVideoController;
  bool _isVideoInitialized = false;

  BannerAd? _bannerAd;
  bool _isBannerAdReady = false;

  RewardedAd? _rewardedAd;
  bool _isRewardedAdReady = false;

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
    _toggleTheme = widget.toggleTheme;
    _loadAdUrlsFromSupabase(); // Load ads from Supabase
    _adTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_adUrls.isNotEmpty) {
        setState(() {
          _currentAdIndex = (_currentAdIndex + 1) % _adUrls.length;
          _initializeVideoPlayer();
        });
      }
    });
    _loadBannerAd();
    _loadRewardedAd();
  }

  Future<void> _loadAdUrlsFromSupabase() async {
    try {
      final List<FileObject> response = await Supabase.instance.client
          .storage
          .from('ads') // Ensure 'ads' is your bucket name
          .list();

      if (response.isNotEmpty) {
        final urls = response.map((file) {
          return Supabase.instance.client.storage
              .from('ads')
              .getPublicUrl(file.name);
        }).toList();

        setState(() {
          _adUrls = urls;
          _initializeVideoPlayer(); // Initialize video player after loading ads
        });
        print('Successfully loaded ad URLs from Supabase: $urls'); // Add this log
      } else {
        print('No files found in the Supabase ads bucket.'); // Add this log
      }
    } catch (e) {
      print('Error loading ads from Supabase: $e');
      // Handle error, maybe show a default image/video or message
    }
  }

  Future<void> _loadRewardedAd() async {
    String adUnitId;
    if (kIsWeb) {
      // Replace with your actual web rewarded ad unit ID for testing
      adUnitId = 'ca-pub-3940256099942544/5224354917'; // Example test web rewarded ID
    } else {
      adUnitId = 'ca-app-pub-3940256099942544/5224354917'; // Android test rewarded ad ID
    }

    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (ad) {
              setState(() {
                _isRewardedAdReady = false;
              });
              _loadRewardedAd(); // Load the next ad
            },
            onAdFailedToShowFullScreenContent: (ad, err) {
              print('Failed to show rewarded ad: $err');
              setState(() {
                _isRewardedAdReady = false;
              });
              _loadRewardedAd(); // Attempt to load another ad
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isRewardedAdReady = true;
          });
        },
        onAdFailedToLoad: (err) {
          print('Failed to load rewarded ad: $err');
          setState(() {
            _isRewardedAdReady = false;
          });
        },
      ),
    );
  }

  Future<void> _showRewardedAd(BuildContext context) async {
    if (_isRewardedAdReady && _rewardedAd != null) {
      _rewardedAd!.show(
          onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        print('Reward earned! Amount: ${reward.amount}, Type: ${reward.type}');
        // Navigate to whiteboard page after reward
        _navigateToWhiteboard();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Reward ad is not ready yet.')),
      );
      // Optionally navigate even if ad is not ready, or handle differently
      _navigateToWhiteboard();
    }
  }

  void _navigateToWhiteboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WhiteboardPage(
          isDarkMode: _isDarkMode,
          toggleTheme: _toggleTheme!,
        ),
      ),
    );
  }

  Future<void> _initializeVideoPlayer() async {
    if (_adUrls.isNotEmpty && _adUrls[_currentAdIndex].endsWith('.mp4')) {
      if (_adVideoController != null) {
        if (_adVideoController!.dataSource != _adUrls[_currentAdIndex]) {
          await _adVideoController!.dispose();
          _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
          try {
            await _adVideoController!.initialize();
            await _adVideoController!.setVolume(0.0); // Mute the video in the grid
            await _adVideoController!.play();
            await _adVideoController!.setLooping(true);
            setState(() {
              _isVideoInitialized = true;
            });
          } catch (e) {
            print("Error initializing video player: $e");
          }
        } else if (!_isVideoInitialized) {
          try {
            await _adVideoController!.initialize();
            await _adVideoController!.setVolume(0.0); // Mute the video in the grid
            await _adVideoController!.play();
            await _adVideoController!.setLooping(true);
            setState(() {
              _isVideoInitialized = true;
            });
          } catch (e) {
            print("Error initializing video player: $e");
          }
        }
      } else {
        _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
        try {
          await _adVideoController!.initialize();
          await _adVideoController!.setVolume(0.0); // Mute the video in the grid
          await _adVideoController!.play();
          await _adVideoController!.setLooping(true);
          setState(() {
            _isVideoInitialized = true;
          });
        } catch (e) {
          print("Error initializing video player: $e");
        }
      }
    } else {
      _disposeVideoPlayer();
    }
  }

  void _disposeVideoPlayer() {
    if (_adVideoController != null) {
      _adVideoController!.pause();
      _adVideoController!.dispose();
      _adVideoController = null;
      _isVideoInitialized = false;
    }
  }

  void _loadBannerAd() {
    String adUnitId;
    if (kIsWeb) {
      // Replace with your actual web banner ad unit ID for testing
      adUnitId = 'ca-pub-3940256099942544/9214589741'; // Example test web banner ID
    } else {
      adUnitId = 'ca-app-pub-3940256099942544/6300978111'; // Android test banner ad ID
    }

    _bannerAd = BannerAd(
      adUnitId: adUnitId,
      request: const AdRequest(),
      size: AdSize.banner,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          setState(() {
            _isBannerAdReady = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          print('Banner Ad failed to load: $error');
          _isBannerAdReady = false;
          ad.dispose();
        },
        onAdOpened: (Ad ad) => print('Banner Ad opened.'),
        onAdClosed: (Ad ad) => print('Banner Ad closed.'),
      ),
    );
    _bannerAd!.load();
  }

  @override
  void dispose() {
    _adTimer.cancel();
    _disposeVideoPlayer();
    _bannerAd?.dispose();
    _rewardedAd?.dispose();
    super.dispose();
  }

  void _navigateToPage(BuildContext context, String title) {
    final Map<String, Widget Function(BuildContext)> pages = {
      'Tertiary': (context) => TertiaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Secondary': (context) => SecondaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Primary': (context) => PrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Pre-Primary': (context) => PrePrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      // 'Board Exam Prep': (context) => BoardExamPrepPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // REMOVED
      // 'Corporate CPD': (context) => CorporateCPDPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // REMOVED
    };

    if (pages.containsKey(title)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: pages[title]!,
        ),
      );
    }
  }

  void _showFullPageAd(BuildContext context, String adUrl) {
    print('_showFullPageAd called with URL: $adUrl');
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (BuildContext context, _, __) {
          return FullScreenAdModal(
            adUrl: adUrl,
          );
        },
      ),
    );
  }

  Widget _buildAdBanner(BuildContext context, ThemeData theme) {
    if (_adUrls.isEmpty) {
      return const SizedBox(); // Or a placeholder
    }

    final currentAdUrl = _adUrls[_currentAdIndex];
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          print('Tapped on ad URL: $currentAdUrl');
          _showFullPageAd(context, currentAdUrl);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Sponsored',
                style: TextStyle(fontSize: 12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              AspectRatio(
                aspectRatio: 16 / 9,
                child: currentAdUrl.endsWith('.mp4')
                    ? _isVideoInitialized && _adVideoController != null
                        ? VideoPlayer(_adVideoController!)
                        : const Center(child: CircularProgressIndicator()) // Show indicator while loading
                    : Image.network(
                        currentAdUrl,
                        fit: BoxFit.cover,
                        loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                          print('Error loading image: $exception');
                          return const Icon(Icons.error_outline);
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(
          'Phygital Access',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.bolt,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ListPage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(
              Icons.calculate,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CalculatePage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(
              Icons.airplay,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd(context);
            },
          ),
          IconButton(
            icon: Icon(
              Icons.self_improvement,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WellnessPage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    int crossAxisCount = 2;
                    if (constraints.maxWidth > 1200) {
                      crossAxisCount = 3; // Adjust count to accommodate the full-width ad
                    } else if (constraints.maxWidth > 800) {
                      crossAxisCount = 2;
                    }

                    return GridView.count(
                      crossAxisCount: crossAxisCount,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 1.1,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildGridItem(context, 'Tertiary', Icons.auto_awesome, theme),
                        _buildGridItem(context, 'Secondary', Icons.star, theme),
                        _buildGridItem(context, 'Primary', Icons.workspace_premium, theme),
                        _buildGridItem(context, 'Pre-Primary', Icons.lightbulb, theme),
                        // _buildGridItem(context, 'Board Exam Prep', Icons.archive, theme), // REMOVED
                        // _buildGridItem(context, 'Corporate CPD', Icons.next_week, theme), // REMOVED
                      ],
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildAdBanner(context, theme),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // AdMob Banner will be placed here
          Container(
            color: theme.colorScheme.surface,
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.home,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () {
                        // Action for Home button
                      },
                    ),
                    const SizedBox(width: 24),
                    IconButton(
                      icon: Icon(
                        _isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: _toggleTheme,
                    ),
                    const SizedBox(width: 24),
                    IconButton(
                      icon: Icon(
                        Icons.person_outline,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoginPage(
                              isDarkMode: _isDarkMode,
                              toggleTheme: _toggleTheme!,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_isBannerAdReady && _bannerAd != null)
            SizedBox(
              width: MediaQuery.of(context).size.width, // Cover the whole width
              height: AdSize.banner.height.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToPage(context, title),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FullScreenAdModal extends StatefulWidget {
  final String adUrl;

  const FullScreenAdModal({Key? key, required this.adUrl}) : super(key: key);

  @override
  State<FullScreenAdModal> createState() => _FullScreenAdModalState();
}

class _FullScreenAdModalState extends State<FullScreenAdModal> {
  VideoPlayerController? _localVideoController;
  Future<void>? _initializeVideoPlayerFuture;

  @override
  void initState() {
    super.initState();
    print('FullScreenAdModal initState with URL: ${widget.adUrl}');
    if (widget.adUrl.endsWith('.mp4')) {
      _localVideoController = VideoPlayerController.network(widget.adUrl);
      _initializeVideoPlayerFuture = _localVideoController!.initialize().then((_) {
        _localVideoController!.setVolume(1.0); // Unmute the video in the modal
        _localVideoController!.play();
        _localVideoController!.setLooping(true);
      });
    }
  }

  @override
  void dispose() {
    print('FullScreenAdModal dispose');
    if (_localVideoController != null) {
      _localVideoController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.9),
      body: Center( // Center the content vertically and horizontally
        child: SingleChildScrollView( // Make the content scrollable if it's too large
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              if (widget.adUrl.endsWith('.mp4'))
                FutureBuilder(
                  future: _initializeVideoPlayerFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            if (_localVideoController!.value.isPlaying) {
                              _localVideoController!.pause();
                            } else {
                              _localVideoController!.play();
                            }
                          });
                        },
                        child: AspectRatio(
                          aspectRatio: _localVideoController!.value.aspectRatio,
                          child: VideoPlayer(_localVideoController!),
                        ),
                      );
                    } else if (snapshot.hasError) {
                      return const Center(child: Icon(Icons.error));
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                )
              else
                Image.network(
                  widget.adUrl,
                  fit: BoxFit.contain, // Ensure the image fits within the modal
                  loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    );
                  },
                  errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                    print('Error loading full screen image: $exception');
                    return const Icon(Icons.error_outline);
                  },
                ),
              Positioned(
                top: 20,
                right: 20,
                child: SafeArea(
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}