import 'package:flutter/material.dart';
import 'login_page.dart';

class TimelineEvent {
  final String year;
  final String title;
  final String description;
  final IconData icon;

  TimelineEvent({
    required this.year,
    required this.title,
    required this.description,
    required this.icon,
  });
}

class TertiaryTimelinePage extends StatelessWidget {
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  TertiaryTimelinePage({
    Key? key,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  // Example timeline events - replace with actual institution history
  final List<TimelineEvent> events = [
    TimelineEvent(
      year: '1965',
      title: 'Institution Founded',
      description: 'The institution was established as a center of excellence in education.',
      icon: Icons.foundation,
    ),
    TimelineEvent(
      year: '1970',
      title: 'First Graduation',
      description: 'The first class of students graduated from the institution.',
      icon: Icons.school,
    ),
    TimelineEvent(
      year: '1985',
      title: 'Research Center',
      description: 'Establishment of the first research center.',
      icon: Icons.science,
    ),
    TimelineEvent(
      year: '2000',
      title: 'Digital Revolution',
      description: 'Implementation of digital learning systems.',
      icon: Icons.computer,
    ),
    TimelineEvent(
      year: '2010',
      title: 'International Programs',
      description: 'Launch of international exchange programs.',
      icon: Icons.public,
    ),
    TimelineEvent(
      year: '2020',
      title: 'Modern Era',
      description: 'Adoption of hybrid learning models.',
      icon: Icons.devices,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Historical Timeline',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: ListView.builder(
        itemCount: events.length,
        padding: const EdgeInsets.all(16),
        itemBuilder: (context, index) {
          final event = events[index];
          final isFirst = index == 0;
          final isLast = index == events.length - 1;

          return IntrinsicHeight(
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child: Text(
                    event.year,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.secondary,
                    ),
                  ),
                ),
                Column(
                  children: [
                    SizedBox(
                      height: 12,
                      child: Center(
                        child: Container(
                          width: 2,
                          color: isFirst ? Colors.transparent : theme.colorScheme.secondary,
                        ),
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.secondary,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        event.icon,
                        color: theme.colorScheme.onSecondary,
                        size: 24,
                      ),
                    ),
                    SizedBox(
                      height: 12,
                      child: Center(
                        child: Container(
                          width: 2,
                          color: isLast ? Colors.transparent : theme.colorScheme.secondary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.title,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            event.description,
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: NavigationBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        height: 65,
        selectedIndex: 0,
        destinations: [
          NavigationDestination(
            icon: Icon(
              Icons.home_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.home,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.person_outline,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.person,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
        ],
        onDestinationSelected: (index) {
          if (index == 0) {
            Navigator.of(context).popUntil((route) => route.isFirst);
          } else if (index == 1) {
            toggleTheme();
          } else if (index == 2) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginPage(
                  isDarkMode: isDarkMode,
                  toggleTheme: toggleTheme,
                ),
              ),
            );
          }
        },
      ),
    );
  }
}
