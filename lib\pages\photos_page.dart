// photos_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'photo_detail_page.dart';

class PhotosPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPhotos;
  final bool isFromDetailPage;

  const PhotosPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPhotos,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PhotosPage> createState() => _PhotosPageState();
}

class _PhotosPageState extends State<PhotosPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('photos_list');
  List<Map<String, dynamic>> _photos = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PhotosPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PhotosPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PhotosPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PhotosPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PhotosPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPhotos != null && widget.preloadedPhotos!.isNotEmpty) {
      print("Preloaded photos found, using them.");
      setState(() {
        _photos = List<Map<String, dynamic>>.from(widget.preloadedPhotos!);
        _photos.forEach((photo) {
          photo['_isImageLoading'] = false;
        });
        _photos.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _hasMore = widget.preloadedPhotos!.length == _pageSize;
      });
      _loadPhotosFromSupabase(initialLoad: false);
    } else {
      print("No preloaded photos or empty list, loading from database.");
      _loadPhotosFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadPhotosFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadPhotosFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final photosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_photos';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(photosTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedPhotos =
          await _updatePhotoImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _photos = updatedPhotos;
        } else {
          _photos.addAll(updatedPhotos);
        }
        _photos.forEach((photo) {
          photo['_isImageLoading'] = false;
        });
        _photos.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the photos
      _cachePhotos(_photos);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching photos: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updatePhotoImageUrls(
      List<Map<String, dynamic>> photos) async {
    List<Future<void>> futures = [];
    for (final photo in photos) {
      if (photo['image_url'] == null ||
          photo['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(photo));
      }
    }
    await Future.wait(futures);
    return photos;
  }

  Future<void> _cachePhotos(List<Map<String, dynamic>> photos) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String photosJson = jsonEncode(photos);
      await prefs.setString(
          'photos_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          photosJson);
    } catch (e) {
      print('Error caching photos: $e');
    }
  }
  
  void _setupRealtime() {
    final photosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_photos';
    _realtimeChannel = Supabase.instance.client
        .channel('photos')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: photosTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newPhotoId = payload.newRecord['id'];
          final newPhotoResponse = await Supabase.instance.client
              .from(photosTableName)
              .select('*')
              .eq('id', newPhotoId)
              .single();
          if (mounted) {
            Map<String, dynamic> newPhoto = Map.from(newPhotoResponse);
            final updatedPhoto = await _updatePhotoImageUrls([newPhoto]);
            setState(() {
              _photos = [..._photos, updatedPhoto.first];
              updatedPhoto.first['_isImageLoading'] = false;
              _photos.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedPhotoId = payload.newRecord['id'];
          final updatedPhotoResponse = await Supabase.instance.client
              .from(photosTableName)
              .select('*')
              .eq('id', updatedPhotoId)
              .single();
          if (mounted) {
            final updatedPhoto = Map<String, dynamic>.from(updatedPhotoResponse);
            setState(() {
              _photos = _photos.map((photo) {
                return photo['id'] == updatedPhoto['id'] ? updatedPhoto : photo;
              }).toList();
              _photos.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedPhotoId = payload.oldRecord['id'];
          setState(() {
            _photos.removeWhere((photo) => photo['id'] == deletedPhotoId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMorePhotos();
    }
  }

  Future<void> _loadMorePhotos() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadPhotosFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> photo) async {
    if (photo['_isImageLoading'] == true) {
      print('Image loading already in progress for ${photo['fullname']}, skipping.');
      return;
    }
    if (photo['image_url'] != null &&
        photo['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${photo['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      photo['_isImageLoading'] = true;
    });

    final fullname = photo['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegePhotoBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/photos';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegePhotoBucket');
    print('Image URL before fetch: ${photo['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegePhotoBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegePhotoBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        photo['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        photo['_isImageLoading'] = false;
        print('Setting image_url for ${photo['fullname']} to: ${photo['image_url']}');
      });
    } else {
      photo['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> photo) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PhotoDetailPage(
            photo: photo,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> photo) {
    final day = photo['day'] as int?;
    final month = photo['month'] as int?;
    final year = photo['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("PhotosPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Photos',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _photos.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadPhotosFromSupabase(initialLoad: true),
              child: _photos.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No photos available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : GridView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.75,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 10,
                      ),
                      padding: const EdgeInsets.all(16),
                      itemCount: _photos.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _photos.length) {
                          final photo = _photos[index];
                          final hasLink = photo['link'] != null && photo['link'].toString().isNotEmpty;
                          final dateStr = _formatDate(photo);
                          final platform = photo['platform'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('photo_${photo['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (photo['image_url'] == null ||
                                      photo['image_url'] == 'assets/placeholder_image.png') &&
                                  !photo['_isImageLoading']) {
                                _fetchImageUrl(photo);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              clipBehavior: Clip.antiAlias,
                              child: InkWell(
                                onTap: () => _navigateToDetail(context, photo),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Stack(
                                        children: [
                                          SizedBox(
                                            width: double.infinity,
                                            height: double.infinity,
                                            child: CachedNetworkImage(
                                              imageUrl: photo['image_url'] ??
                                                  'assets/placeholder_image.png',
                                              errorWidget: (context, url, error) =>
                                                  Image.asset('assets/placeholder_image.png'),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                          if (hasLink)
                                            Positioned(
                                              top: 8,
                                              right: 8,
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: theme.colorScheme.primary.withOpacity(0.8),
                                                  borderRadius: BorderRadius.circular(16),
                                                ),
                                                child: IconButton(
                                                  icon: const Icon(
                                                    Icons.link,
                                                    color: Colors.white,
                                                    size: 16,
                                                  ),
                                                  onPressed: () => _launchURL(photo['link']),
                                                  tooltip: 'Visit website',
                                                  constraints: const BoxConstraints(
                                                    minWidth: 32,
                                                    minHeight: 32,
                                                  ),
                                                  padding: EdgeInsets.zero,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            photo['fullname'] ?? 'Unnamed Photo',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: theme.colorScheme.onSurface,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                          if (dateStr.isNotEmpty) ...[
                                            const SizedBox(height: 4),
                                            Text(
                                              dateStr,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: theme.colorScheme.secondary,
                                              ),
                                            ),
                                          ],
                                          if (platform.isNotEmpty) ...[
                                            const SizedBox(height: 4),
                                            Text(
                                              platform,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: theme.colorScheme.secondary,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
