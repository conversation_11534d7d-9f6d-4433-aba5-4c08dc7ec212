import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SustainabilityDetailPage extends StatefulWidget {
  final Map<String, dynamic> sustainabilityInitiative;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SustainabilityDetailPage({
    Key? key,
    required this.sustainabilityInitiative,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SustainabilityDetailPage> createState() => _SustainabilityDetailPageState();
}

class _SustainabilityDetailPageState extends State<SustainabilityDetailPage> {
  late RealtimeChannel _sustainabilityRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _sustainabilityRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _sustainabilityRealtimeChannel = Supabase.instance.client
        .channel('sustainability_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'sustainability',
      callback: (payload) async {
        // Manual filtering for the specific sustainability initiative
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.sustainabilityInitiative['id']) {
          print("Realtime update received for sustainability detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshSustainabilityInitiative();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshSustainabilityInitiative() async {
    try {
      final response = await Supabase.instance.client
          .from('sustainability')
          .select('*')
          .eq('id', widget.sustainabilityInitiative['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's sustainabilityInitiative with the new data
          widget.sustainabilityInitiative.clear();
          widget.sustainabilityInitiative.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing sustainability initiative: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.sustainabilityInitiative['fullname'] ?? 'Unknown';
    final String about = widget.sustainabilityInitiative['about'] ?? '';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Initiative details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.eco,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About this Initiative:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
