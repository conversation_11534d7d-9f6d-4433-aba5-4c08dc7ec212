import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'majors_page.dart';
import 'minors_page.dart';
import 'funding_page.dart';
import 'course_catalog_page.dart';
import 'grading_scale_page.dart';
import 'academic_resources_page.dart';
import 'academic_honors_page.dart';
import 'academic_prizes_page.dart';
import 'academic_dress_page.dart';

class TertiaryAcademicsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryAcademicsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryAcademicsPage> createState() => _TertiaryAcademicsPageState();
}

class _TertiaryAcademicsPageState extends State<TertiaryAcademicsPage> {
  List<Map<String, dynamic>>? _cachedMajors;
  List<Map<String, dynamic>>? _cachedMinors;
  List<Map<String, dynamic>>? _cachedFunding;
  List<Map<String, dynamic>>? _cachedCourseCatalog;
  List<Map<String, dynamic>>? _cachedGradingScale;
  List<Map<String, dynamic>>? _cachedAcademicResources;
  List<Map<String, dynamic>>? _cachedAcademicHonors;
  List<Map<String, dynamic>>? _cachedAcademicPrizes;
  List<Map<String, dynamic>>? _cachedAcademicDress;
  String? _lastCollegeName;
  bool _isLoadingMajors = false;
  bool _isLoadingMinors = false;
  bool _isLoadingFunding = false;
  bool _isLoadingCourseCatalog = false;
  bool _isLoadingGradingScale = false;
  bool _isLoadingAcademicResources = false;
  bool _isLoadingAcademicHonors = false;
  bool _isLoadingAcademicPrizes = false;
  bool _isLoadingAcademicDress = false;
  late RealtimeChannel _majorsRealtimeChannel;
  late RealtimeChannel _minorsRealtimeChannel;
  late RealtimeChannel _fundingRealtimeChannel;
  late RealtimeChannel _courseCatalogRealtimeChannel;
  late RealtimeChannel _gradingScaleRealtimeChannel;
  late RealtimeChannel _academicResourcesRealtimeChannel;
  late RealtimeChannel _academicHonorsRealtimeChannel;
  late RealtimeChannel _academicPrizesRealtimeChannel;
  late RealtimeChannel _academicDressRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryAcademicsPage initState called for ${widget.institutionName}");
    _loadCachedData();
    _loadDataFromDatabaseAndCache();
    _setupRealtimeListeners();
  }

  @override
  void dispose() {
    _majorsRealtimeChannel.unsubscribe();
    _minorsRealtimeChannel.unsubscribe();
    _fundingRealtimeChannel.unsubscribe();
    _courseCatalogRealtimeChannel.unsubscribe();
    _gradingScaleRealtimeChannel.unsubscribe();
    _academicResourcesRealtimeChannel.unsubscribe();
    _academicHonorsRealtimeChannel.unsubscribe();
    _academicPrizesRealtimeChannel.unsubscribe();
    _academicDressRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadCachedData() async {
    await _loadCachedMajors();
    await _loadCachedMinors();
    await _loadCachedFunding();
    await _loadCachedCourseCatalog();
    await _loadCachedGradingScale();
    await _loadCachedAcademicResources();
    await _loadCachedAcademicHonors();
    await _loadCachedAcademicPrizes();
    await _loadCachedAcademicDress();
  }

  void _loadDataFromDatabaseAndCache() async {
    await _loadMajorsFromDatabaseAndCache();
    await _loadMinorsFromDatabaseAndCache();
    await _loadFundingFromDatabaseAndCache();
    await _loadCourseCatalogFromDatabaseAndCache();
    await _loadGradingScaleFromDatabaseAndCache();
    await _loadAcademicResourcesFromDatabaseAndCache();
    await _loadAcademicHonorsFromDatabaseAndCache();
    await _loadAcademicPrizesFromDatabaseAndCache();
    await _loadAcademicDressFromDatabaseAndCache();
  }

  void _setupRealtimeListeners() {
    _setupMajorsRealtimeListener();
    _setupMinorsRealtimeListener();
    _setupFundingRealtimeListener();
    _setupCourseCatalogRealtimeListener();
    _setupGradingScaleRealtimeListener();
    _setupAcademicResourcesRealtimeListener();
    _setupAcademicHonorsRealtimeListener();
    _setupAcademicPrizesRealtimeListener();
    _setupAcademicDressRealtimeListener();
  }

  // Majors methods
  Future<List<Map<String, dynamic>>?> _getCachedMajors(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? majorsJson = prefs.getString(
        'majors_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (majorsJson != null) {
      List<dynamic> decodedList = jsonDecode(majorsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheMajors(String collegeName, List<Map<String, dynamic>> majors) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String majorsJson = jsonEncode(majors);
    await prefs.setString(
        'majors_${collegeName.toLowerCase().replaceAll(' ', '')}', majorsJson);
    print('Majors cached for $collegeName.');
  }

  Future<void> _loadCachedMajors() async {
    final cachedData = await _getCachedMajors(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedMajors = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded majors from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadMajorsFromDatabaseAndCache() async {
    if (_isLoadingMajors) {
      return;
    }

    setState(() {
      _isLoadingMajors = true;
    });

    print("Fetching majors for ${widget.institutionName} from database");
    final majorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_majors';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(majorsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedMajors = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingMajors = false;
          _cacheMajors(widget.institutionName, response);
          print("Majors fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingMajors = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingMajors = false;
          _cachedMajors = [];
          print("Error fetching majors for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingMajors = false;
      }
    }
  }

  void _setupMajorsRealtimeListener() {
    final majorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_majors';
    _majorsRealtimeChannel = Supabase.instance.client
        .channel('majors_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: majorsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for majors of ${widget.institutionName}: ${payload.eventType}");
        _loadMajorsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Minors methods
  Future<List<Map<String, dynamic>>?> _getCachedMinors(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? minorsJson = prefs.getString(
        'minors_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (minorsJson != null) {
      List<dynamic> decodedList = jsonDecode(minorsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheMinors(String collegeName, List<Map<String, dynamic>> minors) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String minorsJson = jsonEncode(minors);
    await prefs.setString(
        'minors_${collegeName.toLowerCase().replaceAll(' ', '')}', minorsJson);
    print('Minors cached for $collegeName.');
  }

  Future<void> _loadCachedMinors() async {
    final cachedData = await _getCachedMinors(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedMinors = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded minors from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadMinorsFromDatabaseAndCache() async {
    if (_isLoadingMinors) {
      return;
    }

    setState(() {
      _isLoadingMinors = true;
    });

    print("Fetching minors for ${widget.institutionName} from database");
    final minorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_minors';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(minorsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedMinors = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingMinors = false;
          _cacheMinors(widget.institutionName, response);
          print("Minors fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingMinors = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingMinors = false;
          _cachedMinors = [];
          print("Error fetching minors for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingMinors = false;
      }
    }
  }

  void _setupMinorsRealtimeListener() {
    final minorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_minors';
    _minorsRealtimeChannel = Supabase.instance.client
        .channel('minors_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: minorsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for minors of ${widget.institutionName}: ${payload.eventType}");
        _loadMinorsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Funding methods
  Future<List<Map<String, dynamic>>?> _getCachedFunding(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? fundingJson = prefs.getString(
        'funding_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (fundingJson != null) {
      List<dynamic> decodedList = jsonDecode(fundingJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheFunding(String collegeName, List<Map<String, dynamic>> funding) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String fundingJson = jsonEncode(funding);
    await prefs.setString(
        'funding_${collegeName.toLowerCase().replaceAll(' ', '')}', fundingJson);
    print('Funding cached for $collegeName.');
  }

  Future<void> _loadCachedFunding() async {
    final cachedData = await _getCachedFunding(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedFunding = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded funding from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadFundingFromDatabaseAndCache() async {
    if (_isLoadingFunding) {
      return;
    }

    setState(() {
      _isLoadingFunding = true;
    });

    print("Fetching funding for ${widget.institutionName} from database");
    final fundingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_funding';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(fundingTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedFunding = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingFunding = false;
          _cacheFunding(widget.institutionName, response);
          print("Funding fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingFunding = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingFunding = false;
          _cachedFunding = [];
          print("Error fetching funding for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingFunding = false;
      }
    }
  }

  void _setupFundingRealtimeListener() {
    final fundingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_funding';
    _fundingRealtimeChannel = Supabase.instance.client
        .channel('funding_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: fundingTableName,
      callback: (payload) async {
        print(
            "Realtime update received for funding of ${widget.institutionName}: ${payload.eventType}");
        _loadFundingFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Course Catalog methods
  Future<List<Map<String, dynamic>>?> _getCachedCourseCatalog(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? courseCatalogJson = prefs.getString(
        'coursecatalog_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (courseCatalogJson != null) {
      List<dynamic> decodedList = jsonDecode(courseCatalogJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheCourseCatalog(String collegeName, List<Map<String, dynamic>> courseCatalog) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String courseCatalogJson = jsonEncode(courseCatalog);
    await prefs.setString(
        'coursecatalog_${collegeName.toLowerCase().replaceAll(' ', '')}', courseCatalogJson);
    print('Course Catalog cached for $collegeName.');
  }

  Future<void> _loadCachedCourseCatalog() async {
    final cachedData = await _getCachedCourseCatalog(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedCourseCatalog = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded course catalog from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadCourseCatalogFromDatabaseAndCache() async {
    if (_isLoadingCourseCatalog) {
      return;
    }

    setState(() {
      _isLoadingCourseCatalog = true;
    });

    print("Fetching course catalog for ${widget.institutionName} from database");
    final courseCatalogTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_coursecatalog';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(courseCatalogTableName)
          .select('*')
          .order('modulecode', ascending: true);

      if (mounted) {
        setState(() {
          _cachedCourseCatalog = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingCourseCatalog = false;
          _cacheCourseCatalog(widget.institutionName, response);
          print("Course Catalog fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingCourseCatalog = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingCourseCatalog = false;
          _cachedCourseCatalog = [];
          print("Error fetching course catalog for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingCourseCatalog = false;
      }
    }
  }

  void _setupCourseCatalogRealtimeListener() {
    final courseCatalogTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_coursecatalog';
    _courseCatalogRealtimeChannel = Supabase.instance.client
        .channel('coursecatalog_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: courseCatalogTableName,
      callback: (payload) async {
        print(
            "Realtime update received for course catalog of ${widget.institutionName}: ${payload.eventType}");
        _loadCourseCatalogFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Grading Scale methods
  Future<List<Map<String, dynamic>>?> _getCachedGradingScale(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? gradingScaleJson = prefs.getString(
        'gradingscale_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (gradingScaleJson != null) {
      List<dynamic> decodedList = jsonDecode(gradingScaleJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheGradingScale(String collegeName, List<Map<String, dynamic>> gradingScale) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String gradingScaleJson = jsonEncode(gradingScale);
    await prefs.setString(
        'gradingscale_${collegeName.toLowerCase().replaceAll(' ', '')}', gradingScaleJson);
    print('Grading Scale cached for $collegeName.');
  }

  Future<void> _loadCachedGradingScale() async {
    final cachedData = await _getCachedGradingScale(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedGradingScale = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded grading scale from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadGradingScaleFromDatabaseAndCache() async {
    if (_isLoadingGradingScale) {
      return;
    }

    setState(() {
      _isLoadingGradingScale = true;
    });

    print("Fetching grading scale for ${widget.institutionName} from database");
    final gradingScaleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_gradingscale';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(gradingScaleTableName)
          .select('*')
          .order('mark', ascending: false);

      if (mounted) {
        setState(() {
          _cachedGradingScale = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingGradingScale = false;
          _cacheGradingScale(widget.institutionName, response);
          print("Grading Scale fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingGradingScale = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingGradingScale = false;
          _cachedGradingScale = [];
          print("Error fetching grading scale for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingGradingScale = false;
      }
    }
  }

  void _setupGradingScaleRealtimeListener() {
    final gradingScaleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_gradingscale';
    _gradingScaleRealtimeChannel = Supabase.instance.client
        .channel('gradingscale_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: gradingScaleTableName,
      callback: (payload) async {
        print(
            "Realtime update received for grading scale of ${widget.institutionName}: ${payload.eventType}");
        _loadGradingScaleFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Academic Resources methods
  Future<List<Map<String, dynamic>>?> _getCachedAcademicResources(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? academicResourcesJson = prefs.getString(
        'academicresources_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (academicResourcesJson != null) {
      List<dynamic> decodedList = jsonDecode(academicResourcesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheAcademicResources(String collegeName, List<Map<String, dynamic>> academicResources) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String academicResourcesJson = jsonEncode(academicResources);
    await prefs.setString(
        'academicresources_${collegeName.toLowerCase().replaceAll(' ', '')}', academicResourcesJson);
    print('Academic Resources cached for $collegeName.');
  }

  Future<void> _loadCachedAcademicResources() async {
    final cachedData = await _getCachedAcademicResources(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedAcademicResources = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded academic resources from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadAcademicResourcesFromDatabaseAndCache() async {
    if (_isLoadingAcademicResources) {
      return;
    }

    setState(() {
      _isLoadingAcademicResources = true;
    });

    print("Fetching academic resources for ${widget.institutionName} from database");
    final academicResourcesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicresources';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(academicResourcesTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedAcademicResources = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingAcademicResources = false;
          _cacheAcademicResources(widget.institutionName, response);
          print("Academic Resources fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingAcademicResources = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingAcademicResources = false;
          _cachedAcademicResources = [];
          print("Error fetching academic resources for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingAcademicResources = false;
      }
    }
  }

  void _setupAcademicResourcesRealtimeListener() {
    final academicResourcesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicresources';
    _academicResourcesRealtimeChannel = Supabase.instance.client
        .channel('academicresources_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicResourcesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for academic resources of ${widget.institutionName}: ${payload.eventType}");
        _loadAcademicResourcesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Academic Honors methods
  Future<List<Map<String, dynamic>>?> _getCachedAcademicHonors(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? academicHonorsJson = prefs.getString(
        'academichonors_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (academicHonorsJson != null) {
      List<dynamic> decodedList = jsonDecode(academicHonorsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheAcademicHonors(String collegeName, List<Map<String, dynamic>> academicHonors) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String academicHonorsJson = jsonEncode(academicHonors);
    await prefs.setString(
        'academichonors_${collegeName.toLowerCase().replaceAll(' ', '')}', academicHonorsJson);
    print('Academic Honors cached for $collegeName.');
  }

  Future<void> _loadCachedAcademicHonors() async {
    final cachedData = await _getCachedAcademicHonors(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedAcademicHonors = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded academic honors from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadAcademicHonorsFromDatabaseAndCache() async {
    if (_isLoadingAcademicHonors) {
      return;
    }

    setState(() {
      _isLoadingAcademicHonors = true;
    });

    print("Fetching academic honors for ${widget.institutionName} from database");
    final academicHonorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academichonors';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(academicHonorsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedAcademicHonors = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingAcademicHonors = false;
          _cacheAcademicHonors(widget.institutionName, response);
          print("Academic Honors fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingAcademicHonors = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingAcademicHonors = false;
          _cachedAcademicHonors = [];
          print("Error fetching academic honors for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingAcademicHonors = false;
      }
    }
  }

  void _setupAcademicHonorsRealtimeListener() {
    final academicHonorsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academichonors';
    _academicHonorsRealtimeChannel = Supabase.instance.client
        .channel('academichonors_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicHonorsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for academic honors of ${widget.institutionName}: ${payload.eventType}");
        _loadAcademicHonorsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Academic Prizes methods
  Future<List<Map<String, dynamic>>?> _getCachedAcademicPrizes(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? academicPrizesJson = prefs.getString(
        'academicprizes_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (academicPrizesJson != null) {
      List<dynamic> decodedList = jsonDecode(academicPrizesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheAcademicPrizes(String collegeName, List<Map<String, dynamic>> academicPrizes) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String academicPrizesJson = jsonEncode(academicPrizes);
    await prefs.setString(
        'academicprizes_${collegeName.toLowerCase().replaceAll(' ', '')}', academicPrizesJson);
    print('Academic Prizes cached for $collegeName.');
  }

  Future<void> _loadCachedAcademicPrizes() async {
    final cachedData = await _getCachedAcademicPrizes(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedAcademicPrizes = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded academic prizes from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadAcademicPrizesFromDatabaseAndCache() async {
    if (_isLoadingAcademicPrizes) {
      return;
    }

    setState(() {
      _isLoadingAcademicPrizes = true;
    });

    print("Fetching academic prizes for ${widget.institutionName} from database");
    final academicPrizesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicprizes';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(academicPrizesTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedAcademicPrizes = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingAcademicPrizes = false;
          _cacheAcademicPrizes(widget.institutionName, response);
          print("Academic Prizes fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingAcademicPrizes = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingAcademicPrizes = false;
          _cachedAcademicPrizes = [];
          print("Error fetching academic prizes for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingAcademicPrizes = false;
      }
    }
  }

  void _setupAcademicPrizesRealtimeListener() {
    final academicPrizesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicprizes';
    _academicPrizesRealtimeChannel = Supabase.instance.client
        .channel('academicprizes_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicPrizesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for academic prizes of ${widget.institutionName}: ${payload.eventType}");
        _loadAcademicPrizesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Academic Dress methods
  Future<List<Map<String, dynamic>>?> _getCachedAcademicDress(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? academicDressJson = prefs.getString(
        'academicdress_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (academicDressJson != null) {
      List<dynamic> decodedList = jsonDecode(academicDressJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheAcademicDress(String collegeName, List<Map<String, dynamic>> academicDress) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String academicDressJson = jsonEncode(academicDress);
    await prefs.setString(
        'academicdress_${collegeName.toLowerCase().replaceAll(' ', '')}', academicDressJson);
    print('Academic Dress cached for $collegeName.');
  }

  Future<void> _loadCachedAcademicDress() async {
    final cachedData = await _getCachedAcademicDress(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedAcademicDress = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded academic dress from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadAcademicDressFromDatabaseAndCache() async {
    if (_isLoadingAcademicDress) {
      return;
    }

    setState(() {
      _isLoadingAcademicDress = true;
    });

    print("Fetching academic dress for ${widget.institutionName} from database");
    final academicDressTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicdress';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(academicDressTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedAcademicDress = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingAcademicDress = false;
          _cacheAcademicDress(widget.institutionName, response);
          print("Academic Dress fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingAcademicDress = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingAcademicDress = false;
          _cachedAcademicDress = [];
          print("Error fetching academic dress for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingAcademicDress = false;
      }
    }
  }

  void _setupAcademicDressRealtimeListener() {
    final academicDressTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academicdress';
    _academicDressRealtimeChannel = Supabase.instance.client
        .channel('academicdress_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: academicDressTableName,
      callback: (payload) async {
        print(
            "Realtime update received for academic dress of ${widget.institutionName}: ${payload.eventType}");
        _loadAcademicDressFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Courses/Majors') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => MajorsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedMajors: _cachedMajors,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Courses/Minors') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => MinorsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedMinors: _cachedMinors,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Scholarships/Funding') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => FundingPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedFunding: _cachedFunding,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Course Catalog') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CourseCatalogPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedCourseCatalog: _cachedCourseCatalog,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Grading Scale') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GradingScalePage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedGradingScale: _cachedGradingScale,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Academic Resources') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AcademicResourcesPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedAcademicResources: _cachedAcademicResources,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Academic Honors') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AcademicHonorsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedAcademicHonors: _cachedAcademicHonors,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Academic Prizes') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AcademicPrizesPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedAcademicPrizes: _cachedAcademicPrizes,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Academic Dress') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AcademicDressPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedAcademicDress: _cachedAcademicDress,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Courses/Majors', 'icon': Icons.school_outlined},
      {'title': 'Courses/Minors', 'icon': Icons.class_outlined},
      {'title': 'Scholarships/Funding', 'icon': Icons.monetization_on},
      {'title': 'Course Catalog', 'icon': Icons.menu_book},
      {'title': 'Grading Scale', 'icon': Icons.settings_system_daydream},
      {'title': 'Academic Resources', 'icon': Icons.library_books},
      {'title': 'Academic Honors', 'icon': Icons.military_tech},
      {'title': 'Academic Prizes', 'icon': Icons.emoji_events},
      {'title': 'Academic Dress', 'icon': Icons.checkroom},
    ];

    // Filter out 'Courses/Majors', 'Courses/Minors', and 'Short Courses' when not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage &&
          (item['title'] == 'Courses/Majors' ||
              item['title'] == 'Courses/Minors' ||
              item['title'] == 'Short Courses')) {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academics',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
