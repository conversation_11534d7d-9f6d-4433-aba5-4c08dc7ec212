import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:pdfx/pdfx.dart' as pdfx;
import 'package:http/http.dart' as http;

class OfflineLibraryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const OfflineLibraryPage(
      {Key? key, required this.isDarkMode, required this.toggleTheme})
      : super(key: key);

  @override
  State<OfflineLibraryPage> createState() => _OfflineLibraryPageState();
}

class _OfflineLibraryPageState extends State<OfflineLibraryPage> {
  List<Map<String, String>> _pdfFiles = [];
  bool _isLoading = true;
  final String _supabaseBucket = 'offlinelibrary';
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  late SharedPreferences _prefs;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadPdfFilesFromSupabase();
  }

  Future<void> _loadPdfFilesFromSupabase() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final List<FileObject> response =
          await _supabaseClient.storage.from(_supabaseBucket).list();

      print('Supabase Storage List Response: $response');

      setState(() {
        _pdfFiles = response.map((item) {
          final fileName = item.name;
          final fileUrl =
              _supabaseClient.storage.from(_supabaseBucket).getPublicUrl(fileName);
          return {'name': fileName, 'url': fileUrl};
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading PDF files from Supabase: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadFile(String fileName, String fileUrl) async {
    try {
      if (kIsWeb) {
        // Web platform download logic (using http package)
        final response = await http.get(Uri.parse(fileUrl));

        if (response.statusCode == 200) {
          // For web, we are not actually downloading the file to local storage in this simplified example.
          // Instead, we are just marking it as "downloaded" in SharedPreferences so that
          // the app knows it's "ready" to be viewed online.  For true offline web support,
          // you would need to use browser-specific storage APIs like IndexedDB to store the file content.
          await _prefs.setBool('offline_$fileName', true);

          setState(() {});
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('File ready for viewing: $fileName (Online)')), // Indicate online viewing for web
          );
        } else {
          throw Exception('Failed to download file (web): ${response.statusCode}'); // More specific error message for web
        }
      } else {
        // Native platform download logic (using path_provider)
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$fileName';
        final file = File(filePath);

        final response = await _supabaseClient.storage
            .from(_supabaseBucket)
            .download(fileName);

        await file.writeAsBytes(response);
        await _prefs.setBool('offline_$fileName', true);
        setState(() {});
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Downloaded $fileName (Offline)')), // Indicate offline for native
        );
      }
    } catch (e) {
      print('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to download $fileName: ${e.toString()}')), // Include error details
      );
    }
  }

  Future<void> _deleteFile(String fileName) async {
    try {
      if (kIsWeb) {
        // For web, just remove the downloaded flag from SharedPreferences
        await _prefs.remove('offline_$fileName');
        setState(() {});
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download removed for $fileName (Web)')),
        );
      } else {
        // Native platform delete logic
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$fileName';
        final file = File(filePath);

        if (await file.exists()) {
          await file.delete();
          await _prefs.remove('offline_$fileName');
          setState(() {});
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Deleted $fileName (Offline)')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('File not found locally: $fileName')),
          );
        }
      }
    } catch (e) {
      print('Error deleting file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete $fileName: ${e.toString()}')),
      );
    }
  }


  bool _isDownloaded(String fileName) {
    return _prefs.getBool('offline_$fileName') ?? false;
  }

  Future<File?> _getLocalFile(String fileName) async {
    if (kIsWeb) {
      return null; // No direct local file access on web in this example
    } else {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      return await file.exists() ? file : null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text('Offline Library',
            style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(color: theme.colorScheme.primary))
          : _pdfFiles.isEmpty
              ? Center(
                  child: Text('No PDF files found in the Supabase bucket',
                      style: TextStyle(color: theme.colorScheme.onSurface)))
              : Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            itemCount: _pdfFiles.length,
                            itemBuilder: (context, index) {
                              final fileName = _pdfFiles[index]['name']!;
                              final fileUrl = _pdfFiles[index]['url']!;
                              final isDownloaded = _isDownloaded(fileName);
                              return ListTile(
                                leading: Icon(Icons.picture_as_pdf,
                                    color: Colors.red.shade400),
                                title: Text(
                                  fileName,
                                  style: TextStyle(
                                      color: theme.colorScheme.onSurface),
                                ),
                                trailing: Row( // Use Row to arrange icons and text
                                  mainAxisSize: MainAxisSize.min, // Make Row take minimal space
                                  children: [
                                    if (isDownloaded)
                                      Padding(
                                        padding: const EdgeInsets.only(right: 8.0),
                                        child: Text("Downloaded", style: TextStyle(color: Colors.green)),
                                      ),
                                    if (isDownloaded)
                                      IconButton(
                                        icon: const Icon(Icons.delete, color: Colors.red),
                                        onPressed: () async {
                                          await _deleteFile(fileName);
                                        },
                                      ),
                                    if (!isDownloaded)
                                      IconButton(
                                        icon: const Icon(Icons.download_outlined),
                                        onPressed: () async {
                                          await _downloadFile(fileName, fileUrl);
                                        },
                                      ),
                                    if (isDownloaded && !kIsWeb) // Show checkmark only for native downloaded files
                                      const Icon(Icons.check_circle_outline, color: Colors.green),
                                  ],
                                ),
                                onTap: () async {
                                  print('Tapped on PDF: $fileName');
                                  if (kIsWeb || isDownloaded) {
                                    if (kIsWeb) {
                                      // Open online URL for web
                                      final Uri _url = Uri.parse(fileUrl);
                                      if (!await launchUrl(_url)) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text(
                                                  'Could not launch $fileUrl')),
                                        );
                                      }
                                    } else {
                                      // Open downloaded file for mobile/desktop
                                      final pdfFile =
                                          await _getLocalFile(fileName);
                                      if (pdfFile != null) {
                                        print(
                                            'Opening downloaded PDF from: ${pdfFile.path}');
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => PdfViewPage(
                                              pdfFile: pdfFile,
                                              title: fileName,
                                            ),
                                          ),
                                        );
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text(
                                                  'Error opening downloaded PDF: $fileName')),
                                        );
                                      }
                                    }
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }
}

class PdfViewPage extends StatefulWidget {
  final String title;
  final File pdfFile;

  const PdfViewPage({Key? key, required this.pdfFile, required this.title})
      : super(key: key);

  @override
  State<PdfViewPage> createState() => _PdfViewPageState();
}

class _PdfViewPageState extends State<PdfViewPage> {
  pdfx.PdfController? _pdfController;
  int? _totalPages;
  int _currentPage = 1;
  final TextEditingController _pageNumberController = TextEditingController();
  bool _isEditingPageNumber = false;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  void _loadDocument() async {
    _pdfController = pdfx.PdfController(
      document: pdfx.PdfDocument.openFile(widget.pdfFile.path),
    );
  }

  @override
  void dispose() {
    _pdfController?.dispose();
    _pageNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(widget.title,
            style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: _pdfController == null
          ? Center(
              child: CircularProgressIndicator(color: theme.colorScheme.primary))
          : Stack(
              children: [
                pdfx.PdfView(
                  controller: _pdfController!,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  onDocumentLoaded: (document) {
                    setState(() {
                      _totalPages = document.pagesCount;
                    });
                  },
                ),
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _isEditingPageNumber = true;
                        _pageNumberController.text = _currentPage.toString();
                      });
                    },
                    child: _isEditingPageNumber
                        ? SizedBox(
                            width: 80,
                            child: TextField(
                              controller: _pageNumberController,
                              keyboardType: TextInputType.number,
                              textAlign: TextAlign.center,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 8.0),
                              ),
                              onSubmitted: (value) {
                                _goToPage(value);
                                setState(() {
                                  _isEditingPageNumber = false;
                                });
                              },
                              onTapOutside: (_) {
                                setState(() {
                                  _isEditingPageNumber = false;
                                });
                              },
                            ),
                          )
                        : Text(
                            '$_currentPage/${_totalPages ?? '?'}',
                            style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                fontSize: 16),
                          ),
                  ),
                ),
              ],
            ),
    );
  }

  void _goToPage(String value) {
    final pageNumber = int.tryParse(value);
    if (pageNumber != null &&
        pageNumber >= 1 &&
        pageNumber <= (_totalPages ?? 0)) {
      _pdfController?.jumpToPage(pageNumber);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid page number')),
      );
    }
  }
}