// downloader_stub.dart
class FlutterDownloader {
  static Future<void> initialize({bool debug = false}) async {
    // Do nothing on web.
  }

  static void registerCallback(Function callback) {
    // Do nothing on web.
  }

  static Future<String?> enqueue({
    required String url,
    required String savedDir,
    bool showNotification = false,
    bool openFileFromNotification = false,
    bool saveInPublicStorage = false,
  }) async {
    // Do nothing on web; you could also simply open the URL externally.
    return null;
  }

  static void removeCallback() {
    // Do nothing on web.
  }
}
