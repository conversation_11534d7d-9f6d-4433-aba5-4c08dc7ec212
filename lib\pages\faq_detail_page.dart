import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard if ever needed
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';

class FAQDetailPage extends StatefulWidget {
  final Map<String, dynamic> faq;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const FAQDetailPage({
    Key? key,
    required this.faq,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<FAQDetailPage> createState() => _FAQDetailPageState();
}

class _FAQDetailPageState extends State<FAQDetailPage> {
  late RealtimeChannel _realtimeChannel;
  String _selectedQuestionType = '';
  List<String> _questionTypes = [];

  @override
  void initState() {
    super.initState();
    _initializeQuestionTypes();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _initializeQuestionTypes() {
    final faq = widget.faq;
    final types = <String>[];
    void addIf(String key, String label) {
      final s = faq[key] as String?;
      if (s != null && s.isNotEmpty) types.add(label);
    }

    addIf('studentquestion', 'Student');
    addIf('facultyorstaffquestion', 'Faculty/Staff');
    addIf('parentquestion', 'Parent');
    addIf('admissionsquestion', 'Admissions');
    addIf('orientationquestion', 'Orientation');
    addIf('symposiumquestion', 'Symposium');
    addIf('graduationquestion', 'Graduation');
    addIf('alumniquestion', 'Alumni');

    setState(() {
      _questionTypes = types;
      if (types.isNotEmpty) _selectedQuestionType = types.first;
    });
  }

  void _setupRealtimeListener() {
    _realtimeChannel = Supabase.instance.client
        .channel('faq_detail_channel')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'faqs',
          callback: (payload) {
            if (payload.newRecord['id'] == widget.faq['id']) {
              _refreshFAQ();
            }
          },
        )
        .subscribe();
  }

  Future<void> _refreshFAQ() async {
    try {
      final updated = await Supabase.instance.client
          .from('faqs')
          .select('*')
          .eq('id', widget.faq['id'])
          .single();
      if (mounted && updated != null) {
        setState(() {
          widget.faq
            ..clear()
            ..addAll(Map<String, dynamic>.from(updated));
          _initializeQuestionTypes();
        });
      }
    } catch (e) {
      print('Error refreshing FAQ: $e');
    }
  }

  String _questionFor(String type) {
    switch (type) {
      case 'Student':
        return widget.faq['studentquestion'] ?? '';
      case 'Faculty/Staff':
        return widget.faq['facultyorstaffquestion'] ?? '';
      case 'Parent':
        return widget.faq['parentquestion'] ?? '';
      case 'Admissions':
        return widget.faq['admissionsquestion'] ?? '';
      case 'Orientation':
        return widget.faq['orientationquestion'] ?? '';
      case 'Symposium':
        return widget.faq['symposiumquestion'] ?? '';
      case 'Graduation':
        return widget.faq['graduationquestion'] ?? '';
      case 'Alumni':
        return widget.faq['alumniquestion'] ?? '';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final faq = widget.faq;

    final fullname = faq['fullname'] as String? ?? 'Unknown';
    final topic = faq['topic'] as String? ?? '';
    final about = faq['about'] as String? ?? '';
    final dept = faq['department'] as String? ?? '';
    final school = faq['school'] as String? ?? '';
    final question = _questionFor(_selectedQuestionType);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(fullname,
            style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface)),
      ),
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // Header card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment:
                      CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: isDark
                              ? Colors.white
                                  .withOpacity(0.1)
                              : Colors.black
                                  .withOpacity(0.1),
                          child: Icon(Icons.question_answer,
                              size: 30,
                              color: isDark
                                  ? Colors.white
                                  : Colors.black),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment:
                                CrossAxisAlignment.start,
                            children: [
                              Text(fullname,
                                  style: TextStyle(
                                      fontSize: 20,
                                      fontWeight:
                                          FontWeight.bold,
                                      color: theme
                                          .colorScheme
                                          .onSurface)),
                              if (topic.isNotEmpty)
                                Padding(
                                  padding:
                                      const EdgeInsets.only(
                                          top: 4),
                                  child: Text(
                                      'Topic: $topic',
                                      style: TextStyle(
                                          color: theme
                                              .colorScheme
                                              .onSurfaceVariant,
                                          fontStyle:
                                              FontStyle
                                                  .italic)),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 16),
                        child: Text(about,
                            style: TextStyle(
                                color: theme
                                    .colorScheme
                                    .onSurfaceVariant)),
                      ),
                    if (dept.isNotEmpty || school.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 16),
                        child: Column(
                          crossAxisAlignment:
                              CrossAxisAlignment.start,
                          children: [
                            if (dept.isNotEmpty)
                              _buildRow(theme,
                                  Icons.business, 'Department', dept),
                            if (school.isNotEmpty)
                              _buildRow(theme,
                                  Icons.school, 'School', school),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // Question‑type card
          if (_questionTypes.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: 16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment:
                        CrossAxisAlignment.start,
                    children: [
                      Text('View Question For:',
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight:
                                  FontWeight.bold,
                              color: theme
                                  .colorScheme.onSurface)),
                      const SizedBox(height: 8),
                      Container(
                        padding:
                            const EdgeInsets.symmetric(
                                horizontal: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius:
                              BorderRadius.circular(8),
                          border: Border.all(
                              color: theme
                                  .colorScheme
                                  .onSurfaceVariant
                                  .withOpacity(0.3)),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedQuestionType,
                            isExpanded: true,
                            icon: Icon(
                                Icons.arrow_drop_down,
                                color: theme
                                    .colorScheme
                                    .onSurface),
                            style: TextStyle(
                                color: theme
                                    .colorScheme
                                    .onSurface,
                                fontSize: 16),
                            dropdownColor:
                                theme.colorScheme.surface,
                            items: _questionTypes
                                .map((type) =>
                                    DropdownMenuItem(
                                        value: type,
                                        child: Text(type)))
                                .toList(),
                            onChanged: (newType) {
                              if (newType != null) {
                                setState(() {
                                  _selectedQuestionType =
                                      newType;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text('Question:',
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight:
                                  FontWeight.bold,
                              color: theme
                                  .colorScheme.onSurface)),
                      const SizedBox(height: 8),
                      Text(question,
                          style: TextStyle(
                              color: theme
                                  .colorScheme
                                  .onSurfaceVariant)),
                    ],
                  ),
                ),
              ),
            ),
        ]),
      ),

      // Bottom navigation bar
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment:
                  MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined,
                      color: theme.colorScheme.onSurface),
                  onPressed: () =>
                      Navigator.of(context)
                          .popUntil((r) => r.isFirst),
                ),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(Icons.person_outline,
                      color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRow(
      ThemeData theme, IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment:
            CrossAxisAlignment.start,
        children: [
          Icon(icon,
              color: theme.colorScheme.onSurface,
              size: 20),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment:
                  CrossAxisAlignment.start,
              children: [
                Text(title,
                    style: TextStyle(
                        fontWeight:
                            FontWeight.bold,
                        color: theme.colorScheme
                            .onSurface)),
                const SizedBox(height: 4),
                Text(value,
                    style: TextStyle(
                        color: theme.colorScheme
                            .onSurfaceVariant)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
