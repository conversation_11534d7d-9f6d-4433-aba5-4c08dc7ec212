buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // Google services plugin
        classpath 'com.google.gms:google-services:4.4.0'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'

subprojects { proj ->
    // Redirect output for each module
    proj.buildDir = "${rootProject.buildDir}/${proj.name}"
    // Make sure :app is configured first
    proj.evaluationDependsOn(':app')

    // As soon as the Android library plugin is applied, set namespace
    proj.plugins.withId("com.android.library") {
        // configure the LibraryExtension (old‑API) before AGP reads it
        proj.extensions.findByType(com.android.build.gradle.LibraryExtension)?.with {
            if (namespace == null || namespace.trim().isEmpty()) {
                namespace = "com.example.${proj.name}"
            }
        }
    }

    // Same for application modules
    proj.plugins.withId("com.android.application") {
        proj.extensions.findByType(com.android.build.gradle.AppExtension)?.with {
            if (namespace == null || namespace.trim().isEmpty()) {
                namespace = "com.example.${proj.name}"
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
