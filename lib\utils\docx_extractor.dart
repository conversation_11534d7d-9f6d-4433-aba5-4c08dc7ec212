import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:xml/xml.dart';

class DocxExtractor {
  /// Extract text from a DOCX file using the archive package
  /// This works by treating the DOCX as a ZIP archive and extracting the document.xml file
  static Future<String> extractText(Uint8List bytes) async {
    try {
      // Decode the archive from bytes
      final archive = ZipDecoder().decodeBytes(bytes);

      // Find the main document content
      final documentEntry = archive.findFile('word/document.xml');
      if (documentEntry == null) {
        return '[Error: Could not find document.xml in DOCX file]';
      }

      // Extract and decode the content
      final content = utf8.decode(documentEntry.content as List<int>);

      // Parse the XML
      final document = XmlDocument.parse(content);

      // Extract text from paragraphs
      final paragraphs = document.findAllElements('w:p');
      final buffer = StringBuffer();

      for (var paragraph in paragraphs) {
        final texts = paragraph.findAllElements('w:t');
        for (var text in texts) {
          buffer.write(text.innerText);
        }
        buffer.writeln(); // Add a newline after each paragraph
      }

      return buffer.toString();
    } catch (e) {
      return '[Error extracting text from DOCX: $e]';
    }
  }

  /// Extract text from a DOC file (older Word format)
  /// This is more challenging without specialized libraries
  /// We'll use a simple approach that may extract some text but won't be perfect
  static String extractTextFromDoc(Uint8List bytes) {
    try {
      // Try to find text in the binary data
      final result = StringBuffer();
      String text = '';

      // Try UTF-16LE encoding which is common in DOC files
      try {
        // Use the built-in codec from dart:convert
        final ByteData byteData = ByteData.sublistView(Uint8List.fromList(bytes));
        final buffer = StringBuffer();

        // Process 2 bytes at a time for UTF-16LE
        for (int i = 0; i < bytes.length - 1; i += 2) {
          final charCode = byteData.getUint16(i, Endian.little);
          // Skip null bytes and control characters
          if (charCode > 31 && charCode < 65536) {
            buffer.writeCharCode(charCode);
          }
        }
        text = buffer.toString();
      } catch (e) {
        // If UTF-16LE fails, try ASCII with allowMalformed
        try {
          text = ascii.decode(bytes, allowInvalid: true);
        } catch (e) {
          // Last resort: Latin1
          text = latin1.decode(bytes);
        }
      }

      // Clean up the text by removing non-printable characters
      final cleanedText = text.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F]'), ' ');

      // Extract words that look like actual text (at least 2 characters)
      final words = RegExp(r'[A-Za-z]{2,}').allMatches(cleanedText);
      for (var word in words) {
        result.write(word.group(0));
        result.write(' ');
      }

      return result.toString();
    } catch (e) {
      return '[Error extracting text from DOC: $e]';
    }
  }
}
