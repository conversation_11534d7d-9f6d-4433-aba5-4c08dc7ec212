import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'schools_page.dart';
import 'departments_page.dart';
import 'centers_page.dart';
import 'documents_page.dart';

class TertiaryCorePage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryCorePage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryCorePage> createState() => _TertiaryCorePageState();
}

class _TertiaryCorePageState extends State<TertiaryCorePage> {
  List<Map<String, dynamic>>? _cachedSchools;
  List<Map<String, dynamic>>? _cachedDepartments;
  List<Map<String, dynamic>>? _cachedCenters;
  List<Map<String, dynamic>>? _cachedDocuments;
  String? _lastCollegeName;
  bool _isLoadingSchools = false;
  bool _isLoadingDepartments = false;
  bool _isLoadingCenters = false;
  bool _isLoadingDocuments = false;
  late RealtimeChannel _schoolsRealtimeChannel;
  late RealtimeChannel _departmentsRealtimeChannel;
  late RealtimeChannel _centersRealtimeChannel;
  late RealtimeChannel _documentsRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryCorePage initState called for ${widget.institutionName}");
    _loadCachedSchools();
    _loadCachedDepartments();
    _loadCachedCenters();
    _loadCachedDocuments();
    _loadSchoolsFromDatabaseAndCache();
    _loadDepartmentsFromDatabaseAndCache();
    _loadCentersFromDatabaseAndCache();
    _loadDocumentsFromDatabaseAndCache();
    _setupSchoolsRealtimeListener();
    _setupDepartmentsRealtimeListener();
    _setupCentersRealtimeListener();
    _setupDocumentsRealtimeListener();
  }

  @override
  void dispose() {
    _schoolsRealtimeChannel.unsubscribe();
    _departmentsRealtimeChannel.unsubscribe();
    _centersRealtimeChannel.unsubscribe();
    _documentsRealtimeChannel.unsubscribe();
    super.dispose();
  }

  // Schools methods
  Future<List<Map<String, dynamic>>?> _getCachedSchools(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? schoolsJson = prefs.getString(
        'schools_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (schoolsJson != null) {
      List<dynamic> decodedList = jsonDecode(schoolsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheSchools(String collegeName, List<Map<String, dynamic>> schools) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String schoolsJson = jsonEncode(schools);
    await prefs.setString(
        'schools_${collegeName.toLowerCase().replaceAll(' ', '')}', schoolsJson);
    print('Schools cached for $collegeName.');
  }

  Future<void> _loadCachedSchools() async {
    final cachedData = await _getCachedSchools(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedSchools = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded schools from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadSchoolsFromDatabaseAndCache() async {
    if (_isLoadingSchools) {
      return;
    }

    setState(() {
      _isLoadingSchools = true;
    });

    print("Fetching schools for ${widget.institutionName} from database");
    final schoolsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_schools';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(schoolsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedSchools = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingSchools = false;
          _cacheSchools(widget.institutionName, response);
          print("Schools fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingSchools = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingSchools = false;
          _cachedSchools = [];
          print("Error fetching schools for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingSchools = false;
      }
    }
  }

  void _setupSchoolsRealtimeListener() {
    final schoolsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_schools';
    _schoolsRealtimeChannel = Supabase.instance.client
        .channel('schools_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: schoolsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for schools of ${widget.institutionName}: ${payload.eventType}");
        _loadSchoolsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Departments methods
  Future<List<Map<String, dynamic>>?> _getCachedDepartments(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? departmentsJson = prefs.getString(
        'departments_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (departmentsJson != null) {
      List<dynamic> decodedList = jsonDecode(departmentsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheDepartments(String collegeName, List<Map<String, dynamic>> departments) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String departmentsJson = jsonEncode(departments);
    await prefs.setString(
        'departments_${collegeName.toLowerCase().replaceAll(' ', '')}', departmentsJson);
    print('Departments cached for $collegeName.');
  }

  Future<void> _loadCachedDepartments() async {
    final cachedData = await _getCachedDepartments(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedDepartments = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded departments from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadDepartmentsFromDatabaseAndCache() async {
    if (_isLoadingDepartments) {
      return;
    }

    setState(() {
      _isLoadingDepartments = true;
    });

    print("Fetching departments for ${widget.institutionName} from database");
    final departmentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_departments';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(departmentsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedDepartments = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingDepartments = false;
          _cacheDepartments(widget.institutionName, response);
          print("Departments fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingDepartments = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingDepartments = false;
          _cachedDepartments = [];
          print("Error fetching departments for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingDepartments = false;
      }
    }
  }

  void _setupDepartmentsRealtimeListener() {
    final departmentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_departments';
    _departmentsRealtimeChannel = Supabase.instance.client
        .channel('departments_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: departmentsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for departments of ${widget.institutionName}: ${payload.eventType}");
        _loadDepartmentsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Centers methods
  Future<List<Map<String, dynamic>>?> _getCachedCenters(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? centersJson = prefs.getString(
        'centers_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (centersJson != null) {
      List<dynamic> decodedList = jsonDecode(centersJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheCenters(String collegeName, List<Map<String, dynamic>> centers) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String centersJson = jsonEncode(centers);
    await prefs.setString(
        'centers_${collegeName.toLowerCase().replaceAll(' ', '')}', centersJson);
    print('Centers cached for $collegeName.');
  }

  Future<void> _loadCachedCenters() async {
    final cachedData = await _getCachedCenters(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedCenters = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded centers from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadCentersFromDatabaseAndCache() async {
    if (_isLoadingCenters) {
      return;
    }

    setState(() {
      _isLoadingCenters = true;
    });

    print("Fetching centers for ${widget.institutionName} from database");
    final centersTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_centers';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(centersTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedCenters = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingCenters = false;
          _cacheCenters(widget.institutionName, response);
          print("Centers fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingCenters = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingCenters = false;
          _cachedCenters = [];
          print("Error fetching centers for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingCenters = false;
      }
    }
  }

  void _setupCentersRealtimeListener() {
    final centersTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_centers';
    _centersRealtimeChannel = Supabase.instance.client
        .channel('centers_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: centersTableName,
      callback: (payload) async {
        print(
            "Realtime update received for centers of ${widget.institutionName}: ${payload.eventType}");
        _loadCentersFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Documents methods
  Future<List<Map<String, dynamic>>?> _getCachedDocuments(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? documentsJson = prefs.getString(
        'documents_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (documentsJson != null) {
      List<dynamic> decodedList = jsonDecode(documentsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheDocuments(String collegeName, List<Map<String, dynamic>> documents) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String documentsJson = jsonEncode(documents);
    await prefs.setString(
        'documents_${collegeName.toLowerCase().replaceAll(' ', '')}', documentsJson);
    print('Documents cached for $collegeName.');
  }

  Future<void> _loadCachedDocuments() async {
    final cachedData = await _getCachedDocuments(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedDocuments = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded documents from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadDocumentsFromDatabaseAndCache() async {
    if (_isLoadingDocuments) {
      return;
    }

    setState(() {
      _isLoadingDocuments = true;
    });

    print("Fetching documents for ${widget.institutionName} from database");
    final documentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_documents';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(documentsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedDocuments = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingDocuments = false;
          _cacheDocuments(widget.institutionName, response);
          print("Documents fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingDocuments = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingDocuments = false;
          _cachedDocuments = [];
          print("Error fetching documents for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingDocuments = false;
      }
    }
  }

  void _setupDocumentsRealtimeListener() {
    final documentsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_documents';
    _documentsRealtimeChannel = Supabase.instance.client
        .channel('documents_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: documentsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for documents of ${widget.institutionName}: ${payload.eventType}");
        _loadDocumentsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Schools') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SchoolsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedSchools: _cachedSchools,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Departments') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DepartmentsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedDepartments: _cachedDepartments,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Centers') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CentersPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedCenters: _cachedCenters,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Documents') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DocumentsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedDocuments: _cachedDocuments,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define the grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Schools', 'icon': Icons.school},
      {'title': 'Departments', 'icon': Icons.account_balance},
      {'title': 'Centers', 'icon': Icons.location_city},
      {'title': 'Documents', 'icon': Icons.description},
    ];

    // Filter out the "Schools" item when not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage && item['title'] == 'Schools') {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Core',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
