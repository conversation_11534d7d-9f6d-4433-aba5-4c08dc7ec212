import 'package:visibility_detector/visibility_detector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'
    show
        kIsWeb,
        defaultTargetPlatform,
        TargetPlatform,
        WidgetState,
        WidgetStateProperty;
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_messaging_service.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'pages/tertiary_page.dart';
import 'pages/secondary_page.dart';
import 'pages/primary_page.dart';
import 'pages/pre_primary_page.dart';
import 'pages/login_page.dart';
import 'pages/whiteboard_page.dart';
import 'pages/wellness_page.dart';
import 'pages/utilities_page.dart'; // Import UtilitiesPage
import 'pages/calculate_page.dart';
import 'pages/tertiary_detail_page.dart'; // Import TertiaryDetailPage

// Custom scroll behavior that always shows scrollbar on desktop/web
class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb ||
        defaultTargetPlatform == TargetPlatform.linux ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        thickness: 12.0,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFF8c8c8c)
            : const Color.fromRGBO(158, 158, 158, .6),
        trackColor: Theme.of(context).brightness == Brightness.light
            ? Colors.white
            : null,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await FirebaseMessagingService().initialize();

  if (!kIsWeb) {
    await MobileAds.instance.initialize();
  } else {
    MobileAds.instance.initialize();
  }

  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL', // Replace with your Supabase URL
    anonKey: 'YOUR_SUPABASE_ANON_KEY', // Replace with your Supabase anon key
  );

  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  runApp(
    ChangeNotifierProvider(
      create: (context) => _MyAppState(isDarkMode),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<_MyAppState>(context);

    return MaterialApp(
      scrollBehavior: CustomScrollBehavior(),
      title: 'Apptelligent',
      themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      theme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFFEEEEEE),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
        navigationBarTheme: const NavigationBarThemeData(
          backgroundColor: Colors.white,
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: ColorScheme.light(
          surface: Colors.white,
          onSurface: Colors.black,
          secondary: Colors.black.withOpacity(0.6),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return appState.isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return appState.isDarkMode
                ? Colors.white.withOpacity(0.3)
                : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      darkTheme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFF090909),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF202020),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        navigationBarTheme: const NavigationBarThemeData(
          backgroundColor: Color(0xFF202020),
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: const ColorScheme.dark(
          surface: Color(0xFF202020),
          onSurface: Colors.white,
          secondary: Colors.white70,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return appState.isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return appState.isDarkMode
                ? Colors.white.withOpacity(0.3)
                : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      home: HomeScreen(
        isDarkMode: appState.isDarkMode,
        toggleTheme: appState.toggleTheme,
      ),
    );
  }
}

class _MyAppState extends ChangeNotifier with WidgetsBindingObserver {
  bool isDarkMode;

  static List<Map<String, dynamic>>? preloadedColleges;
  static Map<String, List<Map<String, dynamic>>> preloadedHelpdesks = {};

  StreamSubscription<List<Map<String, dynamic>>>? _collegesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _helpdesksSubscription;

  _MyAppState(this.isDarkMode) {
    WidgetsBinding.instance.addObserver(this);
    _preloadCollegesAndListen();
  }

  void _preloadCollegesAndListen() async {
    await _preloadColleges();
    _listenToCollegeChanges();
  }

  Future<void> _preloadColleges() async {
    _collegesSubscription?.cancel();
  }

  void _listenToCollegeChanges() {
    // ... (rest of _listenToCollegeChanges function - same as before) ...
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(
      List<Map<String, dynamic>> colleges) {
    if (colleges == null) {
      return Future.value([]); // Return empty list if colleges is null
    }
    return Future.value([]);
  }

  void _updatePreloadedColleges(List<Map<String, dynamic>> changes) {}

  void _preloadHelpdesksAndListen(String collegeNameForTable) async {
    await _preloadHelpdesks(collegeNameForTable);
     _listenToHelpdeskChanges(collegeNameForTable);
  }

  Future<void> _preloadHelpdesks(String collegeNameForTable) async {
    _helpdesksSubscription?.cancel();
  }

  void _listenToHelpdeskChanges(String collegeNameForTable) {
    // ... (rest of _listenToHelpdeskChanges function - same as before) ...
  }

  void _updatePreloadedHelpdesks(
      String collegeNameForTable, List<Map<String, dynamic>> changes) {
    // ... (rest of _updatePreloadedHelpdesks function - same as before) ...
  }

  void _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('darkMode', isDark);
  }

  void toggleTheme() {
    isDarkMode = !isDarkMode;
    _saveThemePreference(isDarkMode);
    notifyListeners();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _collegesSubscription?.cancel();
    _helpdesksSubscription?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      notifyListeners();
    }
  }
}

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HomeScreen({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _adTimer;
  List<String> _adUrls = [];
  int _currentAdIndex = 0;
  late bool _isDarkMode;
  VoidCallback? _toggleTheme;
  VideoPlayerController? _adVideoController;
  bool _isVideoInitialized = false;
  BannerAd? _bannerAd;
  bool _isBannerAdReady = false;
  RewardedAd? _rewardedAd;
  bool _isRewardedAdReady = false;

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
    _toggleTheme = widget.toggleTheme;
    _loadAdUrlsFromSupabase();
    _adTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_adUrls.isNotEmpty) {
        setState(() {
          _currentAdIndex = (_currentAdIndex + 1) % _adUrls.length;
          _initializeVideoPlayer();
        });
      }
    });
    _loadBannerAd();
    _loadRewardedAd();
  }

  void _loadAdUrlsFromSupabase() async {
    final supabase = Supabase.instance.client;
    try {
      final response = await supabase.from('ads_table').select('ad_url');
      if (response != null && response is List) {
        _adUrls = response.map((item) => item['ad_url'] as String).toList();
      }
    } catch (error) {
      print('Error loading ad URLs: $error');
      _adUrls = []; // Ensure _adUrls is initialized even on error
    }
  }

  void _loadRewardedAd() async {
    // ... (rest of _loadRewardedAd method - same as before) ...
  }

  void _showRewardedAd(BuildContext context) async {
    // ... (rest of _showRewardedAd method - same as before) ...
  }

  void _navigateToWhiteboard() {
    // ... (rest of _navigateToWhiteboard method - same as before) ...
  }

  void _initializeVideoPlayer() {
    if (_adUrls.isEmpty) return;
    _disposeVideoPlayer();
    String adUrl = _adUrls[_currentAdIndex];
    if (adUrl.toLowerCase().endsWith('.mp4')) {
      _adVideoController = VideoPlayerController.networkUrl(Uri.parse(adUrl))
        ..initialize().then((_) {
          setState(() {
            _isVideoInitialized = true;
          });
        }).catchError((error) {
          print('Video initialization error: $error');
        });
    }
  }

  void _disposeVideoPlayer() {
    if (_adVideoController != null) {
      _adVideoController!.dispose();
      _adVideoController = null;
      _isVideoInitialized = false;
    }
  }

  void _loadBannerAd() {
    BannerAdListener listener = BannerAdListener(
      onAdLoaded: (Ad ad) {
        setState(() {
          _bannerAd = ad as BannerAd;
          _isBannerAdReady = true;
        });
      },
      onAdFailedToLoad: (Ad ad, LoadAdError error) {
        ad.dispose();
        print('Ad load failed (Banner Ad): ${error.message}');
      },
    );
    _bannerAd = BannerAd(
      adUnitId: kIsWeb ? 'ca-pub-3940256099942544/6300978111' : 'ca-app-pub-3940256099942544/6300978111',
      request: const AdRequest(),
      size: AdSize.banner, listener: listener,)..load();
  }

  @override
  void dispose() {
    _adTimer.cancel();
    _disposeVideoPlayer();
    _bannerAd?.dispose();
    _rewardedAd?.dispose();
    super.dispose();
  }

  void _navigateToPage(BuildContext context, String title) {
    if (title == 'Tertiary') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ListPage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Secondary') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SecondaryPage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Pre-Primary') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrePrimaryPage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Primary') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrimaryPage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Whiteboard') {
      _navigateToWhiteboard();
    } else if (title == 'Wellness') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WellnessPage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Utilities') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UtilitiesPage( // Use imported UtilitiesPage
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    } else if (title == 'Calculate') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CalculatePage(
            isDarkMode: _isDarkMode,
            toggleTheme: _toggleTheme!,
          ),
        ),
      );
    }
  }

  void _showFullPageAd(BuildContext context, String adUrl) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(0),
          child: FullScreenAdModal(adUrl: adUrl),
        );
      },
    );
  }

  Widget _buildAdBanner(BuildContext context, ThemeData theme) {
    if (_adUrls.isEmpty) {
      return Container(); // Return an empty container if no ads
    }
    String adUrl = _adUrls[_currentAdIndex];

    if (adUrl.toLowerCase().endsWith('.mp4')) {
      if (_adVideoController != null && _isVideoInitialized) {
        return AspectRatio(
          aspectRatio: _adVideoController!.value.aspectRatio,
          child: VideoPlayer(_adVideoController!),
        );
      } else {
        return Container(
          height: 200, // Adjust as needed
          color: theme.colorScheme.surface,
          child: const Center(child: CircularProgressIndicator()),
        );
      }
    } else if (adUrl.toLowerCase().startsWith('http')) {
      return GestureDetector(
        onTap: () => _showFullPageAd(context, adUrl),
        child: Image.network(adUrl, fit: BoxFit.cover,
            errorBuilder: (BuildContext context, Object exception,
                StackTrace? stackTrace) {
          return const Center(child: Icon(Icons.error_outline));
        }),
      );
    } else {
      return Container(); // Fallback for unknown ad types
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final appState = Provider.of<_MyAppState>(context); // Access _MyAppState
    return Scaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(left: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Image.asset(
                theme.brightness == Brightness.light
                    ? 'assets/apptelligent_b.png'
                    : 'assets/apptelligent_w.png',
                fit: BoxFit.contain,
                height: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Apptelligent',
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18),
              ),
            ],
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: IconButton(
              icon: const Icon(Icons.notifications_none_outlined),
              onPressed: () {
                // Handle notification icon press
              },
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              _buildAdBanner(context, theme),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Pre-Primary',
                      Icons.school_outlined,
                      theme,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Primary',
                      Icons.school_outlined,
                      theme,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Secondary',
                      Icons.school_outlined,
                      theme,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Tertiary',
                      Icons.school_outlined,
                      theme,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Whiteboard',
                      Icons.draw_outlined,
                      theme,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Wellness',
                      Icons.favorite_outline,
                      theme,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Utilities',
                      Icons.build_outlined,
                      theme,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildGridItem(
                      context,
                      'Calculate',
                      Icons.calculate_outlined,
                      theme,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // AdMob Banner will be placed here
          Container(
            color: theme.colorScheme.surface,
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.home,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () {},
                    ),
                    const SizedBox(width: 24),
                    IconButton(
                      icon: Icon(
                        appState.isDarkMode
                            ? Icons.light_mode_outlined
                            : Icons.dark_mode_outlined,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: appState.toggleTheme, // Use appState.toggleTheme
                    ),
                    const SizedBox(width: 24),
                    IconButton(
                      icon: Icon(
                        Icons.person_outline,
                        color: theme.colorScheme.onSurface,
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoginPage(
                              isDarkMode: _isDarkMode,
                              toggleTheme: _toggleTheme!,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_isBannerAdReady && _bannerAd != null)
            SizedBox(
              width: MediaQuery.of(context).size.width,
              height: AdSize.banner.height.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme) {
    return GestureDetector(
        onTap: () => _navigateToPage(context, title),
        child: Card(
          color: theme.colorScheme.surface,
          elevation: 0,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, size: 40, color: theme.colorScheme.onSurface),
                  const SizedBox(height: 8),
                  Text(title,
                      style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                          fontSize: 16),
                      textAlign: TextAlign.center)
                ]),
          ),
        ));
  }
}

class FullScreenAdModal extends StatefulWidget {
  final String adUrl;

  const FullScreenAdModal({Key? key, required this.adUrl}) : super(key: key);

  @override
  State<FullScreenAdModal> createState() => _FullScreenAdModalState();
}

class _FullScreenAdModalState extends State<FullScreenAdModal> {
  VideoPlayerController? _localVideoController;

  @override
  void initState() {
    super.initState();
    _localVideoController =
        VideoPlayerController.networkUrl(Uri.parse(widget.adUrl))
          ..initialize().then((_) {
            setState(() {});
            _localVideoController!.play(); // Start playing video once initialized
          }).catchError((error) {
            print('Fullscreen Ad Video initialization error: $error');
          });
  }

  @override
  void dispose() {
    if (_localVideoController != null) {
      _localVideoController!.pause();
      _localVideoController!.dispose();
      _localVideoController = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.8), // Dimmed background
      body: Center(
        child: SingleChildScrollView(
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              if (widget.adUrl.toLowerCase().endsWith('.mp4'))
                _localVideoController != null &&
                        _localVideoController!.value.isInitialized
                    ? AspectRatio(
                        aspectRatio: _localVideoController!.value.aspectRatio,
                        child: VideoPlayer(_localVideoController!),
                      )
                    : const CircularProgressIndicator()
              else if (widget.adUrl.toLowerCase().startsWith('http'))
                Image.network(widget.adUrl, fit: BoxFit.contain)
              else
                const Text("Unsupported Ad Format"),
              Positioned(
                top: 20,
                right: 20,
                child: SafeArea(
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ListPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ListPage({super.key, required this.isDarkMode, required this.toggleTheme});

  @override
  State<ListPage> createState() => _ListPageState();
}

class _ListPageState extends State<ListPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadPreloadedColleges() {
    if (_MyAppState.preloadedColleges != null) {
      _colleges =
          List<Map<String, dynamic>>.from(_MyAppState.preloadedColleges!);
    } else {
      _loadCollegesFromSupabase();
    }
  }

  Future<void> _loadCollegesFromSupabase() async {
    if (_isLoading || _isDisposed) return;
    setState(() {
      _isLoading = true;
    });
    try {
      final supabase = Supabase.instance.client;
      final response = await supabase
          .from('tertiary_institutions')
          .select()
          .order('fullname', ascending: true);
      if (response != null && response is List) {
        List<Map<String, dynamic>> updatedColleges =
            List<Map<String, dynamic>>.from(response as List); // Cast response to List
        updatedColleges = await _updateCollegeImageUrls(updatedColleges);
        setState(() {
          _colleges = updatedColleges;
          _MyAppState.preloadedColleges = updatedColleges;
        });
      }
    } catch (error) {
      print('Error loading colleges: $error');
      // Handle error appropriately
    } finally {
      if (!_isDisposed) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(
      List<Map<String, dynamic>> colleges) {
    if (colleges == null) {
      return Future.value([]); // Return empty list if colleges is null
    }
    return Future.value([]);
  }

  void _setupRealtime() {
    final supabase = Supabase.instance.client;
    _realtimeChannel = supabase.realtime
        .channel('public:tertiary_institutions')
      ..on(
          RealtimeListenTypes.postgresChanges,
          channelFilter: PostgresChangeFilter( // Use PostgresChangeFilter
              event: '*', schema: 'public', table: 'tertiary_institutions'),
          (payload, {ref}) {
        if (payload['eventType'] == 'INSERT') {
          final newCollege = payload['new'] as Map<String, dynamic>;
          if (_MyAppState.preloadedColleges != null) {
            _MyAppState.preloadedColleges = [
              ..._MyAppState.preloadedColleges ?? [],
              newCollege
            ];
            _MyAppState.preloadedColleges?.sort((a, b) =>
                (a['fullname'] ?? '').toLowerCase().compareTo(
                    (b['fullname'] ?? '').toLowerCase()));
          }
          if (mounted) {
            setState(() {
              _colleges = List<Map<String, dynamic>>.from(
                  _MyAppState.preloadedColleges ?? []);
            });
          }
        } else if (payload['eventType'] == 'UPDATE') {
          final updatedCollege = payload['new'] as Map<String, dynamic>;
          if (_MyAppState.preloadedColleges != null) {
            _MyAppState.preloadedColleges =
                _MyAppState.preloadedColleges?.map((college) {
              if (college['id'] == updatedCollege['id']) {
                return updatedCollege;
              }
              return college;
            }).toList();
            _MyAppState.preloadedColleges?.sort((a, b) =>
                (a['fullname'] ?? '').toLowerCase().compareTo(
                    (b['fullname'] ?? '').toLowerCase()));
          }
          if (mounted) {
            setState(() {
              _colleges = List<Map<String, dynamic>>.from(
                  _MyAppState.preloadedColleges ?? []);
            });
          }
        } else if (payload['eventType'] == 'DELETE') {
          final deletedCollegeId = payload['old']['id'];
          if (_MyAppState.preloadedColleges != null) {
            _MyAppState.preloadedColleges
                ?.removeWhere((college) => college['id'] == deletedCollegeId);
          }
          if (mounted) {
            setState(() {
              _colleges = List<Map<String, dynamic>>.from(
                  _MyAppState.preloadedColleges ?? []);
            });
          }
        }
      })
      ..subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _realtimeChannel.unsubscribe();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Load more data if needed, or handle infinite scroll
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TertiaryDetailPage( // Use imported TertiaryDetailPage
          college: college,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tertiary Institutions'),
        actions: [
          IconButton(
            icon: Icon(
                widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadCollegesFromSupabase,
              child: _colleges.isEmpty
                  ? LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                    return SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: SizedBox(
                        height: constraints.maxHeight,
                        child: const Center(
                          child: Text('No colleges available.'),
                        ),
                      ),
                    );
                  })
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _colleges.length,
                      itemBuilder: (context, index) {
                        final university = _colleges[index];
                        return VisibilityDetector(
                          key: Key('college-${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo visibilityInfo) {
                            var visiblePercentage =
                                visibilityInfo.visibleFraction * 100;
                            bool inView = visiblePercentage > 50;
                            if (inView && university['image_url'] == null) {
                              _fetchImageUrl(university);
                            }
                          },
                          child: Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10)),
                            child: ListTile(
                              leading: CircleAvatar(
                                radius: 24,
                                backgroundColor: theme.colorScheme.secondary,
                                backgroundImage: university['image_url'] != null
                                    ? NetworkImage(university['image_url']!)
                                    : null,
                                child: university['image_url'] == null
                                    ? const Icon(Icons.image,
                                        color: Colors.white)
                                    : null,
                              ),
                              title: Text(
                                university['fullname'] ?? 'N/A',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Text(
                                  university['location'] ?? 'Location N/A'),
                              onTap: () =>
                                  _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    if (_isDisposed) return;
    final supabase = Supabase.instance.client;
    final collegeId = college['id'];
    try {
      final imageUrlResponse = await supabase.functions
          .invoke('get-college-image')
          .functionCall(functionName: 'get-college-image') // Use functionCall and functionName
          .params({'college_name': college['fullname']});

      final imageUrl = imageUrlResponse.data as String?;

      if (imageUrl != null) {
        setState(() {
          _colleges = _colleges.map((c) {
            if (c['id'] == collegeId) {
              c['image_url'] = imageUrl;
            }
            return c;
          }).toList();
          if (_MyAppState.preloadedColleges != null) {
            _MyAppState.preloadedColleges =
                _MyAppState.preloadedColleges?.map((c) {
              if (c['id'] == collegeId) {
                c['image_url'] = imageUrl;
              }
              return c;
            }).toList();
          }
        });
      }
    } catch (e) {
      print('Error fetching image URL for ${college['fullname']}: $e');
    }
  }
}