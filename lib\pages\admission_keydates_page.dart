// admission_keydates_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class AdmissionKeyDatesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AdmissionKeyDatesPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AdmissionKeyDatesPageState createState() => _AdmissionKeyDatesPageState();
}

class _AdmissionKeyDatesPageState extends State<AdmissionKeyDatesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('admission_keydates_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _keyDates = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedYearFilter = 'All Years';
  List<String> _yearFilterOptions = ['All Years'];
  String _selectedMonthFilter = 'All Months';
  List<String> _monthFilterOptions = ['All Months'];

  final List<String> _monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadKeyDatesFromCache();
    _loadKeyDatesFromSupabase();
  }

  void _setupRealtime() {
    final eventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_events';
    _realtimeChannel = Supabase.instance.client
        .channel('admission_keydates_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: eventsTableName,
      callback: (payload) async {
        print("Realtime update received for key dates: ${payload.eventType}");
        _loadKeyDatesFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadKeyDatesFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_keydates_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> keyDates = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && keyDates.isNotEmpty) {
          setState(() {
            _keyDates = keyDates;
            _updateFilterOptions(keyDates);
          });
          print("Loaded ${keyDates.length} key dates from cache");
        }
      }
    } catch (e) {
      print("Error loading key dates from cache: $e");
    }
  }

  void _updateFilterOptions(List<Map<String, dynamic>> events) {
    Set<String> years = {'All Years'};
    Set<String> months = {'All Months'};
    
    for (var event in events) {
      if (event['year'] != null) {
        years.add(event['year'].toString());
      }
      if (event['month'] != null) {
        final int monthIndex = event['month'] as int;
        if (monthIndex >= 1 && monthIndex <= 12) {
          months.add(_monthNames[monthIndex - 1]);
        }
      }
    }
    
    setState(() {
      _yearFilterOptions = years.toList()..sort((a, b) {
        if (a == 'All Years') return -1;
        if (b == 'All Years') return 1;
        return int.parse(b).compareTo(int.parse(a)); // Sort years in descending order
      });
      
      _monthFilterOptions = ['All Months'];
      // Add months in calendar order
      for (var month in _monthNames) {
        if (months.contains(month)) {
          _monthFilterOptions.add(month);
        }
      }
    });
  }

  Future<void> _loadKeyDatesFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final eventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_events';
      var query = Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .eq('admissionskeydate', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('fullname.ilike.%$_searchQuery%,about.ilike.%$_searchQuery%,venue.ilike.%$_searchQuery%');
      }
      
      // Apply year filter if not "All Years"
      if (_selectedYearFilter != 'All Years') {
        query = query.eq('year', int.parse(_selectedYearFilter));
      }
      
      // Apply month filter if not "All Months"
      if (_selectedMonthFilter != 'All Months') {
        final int monthIndex = _monthNames.indexOf(_selectedMonthFilter) + 1;
        if (monthIndex > 0) {
          query = query.eq('month', monthIndex);
        }
      }
      
      // Sort by date (year, month, day)
      final response = await query
          .order('year', ascending: true)
          .order('month', ascending: true)
          .order('day', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _keyDates = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Update filter options
      _updateFilterOptions(_keyDates);

      // Cache the data
      _cacheKeyDates(_keyDates);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading key dates: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading key dates: $e')),
      );
    }
  }

  Future<void> _cacheKeyDates(List<Map<String, dynamic>> events) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_keydates_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(events));
      print("Cached ${events.length} key dates");
    } catch (e) {
      print("Error caching key dates: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadKeyDatesFromSupabase();
  }

  String _formatDate(Map<String, dynamic> event) {
    final int? day = event['day'];
    final int? month = event['month'];
    final int? year = event['year'];
    
    if (day != null && month != null && year != null) {
      final DateTime date = DateTime(year, month, day);
      return DateFormat('MMMM d, y').format(date);
    } else if (month != null && year != null) {
      return '${_monthNames[month - 1]} $year';
    } else if (year != null) {
      return year.toString();
    }
    return 'Date not specified';
  }

  String _formatTime(Map<String, dynamic> event) {
    final String startTime = event['starttime'] ?? '';
    final String endTime = event['endtime'] ?? '';
    
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      return '$startTime - $endTime';
    } else if (startTime.isNotEmpty) {
      return 'Starts at $startTime';
    } else if (endTime.isNotEmpty) {
      return 'Ends at $endTime';
    }
    return '';
  }

  bool _isUpcoming(Map<String, dynamic> event) {
    final int? day = event['day'];
    final int? month = event['month'];
    final int? year = event['year'];
    
    if (day != null && month != null && year != null) {
      final DateTime eventDate = DateTime(year, month, day);
      final DateTime now = DateTime.now();
      return eventDate.isAfter(now) || 
             (eventDate.year == now.year && eventDate.month == now.month && eventDate.day == now.day);
    }
    return false;
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Admission Key Dates',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search key dates...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Filters
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                // Year filter
                if (_yearFilterOptions.length > 1) ...[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Year:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                            ),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: _selectedYearFilter,
                              isExpanded: true,
                              icon: Icon(
                                Icons.arrow_drop_down,
                                color: theme.colorScheme.onSurface,
                              ),
                              style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                fontSize: 14,
                              ),
                              dropdownColor: theme.colorScheme.surface,
                              items: _yearFilterOptions.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null && newValue != _selectedYearFilter) {
                                  setState(() {
                                    _selectedYearFilter = newValue;
                                  });
                                  _loadKeyDatesFromSupabase();
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                // Month filter
                if (_monthFilterOptions.length > 1)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Month:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                            ),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: _selectedMonthFilter,
                              isExpanded: true,
                              icon: Icon(
                                Icons.arrow_drop_down,
                                color: theme.colorScheme.onSurface,
                              ),
                              style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                fontSize: 14,
                              ),
                              dropdownColor: theme.colorScheme.surface,
                              items: _monthFilterOptions.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null && newValue != _selectedMonthFilter) {
                                  setState(() {
                                    _selectedMonthFilter = newValue;
                                  });
                                  _loadKeyDatesFromSupabase();
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Key dates list
          Expanded(
            child: VisibilityDetector(
              key: const Key('admission_keydates_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _keyDates.isEmpty && !_isLoading) {
                  _loadKeyDatesFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadKeyDatesFromSupabase,
                child: _isLoading && _keyDates.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _keyDates.isEmpty
                        ? Center(
                            child: Text(
                              'No key dates found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _keyDates.length,
                            itemBuilder: (context, index) {
                              return _buildKeyDateCard(
                                _keyDates[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildKeyDateCard(
    Map<String, dynamic> event,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = event['fullname'] ?? 'Unnamed Event';
    final String about = event['about'] ?? '';
    final String venue = event['venue'] ?? '';
    final String formattedDate = _formatDate(event);
    final String formattedTime = _formatTime(event);
    final bool isUpcoming = _isUpcoming(event);

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date circle
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isUpcoming ? theme.colorScheme.primary : theme.colorScheme.surfaceVariant,
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.event,
                          color: isUpcoming ? theme.colorScheme.onPrimary : theme.colorScheme.onSurfaceVariant,
                          size: 24,
                        ),
                        Text(
                          event['day']?.toString() ?? '',
                          style: TextStyle(
                            color: isUpcoming ? theme.colorScheme.onPrimary : theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Event details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        fullname,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (formattedTime.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          formattedTime,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      if (venue.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          'Location: $venue',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Status badge
                if (isUpcoming)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      'Upcoming',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
              ],
            ),
            
            // Description
            if (about.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                about,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
