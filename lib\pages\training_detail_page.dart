import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';

class TrainingDetailPage extends StatelessWidget {
  final Map<String, dynamic> trainingData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TrainingDetailPage({
    Key? key,
    required this.trainingData,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Extract training details
    final String trainingName = trainingData['fullname'] ?? 'Unnamed Training';
    final String teamOrOrg = trainingData['teamororg'] ?? 'Not specified';
    final String building = trainingData['building'] ?? 'Not specified';
    final String room = trainingData['room'] ?? 'Not specified';
    final String startTime = trainingData['starttime'] ?? 'Not specified';
    final String endTime = trainingData['endtime'] ?? 'Not specified';
    final String about = trainingData['about'] ?? 'No description available';
    final int capacity = trainingData['capacity'] ?? 0;
    
    // Schedule days
    final bool monday = trainingData['_mon'] == true;
    final bool tuesday = trainingData['_tue'] == true;
    final bool wednesday = trainingData['_wed'] == true;
    final bool thursday = trainingData['_thur'] == true;
    final bool friday = trainingData['_fri'] == true;
    final bool saturday = trainingData['_sat'] == true;
    final bool sunday = trainingData['_sun'] == true;
    
    // Format schedule days
    final List<String> scheduleDays = [];
    if (monday) scheduleDays.add('Monday');
    if (tuesday) scheduleDays.add('Tuesday');
    if (wednesday) scheduleDays.add('Wednesday');
    if (thursday) scheduleDays.add('Thursday');
    if (friday) scheduleDays.add('Friday');
    if (saturday) scheduleDays.add('Saturday');
    if (sunday) scheduleDays.add('Sunday');
    
    // Contact information
    final String phone = trainingData['phone'] ?? '';
    final String email = trainingData['email'] ?? '';
    final String whatsapp = trainingData['whatsapp'] ?? '';
    
    // Location coordinates
    final double? latitude = trainingData['latitude'];
    final double? longitude = trainingData['longitude'];
    final bool hasLocation = latitude != null && longitude != null && 
                            latitude != 0.0 && longitude != 0.0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          trainingName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Training header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: theme.colorScheme.primary.withOpacity(0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    trainingName,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (teamOrOrg.isNotEmpty && teamOrOrg != 'Not specified')
                    Text(
                      'Team/Organization: $teamOrOrg',
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                ],
              ),
            ),
            
            // Schedule information
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Schedule',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.calendar_today, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Text(
                                scheduleDays.isEmpty 
                                  ? 'No scheduled days' 
                                  : scheduleDays.join(', '),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.access_time, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Text(
                                '$startTime - $endTime',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.location_on, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '$building, Room $room',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (capacity > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                children: [
                                  Icon(Icons.people, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Capacity: $capacity people',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Contact information
            if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            if (phone.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.phone, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  phone,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('tel:$phone'),
                              ),
                            if (email.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.email, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  email,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('mailto:$email'),
                              ),
                            if (whatsapp.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.message, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  'WhatsApp: $whatsapp',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('https://wa.me/$whatsapp'),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // About/Description
            if (about.isNotEmpty && about != 'No description available')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About This Training',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Map
            if (hasLocation)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: FlutterMap(
                          options: MapOptions(
                            initialCenter: LatLng(latitude!, longitude!),
                            initialZoom: 15.0,
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.harmonizr.app',
                            ),
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: LatLng(latitude, longitude),
                                  width: 40,
                                  height: 40,
                                  child: Icon(
                                    Icons.location_on,
                                    color: theme.colorScheme.primary,
                                    size: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
