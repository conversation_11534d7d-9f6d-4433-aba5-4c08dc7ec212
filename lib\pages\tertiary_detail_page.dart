import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';

import 'tertiary_info_page.dart';
import 'tertiary_start_page.dart';
import 'tertiary_updates_page.dart';
import 'tertiary_path_page.dart';
import 'tertiary_people_page.dart';
import 'tertiary_lodging_page.dart';
import 'tertiary_shop_eat_page.dart';
import 'tertiary_transport_page.dart';
import 'tertiary_core_page.dart';
import 'tertiary_academics_page.dart';
import 'tertiary_programs_page.dart';
import 'tertiary_athletics_groups_page.dart';
import 'tertiary_media_page.dart';
import 'tertiary_startups_page.dart';
import 'tertiary_projects_publications_page.dart';
import 'tertiary_buildings_spaces_page.dart';
import 'tertiary_calendar_page.dart';
import 'tertiary_statistics_page.dart';
import 'tertiary_map_page.dart';
import 'tertiary_feedback_page.dart';
import 'tertiary_timeline_page.dart';
import 'tertiary_connectivity_page.dart';
import 'tertiary_giving_page.dart';
import 'tertiary_virtual_tour_page.dart';
import 'tertiary_today_page.dart';
import '../main.dart';
import 'tertiary_rentals_page.dart';
import 'tertiary_jobs_page.dart';
import 'tertiary_services_page.dart';
import 'tertiary_money_page.dart';
import 'tertiary_health_page.dart';
import 'tertiary_safety_page.dart';
import 'ai_agent_page.dart';

class TertiaryDetailPage extends StatefulWidget {
  final Map<String, dynamic> college;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool hasTodayEventsPreloaded;
  final bool isFromTertiaryPage;

  const TertiaryDetailPage({
    Key? key,
    required this.college,
    required this.isDarkMode,
    required this.toggleTheme,
    this.hasTodayEventsPreloaded = false,
    this.isFromTertiaryPage = false,
  }) : super(key: key);

  @override
  State<TertiaryDetailPage> createState() => _TertiaryDetailPageState();
}

class _TertiaryDetailPageState extends State<TertiaryDetailPage> {
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;
  late bool _hasTodayEvents;
  final String _adUnitId = 'ca-app-pub-3940256099942544/**********';
  List<Map<String, dynamic>>? _preloadedHelpdesks;
  List<Map<String, dynamic>>? _preloadedTodayEvents;
  late final RealtimeChannel _eventsChannel;

  Future<void> _prefetchInfoPageData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('about_${widget.college['id']}');
    await prefs.remove('vision_${widget.college['id']}');
    await prefs.remove('mission_${widget.college['id']}');
    await prefs.remove('address_${widget.college['id']}');
    await prefs.remove('daysnhours_${widget.college['id']}');
    await prefs.remove('postaladdress_${widget.college['id']}');
    await prefs.remove('corevalues_${widget.college['id']}');
    await prefs.remove('motto_${widget.college['id']}');
    await prefs.remove('goals_${widget.college['id']}');
    await prefs.remove('mandate_${widget.college['id']}');
    await prefs.remove('founded_${widget.college['id']}');
    await prefs.remove('accreditation_${widget.college['id']}');
    await prefs.remove('freewifi_${widget.college['id']}');
    await prefs.remove('objectives_${widget.college['id']}');
    await prefs.remove('aims_${widget.college['id']}');
    await prefs.remove('pledge_${widget.college['id']}');
    await prefs.remove('statementoffaith_${widget.college['id']}');
    await prefs.remove('religiousaffiliation_${widget.college['id']}');
    await prefs.remove('whychooseus_${widget.college['id']}');
    await prefs.remove('institutiontype_${widget.college['id']}');
    await prefs.remove('campussetting_${widget.college['id']}');
    await prefs.remove('highestqualificationoffered_${widget.college['id']}');
    await prefs.remove('studentpopulation_${widget.college['id']}');
    await prefs.remove('academicyearcalendar_${widget.college['id']}');
    await prefs.remove('website_${widget.college['id']}');

    Map<String, dynamic> updatedData =
        await fetchUpdatedCollegeData(widget.college['id']);

    await prefs.setString('about_${widget.college['id']}', updatedData['about'] ?? '');
    await prefs.setString(
        'vision_${widget.college['id']}', updatedData['vision'] ?? '');
    await prefs.setString(
        'mission_${widget.college['id']}', updatedData['mission'] ?? '');
    await prefs.setString(
        'address_${widget.college['id']}', updatedData['address'] ?? '');
    await prefs.setString(
        'daysnhours_${widget.college['id']}', updatedData['daysnhours'] ?? '');
    await prefs.setString(
        'postaladdress_${widget.college['id']}', updatedData['postaladdress'] ?? '');
    await prefs.setString(
        'corevalues_${widget.college['id']}', updatedData['corevalues'] ?? '');
    await prefs.setString('motto_${widget.college['id']}', updatedData['motto'] ?? '');
    await prefs.setString('goals_${widget.college['id']}', updatedData['goals'] ?? '');
    await prefs.setString(
        'mandate_${widget.college['id']}', updatedData['mandate'] ?? '');
    await prefs.setString(
        'founded_${widget.college['id']}', updatedData['founded'] ?? '');
    await prefs.setString(
        'accreditation_${widget.college['id']}', updatedData['accreditation'] ?? '');
    await prefs.setString(
        'freewifi_${widget.college['id']}', updatedData['freewifi']?.toString() ?? '');
    await prefs.setString(
        'objectives_${widget.college['id']}', updatedData['objectives'] ?? '');
    await prefs.setString('aims_${widget.college['id']}', updatedData['aims'] ?? '');
    await prefs.setString(
        'pledge_${widget.college['id']}', updatedData['pledge'] ?? '');
    await prefs.setString('statementoffaith_${widget.college['id']}',
        updatedData['statementoffaith'] ?? '');
    await prefs.setString('religiousaffiliation_${widget.college['id']}',
        updatedData['religiousaffiliation'] ?? '');
    await prefs.setString(
        'whychooseus_${widget.college['id']}', updatedData['whychooseus'] ?? '');
    await prefs.setString('institutiontype_${widget.college['id']}',
        updatedData['institutiontype'] ?? '');
    await prefs.setString(
        'campussetting_${widget.college['id']}', updatedData['campussetting'] ?? '');
    await prefs.setString(
        'highestqualificationoffered_${widget.college['id']}',
        updatedData['highestqualificationoffered'] ?? '');
    await prefs.setString('studentpopulation_${widget.college['id']}',
        updatedData['studentpopulation'] ?? '');
    await prefs.setString('academicyearcalendar_${widget.college['id']}',
        updatedData['academicyearcalendar'] ?? '');
    await prefs.setString(
        'website_${widget.college['id']}', updatedData['website'] ?? '');

    print("Data prefetched and cached for TertiaryInfoPage.");
  }

  Future<Map<String, dynamic>> fetchUpdatedCollegeData(int collegeId) async {
    await Future.delayed(Duration(seconds: 2));

    Map<String, dynamic> currentData = widget.college;

    return {
      'about': currentData['about'],
      'vision': currentData['vision'],
      'mission': currentData['mission'],
      'address': currentData['address'],
      'daysnhours': currentData['daysnhours'],
      'postaladdress': currentData['postaladdress'],
      'corevalues': currentData['corevalues'],
      'motto': currentData['motto'],
      'goals': currentData['goals'],
      'mandate': currentData['mandate'],
      'founded': currentData['founded'],
      'accreditation': currentData['accreditation'],
      'freewifi': currentData['freewifi'],
      'objectives': currentData['objectives'],
      'aims': currentData['aims'],
      'pledge': currentData['pledge'],
      'statementoffaith': currentData['statementoffaith'],
      'religiousaffiliation': currentData['religiousaffiliation'],
      'whychooseus': currentData['whychooseus'],
      'institutiontype': currentData['institutiontype'],
      'campussetting': currentData['campussetting'],
      'highestqualificationoffered': currentData['highestqualificationoffered'],
      'studentpopulation': currentData['studentpopulation'],
      'academicyearcalendar': currentData['academicyearcalendar'],
      'website': currentData['website'],
    };
  }

  Future<List<Map<String, dynamic>>> _preloadTodaysEvents() async {
    final now = DateTime.now();
    final todayDay = DateFormat('dd').format(now);
    final todayMonth = DateFormat('MM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .order('starttime', ascending: true);

      if (response == null) {
        return [];
      }

      List<Map<String, dynamic>> eventsData = List<Map<String, dynamic>>
          .from(response as List<dynamic>);

      List<Map<String, dynamic>> filteredEvents = [];
      for (var event in eventsData) {
        if (!_isEventOver(event, now)) {
          filteredEvents.add(event);
        }
      }

      List<Future<void>> futures = [];
      for (final event in filteredEvents) {
        futures.add(_fetchEventImageUrlForPreload(event));
      }
      await Future.wait(futures);

      filteredEvents.sort((a, b) {
        final startTimeA = _parseTimeOfDayForPreload(a['starttime'] ?? '00:00') ?? TimeOfDay.now();
        final startTimeB = _parseTimeOfDayForPreload(b['starttime'] ?? '00:00') ?? TimeOfDay.now();
        if (startTimeA.hour != startTimeB.hour) {
          return startTimeA.hour.compareTo(startTimeB.hour);
        }
        return startTimeA.minute.compareTo(startTimeB.minute);
      });

      String collegeNameForEvents = widget.college['fullname'] ?? '';
      await _cacheTodayEvents(collegeNameForEvents, filteredEvents);

      return filteredEvents;
    } catch (error) {
      print('Error preloading today events: $error');
      return [];
    }
  }

  Future<void> _clearTodayEventsCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Today\'s events cache cleared for $collegeName.');
  }

  Future<void> _cacheTodayEvents(String collegeName, List<Map<String, dynamic>> events) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String eventsJson = jsonEncode(events);
    await prefs.setString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}', eventsJson);
    print('Today\'s events cached for $collegeName.');
  }

  Future<List<Map<String, dynamic>>?> _getCachedTodayEvents(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? eventsJson = prefs.getString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (eventsJson != null) {
      List<dynamic> decodedList = jsonDecode(eventsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  bool _isEventOver(Map<String, dynamic> event, DateTime now) {
    final eventStartTime = event['starttime'] as String? ?? '00:00';
    final eventEndTime = event['endtime'] as String? ?? '23:59';
    final eventDay = int.tryParse(event['startday'] as String? ?? '0') ?? 0;
    final eventMonthStr = event['startmonth'] as String? ?? '0';
    final eventYear = int.tryParse(event['startyear'] as String? ?? '0') ?? 0;

    int monthNumber = int.tryParse(eventMonthStr) ?? 0;

    if (eventDay == 0 || monthNumber == 0 || eventYear == 0) return false;

    final startDate = DateTime(eventYear, monthNumber, eventDay);

    TimeOfDay? endTimeOfDay;
    try {
      endTimeOfDay = _parseTimeOfDayForPreload(eventEndTime);
    } catch (e) {
      print("Error parsing endtime: $eventEndTime for event ${event['fullname']}");
      return false;
    }

    if (endTimeOfDay != null) {
      final eventEndDate = DateTime(startDate.year, startDate.month, startDate.day, endTimeOfDay.hour, endTimeOfDay.minute);
      print("Event End Date: $eventEndDate, Current Time: $now");
      return now.isAfter(eventEndDate.add(const Duration(minutes: 1)));
    }
    return false;
  }

  TimeOfDay? _parseTimeOfDayForPreload(String timeString) {
    try {
      DateTime parsedTime12H = DateFormat('h:mm a').parse(timeString.trim());
      return TimeOfDay.fromDateTime(parsedTime12H);
    } catch (e1) {
      try {
        DateTime parsedTime24H = DateFormat('HH:mm').parse(timeString.trim());
        return TimeOfDay.fromDateTime(parsedTime24H);
      } catch (e2) {
        print("Error parsing time string (preload): $timeString, errors: 12H: $e1, 24H: $e2");
        return null;
      }
    }
  }

  Future<void> _fetchEventImageUrlForPreload(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) {
      return;
    }
    if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') {
      return;
    }
    event['_isImageLoading'] = true;

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventsBucket = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}/events';

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventsBucket)
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventsBucket)
            .getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      // Handle error if needed, e.g., image not found or network issue
    }
    event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
    event['_isImageLoading'] = false;
  }

  Future<bool> _checkHelpdesksAvailability(Map<String, dynamic> college) async {
    final helpdesksTableName = '${college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_helpdesks';

    print("Checking helpdesks availability for table: $helpdesksTableName");

    try {
      final response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('id')
          .limit(1);

      bool hasHelpdesks = (response is List) && response.isNotEmpty;
      print("Helpdesks query response: $response, hasHelpdesks: $hasHelpdesks");
      return hasHelpdesks;
    } catch (error) {
      print('Error checking helpdesks availability (pre-fetch): $error');
      return false;
    }
  }

  Future<bool> _checkLinksAvailability(Map<String, dynamic> college) async {
    final linksTableName = '${college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_links';

    print("Checking links availability for table: $linksTableName");

    try {
      final response = await Supabase.instance.client
          .from(linksTableName)
          .select('id')
          .limit(1);

      bool hasLinks = (response is List) && response.isNotEmpty;
      print("Links query response: $response, hasLinks: $hasLinks");
      return hasLinks;
    } catch (error) {
      print('Error checking links availability (pre-fetch): $error');
      return false;
    }
  }

  void _setupRealtimeEventsListener() {
    final eventsTableName = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';

    _eventsChannel = Supabase.instance.client.realtime
        .channel('public:$eventsTableName')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: eventsTableName,
      callback: (PostgresChangePayload payload) {
        print('Realtime event received: ${payload.eventType}');
        if (mounted) {
          setState(() {
            _hasTodayEvents = true;
          });
        }
      },
    ).subscribe();
  }

  @override
  void initState() {
    super.initState();
    _loadRewardedAd();
    _prefetchInfoPageData();
    _preloadAndCacheHelpdesks();
    _hasTodayEvents = widget.hasTodayEventsPreloaded;
    _loadPreloadTodayEvents();
    _setupRealtimeEventsListener();
  }

  Future<void> _loadPreloadTodayEvents() async {
    if (!_hasTodayEvents) return;
    List<Map<String, dynamic>> todayEvents = await _preloadTodaysEvents();
    setState(() {
      _preloadedTodayEvents = todayEvents;
    });
  }

  Future<void> _preloadAndCacheHelpdesks() async {
    String collegeNameForTable = widget.college['fullname'] ?? '';
    if (collegeNameForTable.isEmpty) return;

    await _clearHelpdeskCache(collegeNameForTable);
    List<Map<String, dynamic>> helpdesks = await _fetchHelpdesksFromDatabase(collegeNameForTable);
    await _cacheHelpdesks(collegeNameForTable, helpdesks);
    setState(() {
      _preloadedHelpdesks = helpdesks;
      print('Helpdesks preloaded and cached for ${widget.college['fullname']}.');
    });
  }

  Future<void> _clearHelpdeskCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Helpdesk cache cleared for $collegeName.');
  }

  Future<List<Map<String, dynamic>>> _fetchHelpdesksFromDatabase(String collegeNameForTable) async {
    final helpdesksTableName = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (response == null || response.isEmpty) {
        return [];
      }
      List<Map<String, dynamic>> updatedHelpdesks = await _updateHelpdeskImageUrls(response, collegeNameForTable);
      return updatedHelpdesks;
    } catch (error) {
      print('Error fetching helpdesks from database: $error');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _updateHelpdeskImageUrls(List<Map<String, dynamic>> helpdesks, String collegeNameForTable) async {
    final collegeHelpdeskBucket = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';
    for (final helpdesk in helpdesks) {
      final fullname = helpdesk['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = 'assets/placeholder_image.png';

      try {
        await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          } catch (e) {
             // Handle error if needed, e.g., image not found or network issue
          }
        }
      }
      helpdesk['image_url'] = imageUrl;
    }
    return helpdesks;
  }

  Future<void> _cacheHelpdesks(String collegeName, List<Map<String, dynamic>> helpdesks) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String helpdesksJson = jsonEncode(helpdesks);
    await prefs.setString('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}', helpdesksJson);
    print('Helpdesks cached for $collegeName.');
  }

  Future<List<Map<String, dynamic>>?> _getCachedHelpdesks(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? helpdesksJson = prefs.getString('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (helpdesksJson != null) {
      List<dynamic> decodedList = jsonDecode(helpdesksJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (AdAd) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              _loadRewardedAd(); // Load a new ad after dismissal
              Navigator.pop(context); // Navigate back after ad dismissed
            },
            onAdFailedToShowFullScreenContent: (AdAd, error) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              print('Failed to show rewarded ad: $error');
              _loadRewardedAd(); // Retry loading the ad
              Navigator.pop(context); // Navigate back even if ad fails to show
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          setState(() {
            _rewardedAd = null;
            _isAdLoaded = false;
          });
        },
      ),
    );
  }

  void _showRewardedAd() {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('User earned reward: ${reward.amount} ${reward.type}');
          // Navigation.pop(context) is now handled in ad callbacks in _loadRewardedAd
        },
      );
    } else {
      print('Rewarded ad not ready, navigating back directly.');
      Navigator.pop(context); // Navigate back directly if ad is not ready
    }
  }

  Future<void> _launchDialer() async {
    final String phoneNumber = widget.college['phone'] ?? '';
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchWhatsapp() async {
    final String whatsappNumber = widget.college['whatsapp'] ?? '';
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchNavigation() async {
    final double latitude = widget.college['latitude'] ?? 0.0;
    final double longitude = widget.college['longitude'] ?? 0.0;
    final Uri url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    _eventsChannel.unsubscribe();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.college['phone'];
    final hasPhone = phone != null && phone.isNotEmpty;
    final latitude = widget.college['latitude'];
    final longitude = widget.college['longitude'];
    final hasLocation = latitude != null && longitude != null;
    final whatsapp = widget.college['whatsapp'];
    final hasWhatsapp = whatsapp != null && whatsapp.isNotEmpty;

    return WillPopScope(
      onWillPop: () async {
        _showRewardedAd();
        return false; // Prevent immediate back navigation
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd(); // Show ad on back button press
              // Navigation.pop(context) will be handled in ad callbacks
            },
          ),
          title: Text(
            widget.college['fullname'] ?? '',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
actions: [
            IconButton(
              icon: Icon(
                Icons.support_agent, // AI Agent Icon - Replaced Connectivity Icon
                color: theme.colorScheme.onSurface,
              ),
              // Start of onPressed callback
              onPressed: () {
			      // --- MORE DEBUGGING ---
                print("--- AI Agent Button Tapped ---");
                if (widget.college == null) {
                  print("ERROR: widget.college is NULL when trying to navigate to AI Agent!");
                  // Optional: Show a snackbar or disable button if data is null
                  // ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("College data not loaded yet.")));
                } else { // Start of else block
                  print("Passing collegeData to AiAgentPage:");
                  // Print a few key fields to be sure
                  print("  Fullname: ${widget.college['fullname']}");
                  print("  City: ${widget.college['city']}");
                  print("  ID: ${widget.college['id']}"); // Or another field you expect

                  // Navigation should be INSIDE the else block if it depends on widget.college
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AiAgentPage(
                        isDarkMode: widget.isDarkMode,
                        toggleTheme: widget.toggleTheme,
                        collegeData: widget.college, // Pass the map
                      ),
                    ),
                  );
                } // <--- End of else block
              }, // <--- THIS WAS THE MISSING CLOSING BRACE for onPressed
            ),
            IconButton(
              icon: Icon(
                Icons.view_in_ar,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryVirtualTourPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = 2;
              double aspectRatio = 1.3;

              if (constraints.maxWidth > 1200) {
                crossAxisCount = 6;
                aspectRatio = 1.4;
              } else if (constraints.maxWidth > 900) {
                crossAxisCount = 4;
                aspectRatio = 1.3;
              } else if (constraints.maxWidth > 600) {
                crossAxisCount = 3;
                aspectRatio = 1.2;
              }

              return GridView.count(
                crossAxisCount: crossAxisCount,
                padding: const EdgeInsets.all(16),
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: aspectRatio,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildGridItem(context, 'Start', Icons.play_arrow, theme, () async {
                    bool hasHelpdesks = await _checkHelpdesksAvailability(widget.college);
                    bool hasLinks = await _checkLinksAvailability(widget.college);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryStartPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          hasHelpdesksPreloaded: hasHelpdesks,
                          hasLinksPreloaded: hasLinks,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Updates', Icons.update, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryUpdatesPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Path', Icons.timeline, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryPathPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'People', Icons.people, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryPeoplePage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Lodging', Icons.hotel, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryLodgingPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Shop & Eat', Icons.restaurant_menu, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryShopEatPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Transport', Icons.directions_bus, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryTransportPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Core', Icons.stars, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryCorePage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Academics', Icons.school, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryAcademicsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Programs', Icons.list_alt, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryProgramsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Athletics & Groups', Icons.sports_soccer, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryAthleticsGroupsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Media', Icons.perm_media, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryMediaPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Startups', Icons.rocket_launch, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryStartupsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Projects & Publications',
                      Icons.article, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryProjectsPublicationsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Buildings & Spaces', Icons.apartment, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryBuildingsSpacesPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Statistics', Icons.bar_chart, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryStatisticsPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Calendar', Icons.calendar_month, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryCalendarPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Map', Icons.map, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryMapPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Feedback', Icons.feedback, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryFeedbackPage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Historical Timeline', Icons.history_edu, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryTimelinePage(
                          institutionName: widget.college['fullname'] ?? '',
                          collegeData: widget.college,
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Rentals', Icons.swap_horiz, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryRentalsPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Jobs', Icons.work, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryJobsPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(
                      context, 'Services', Icons.miscellaneous_services, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryServicesPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Money', Icons.payments, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryMoneyPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Health', Icons.local_hospital, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiaryHealthPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                  _buildGridItem(context, 'Safety', Icons.security, theme, () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TertiarySafetyPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                          collegeData: widget.college,
                          institutionName: widget.college['fullname'] ?? '',
                          isFromDetailPage: widget.isFromTertiaryPage,
                        ),
                      ),
                    );
                  }),
                ],
              );
            },
          ),
        ),
        bottomNavigationBar: Container(
          color: theme.colorScheme.surface,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface.withOpacity(hasPhone ? 1.0 : 0.5),
                    ),
                    onPressed: hasPhone ? _launchDialer : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.navigation,
                      color: theme.colorScheme.onSurface.withOpacity(hasLocation ? 1.0 : 0.5),
                    ),
                    onPressed: hasLocation ? _launchNavigation : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.watch,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryTodayPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            collegeData: widget.college,
                            preloadedEvents: _preloadedTodayEvents,
                            isFromDetailPage: widget.isFromTertiaryPage,
                          ),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface.withOpacity(hasWhatsapp ? 1.0 : 0.5),
                    ),
                    onPressed: hasWhatsapp ? _launchWhatsapp : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.info,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryInfoPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            schoolName: widget.college['fullname'] ?? '',
                            collegeData: widget.college,
                            isFromDetailPage: widget.isFromTertiaryPage,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme, VoidCallback? onTap) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchExternalPage(String title, String? urlString) async {
    if (urlString == null || urlString.isEmpty) {
      print('No URL provided for $title'); // Or show a snackbar to the user
      return;
    }
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}