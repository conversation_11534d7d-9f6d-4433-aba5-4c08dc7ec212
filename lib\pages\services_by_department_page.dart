import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'service_detail_page.dart';
import 'tertiary_services_page.dart';

class ServicesByDepartmentPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ServicesByDepartmentPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ServicesByDepartmentPage> createState() => _ServicesByDepartmentPageState();
}

class _ServicesByDepartmentPageState extends State<ServicesByDepartmentPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Service> _services = [];
  Map<String, List<Service>> _servicesByDepartment = {};
  TextEditingController _searchController = TextEditingController();
  List<String> _expandedDepartments = [];

  @override
  void initState() {
    super.initState();
    _fetchServices();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchServices() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_services';

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('department', ascending: true)
          .order('fullname', ascending: true);

      final List<Service> services = List<Map<String, dynamic>>.from(response)
          .map((json) => Service.fromJson(json))
          .toList();

      // Group services by department
      final Map<String, List<Service>> servicesByDepartment = {};

      for (var service in services) {
        final department = service.department.isNotEmpty
            ? service.department
            : 'Other';

        if (!servicesByDepartment.containsKey(department)) {
          servicesByDepartment[department] = [];
        }

        servicesByDepartment[department]!.add(service);
      }

      setState(() {
        _services = services;
        _servicesByDepartment = servicesByDepartment;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading services: $e';
      });
      print('Error fetching services: $e');
    }
  }

  void _filterServices(String query) {
    if (query.isEmpty) {
      _fetchServices();
      return;
    }

    final String searchQuery = query.toLowerCase();
    final Map<String, List<Service>> filteredServicesByDepartment = {};

    _servicesByDepartment.forEach((department, services) {
      final filteredServices = services.where((service) {
        return service.fullname.toLowerCase().contains(searchQuery) ||
               service.about.toLowerCase().contains(searchQuery) ||
               service.requirements.toLowerCase().contains(searchQuery) ||
               service.price.toLowerCase().contains(searchQuery);
      }).toList();

      if (filteredServices.isNotEmpty) {
        filteredServicesByDepartment[department] = filteredServices;
      }
    });

    setState(() {
      _servicesByDepartment = filteredServicesByDepartment;
      // Expand all departments when searching
      _expandedDepartments = filteredServicesByDepartment.keys.toList();
    });
  }

  void _toggleDepartmentExpansion(String department) {
    setState(() {
      if (_expandedDepartments.contains(department)) {
        _expandedDepartments.remove(department);
      } else {
        _expandedDepartments.add(department);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Services by Department',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchServices,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search services...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _fetchServices();
                          });
                        },
                      )
                    : null,
              ),
              onChanged: _filterServices,
            ),
          ),

          // Services list grouped by department
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchServices,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _servicesByDepartment.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.room_service,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _services.isEmpty
                                      ? 'No services available'
                                      : 'No services match your search',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _servicesByDepartment.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            itemBuilder: (context, index) {
                              final department = _servicesByDepartment.keys.elementAt(index);
                              final departmentServices = _servicesByDepartment[department]!;
                              final isExpanded = _expandedDepartments.contains(department);

                              return Card(
                                margin: const EdgeInsets.only(bottom: 12.0),
                                child: Column(
                                  children: [
                                    // Department header
                                    ListTile(
                                      contentPadding: const EdgeInsets.all(16.0),
                                      title: Text(
                                        department,
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                      subtitle: Text(
                                        '${departmentServices.length} service${departmentServices.length != 1 ? 's' : ''}',
                                        style: TextStyle(
                                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                                        ),
                                      ),
                                      trailing: Icon(
                                        isExpanded ? Icons.expand_less : Icons.expand_more,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      onTap: () => _toggleDepartmentExpansion(department),
                                    ),

                                    // Department services
                                    if (isExpanded)
                                      ListView.builder(
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemCount: departmentServices.length,
                                        itemBuilder: (context, serviceIndex) {
                                          final service = departmentServices[serviceIndex];

                                          return Container(
                                            decoration: BoxDecoration(
                                              border: Border(
                                                top: BorderSide(
                                                  color: theme.colorScheme.onSurface.withOpacity(0.1),
                                                ),
                                              ),
                                            ),
                                            child: ListTile(
                                              contentPadding: const EdgeInsets.symmetric(
                                                horizontal: 24.0,
                                                vertical: 8.0,
                                              ),
                                              title: Text(
                                                service.fullname,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: theme.colorScheme.onSurface,
                                                ),
                                              ),
                                              subtitle: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  if (service.price.isNotEmpty && service.price != 'Price not specified')
                                                    Padding(
                                                      padding: const EdgeInsets.only(top: 4.0),
                                                      child: Row(
                                                        children: [
                                                          Icon(Icons.attach_money, size: 16, color: theme.colorScheme.primary),
                                                          const SizedBox(width: 4),
                                                          Text('Price: ${service.price}'),
                                                        ],
                                                      ),
                                                    ),
                                                  if (service.time.isNotEmpty && service.time != 'Time not specified')
                                                    Padding(
                                                      padding: const EdgeInsets.only(top: 4.0),
                                                      child: Row(
                                                        children: [
                                                          Icon(Icons.access_time, size: 16, color: theme.colorScheme.primary),
                                                          const SizedBox(width: 4),
                                                          Text('Time: ${service.time}'),
                                                        ],
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              trailing: Icon(
                                                Icons.arrow_forward_ios,
                                                size: 16,
                                                color: theme.colorScheme.onSurface.withOpacity(0.5),
                                              ),
                                              onTap: () {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => ServiceDetailPage(
                                                      service: service,
                                                      institutionName: widget.institutionName,
                                                      isDarkMode: currentIsDarkMode,
                                                      toggleTheme: widget.toggleTheme,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        },
                                      ),
                                  ],
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
