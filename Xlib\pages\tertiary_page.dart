import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadPreloadedColleges() {
    if (_MyAppState.preloadedColleges != null) {
      setState(() {
        _colleges = List<Map<String, dynamic>>.from(_MyAppState.preloadedColleges!);
        _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      });
      // Fetch image URLs for colleges without them
      for (var college in _colleges) {
        if (college['image_url'] == null || college['image_url'] == 'assets/placeholder_image.png') {
          _fetchImageUrl(college);
        }
      }
    } else {
      // Handle the case where preloaded data is not available (shouldn't happen often)
      _loadCollegesFromSupabase();
    }
  }

  Future<void> _loadCollegesFromSupabase() async {
    setState(() {
      _isLoading = true;
      print("isloading = true");
    });
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('colleges')
          .select('*')
          .order('fullname', ascending: true);

      final updatedColleges = await _updateCollegeImageUrls(response);

      setState(() {
        _colleges = updatedColleges;
        _isLoading = false;
        print("isloading = false");
        _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      });
      _MyAppState.preloadedColleges = List<Map<String, dynamic>>.from(updatedColleges);
    } catch (error) {
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching colleges: $error')),
        );
        setState(() {
          _isLoading = false;
          print("isloading = false error");
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(List<Map<String, dynamic>> colleges) async {
    for (final college in colleges) {
      final fullname = college['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = '';

      try {
        await Supabase.instance.client.storage.from('colleges').download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from('colleges').download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
          } catch (e) {
            // Image not found, use placeholder
          }
        }
      }
      college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
    }
    return colleges;
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'colleges',
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .eq('id', newCollegeId);
          if (newCollegeResponse.isNotEmpty) {
            final newCollege = newCollegeResponse.first;
            final updatedCollege = await _updateCollegeImageUrls([newCollege]);
            setState(() {
              _colleges = [..._colleges, updatedCollege.first];
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data
            _MyAppState.preloadedColleges = [..._MyAppState.preloadedColleges ?? [], updatedCollege.first];
            _MyAppState.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .eq('id', updatedCollegeId);
          if (updatedCollegeResponse.isNotEmpty) {
            final updatedCollege = updatedCollegeResponse.first;
            setState(() {
              _colleges = _colleges.map((college) {
                return college['id'] == updatedCollege['id'] ? updatedCollege : college;
              }).toList();
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data
            _MyAppState.preloadedColleges = _MyAppState.preloadedColleges?.map((college) {
              return college['id'] == updatedCollege['id'] ? updatedCollege : college;
            }).toList();
            _MyAppState.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCollegeId = payload.oldRecord['id'];
          setState(() {
            _colleges.removeWhere((college) => college['id'] == deletedCollegeId);
          });
          // Update preloaded data
          _MyAppState.preloadedColleges?.removeWhere((college) => college['id'] == deletedCollegeId);
        }
      },
    )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed) {
      setState(() {});
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TertiaryDetailPage(
            college: college,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Tertiary',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadCollegesFromSupabase,
              child: _colleges.isEmpty
                  ? LayoutBuilder(builder:
                      (BuildContext context, BoxConstraints constraints) {
                    return SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: SizedBox(
                        height: constraints.maxHeight,
                        child: const Center(
                          child: Text('No colleges available.'),
                        ),
                      ),
                    );
                  })
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _colleges.length,
                      itemBuilder: (context, index) {
                        final university = _colleges[index];
                        return VisibilityDetector(
                          key: Key('college_${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 &&
                                (university['image_url'] == null ||
                                    university['image_url'] ==
                                        'assets/placeholder_image.png')) {
                              _fetchImageUrl(university);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor:
                                    theme.colorScheme.secondary.withOpacity(0.1),
                                backgroundImage: CachedNetworkImageProvider(
                                        university['image_url'] ??
                                            'assets/placeholder_image.png')
                                    as ImageProvider<Object>,
                                onBackgroundImageError:
                                    (exception, stackTrace) {
                                  print(
                                      'Error loading image for ${university['fullname']}: $exception');
                                },
                              ),
                              title: Text(
                                university['fullname'] ?? 'Unnamed College',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '${university['city'] ?? ''}${university['state'] != null && university['city'] != null ? ', ' : ''}${university['state'] ?? ''}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () =>
                                  _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    final fullname = college['fullname'] as String? ?? '';
    final imageNamePng = '$fullname.png';
    final imageNameJpg = '$fullname.jpg';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';

    try {
      final file = await Supabase.instance.client.storage.from('colleges').download(imageNamePng);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNamePng);
      }
    } catch (e) {
      try {
        final file = await Supabase.instance.client.storage.from('colleges').download(imageNameJpg);
        if (file.isNotEmpty) {
          imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameJpg);
        }
      } catch (e) {
        try {
          final file = await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
          if (file.isNotEmpty) {
            imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
          }
        } catch (e) {
          // Image not found, use placeholder
        }
      }
    }

    if (mounted) {
      setState(() {
        college['image_url'] =
            imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      });
    }
  }
}