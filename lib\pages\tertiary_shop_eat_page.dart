import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'shops_eateries_page.dart';
import 'meal_plans_page.dart';
import 'local_dining_page.dart';
import 'student_discounts_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryShopEatPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryShopEatPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryShopEatPage> createState() => _TertiaryShopEatPageState();
}

class _TertiaryShopEatPageState extends State<TertiaryShopEatPage> {
  List<Map<String, dynamic>>? _cachedShopsEateries;
  List<Map<String, dynamic>>? _cachedMealPlans;
  List<Map<String, dynamic>>? _cachedLocalDining;
  List<Map<String, dynamic>>? _cachedStudentDiscounts;
  String? _lastCollegeName;
  bool _isLoadingShopsEateries = false;
  bool _isLoadingMealPlans = false;
  bool _isLoadingLocalDining = false;
  bool _isLoadingStudentDiscounts = false;
  late RealtimeChannel _shopsEateriesRealtimeChannel;
  late RealtimeChannel _mealPlansRealtimeChannel;
  late RealtimeChannel _localDiningRealtimeChannel;
  late RealtimeChannel _studentDiscountsRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryShopEatPage initState called for ${widget.institutionName}");
    _loadCachedShopsEateries();
    _loadCachedMealPlans();
    _loadCachedLocalDining();
    _loadCachedStudentDiscounts();
    _loadShopsEateriesFromDatabaseAndCache();
    _loadMealPlansFromDatabaseAndCache();
    _loadLocalDiningFromDatabaseAndCache();
    _loadStudentDiscountsFromDatabaseAndCache();
    _setupShopsEateriesRealtimeListener();
    _setupMealPlansRealtimeListener();
    _setupLocalDiningRealtimeListener();
    _setupStudentDiscountsRealtimeListener();
  }

  @override
  void dispose() {
    _shopsEateriesRealtimeChannel.unsubscribe();
    _mealPlansRealtimeChannel.unsubscribe();
    _localDiningRealtimeChannel.unsubscribe();
    _studentDiscountsRealtimeChannel.unsubscribe();
    super.dispose();
  }

  // Shops & Eateries methods
  Future<List<Map<String, dynamic>>?> _getCachedShopsEateries(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? shopsEateriesJson = prefs.getString(
        'shopsoreateries_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (shopsEateriesJson != null) {
      List<dynamic> decodedList = jsonDecode(shopsEateriesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheShopsEateries(String collegeName, List<Map<String, dynamic>> shopsEateries) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String shopsEateriesJson = jsonEncode(shopsEateries);
    await prefs.setString(
        'shopsoreateries_${collegeName.toLowerCase().replaceAll(' ', '')}', shopsEateriesJson);
    print('Shops & Eateries cached for $collegeName.');
  }

  Future<void> _loadCachedShopsEateries() async {
    final cachedData = await _getCachedShopsEateries(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedShopsEateries = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded shops & eateries from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadShopsEateriesFromDatabaseAndCache() async {
    if (_isLoadingShopsEateries) {
      return;
    }

    setState(() {
      _isLoadingShopsEateries = true;
    });

    print("Fetching shops & eateries for ${widget.institutionName} from database");
    final shopsEateriesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_shopsoreateries';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(shopsEateriesTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedShopsEateries = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingShopsEateries = false;
          _cacheShopsEateries(widget.institutionName, response);
          print("Shops & Eateries fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingShopsEateries = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingShopsEateries = false;
          _cachedShopsEateries = [];
          print("Error fetching shops & eateries for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingShopsEateries = false;
      }
    }
  }

  void _setupShopsEateriesRealtimeListener() {
    final shopsEateriesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_shopsoreateries';
    _shopsEateriesRealtimeChannel = Supabase.instance.client
        .channel('shopsoreateries_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: shopsEateriesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for shops & eateries of ${widget.institutionName}: ${payload.eventType}");
        _loadShopsEateriesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Meal Plans methods
  Future<List<Map<String, dynamic>>?> _getCachedMealPlans(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? mealPlansJson = prefs.getString(
        'mealplans_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (mealPlansJson != null) {
      List<dynamic> decodedList = jsonDecode(mealPlansJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheMealPlans(String collegeName, List<Map<String, dynamic>> mealPlans) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String mealPlansJson = jsonEncode(mealPlans);
    await prefs.setString(
        'mealplans_${collegeName.toLowerCase().replaceAll(' ', '')}', mealPlansJson);
    print('Meal Plans cached for $collegeName.');
  }

  Future<void> _loadCachedMealPlans() async {
    final cachedData = await _getCachedMealPlans(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedMealPlans = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded meal plans from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadMealPlansFromDatabaseAndCache() async {
    if (_isLoadingMealPlans) {
      return;
    }

    setState(() {
      _isLoadingMealPlans = true;
    });

    print("Fetching meal plans for ${widget.institutionName} from database");
    final mealPlansTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_mealplans';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(mealPlansTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedMealPlans = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingMealPlans = false;
          _cacheMealPlans(widget.institutionName, response);
          print("Meal Plans fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingMealPlans = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingMealPlans = false;
          _cachedMealPlans = [];
          print("Error fetching meal plans for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingMealPlans = false;
      }
    }
  }

  void _setupMealPlansRealtimeListener() {
    final mealPlansTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_mealplans';
    _mealPlansRealtimeChannel = Supabase.instance.client
        .channel('mealplans_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: mealPlansTableName,
      callback: (payload) async {
        print(
            "Realtime update received for meal plans of ${widget.institutionName}: ${payload.eventType}");
        _loadMealPlansFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Local Dining methods
  Future<List<Map<String, dynamic>>?> _getCachedLocalDining(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? localDiningJson = prefs.getString(
        'localareadining_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (localDiningJson != null) {
      List<dynamic> decodedList = jsonDecode(localDiningJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheLocalDining(String collegeName, List<Map<String, dynamic>> localDining) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String localDiningJson = jsonEncode(localDining);
    await prefs.setString(
        'localareadining_${collegeName.toLowerCase().replaceAll(' ', '')}', localDiningJson);
    print('Local Dining cached for $collegeName.');
  }

  Future<void> _loadCachedLocalDining() async {
    final cachedData = await _getCachedLocalDining(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedLocalDining = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded local dining from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadLocalDiningFromDatabaseAndCache() async {
    if (_isLoadingLocalDining) {
      return;
    }

    setState(() {
      _isLoadingLocalDining = true;
    });

    print("Fetching local dining for ${widget.institutionName} from database");
    final localDiningTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_localareadining';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(localDiningTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedLocalDining = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingLocalDining = false;
          _cacheLocalDining(widget.institutionName, response);
          print("Local Dining fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingLocalDining = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingLocalDining = false;
          _cachedLocalDining = [];
          print("Error fetching local dining for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingLocalDining = false;
      }
    }
  }

  void _setupLocalDiningRealtimeListener() {
    final localDiningTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_localareadining';
    _localDiningRealtimeChannel = Supabase.instance.client
        .channel('localareadining_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localDiningTableName,
      callback: (payload) async {
        print(
            "Realtime update received for local dining of ${widget.institutionName}: ${payload.eventType}");
        _loadLocalDiningFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Student Discounts methods
  Future<List<Map<String, dynamic>>?> _getCachedStudentDiscounts(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? studentDiscountsJson = prefs.getString(
        'studentdiscounts_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (studentDiscountsJson != null) {
      List<dynamic> decodedList = jsonDecode(studentDiscountsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheStudentDiscounts(String collegeName, List<Map<String, dynamic>> studentDiscounts) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String studentDiscountsJson = jsonEncode(studentDiscounts);
    await prefs.setString(
        'studentdiscounts_${collegeName.toLowerCase().replaceAll(' ', '')}', studentDiscountsJson);
    print('Student Discounts cached for $collegeName.');
  }

  Future<void> _loadCachedStudentDiscounts() async {
    final cachedData = await _getCachedStudentDiscounts(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedStudentDiscounts = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded student discounts from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadStudentDiscountsFromDatabaseAndCache() async {
    if (_isLoadingStudentDiscounts) {
      return;
    }

    setState(() {
      _isLoadingStudentDiscounts = true;
    });

    print("Fetching student discounts for ${widget.institutionName} from database");
    final studentDiscountsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_studentdiscounts';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(studentDiscountsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedStudentDiscounts = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingStudentDiscounts = false;
          _cacheStudentDiscounts(widget.institutionName, response);
          print("Student Discounts fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingStudentDiscounts = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingStudentDiscounts = false;
          _cachedStudentDiscounts = [];
          print("Error fetching student discounts for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingStudentDiscounts = false;
      }
    }
  }

  void _setupStudentDiscountsRealtimeListener() {
    final studentDiscountsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_studentdiscounts';
    _studentDiscountsRealtimeChannel = Supabase.instance.client
        .channel('studentdiscounts_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: studentDiscountsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for student discounts of ${widget.institutionName}: ${payload.eventType}");
        _loadStudentDiscountsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    // Always show grid items
    return Visibility(
      key: Key('shop_eat_grid_item_$title'),
      visible: true,
      child: Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (title == 'Shops & Eateries') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ShopsEateriesPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedShopsEateries: _cachedShopsEateries,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Meal Plans') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MealPlansPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedMealPlans: _cachedMealPlans,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Local Dining') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LocalDiningPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedLocalDining: _cachedLocalDining,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Student Discounts') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => StudentDiscountsPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedStudentDiscounts: _cachedStudentDiscounts,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                ),
                ...[
                  const SizedBox(height: 8),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Shop & Eat',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Shops & Eateries', Icons.store, theme, isFromDetailPage),
                _buildGridItem(context, 'Meal Plans', Icons.restaurant_menu, theme, isFromDetailPage),
                _buildGridItem(context, 'Local Dining', Icons.local_dining, theme, isFromDetailPage),
                _buildGridItem(context, 'Student Discounts', Icons.loyalty, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}