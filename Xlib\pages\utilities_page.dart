// list_page.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'login_page.dart';
import 'todo_page.dart';
import 'shopping_page.dart';
import 'wallet_page.dart';
import 'notes_page.dart';
// import 'yt_video_downloader_page.dart'; // Removed
import 'offline_library_page.dart';
// import 'file_manager_page.dart'; // Removed
import 'budget_tracker_page.dart';
// import 'barcode_qr_scanner_page.dart'; // Removed
import 'timer_page.dart'; // Now contains TimerScreen
import 'stopwatch_page.dart'; // Now contains StopwatchScreen

class ListPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ListPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<ListPage> createState() => _ListPageState();
}

class _ListPageState extends State<ListPage> {
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;

  // Replace with your actual AdMob Rewarded Ad Unit ID
  final String _adUnitId = 'YOUR_REWARDED_AD_UNIT_ID';

  @override
  void initState() {
    super.initState();
    _loadRewardedAd();
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (AdAd) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              _loadRewardedAd(); // Load a new ad after dismissal
            },
            onAdFailedToShowFullScreenContent: (AdAd, error) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              print('Failed to show rewarded ad: $error');
              _loadRewardedAd(); // Retry loading the ad
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          setState(() {
            _rewardedAd = null;
            _isAdLoaded = false;
          });
        },
      ),
    );
  }

  void _showRewardedAd(BuildContext context, String title) {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        // Reward the user (optional, but good practice for true rewarded ads)
        print('User earned reward: ${reward.amount} ${reward.type}');
        _navigateToPage(context, title);
      });
    } else {
      // If the ad is not loaded, navigate directly (or handle as needed)
      print('Rewarded ad not ready, navigating directly.');
      _navigateToPage(context, title);
    }
  }

  void _navigateToPage(BuildContext context, String title) {
    final currentIsDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (title == 'Wallet') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => WalletPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Todo List') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => TodoApp(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Shopping List') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ShoppingApp(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Notes') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => NotesPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
      // Removed YT Video Downloader navigation
      // Removed File Manager navigation
    } else if (title == 'Offline Library') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => OfflineLibraryPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Budget & Expense Tracker') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => BudgetTrackerPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Timer') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const TimerScreen()), // Navigate to TimerScreen
      );
    } else if (title == 'Stopwatch') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const StopwatchScreen()), // Navigate to StopwatchScreen
      );
    }
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool currentIsDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          _showRewardedAd(context, title);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: currentIsDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Utilities',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Wallet', Icons.wallet, theme),
                _buildGridItem(context, 'Todo List', Icons.list, theme),
                _buildGridItem(context, 'Shopping List', Icons.shopping_cart_outlined, theme),
                _buildGridItem(context, 'Notes', Icons.note, theme),
                _buildGridItem(context, 'Offline Library', Icons.library_add_check, theme),
                _buildGridItem(context, 'Budget & Expense Tracker', Icons.account_balance, theme),
                _buildGridItem(context, 'Timer', Icons.timer_outlined, theme), // Added
                _buildGridItem(context, 'Stopwatch', Icons.watch_later_outlined, theme), // Added
                // _buildGridItem(context, 'Barcode & QR Code Scanner', Icons.qr_code_scanner, theme), // Removed
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    // Action for Home button
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}