import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DensityConverterPage extends StatefulWidget {
  const DensityConverterPage({Key? key}) : super(key: key);

  @override
  _DensityConverterPageState createState() => _DensityConverterPageState();
}

class _DensityConverterPageState extends State<DensityConverterPage> {
  final Map<String, double> _densityUnits = {
    'kg/m³': 1.0,
    'g/cm³': 1000.0,
    'lb/ft³': 16.0185,
  };

  String _fromUnit = 'kg/m³';
  String _toUnit = 'g/cm³';
  double _inputValue = 0.0;
  double _outputValue = 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Density Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Value to Convert',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _inputValue = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _fromUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _densityUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromUnit = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<String>(
                        value: _toUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _densityUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toUnit = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertDensity();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_outputValue $_toUnit',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertDensity() {
    if (_densityUnits[_fromUnit] != null && _densityUnits[_toUnit] != null) {
      setState(() {
        _outputValue = _inputValue * (_densityUnits[_fromUnit]! / _densityUnits[_toUnit]!);
      });
    } else {
      setState(() {
        _outputValue = 0.0;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Density unit not found.')),
      );
    }
  }
}