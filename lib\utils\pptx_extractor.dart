import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:archive/archive.dart';
import 'package:xml/xml.dart';

class PptxExtractor {
  /// Extract text from a PPTX file using the archive package
  /// This works by treating the PPTX as a ZIP archive and extracting the slide XML files
  static Future<String> extractText(Uint8List bytes) async {
    try {
      // Decode the archive from bytes
      final archive = ZipDecoder().decodeBytes(bytes);

      // Find the presentation content
      final presentationEntry = archive.findFile('ppt/presentation.xml');
      if (presentationEntry == null) {
        return '[Error: Could not find presentation.xml in PPTX file]';
      }

      // Parse the presentation XML to find slides
      final presentationContent = utf8.decode(presentationEntry.content as List<int>);
      final presentationDoc = XmlDocument.parse(presentationContent);

      // Extract slide references
      final slideRefs = presentationDoc.findAllElements('p:sldId');
      final slideIds = slideRefs.map((ref) => ref.getAttribute('r:id')).toList();

      // Find the relationship file to map slide IDs to filenames
      final relsEntry = archive.findFile('ppt/_rels/presentation.xml.rels');
      if (relsEntry == null) {
        return '[Error: Could not find relationship file in PPTX]';
      }

      final relsContent = utf8.decode(relsEntry.content as List<int>);
      final relsDoc = XmlDocument.parse(relsContent);

      // Map slide IDs to target files
      final Map<String, String> slideFiles = {};
      final relationships = relsDoc.findAllElements('Relationship');

      for (var rel in relationships) {
        final id = rel.getAttribute('Id');
        final target = rel.getAttribute('Target');
        if (id != null && target != null && slideIds.contains(id)) {
          slideFiles[id] = target;
        }
      }

      // Extract text from each slide
      final buffer = StringBuffer();

      for (var id in slideIds) {
        if (id == null || !slideFiles.containsKey(id)) continue;

        final slideFile = slideFiles[id]!;
        final slideEntry = archive.findFile('ppt/$slideFile');

        if (slideEntry != null) {
          final slideContent = utf8.decode(slideEntry.content as List<int>);
          final slideDoc = XmlDocument.parse(slideContent);

          // Extract slide title
          final title = slideDoc.findAllElements('p:title').map((e) => _extractTextFromElement(e)).join(' ');
          if (title.isNotEmpty) {
            buffer.writeln('Slide Title: $title');
          }

          // Extract text from text boxes
          final textElements = slideDoc.findAllElements('a:t');
          for (var textElement in textElements) {
            final text = textElement.innerText.trim();
            if (text.isNotEmpty) {
              buffer.writeln(text);
            }
          }

          buffer.writeln(); // Add a blank line between slides
        }
      }

      return buffer.toString();
    } catch (e) {
      return '[Error extracting text from PPTX: $e]';
    }
  }

  /// Extract text from a PPT file (older PowerPoint format)
  /// This is more challenging without specialized libraries
  /// We'll use a simple approach that may extract some text but won't be perfect
  static String extractTextFromPpt(Uint8List bytes) {
    try {
      // Try to find text in the binary data
      final result = StringBuffer();
      String text = '';

      // Try UTF-16LE encoding which is common in PPT files
      try {
        // Use the built-in codec from dart:convert
        final ByteData byteData = ByteData.sublistView(Uint8List.fromList(bytes));
        final buffer = StringBuffer();

        // Process 2 bytes at a time for UTF-16LE
        for (int i = 0; i < bytes.length - 1; i += 2) {
          final charCode = byteData.getUint16(i, Endian.little);
          // Skip null bytes and control characters
          if (charCode > 31 && charCode < 65536) {
            buffer.writeCharCode(charCode);
          }
        }
        text = buffer.toString();
      } catch (e) {
        // If UTF-16LE fails, try ASCII with allowMalformed
        try {
          text = ascii.decode(bytes, allowInvalid: true);
        } catch (e) {
          // Last resort: Latin1
          text = latin1.decode(bytes);
        }
      }

      // Clean up the text by removing non-printable characters
      final cleanedText = text.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F]'), ' ');

      // Extract words that look like actual text (at least 2 characters)
      final words = RegExp(r'[A-Za-z]{2,}').allMatches(cleanedText);
      for (var word in words) {
        result.write(word.group(0));
        result.write(' ');
      }

      return result.toString();
    } catch (e) {
      return '[Error extracting text from PPT: $e]';
    }
  }

  // Helper method to extract text from an XML element
  static String _extractTextFromElement(XmlElement element) {
    final textElements = element.findAllElements('a:t');
    return textElements.map((e) => e.innerText).join(' ');
  }
}
