import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class DaycareDetailPage extends StatefulWidget {
  final Map<String, dynamic> daycare;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DaycareDetailPage({
    Key? key,
    required this.daycare,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DaycareDetailPage> createState() => _DaycareDetailPageState();
}

class _DaycareDetailPageState extends State<DaycareDetailPage> {
  late RealtimeChannel _daycareRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _daycareRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _daycareRealtimeChannel = Supabase.instance.client
        .channel('daycare_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'daycares',
      callback: (payload) async {
        // Manual filtering for the specific daycare
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.daycare['id']) {
          print("Realtime update received for daycare detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshDaycare();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshDaycare() async {
    try {
      final response = await Supabase.instance.client
          .from('daycares')
          .select('*')
          .eq('id', widget.daycare['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's daycare with the new data
          widget.daycare.clear();
          widget.daycare.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing daycare: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      print('Could not launch $emailUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$phoneNumber',
    );
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $whatsappUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract location information
    final String fullname = widget.daycare['fullname'] ?? 'Unknown';
    final String building = widget.daycare['building'] ?? '';
    final String room = widget.daycare['room'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    // Extract other information
    final String hours = widget.daycare['hours'] ?? '';
    final String boxNumber = widget.daycare['boxnumber'] ?? '';
    final String about = widget.daycare['about'] ?? '';

    // Extract contact information
    final String phone = widget.daycare['phone']?.toString() ?? '';
    final String email = widget.daycare['email']?.toString() ?? '';
    final String fax = widget.daycare['fax']?.toString() ?? '';
    final String whatsappNumber = widget.daycare['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.daycare['latitude'];
    final dynamic longitude = widget.daycare['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Daycare details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.child_care,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (locationText.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      locationText,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Hours and box number information
                      if (hours.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                      if (boxNumber.isNotEmpty)
                        _buildDetailRow(theme, Icons.inbox, 'Box Number', boxNumber),
                      
                      // Contact information
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                      if (email.isNotEmpty)
                        _buildDetailRow(theme, Icons.email, 'Email', email, canCopy: true),
                      if (fax.isNotEmpty)
                        _buildDetailRow(theme, Icons.fax, 'Fax', fax, canCopy: true),
                      if (whatsappNumber.isNotEmpty)
                        _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber, canCopy: true),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About this Daycare:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isEmailAvailable || isWhatsappAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isEmailAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.email,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchEmail(email),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                    if (isWhatsappAvailable)
                      IconButton(
                        icon: FaIcon(
                          FontAwesomeIcons.whatsapp,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchWhatsapp(whatsappNumber),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
