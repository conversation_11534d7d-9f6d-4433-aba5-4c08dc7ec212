import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:math_expressions/math_expressions.dart';
import 'dart:math' as math;

class GraphingCalculatorPage extends StatefulWidget {
  const GraphingCalculatorPage({Key? key}) : super(key: key);

  @override
  _GraphingCalculatorPageState createState() => _GraphingCalculatorPageState();
}

class _GraphingCalculatorPageState extends State<GraphingCalculatorPage> {
  String _function = '';
  List<FlSpot> _dataPoints = [];
  String? _errorMessage;

  List<FlSpot> _generateDataPoints(String function) {
    List<FlSpot> points = [];
    _errorMessage = null;

    if (function.isEmpty) {
      _errorMessage = "Please enter a function.";
      return [];
    }

    try {
      Parser p = Parser();

      // Preprocess the function:
      // 1. Remove any spaces.
      String processedFunction = function.replaceAll(' ', '');
      // 2. Insert an explicit multiplication sign between a number and a variable/function.
      processedFunction = processedFunction.replaceAllMapped(
        RegExp(r'(\d)([a-zA-Z])'),
        (match) => '${match.group(1)}*${match.group(2)}',
      );
      // 3. Insert multiplication between a number and an opening parenthesis.
      processedFunction = processedFunction.replaceAllMapped(
        RegExp(r'(\d)\('),
        (match) => '${match.group(1)}*(',
      );
      // 4. Replace "log(" with "ln(" to avoid parser issues.
      if (processedFunction.contains("log(")) {
        processedFunction = processedFunction.replaceAll("log(", "ln(");
      }

      // math_expressions expects '^' for exponentiation.
      Expression exp = p.parse(processedFunction);

      for (double x = -10; x <= 10; x += 0.25) {
        ContextModel cm = ContextModel();
        cm.bindVariable(Variable('x'), Number(x));
        // Bind common constants.
        cm.bindVariable(Variable('e'), Number(math.e));
        cm.bindVariable(Variable('pi'), Number(math.pi));

        double y = exp.evaluate(EvaluationType.REAL, cm);

        // Clipping y to a basic range to avoid extreme values.
        if (y.abs() < 100) {
          points.add(FlSpot(x, y));
        }
      }
      if (points.isEmpty) {
        _errorMessage =
            "No valid points to graph within the range. Check function or range.";
      }
    } catch (e) {
      String errorString = e.toString();
      List<String> parts = errorString.split(':');
      if (parts.isNotEmpty && parts[0].isNotEmpty) {
        _errorMessage = "Invalid function: ${parts[0].trim()}";
      } else {
        _errorMessage = "Invalid function: $errorString";
      }
      points = [];
      print("Error parsing function: $e");
    }
    return points;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Graphing Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    decoration: InputDecoration(
                      labelText:
                          'Enter Function (e.g., x^2, sin(x), sqrt(x), log(x))',
                      hintText: 'e.g., sin(x)*log(abs(x))',
                      border: const OutlineInputBorder(),
                      labelStyle:
                          TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: theme.colorScheme.onSurfaceVariant),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: theme.colorScheme.onSurface),
                      ),
                      errorText: _errorMessage == "Please enter a function."
                          ? null
                          : null,
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _function = value;
                        _errorMessage = null;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      setState(() {
                        _dataPoints = _generateDataPoints(_function);
                      });
                    },
                    child: const Text('Graph Function'),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    height: 300,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: theme.colorScheme.outline.withOpacity(0.5)),
                    ),
                    child: _errorMessage != null
                        ? Center(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        : _dataPoints.isNotEmpty
                            ? Builder(builder: (context) {
                                // Calculate dynamic boundaries from data points.
                                final dynamicMinX = _dataPoints
                                    .map((e) => e.x)
                                    .reduce(math.min);
                                final dynamicMaxX = _dataPoints
                                    .map((e) => e.x)
                                    .reduce(math.max);
                                final dynamicMinY = _dataPoints
                                    .map((e) => e.y)
                                    .reduce(math.min);
                                final dynamicMaxY = _dataPoints
                                    .map((e) => e.y)
                                    .reduce(math.max);

                                // If the range is zero, add a default margin.
                                double finalMinX = dynamicMinX;
                                double finalMaxX = dynamicMaxX;
                                if (finalMaxX - finalMinX == 0) {
                                  finalMinX -= 1;
                                  finalMaxX += 1;
                                }

                                double finalMinY = dynamicMinY;
                                double finalMaxY = dynamicMaxY;
                                if (finalMaxY - finalMinY == 0) {
                                  finalMinY -= 1;
                                  finalMaxY += 1;
                                }

                                // Add a 10% margin.
                                final xMargin = (finalMaxX - finalMinX) * 0.1;
                                final yMargin = (finalMaxY - finalMinY) * 0.1;
                                final minX = finalMinX - xMargin;
                                final maxX = finalMaxX + xMargin;
                                final minY = finalMinY - yMargin;
                                final maxY = finalMaxY + yMargin;

                                // Calculate intervals for axis titles.
                                final xInterval = (maxX - minX) / 5;
                                final yInterval = (maxY - minY) / 5;

                                return LineChart(
                                  LineChartData(
                                    lineBarsData: [
                                      LineChartBarData(
                                        spots: _dataPoints,
                                        isCurved: true,
                                        color: theme.colorScheme.primary,
                                        barWidth: 3,
                                        belowBarData: BarAreaData(show: false),
                                      ),
                                    ],
                                    minX: minX,
                                    maxX: maxX,
                                    minY: minY,
                                    maxY: maxY,
                                    titlesData: FlTitlesData(
                                      show: true,
                                      bottomTitles: AxisTitles(
                                        sideTitles: SideTitles(
                                          showTitles: true,
                                          reservedSize: 30,
                                          interval: xInterval,
                                          getTitlesWidget: (value, meta) => Text(
                                            value.toStringAsFixed(1),
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: theme.colorScheme
                                                  .onSurfaceVariant,
                                            ),
                                          ),
                                        ),
                                      ),
                                      leftTitles: AxisTitles(
                                        sideTitles: SideTitles(
                                          showTitles: true,
                                          reservedSize: 40,
                                          interval: yInterval,
                                          getTitlesWidget: (value, meta) => Text(
                                            value.toStringAsFixed(1),
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: theme.colorScheme
                                                  .onSurfaceVariant,
                                            ),
                                          ),
                                        ),
                                      ),
                                      topTitles: AxisTitles(
                                          sideTitles:
                                              SideTitles(showTitles: false)),
                                      rightTitles: AxisTitles(
                                          sideTitles:
                                              SideTitles(showTitles: false)),
                                    ),
                                    gridData: FlGridData(
                                      show: true,
                                      getDrawingHorizontalLine: (value) => FlLine(
                                        color: theme.colorScheme.outline
                                            .withOpacity(0.2),
                                        strokeWidth: 0.8,
                                      ),
                                      getDrawingVerticalLine: (value) => FlLine(
                                        color: theme.colorScheme.outline
                                            .withOpacity(0.2),
                                        strokeWidth: 0.8,
                                      ),
                                    ),
                                    borderData: FlBorderData(
                                      show: true,
                                      border: Border.all(
                                        color: theme.colorScheme.outline
                                            .withOpacity(0.5),
                                        width: 1,
                                      ),
                                    ),
                                    lineTouchData: LineTouchData(
                                      touchTooltipData: LineTouchTooltipData(
                                        tooltipBgColor:
                                            theme.colorScheme.surfaceVariant,
                                        getTooltipItems: (touchedSpots) {
                                          return touchedSpots.map((spot) {
                                            final textStyle = TextStyle(
                                              color: theme.colorScheme
                                                  .onSurfaceVariant,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14,
                                            );
                                            return LineTooltipItem(
                                              '(${spot.x.toStringAsFixed(2)}, ${spot.y.toStringAsFixed(2)})',
                                              textStyle,
                                            );
                                          }).toList();
                                        },
                                      ),
                                    ),
                                  ),
                                );
                              })
                            : Center(
                                child: Text(
                                  'Enter a function and press "Graph Function"',
                                  style: TextStyle(
                                      color:
                                          theme.colorScheme.onSurfaceVariant),
                                ),
                              ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
