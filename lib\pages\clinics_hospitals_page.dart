import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'login_page.dart';
import 'clinic_hospital_detail_page.dart';
import 'tertiary_health_page.dart';

class ClinicsHospitalsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ClinicsHospitalsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ClinicsHospitalsPage> createState() => _ClinicsHospitalsPageState();
}

class _ClinicsHospitalsPageState extends State<ClinicsHospitalsPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<ClinicOrHospital> _clinicsHospitals = [];
  List<ClinicOrHospital> _filteredClinicsHospitals = [];
  TextEditingController _searchController = TextEditingController();
  String _selectedBuilding = 'All';
  List<String> _buildings = ['All'];
  bool _showMap = false;
  LatLng? _mapCenter;

  @override
  void initState() {
    super.initState();
    _fetchClinicsHospitals();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchClinicsHospitals() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_clinicsorhospitals';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final List<ClinicOrHospital> clinicsHospitals = List<Map<String, dynamic>>.from(response)
          .map((json) => ClinicOrHospital.fromJson(json))
          .toList();
      
      // Extract unique buildings for filters
      final Set<String> buildings = {'All'};
      for (var clinic in clinicsHospitals) {
        if (clinic.building.isNotEmpty) {
          buildings.add(clinic.building);
        }
      }

      // Find map center (use first clinic with location or default to a central point)
      LatLng? mapCenter;
      for (var clinic in clinicsHospitals) {
        if (clinic.hasLocation()) {
          mapCenter = LatLng(clinic.latitude!, clinic.longitude!);
          break;
        }
      }

      setState(() {
        _clinicsHospitals = clinicsHospitals;
        _filteredClinicsHospitals = List.from(clinicsHospitals);
        _buildings = buildings.toList();
        _mapCenter = mapCenter;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading clinics and hospitals: $e';
      });
      print('Error fetching clinics and hospitals: $e');
    }
  }

  void _filterClinicsHospitals() {
    final String searchQuery = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredClinicsHospitals = _clinicsHospitals.where((clinic) {
        // Filter by building
        if (_selectedBuilding != 'All' && clinic.building != _selectedBuilding) {
          return false;
        }
        
        // Filter by search query
        if (searchQuery.isNotEmpty) {
          return clinic.fullname.toLowerCase().contains(searchQuery) ||
                 clinic.about.toLowerCase().contains(searchQuery) ||
                 clinic.building.toLowerCase().contains(searchQuery) ||
                 clinic.room.toLowerCase().contains(searchQuery) ||
                 clinic.address.toLowerCase().contains(searchQuery);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Clinics & Hospitals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showMap ? Icons.list : Icons.map,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _showMap = !_showMap;
              });
            },
            tooltip: _showMap ? 'Show List' : 'Show Map',
          ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchClinicsHospitals,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search clinics and hospitals...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _filterClinicsHospitals();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    _filterClinicsHospitals();
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Building filter
                if (_buildings.length > 1) ...[
                  Text(
                    'Building:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _buildings.map((building) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(building),
                            selected: _selectedBuilding == building,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedBuilding = building;
                                  _filterClinicsHospitals();
                                });
                              }
                            },
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: Colors.white,
                            labelStyle: TextStyle(
                              color: _selectedBuilding == building
                                  ? Colors.black
                                  : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                            side: BorderSide(
                              color: _selectedBuilding == building
                                  ? Colors.black
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Clinics and hospitals list or map
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchClinicsHospitals,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredClinicsHospitals.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.local_hospital,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _clinicsHospitals.isEmpty
                                      ? 'No clinics or hospitals available'
                                      : 'No clinics or hospitals match your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _showMap && _mapCenter != null
                            ? _buildMap()
                            : _buildList(),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildList() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    return ListView.builder(
      itemCount: _filteredClinicsHospitals.length,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemBuilder: (context, index) {
        final clinic = _filteredClinicsHospitals[index];
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12.0),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16.0),
            leading: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
              child: Icon(
                Icons.local_hospital,
                color: theme.colorScheme.primary,
              ),
            ),
            title: Text(
              clinic.fullname,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: theme.colorScheme.onSurface,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                if (clinic.address.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, size: 16, color: theme.colorScheme.primary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(clinic.address),
                        ),
                      ],
                    ),
                  ),
                if (clinic.phone.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: theme.colorScheme.primary),
                        const SizedBox(width: 4),
                        Text(clinic.phone),
                      ],
                    ),
                  ),
                if (clinic.daysnhours.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Icon(Icons.access_time, size: 16, color: theme.colorScheme.primary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(clinic.daysnhours),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ClinicHospitalDetailPage(
                    clinic: clinic,
                    institutionName: widget.institutionName,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildMap() {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Filter clinics with valid locations
    final clinicsWithLocation = _filteredClinicsHospitals.where((clinic) => clinic.hasLocation()).toList();
    
    if (clinicsWithLocation.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No clinics or hospitals with location data available',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _showMap = false;
                });
              },
              child: const Text('Show List View'),
            ),
          ],
        ),
      );
    }
    
    return FlutterMap(
      options: MapOptions(
        initialCenter: _mapCenter!,
        initialZoom: 16.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.harmonizr.app',
        ),
        MarkerLayer(
          markers: clinicsWithLocation.map((clinic) {
            return Marker(
              point: LatLng(clinic.latitude!, clinic.longitude!),
              width: 40,
              height: 40,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ClinicHospitalDetailPage(
                        clinic: clinic,
                        institutionName: widget.institutionName,
                        isDarkMode: currentIsDarkMode,
                        toggleTheme: widget.toggleTheme,
                      ),
                    ),
                  );
                },
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.local_hospital,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      child: Text(
                        clinic.fullname,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
