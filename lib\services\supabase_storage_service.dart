import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'secure_storage_service.dart';

class SavedContent {
  final String id;
  final String title;
  final String content;
  final String contentType;
  final DateTime createdAt;
  final String? courseId;

  SavedContent({
    required this.id,
    required this.title,
    required this.content,
    required this.contentType,
    required this.createdAt,
    this.courseId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'content_type': contentType,
      'created_at': createdAt.toIso8601String(),
      'course_id': courseId,
    };
  }

  factory SavedContent.fromJson(Map<String, dynamic> json) {
    return SavedContent(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      contentType: json['content_type'],
      createdAt: DateTime.parse(json['created_at']),
      courseId: json['course_id'],
    );
  }
}

class Course {
  final String id;
  final String name;
  final DateTime createdAt;
  final int sortOrder;

  Course({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.sortOrder,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'created_at': createdAt.toIso8601String(),
      'sort_order': sortOrder,
    };
  }

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'],
      name: json['name'],
      createdAt: DateTime.parse(json['created_at']),
      sortOrder: json['sort_order'],
    );
  }
}

class SupabaseStorageService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Storage bucket names
  static const String DOCUMENTS_BUCKET = 'documents';
  static const String IMAGES_BUCKET = 'images';
  static const String AUDIO_BUCKET = 'audio';
  static const String DATA_BUCKET = 'data';
  static const String CHARTS_BUCKET = 'charts';

  // Initialize storage buckets if they don't exist
  Future<void> initializeStorageBuckets() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // List of buckets to create
      final buckets = [
        DOCUMENTS_BUCKET,
        IMAGES_BUCKET,
        AUDIO_BUCKET,
        DATA_BUCKET,
        CHARTS_BUCKET,
      ];

      // Get existing buckets
      final existingBuckets = await _supabase.storage.listBuckets();
      final existingBucketNames = existingBuckets.map((bucket) => bucket.name).toList();

      // Create buckets that don't exist
      for (final bucketName in buckets) {
        if (!existingBucketNames.contains(bucketName)) {
          try {
            await _supabase.storage.createBucket(
              bucketName,
              const BucketOptions(
                public: false,
                fileSizeLimit: '50MB', // 50MB limit
              ),
            );
            debugPrint('Created bucket: $bucketName');
          } catch (e) {
            debugPrint('Error creating bucket $bucketName: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error initializing storage buckets: $e');
    }
  }

  // Upload a file to Supabase storage
  Future<String?> uploadFile({
    required Uint8List fileBytes,
    required String fileName,
    required String contentType,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Ensure buckets are initialized
      await initializeStorageBuckets();

      // Determine which bucket to use based on content type
      String bucketName;
      if (contentType.startsWith('image/')) {
        bucketName = IMAGES_BUCKET;
      } else if (contentType.startsWith('audio/')) {
        bucketName = AUDIO_BUCKET;
      } else if (contentType == 'application/vnd.ms-excel' ||
                contentType == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                contentType == 'text/csv') {
        bucketName = DATA_BUCKET;
      } else {
        bucketName = DOCUMENTS_BUCKET;
      }

      // Generate a unique file path
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '$userId/$timestamp-$fileName';

      // Upload the file
      await _supabase.storage
          .from(bucketName)
          .uploadBinary(
            filePath,
            fileBytes,
            fileOptions: FileOptions(
              contentType: contentType,
              upsert: true,
            ),
          );

      // Get the public URL
      final fileUrl = _supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

      return fileUrl;
    } catch (e) {
      debugPrint('Error uploading file: $e');
      return null;
    }
  }

  // Upload a chart image to Supabase storage
  Future<String?> uploadChartImage({
    required Uint8List imageBytes,
    required String title,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Ensure buckets are initialized
      await initializeStorageBuckets();

      // Generate a unique file path
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final sanitizedTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(RegExp(r'\s+'), '-');
      final filePath = '$userId/$timestamp-$sanitizedTitle.png';

      // Upload the chart image
      await _supabase.storage
          .from(CHARTS_BUCKET)
          .uploadBinary(
            filePath,
            imageBytes,
            fileOptions: FileOptions(
              contentType: 'image/png',
              upsert: true,
            ),
          );

      // Get the public URL
      final fileUrl = _supabase.storage
          .from(CHARTS_BUCKET)
          .getPublicUrl(filePath);

      return fileUrl;
    } catch (e) {
      debugPrint('Error uploading chart image: $e');
      return null;
    }
  }

  // Save content to Supabase and locally (encrypted)
  Future<SavedContent> saveContent({
    required String title,
    required String content,
    required String contentType,
    String? courseId,
    bool saveOffline = true,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final id = DateTime.now().millisecondsSinceEpoch.toString();
      final savedContent = SavedContent(
        id: id,
        title: title,
        content: content,
        contentType: contentType,
        createdAt: DateTime.now(),
        courseId: courseId,
      );

      // Save to Supabase
      final response = await _supabase
          .from('saved_contents')
          .insert(savedContent.toJson())
          .select()
          .single();

      // Save offline (encrypted) if requested
      if (saveOffline) {
        final secureStorage = SecureStorageService();
        await secureStorage.saveContentOffline(
          id,
          title,
          content,
          contentType,
        );
      }

      return SavedContent.fromJson(response);
    } catch (e) {
      debugPrint('Error saving content: $e');
      rethrow;
    }
  }

  // Get all saved content for the current user (both online and offline)
  Future<List<SavedContent>> getSavedContents() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get online content from Supabase
      final response = await _supabase
          .from('saved_contents')
          .select()
          .order('created_at', ascending: false);

      final onlineContents = response.map<SavedContent>((json) => SavedContent.fromJson(json)).toList();

      // Get offline content
      final secureStorage = SecureStorageService();
      final offlineMetadata = await secureStorage.listOfflineContent();

      // Create SavedContent objects from offline metadata
      final offlineContents = <SavedContent>[];
      for (final metadata in offlineMetadata) {
        final content = await secureStorage.getOfflineContent(metadata['id']);
        if (content != null) {
          offlineContents.add(SavedContent(
            id: metadata['id'],
            title: metadata['title'],
            content: content,
            contentType: metadata['contentType'],
            createdAt: DateTime.parse(metadata['createdAt']),
          ));
        }
      }

      // Combine online and offline content, removing duplicates
      final allContents = [...onlineContents];

      // Add offline contents that don't exist online
      for (final offlineContent in offlineContents) {
        if (!allContents.any((content) => content.id == offlineContent.id)) {
          allContents.add(offlineContent);
        }
      }

      // Sort by creation date (newest first)
      allContents.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return allContents;
    } catch (e) {
      debugPrint('Error getting saved contents: $e');
      return [];
    }
  }

  // Get saved content by ID (checks both online and offline)
  Future<SavedContent?> getSavedContentById(String id) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      try {
        // First try to get from Supabase
        final response = await _supabase
            .from('saved_contents')
            .select()
            .eq('id', id)
            .single();

        return SavedContent.fromJson(response);
      } catch (onlineError) {
        // If not found online, try offline
        debugPrint('Content not found online, checking offline: $onlineError');

        final secureStorage = SecureStorageService();
        final offlineContent = await secureStorage.getOfflineContent(id);

        if (offlineContent != null) {
          // Get metadata to create SavedContent object
          final offlineMetadata = await secureStorage.listOfflineContent();
          final metadata = offlineMetadata.firstWhere(
            (item) => item['id'] == id,
            orElse: () => {},
          );

          if (metadata.isNotEmpty) {
            return SavedContent(
              id: id,
              title: metadata['title'],
              content: offlineContent,
              contentType: metadata['contentType'],
              createdAt: DateTime.parse(metadata['createdAt']),
            );
          }
        }

        // Not found online or offline
        return null;
      }
    } catch (e) {
      debugPrint('Error getting saved content by ID: $e');
      return null;
    }
  }

  // Update saved content (both online and offline)
  Future<SavedContent?> updateSavedContent(SavedContent content) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      try {
        // Update online content
        final response = await _supabase
            .from('saved_contents')
            .update(content.toJson())
            .eq('id', content.id)
            .select()
            .single();

        // Also update offline content
        final secureStorage = SecureStorageService();
        await secureStorage.saveContentOffline(
          content.id,
          content.title,
          content.content,
          content.contentType,
        );

        return SavedContent.fromJson(response);
      } catch (onlineError) {
        // If online update fails, just update offline
        debugPrint('Online update failed, updating offline only: $onlineError');

        final secureStorage = SecureStorageService();
        final success = await secureStorage.saveContentOffline(
          content.id,
          content.title,
          content.content,
          content.contentType,
        );

        return success ? content : null;
      }
    } catch (e) {
      debugPrint('Error updating saved content: $e');
      return null;
    }
  }

  // Delete saved content (both online and offline)
  Future<void> deleteSavedContent(String id) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      try {
        // Delete from Supabase
        await _supabase
            .from('saved_contents')
            .delete()
            .eq('id', id);
      } catch (onlineError) {
        debugPrint('Online deletion failed: $onlineError');
        // Continue to delete offline even if online fails
      }

      // Also delete offline content
      final secureStorage = SecureStorageService();
      await secureStorage.deleteOfflineContent(id);

    } catch (e) {
      debugPrint('Error deleting saved content: $e');
      rethrow;
    }
  }

  // Create a new course
  Future<Course> createCourse(String name) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get the highest sort order
      final courses = await _supabase
          .from('courses')
          .select('sort_order')
          .order('sort_order', ascending: false)
          .limit(1);

      int sortOrder = 0;
      if (courses.isNotEmpty) {
        sortOrder = (courses[0]['sort_order'] ?? 0) + 1;
      }

      final id = DateTime.now().millisecondsSinceEpoch.toString();
      final course = Course(
        id: id,
        name: name,
        createdAt: DateTime.now(),
        sortOrder: sortOrder,
      );

      final response = await _supabase
          .from('courses')
          .insert(course.toJson())
          .select()
          .single();

      return Course.fromJson(response);
    } catch (e) {
      debugPrint('Error creating course: $e');
      rethrow;
    }
  }

  // Get all courses for the current user
  Future<List<Course>> getCourses() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('courses')
          .select()
          .order('sort_order', ascending: true);

      return response.map<Course>((json) => Course.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting courses: $e');
      return [];
    }
  }

  // Update course
  Future<Course?> updateCourse(Course course) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from('courses')
          .update(course.toJson())
          .eq('id', course.id)
          .select()
          .single();

      return Course.fromJson(response);
    } catch (e) {
      debugPrint('Error updating course: $e');
      return null;
    }
  }

  // Delete course
  Future<void> deleteCourse(String id) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // First, update all content items to remove the course_id
      await _supabase
          .from('saved_contents')
          .update({'course_id': null})
          .eq('course_id', id);

      // Then delete the course
      await _supabase
          .from('courses')
          .delete()
          .eq('id', id);
    } catch (e) {
      debugPrint('Error deleting course: $e');
      rethrow;
    }
  }

  // Update course sort orders
  Future<void> updateCourseSortOrders(List<Course> courses) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Update each course with its new sort order
      for (int i = 0; i < courses.length; i++) {
        final course = courses[i];
        await _supabase
            .from('courses')
            .update({'sort_order': i})
            .eq('id', course.id);
      }
    } catch (e) {
      debugPrint('Error updating course sort orders: $e');
      rethrow;
    }
  }
}
