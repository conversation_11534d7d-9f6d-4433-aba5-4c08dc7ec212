import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'meal_plan_detail_page.dart';
import 'login_page.dart';

class MealPlansPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedMealPlans;
  final bool isFromDetailPage;

  const MealPlansPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedMealPlans,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MealPlansPageState createState() => _MealPlansPageState();
}

class _MealPlansPageState extends State<MealPlansPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('meal_plans_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _mealPlans = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("MealPlansPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedMealPlans != null &&
        widget.preloadedMealPlans!.isNotEmpty) {
      setState(() {
        _mealPlans = List.from(widget.preloadedMealPlans!);
        _isLoading = false;
      });
    } else {
      _loadMealPlansFromDatabase();
    }
  }

  void _setupRealtime() {
    final mealPlansTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_mealplans';
    _realtimeChannel = Supabase.instance.client
        .channel('meal_plans_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: mealPlansTableName,
      callback: (payload) async {
        print("Realtime update received for meal plans: ${payload.eventType}");
        _loadMealPlansFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadMealPlansFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _mealPlans = [];
    });

    await _loadMoreMealPlans();
  }

  Future<void> _loadMoreMealPlans() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final mealPlansTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_mealplans';
      final response = await Supabase.instance.client
          .from(mealPlansTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _mealPlans.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading meal plans: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading meal plans: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreMealPlans();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Meal Plans',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('meal_plans_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _mealPlans.isEmpty && !_isLoading) {
            _loadMealPlansFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadMealPlansFromDatabase,
          child: _mealPlans.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No meal plans found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _mealPlans.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _mealPlans.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildMealPlanCard(
                      _mealPlans[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildMealPlanCard(
    Map<String, dynamic> mealPlan,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = mealPlan['fullname'] ?? 'Unknown';
    final String about = mealPlan['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MealPlanDetailPage(
                mealPlan: mealPlan,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.restaurant_menu,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
