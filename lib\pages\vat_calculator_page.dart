import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VatCalculatorPage extends StatefulWidget {
  const VatCalculatorPage({Key? key}) : super(key: key);

  @override
  _VatCalculatorPageState createState() => _VatCalculatorPageState();
}

class _VatCalculatorPageState extends State<VatCalculatorPage> {
  double _netPrice = 0.0;
  double _vatRate = 0.0;
  String _vatAmount = '';
  String _grossPrice = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'VAT/Tax Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Net Price (Excluding VAT/Tax)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _netPrice = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'VAT/Tax Rate (%)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _vatRate = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateVat();
                    },
                    child: const Text('Calculate VAT/Tax'),
                  ),
                  const SizedBox(height: 20),
                  Text('VAT/Tax Amount: $_vatAmount', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Gross Price (Including VAT/Tax): $_grossPrice', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateVat() {
    double vatAmountValue = (_vatRate / 100) * _netPrice;
    double grossPriceValue = _netPrice + vatAmountValue;

    setState(() {
      _vatAmount = '\$${vatAmountValue.toStringAsFixed(2)}';
      _grossPrice = '\$${grossPriceValue.toStringAsFixed(2)}';
    });
  }
}