import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheManager {
  // Cache data with a timestamp
  static Future<void> cacheData(String key, List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'data': data,
      };
      await prefs.setString(key, jsonEncode(cacheData));
      print('Data cached for $key: ${data.length} items');
    } catch (e) {
      print('Error caching data for $key: $e');
    }
  }

  // Get cached data
  static Future<List<Map<String, dynamic>>?> getCachedData(String key, {int maxAgeMinutes = 60}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(key);
      
      if (cachedJson == null) {
        return null;
      }
      
      final cachedData = jsonDecode(cachedJson);
      final timestamp = cachedData['timestamp'] as int;
      final data = List<Map<String, dynamic>>.from(cachedData['data']);
      
      // Check if cache is still valid (not older than maxAgeMinutes)
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      final maxAgeMillis = maxAgeMinutes * 60 * 1000;
      
      if (cacheAge > maxAgeMillis) {
        print('Cache for $key is expired (${cacheAge ~/ 60000} minutes old)');
        return data; // Still return data but mark as expired
      }
      
      print('Using cached data for $key: ${data.length} items');
      return data;
    } catch (e) {
      print('Error retrieving cached data for $key: $e');
      return null;
    }
  }

  // Check if cache is expired
  static Future<bool> isCacheExpired(String key, {int maxAgeMinutes = 60}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(key);
      
      if (cachedJson == null) {
        return true;
      }
      
      final cachedData = jsonDecode(cachedJson);
      final timestamp = cachedData['timestamp'] as int;
      
      // Check if cache is still valid (not older than maxAgeMinutes)
      final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      final maxAgeMillis = maxAgeMinutes * 60 * 1000;
      
      return cacheAge > maxAgeMillis;
    } catch (e) {
      print('Error checking cache expiration for $key: $e');
      return true;
    }
  }

  // Clear specific cache
  static Future<void> clearCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
      print('Cache cleared for $key');
    } catch (e) {
      print('Error clearing cache for $key: $e');
    }
  }
}
