import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class LinksDetailPage extends StatefulWidget {
  final Map<String, dynamic> link;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const LinksDetailPage({
    Key? key,
    required this.link,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<LinksDetailPage> createState() => _LinksDetailPageState();
}

class _LinksDetailPageState extends State<LinksDetailPage> {
  late RealtimeChannel _linkRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupLinkRealtimeListener();
  }

  @override
  void dispose() {
    _linkRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupLinkRealtimeListener() {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    _linkRealtimeChannel = Supabase.instance.client
        .channel('link_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: table,
      callback: (payload) {
        if (payload.newRecord['id'] == widget.link['id']) {
          _fetchUpdatedLinkData();
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedLinkData() async {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    try {
      final updated = await Supabase.instance.client
          .from(table)
          .select('*')
          .eq('id', widget.link['id'])
          .single();
      if (mounted && updated != null) {
        setState(() {
          widget.link
            ..clear()
            ..addAll(Map<String, dynamic>.from(updated));
        });
        _updateLinksCache(updated);
      }
    } catch (e) {
      print('Error fetching updated link data: $e');
    }
  }

  Future<void> _updateLinksCache(Map<String, dynamic> updated) async {
    final prefs = await SharedPreferences.getInstance();
    final key =
        'links_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    final jsonStr = prefs.getString(key);
    if (jsonStr != null) {
      final list = (jsonDecode(jsonStr) as List)
          .cast<Map<String, dynamic>>();
      for (var i = 0; i < list.length; i++) {
        if (list[i]['id'] == updated['id']) {
          list[i] = updated;
          break;
        }
      }
      await prefs.setString(key, jsonEncode(list));
    }
  }

  Future<void> _launchResourceLink() async {
    final urlStr = widget.link['link'] as String? ?? '';
    if (urlStr.isEmpty) {
      _showSnackbar('No resource link available.');
      return;
    }
    final uri = Uri.parse(urlStr);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      _showSnackbar('Could not launch resource link.');
    }
  }

  void _showSnackbar(String msg) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(msg)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final link = widget.link;

    final title = link['fullname'] as String? ?? '';
    final about = link['about'] as String? ?? '';
    final urlStr = link['link'] as String? ?? '';
    final hasUrl = urlStr.isNotEmpty;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon:
              Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(children: [
          // Detail Card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              color: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Icon(Icons.link_rounded,
                        size: 64,
                        color: isDark ? Colors.white : Colors.black),
                    const SizedBox(height: 16),
                    Text(
                      title.isNotEmpty ? title : 'Unnamed Link',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (about.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildDetailRow(
                          theme, Icons.info_outline, 'About', about),
                    ],
                    if (hasUrl) ...[
                      const SizedBox(height: 16),
                      _buildDetailRow(theme, Icons.link, 'URL', urlStr),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ]),
      ),

      // *** Updated Footer: single link icon ***
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: IconButton(
            icon: Icon(
              Icons.link,
              size: 28,
              color: theme.colorScheme.onSurface,
            ),
            tooltip: hasUrl ? 'Open resource link' : 'No link available',
            onPressed: hasUrl ? _launchResourceLink : null,
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
      ThemeData theme, IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon,
              color: theme.colorScheme.onSurfaceVariant, size: 20),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label,
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface)),
                const SizedBox(height: 4),
                Text(value,
                    style: TextStyle(
                        color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
