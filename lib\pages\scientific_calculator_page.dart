import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:math_expressions/math_expressions.dart';
import 'dart:math';

class ScientificCalculatorPage extends StatefulWidget {
  const ScientificCalculatorPage({Key? key}) : super(key: key);

  @override
  _ScientificCalculatorPageState createState() => _ScientificCalculatorPageState();
}

class _ScientificCalculatorPageState extends State<ScientificCalculatorPage> {
  String _expression = '';
  String _output = '0';

  // Returns true if the button is a single digit or a decimal point.
  bool _isDigit(String s) {
    return RegExp(r'^[0-9.]$').hasMatch(s);
  }

  void _buttonPressed(String buttonText) {
    setState(() {
      if (buttonText == '⌫') {
        if (_expression.isNotEmpty) {
          _expression = _expression.substring(0, _expression.length - 1);
        }
      } else if (buttonText == '=') {
        _evaluateExpression();
        // Replace the current expression with the result.
        _expression = _output;
      } else if (buttonText == 'C') {
        _expression = '';
        _output = '0';
      } else {
        _expression += buttonText;
      }
    });
  }

  /// Process factorial operators.
  /// This function looks for:
  /// • Bracketed factorials, e.g. (3+2)!, and evaluates the expression inside.
  /// • Simple number factorials, e.g. 5!
  String processFactorials(String expr) {
    // Process bracketed factorials e.g. "(3+2)!"
    RegExp bracketFact = RegExp(r'\(([^()]+)\)!'); 
    while (bracketFact.hasMatch(expr)) {
      expr = expr.replaceAllMapped(bracketFact, (match) {
        String inside = match.group(1)!;
        try {
          Parser p = Parser();
          Expression e = p.parse(inside);
          double val = e.evaluate(EvaluationType.REAL, ContextModel());
          if (val % 1 != 0) throw Exception("Non-integer factorial");
          int num = val.toInt();
          int fact = 1;
          for (int i = 1; i <= num; i++) {
            fact *= i;
          }
          return fact.toString();
        } catch (ex) {
          return 'Error';
        }
      });
    }
    // Process simple number factorials e.g. "5!"
    RegExp numberFact = RegExp(r'(\d+)!'); 
    while (numberFact.hasMatch(expr)) {
      expr = expr.replaceAllMapped(numberFact, (match) {
        int num = int.parse(match.group(1)!);
        if (num > 20) { // Arbitrary limit to prevent overflow
          return 'Overflow';
        }
        int fact = 1;
        for (int i = 1; i <= num; i++) {
          fact *= i;
          if (fact == double.infinity || fact == -double.infinity) {
            return 'Overflow';
          }
        }
        return fact.toString();
      });
    }
    return expr;
  }

  void _evaluateExpression() {
    if (_expression.isEmpty) {
      _output = '0';
      return;
    }
    try {
      String formattedExpression = _formatExpression(_expression);
      formattedExpression = processFactorials(formattedExpression);
      
      final Parser p = Parser();
      final Expression exp = p.parse(formattedExpression);
      final ContextModel cm = ContextModel()..bindVariableName('pi', Number(pi))..bindVariableName('e', Number(e));
      final result = exp.evaluate(EvaluationType.REAL, cm);
      _output = (result % 1 == 0 ? result.toInt() : result).toStringAsFixed(10); // More precise output
    } catch (e) {
      if (e.toString().contains('Division by zero')) {
        _output = 'Division by Zero';
      } else if (e.toString().contains('Invalid expression')) {
        _output = 'Invalid Expression';
      } else {
        _output = 'Error';
      }
      print('Error evaluating expression: $e');
    }
  }

  String _formatExpression(String expr) {
    return expr
        .replaceAll('sin(', 'sin(')
        .replaceAll('cos(', 'cos(')
        .replaceAll('tan(', 'tan(')
        .replaceAll('ln(', 'log(')
        .replaceAll('√(', 'sqrt(')
        .replaceAll('π', 'pi')
        .replaceAll('÷', '/')
        .replaceAll('x', '*');
  }

  /// A unified builder that adjusts the button style based on its content.
  Widget _buildCalcButton(String buttonText) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    bool isDigitButton = _isDigit(buttonText);
    // For digits, use a lighter background; for operators/scientific functions, use a uniform gray.
    Color buttonColor = isDigitButton
        ? (isDarkMode ? Colors.grey[850]! : Colors.white)
        : (isDarkMode ? Colors.grey[700]! : Colors.grey[300]!);
    Color foregroundColor = isDarkMode ? Colors.white : Colors.black;
    // Use a larger font for single-digit buttons.
    double fontSize = isDigitButton ? 20.0 : 16.0;
    // Adjust font size for longer labels.
    if (buttonText.length > 3) {
      fontSize = 14.0;
    }

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: ElevatedButton(
            onPressed: () => _buttonPressed(buttonText),
            style: ElevatedButton.styleFrom(
              backgroundColor: buttonColor,
              foregroundColor: foregroundColor,
              padding: const EdgeInsets.all(18.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
                side: BorderSide(
                    color: isDarkMode
                        ? Colors.grey[700]!
                        : Colors.grey[400]!),
              ),
            ),
            child: Center(
              child: Text(
                buttonText,
                semanticsLabel: buttonText == 'x' ? 'Multiply' : buttonText,
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: foregroundColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Scientific Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            // Display area for the expression and result.
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                alignment: Alignment.bottomRight,
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical, // Allow vertical scrolling for long expressions
                  reverse: true,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _expression,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          fontSize: 18.0, // Reduced font size for long expressions
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ),
                      Text(
                        _output,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          fontSize: 40.0,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
            // Button grid area.
            Expanded(
              flex: 3,
              child: Column(
                children: [
                  // Row 1: Scientific functions.
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('sin('),
                        _buildCalcButton('cos('),
                        _buildCalcButton('tan('),
                        _buildCalcButton('log('),
                        _buildCalcButton('ln('),
                      ],
                    ),
                  ),
                  // Row 2: More scientific operators.
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('√('),
                        _buildCalcButton('^'),
                        _buildCalcButton('!'),
                        _buildCalcButton('π'),
                        _buildCalcButton('e'),
                      ],
                    ),
                  ),
                  // Row 3: Digits and basic operators.
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('7'),
                        _buildCalcButton('8'),
                        _buildCalcButton('9'),
                        _buildCalcButton('÷'),
                        _buildCalcButton('('),
                      ],
                    ),
                  ),
                  // Row 4:
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('4'),
                        _buildCalcButton('5'),
                        _buildCalcButton('6'),
                        _buildCalcButton('x'),
                        _buildCalcButton(')'),
                      ],
                    ),
                  ),
                  // Row 5:
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('1'),
                        _buildCalcButton('2'),
                        _buildCalcButton('3'),
                        _buildCalcButton('-'),
                        _buildCalcButton('C'),
                      ],
                    ),
                  ),
                  // Row 6:
                  Expanded(
                    child: Row(
                      children: [
                        _buildCalcButton('0'),
                        _buildCalcButton('.'),
                        _buildCalcButton('⌫'),
                        _buildCalcButton('+'),
                        _buildCalcButton('='),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}