// startupfunds_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'startupfund_detail_page.dart';

class StartupFundsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedStartupFunds;
  final bool isFromDetailPage;

  const StartupFundsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedStartupFunds,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<StartupFundsPage> createState() => _StartupFundsPageState();
}

class _StartupFundsPageState extends State<StartupFundsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('startupfunds_list');
  List<Map<String, dynamic>> _startupFunds = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("StartupFundsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant StartupFundsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("StartupFundsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("StartupFundsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("StartupFundsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedStartupFunds != null && widget.preloadedStartupFunds!.isNotEmpty) {
      print("Preloaded startup funds found, using them.");
      setState(() {
        _startupFunds = List<Map<String, dynamic>>.from(widget.preloadedStartupFunds!);
        _startupFunds.forEach((fund) {
          fund['_isImageLoading'] = false;
        });
        _startupFunds.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedStartupFunds!.length == _pageSize;
      });
      _loadStartupFundsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded startup funds or empty list, loading from database.");
      _loadStartupFundsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadStartupFundsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadStartupFundsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final startupFundsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startupfunds';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(startupFundsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedStartupFunds =
          await _updateStartupFundImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _startupFunds = updatedStartupFunds;
        } else {
          _startupFunds.addAll(updatedStartupFunds);
        }
        _startupFunds.forEach((fund) {
          fund['_isImageLoading'] = false;
        });
        _startupFunds.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the startup funds
      _cacheStartupFunds(_startupFunds);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching startup funds: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateStartupFundImageUrls(
      List<Map<String, dynamic>> startupFunds) async {
    List<Future<void>> futures = [];
    for (final fund in startupFunds) {
      if (fund['image_url'] == null ||
          fund['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(fund));
      }
    }
    await Future.wait(futures);
    return startupFunds;
  }

  Future<void> _cacheStartupFunds(List<Map<String, dynamic>> startupFunds) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String startupFundsJson = jsonEncode(startupFunds);
      await prefs.setString(
          'startupfunds_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          startupFundsJson);
    } catch (e) {
      print('Error caching startup funds: $e');
    }
  }
  
  void _setupRealtime() {
    final startupFundsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startupfunds';
    _realtimeChannel = Supabase.instance.client
        .channel('startupfunds')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: startupFundsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newFundId = payload.newRecord['id'];
          final newFundResponse = await Supabase.instance.client
              .from(startupFundsTableName)
              .select('*')
              .eq('id', newFundId)
              .single();
          if (mounted) {
            Map<String, dynamic> newFund = Map.from(newFundResponse);
            final updatedFund = await _updateStartupFundImageUrls([newFund]);
            setState(() {
              _startupFunds = [..._startupFunds, updatedFund.first];
              updatedFund.first['_isImageLoading'] = false;
              _startupFunds.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedFundId = payload.newRecord['id'];
          final updatedFundResponse = await Supabase.instance.client
              .from(startupFundsTableName)
              .select('*')
              .eq('id', updatedFundId)
              .single();
          if (mounted) {
            final updatedFund = Map<String, dynamic>.from(updatedFundResponse);
            setState(() {
              _startupFunds = _startupFunds.map((fund) {
                return fund['id'] == updatedFund['id'] ? updatedFund : fund;
              }).toList();
              _startupFunds.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedFundId = payload.oldRecord['id'];
          setState(() {
            _startupFunds.removeWhere((fund) => fund['id'] == deletedFundId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreStartupFunds();
    }
  }

  Future<void> _loadMoreStartupFunds() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadStartupFundsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> fund) async {
    if (fund['_isImageLoading'] == true) {
      print('Image loading already in progress for ${fund['fullname']}, skipping.');
      return;
    }
    if (fund['image_url'] != null &&
        fund['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${fund['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      fund['_isImageLoading'] = true;
    });

    final fullname = fund['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeStartupFundBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/startupfunds';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeStartupFundBucket');
    print('Image URL before fetch: ${fund['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeStartupFundBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeStartupFundBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        fund['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        fund['_isImageLoading'] = false;
        print('Setting image_url for ${fund['fullname']} to: ${fund['image_url']}');
      });
    } else {
      fund['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> fund) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StartupFundDetailPage(
            startupFund: fund,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("StartupFundsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Startup Funds',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _startupFunds.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadStartupFundsFromSupabase(initialLoad: true),
              child: _startupFunds.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No startup funds available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _startupFunds.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _startupFunds.length) {
                          final fund = _startupFunds[index];
                          final phone = fund['phone'] as String? ?? '';
                          final email = fund['email'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('fund_${fund['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (fund['image_url'] == null ||
                                      fund['image_url'] == 'assets/placeholder_image.png') &&
                                  !fund['_isImageLoading']) {
                                _fetchImageUrl(fund);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: fund['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      fund['fullname'] ?? 'Unnamed Fund',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        fund['about'] ?? '',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: theme.colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                    onTap: () => _navigateToDetail(context, fund),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
