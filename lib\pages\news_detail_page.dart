// news_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';

class NewsDetailPage extends StatefulWidget {
  final Map<String, dynamic> news;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const NewsDetailPage({
    Key? key,
    required this.news,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<NewsDetailPage> createState() => _NewsDetailPageState();
}

class _NewsDetailPageState extends State<NewsDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _newsRealtimeChannel; // Realtime channel for news updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupNewsRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _newsRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.news['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupNewsRealtimeListener() {
    final newsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_news';
    _newsRealtimeChannel = Supabase.instance.client
        .channel('news_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: newsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current news's ID
        if (payload.newRecord['id'] == widget.news['id']) {
          print("Realtime UPDATE event received for THIS news (manual filter applied): ${widget.news['fullname']}");
          _fetchUpdatedNewsData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER news, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedNewsData() async {
    final newsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_news';
    try {
      final updatedNewsResponse = await Supabase.instance.client
          .from(newsTableName)
          .select('*')
          .eq('id', widget.news['id'])
          .single();

      if (mounted && updatedNewsResponse != null) {
        Map<String, dynamic> updatedNews = Map.from(updatedNewsResponse);
        // Update the widget.news with the new data
        setState(() {
          widget.news.clear(); // Clear old data
          widget.news.addAll(updatedNews); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("News data updated in detail page for ${widget.news['fullname']}");
          _updateNewsCache(updatedNews); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated news data: $error");
    }
  }

  Future<void> _updateNewsCache(Map<String, dynamic> updatedNews) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'news_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedNewsJson = prefs.getString(cacheKey);

    if (cachedNewsJson != null) {
      List<Map<String, dynamic>> cachedNews = (jsonDecode(cachedNewsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the news in the cached list
      for (int i = 0; i < cachedNews.length; i++) {
        if (cachedNews[i]['id'] == updatedNews['id']) {
          cachedNews[i] = updatedNews;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedNews));
      print("News cache updated with realtime change for ${updatedNews['fullname']}");
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> newsItem) {
    final day = newsItem['day'] as int?;
    final month = newsItem['month'] as int?;
    final year = newsItem['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final link = widget.news['link'] as String? ?? '';
    final publisher = widget.news['publisher'] as String? ?? '';
    final dateStr = _formatDate(widget.news);

    final bool isLinkAvailable = link.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.news['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          if (isLinkAvailable)
            IconButton(
              icon: Icon(
                Icons.link,
                color: theme.colorScheme.primary,
              ),
              onPressed: () => _launchURL(link),
              tooltip: 'Visit website',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.news['fullname'] ?? '',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (dateStr.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      dateStr,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.secondary,
                      ),
                    ),
                  ],
                  if (publisher.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Published by: $publisher',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.secondary,
                      ),
                    ),
                  ],
                  if (isLinkAvailable) ...[
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.link),
                      label: const Text('Read Full Article'),
                      onPressed: () => _launchURL(link),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
