# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.embedding.** { *; }

# If you're using any additional libraries or plugins, add their rules here.

-keep class com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
-keep class com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions$Builder

-keep class com.google.mlkit.vision.text.devanagari.DevanagariTextRecognizerOptions
-keep class com.google.mlkit.vision.text.devanagari.DevanagariTextRecognizerOptions$Builder

-keep class com.google.mlkit.vision.text.japanese.JapaneseTextRecognizerOptions
-keep class com.google.mlkit.vision.text.japanese.JapaneseTextRecognizerOptions$Builder

-keep class com.google.mlkit.vision.text.korean.KoreanTextRecognizerOptions
-keep class com.google.mlkit.vision.text.korean.KoreanTextRecognizerOptions$Builder




# -------------------------------
# ML Kit Text Recognition Rules
# -------------------------------
# Since you're not using the language-specific ML Kit text recognition options,
# we suppress warnings for their missing classes.

-dontwarn com.google.mlkit.vision.text.chinese.**
-dontwarn com.google.mlkit.vision.text.devanagari.**
-dontwarn com.google.mlkit.vision.text.japanese.**
-dontwarn com.google.mlkit.vision.text.korean.**


-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.SplitInstallException
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManager
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManagerFactory
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest$Builder
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest
-dontwarn com.google.android.play.core.splitinstall.SplitInstallSessionState
-dontwarn com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task

# Optionally, you can add other keep rules for libraries you're actively using.
