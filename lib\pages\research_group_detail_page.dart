// research_group_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class ResearchGroupDetailPage extends StatefulWidget {
  final Map<String, dynamic> researchGroup;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const ResearchGroupDetailPage({
    Key? key,
    required this.researchGroup,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<ResearchGroupDetailPage> createState() => _ResearchGroupDetailPageState();
}

class _ResearchGroupDetailPageState extends State<ResearchGroupDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _researchGroupRealtimeChannel; // Realtime channel for research group updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupResearchGroupRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _researchGroupRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.researchGroup['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupResearchGroupRealtimeListener() {
    final researchGroupsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_researchgroups';
    _researchGroupRealtimeChannel = Supabase.instance.client
        .channel('researchgroup_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: researchGroupsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current research group's ID
        if (payload.newRecord['id'] == widget.researchGroup['id']) {
          print("Realtime UPDATE event received for THIS research group (manual filter applied): ${widget.researchGroup['fullname']}");
          _fetchUpdatedResearchGroupData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER research group, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedResearchGroupData() async {
    final researchGroupsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_researchgroups';
    try {
      final updatedResearchGroupResponse = await Supabase.instance.client
          .from(researchGroupsTableName)
          .select('*')
          .eq('id', widget.researchGroup['id'])
          .single();

      if (mounted && updatedResearchGroupResponse != null) {
        Map<String, dynamic> updatedResearchGroup = Map.from(updatedResearchGroupResponse);
        // Update the widget.researchGroup with the new data
        setState(() {
          widget.researchGroup.clear(); // Clear old data
          widget.researchGroup.addAll(updatedResearchGroup); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Research Group data updated in detail page for ${widget.researchGroup['fullname']}");
          _updateResearchGroupsCache(updatedResearchGroup); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated research group data: $error");
    }
  }

  Future<void> _updateResearchGroupsCache(Map<String, dynamic> updatedResearchGroup) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'researchgroups_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedResearchGroupsJson = prefs.getString(cacheKey);

    if (cachedResearchGroupsJson != null) {
      List<Map<String, dynamic>> cachedResearchGroups = (jsonDecode(cachedResearchGroupsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the research group in the cached list
      for (int i = 0; i < cachedResearchGroups.length; i++) {
        if (cachedResearchGroups[i]['id'] == updatedResearchGroup['id']) {
          cachedResearchGroups[i] = updatedResearchGroup;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedResearchGroups));
      print("Research Groups cache updated with realtime change for ${updatedResearchGroup['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.researchGroup['phone'] as String? ?? '';
    final email = widget.researchGroup['email'] as String? ?? '';
    final about = widget.researchGroup['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.researchGroup['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                  if (email.isNotEmpty)
                    _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                  if (phone.isNotEmpty)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
