import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';

class PodcastPlayer extends StatefulWidget {
  final Uint8List audioBytes;
  final String title;
  final String description;
  final bool isDarkMode;
  
  const PodcastPlayer({
    Key? key,
    required this.audioBytes,
    required this.title,
    required this.description,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  State<PodcastPlayer> createState() => _PodcastPlayerState();
}

class _PodcastPlayerState extends State<PodcastPlayer> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isLoading = true;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  String? _tempFilePath;
  double _playbackSpeed = 1.0;
  
  @override
  void initState() {
    super.initState();
    _initAudioPlayer();
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    _cleanupTempFile();
    super.dispose();
  }
  
  Future<void> _initAudioPlayer() async {
    try {
      // Create a temporary file from the audio bytes
      _tempFilePath = await _createTempFile(widget.audioBytes);
      
      if (_tempFilePath != null) {
        // Set the audio source
        await _audioPlayer.setFilePath(_tempFilePath!);
        
        // Get the duration
        _duration = _audioPlayer.duration ?? Duration.zero;
        
        // Listen to position changes
        _audioPlayer.positionStream.listen((position) {
          if (mounted) {
            setState(() {
              _position = position;
            });
          }
        });
        
        // Listen to player state changes
        _audioPlayer.playerStateStream.listen((playerState) {
          if (mounted) {
            setState(() {
              _isPlaying = playerState.playing;
              if (playerState.processingState == ProcessingState.completed) {
                _audioPlayer.seek(Duration.zero);
                _audioPlayer.pause();
              }
            });
          }
        });
        
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error initializing audio player: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<String?> _createTempFile(Uint8List bytes) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = path.join(tempDir.path, 'podcast_$timestamp.mp3');
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      return filePath;
    } catch (e) {
      print('Error creating temp file: $e');
      return null;
    }
  }
  
  Future<void> _cleanupTempFile() async {
    if (_tempFilePath != null) {
      try {
        final file = File(_tempFilePath!);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        print('Error deleting temp file: $e');
      }
    }
  }
  
  Future<void> _downloadAudio() async {
    if (kIsWeb) {
      // Web download logic would go here
      return;
    }
    
    try {
      // Check storage permission
      final status = await Permission.storage.request();
      if (status.isGranted) {
        // Get the downloads directory
        Directory? downloadsDir;
        if (Platform.isAndroid) {
          downloadsDir = Directory('/storage/emulated/0/Download');
        } else if (Platform.isIOS) {
          downloadsDir = await getApplicationDocumentsDirectory();
        } else {
          downloadsDir = await getDownloadsDirectory();
        }
        
        if (downloadsDir != null) {
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final sanitizedTitle = widget.title.replaceAll(RegExp(r'[^\w\s]+'), '_');
          final fileName = '${sanitizedTitle}_$timestamp.mp3';
          final filePath = path.join(downloadsDir.path, fileName);
          
          // Write the audio bytes to the file
          final file = File(filePath);
          await file.writeAsBytes(widget.audioBytes);
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Downloaded to: $filePath')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not access downloads directory')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Storage permission denied')),
        );
      }
    } catch (e) {
      print('Error downloading audio: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error downloading audio: $e')),
      );
    }
  }
  
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    return duration.inHours > 0 
        ? '$hours:$minutes:$seconds' 
        : '$minutes:$seconds';
  }
  
  @override
  Widget build(BuildContext context) {
    final textColor = widget.isDarkMode ? Colors.white : Colors.black;
    final backgroundColor = widget.isDarkMode ? Colors.grey[850] : Colors.grey[200];
    final progressBarColor = widget.isDarkMode ? Colors.white70 : Colors.black87;
    final thumbColor = widget.isDarkMode ? Colors.white : Colors.black;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Podcast title
          Text(
            widget.title,
            style: GoogleFonts.notoSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          
          // Podcast description
          Text(
            widget.description,
            style: GoogleFonts.notoSans(
              fontSize: 14,
              color: textColor.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 24),
          
          // Player controls
          if (_isLoading)
            Center(child: CircularProgressIndicator())
          else
            Column(
              children: [
                // Progress bar
                ProgressBar(
                  progress: _position,
                  total: _duration,
                  buffered: _duration,
                  progressBarColor: progressBarColor,
                  baseBarColor: progressBarColor.withOpacity(0.3),
                  bufferedBarColor: progressBarColor.withOpacity(0.5),
                  thumbColor: thumbColor,
                  barHeight: 4,
                  thumbRadius: 6,
                  timeLabelTextStyle: GoogleFonts.notoSans(
                    color: textColor,
                    fontSize: 12,
                  ),
                  onSeek: (duration) {
                    _audioPlayer.seek(duration);
                  },
                ),
                const SizedBox(height: 16),
                
                // Playback controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Rewind 10 seconds
                    IconButton(
                      icon: Icon(Icons.replay_10, color: textColor),
                      onPressed: () {
                        final newPosition = _position - Duration(seconds: 10);
                        _audioPlayer.seek(newPosition < Duration.zero ? Duration.zero : newPosition);
                      },
                    ),
                    
                    // Play/Pause
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: textColor,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: backgroundColor,
                          size: 32,
                        ),
                        onPressed: () {
                          if (_isPlaying) {
                            _audioPlayer.pause();
                          } else {
                            _audioPlayer.play();
                          }
                        },
                      ),
                    ),
                    
                    // Forward 30 seconds
                    IconButton(
                      icon: Icon(Icons.forward_30, color: textColor),
                      onPressed: () {
                        final newPosition = _position + Duration(seconds: 30);
                        _audioPlayer.seek(newPosition > _duration ? _duration : newPosition);
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Playback speed and download
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Playback speed
                    DropdownButton<double>(
                      value: _playbackSpeed,
                      dropdownColor: backgroundColor,
                      style: GoogleFonts.notoSans(color: textColor),
                      underline: Container(
                        height: 1,
                        color: textColor.withOpacity(0.5),
                      ),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _playbackSpeed = value;
                          });
                          _audioPlayer.setSpeed(value);
                        }
                      },
                      items: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0].map((speed) {
                        return DropdownMenuItem<double>(
                          value: speed,
                          child: Text('${speed}x'),
                        );
                      }).toList(),
                    ),
                    
                    // Download button
                    ElevatedButton.icon(
                      icon: Icon(Icons.download, color: textColor),
                      label: Text(
                        'Download',
                        style: GoogleFonts.notoSans(color: textColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: backgroundColor!.withOpacity(0.7),
                        side: BorderSide(color: textColor.withOpacity(0.5)),
                      ),
                      onPressed: _downloadAudio,
                    ),
                  ],
                ),
              ],
            ),
        ],
      ),
    );
  }
}
