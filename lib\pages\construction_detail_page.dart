import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
// Import for Map (if you add map functionality later)
// import 'package:flutter_map/flutter_map.dart';
// import 'package:latlong2/latlong.dart';

// Assuming LoginPage is still needed if you add a profile button to bottom nav here
// import 'login_page.dart'; 

class ConstructionDetailPage extends StatefulWidget {
  final Map<String, dynamic> constructionProject;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable; // Added for realtime table name

  const ConstructionDetailPage({
    Key? key,
    required this.constructionProject,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<ConstructionDetailPage> createState() => _ConstructionDetailPageState();
}

class _ConstructionDetailPageState extends State<ConstructionDetailPage> {
  late RealtimeChannel _constructionRealtimeChannel;
  late Map<String, dynamic> _currentProjectData; // Mutable copy

  String get _constructionTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_construction';

  @override
  void initState() {
    super.initState();
    _currentProjectData = Map<String, dynamic>.from(widget.constructionProject);
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _constructionRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _constructionRealtimeChannel = Supabase.instance.client
        .channel('public:$_constructionTableName:id=eq.${_currentProjectData['id']}') // More specific channel
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Only listen for updates to this specific item
      schema: 'public',
      table: _constructionTableName,
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'id',
        value: _currentProjectData['id'],
      ),
      callback: (payload) async {
        if (mounted && payload.newRecord['id'] == _currentProjectData['id']) {
          print("Realtime UPDATE event received for THIS construction project: ${_currentProjectData['fullname']}");
          _refreshConstructionProjectData();
        }
      },
    ).subscribe((status, [_]) {
        print('Construction Detail Realtime status: $status for ID: ${_currentProjectData['id']}');
        if (status == RealtimeSubscribeStatus.subscribed) {
            print('Successfully subscribed to construction detail updates.');
        } else if (status == RealtimeSubscribeStatus.channelError || status == RealtimeSubscribeStatus.timedOut) { // CORRECTED LINE
            print('Error subscribing to construction detail updates: $status');
            _showErrorSnackbar("Error with real-time updates for this project ($status).");
        }
    });
  }

  Future<void> _refreshConstructionProjectData() async {
    try {
      final response = await Supabase.instance.client
          .from(_constructionTableName)
          .select('*')
          .eq('id', _currentProjectData['id'])
          .single();

      if (mounted) {
        setState(() {
          _currentProjectData = Map<String, dynamic>.from(response);
        });
        print("Construction project data updated in detail page for ${_currentProjectData['fullname']}");
      }
    } catch (error) {
      print("Error refreshing construction project data: $error");
      if (mounted) {
        _showErrorSnackbar("Could not refresh project details: ${error.toString()}");
      }
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  Future<void> _launchUrlHelper(Uri url, String actionDescription) async {
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      print('Error launching $actionDescription: $e');
      if (mounted) {
        _showErrorSnackbar('Could not $actionDescription.');
      }
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    if (phoneNumber.isEmpty) return;
    final Uri telUri = Uri(scheme: 'tel', path: phoneNumber);
    await _launchUrlHelper(telUri, 'place a call');
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat = double.tryParse(latitude?.toString() ?? '');
    double? lng = double.tryParse(longitude?.toString() ?? '');

    if (lat == null || lng == null) {
      _showErrorSnackbar('Location coordinates are invalid.');
      return;
    }
    final Uri mapUri = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    await _launchUrlHelper(mapUri, 'open navigation');
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    if (whatsappNumber.isEmpty) return;
    final cleanNumber = whatsappNumber.replaceAll(RegExp(r'[+\s()-]'), ''); // More robust cleaning
    final Uri whatsappUri = Uri.parse('https://wa.me/$cleanNumber');
    await _launchUrlHelper(whatsappUri, 'open WhatsApp');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark; // Use theme for current mode

    // Use _currentProjectData for all project fields
    final String fullname = _currentProjectData['fullname'] ?? 'Unnamed Project';
    final String location = _currentProjectData['location'] ?? '';
    final String about = _currentProjectData['about'] ?? '';
    
    String startDateFormatted = '';
    if (_currentProjectData['startdate'] != null) {
      try {
        final startDate = DateTime.parse(_currentProjectData['startdate']);
        startDateFormatted = DateFormat('MMMM d, yyyy').format(startDate);
      } catch (e) { /* Handle parsing error if necessary */ }
    }
    
    String endDateFormatted = '';
    if (_currentProjectData['enddate'] != null) {
      try {
        final endDate = DateTime.parse(_currentProjectData['enddate']);
        endDateFormatted = DateFormat('MMMM d, yyyy').format(endDate);
      } catch (e) { /* Handle parsing error */ }
    }

    final String phone = _currentProjectData['phone']?.toString() ?? '';
    final String whatsapp = _currentProjectData['whatsapp']?.toString() ?? '';
    final dynamic latitude = _currentProjectData['latitude'];
    final dynamic longitude = _currentProjectData['longitude'];

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null &&
                                     double.tryParse(latitude.toString()) != null &&
                                     double.tryParse(longitude.toString()) != null;

    final bool hasAnyAction = isPhoneAvailable || isWhatsappAvailable || isNavigationAvailable;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            elevation: 1,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
                        child: Icon(
                          Icons.construction_outlined,
                          size: 30,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            if (location.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  location,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurfaceVariant,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  if (startDateFormatted.isNotEmpty)
                    _buildDetailRow(theme, Icons.calendar_today_outlined, 'Start Date', startDateFormatted),
                  if (endDateFormatted.isNotEmpty)
                    _buildDetailRow(theme, Icons.event_outlined, 'End Date', endDateFormatted),
                  
                  if (phone.isNotEmpty)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Contact Phone', phone, canCopy: true, onTap: () => _launchDialer(phone)),
                  if (whatsapp.isNotEmpty)
                    _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp Contact', whatsapp, canCopy: true, onTap: () => _launchWhatsapp(whatsapp)),
                  
                  if (about.isNotEmpty) ...[
                    const SizedBox(height: 24),
                    _buildSectionTitle(theme, Icons.info_outline, 'About this Project'),
                    const SizedBox(height: 8),
                    Text(
                      about,
                      style: TextStyle(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                  ],

                  // Placeholder for map if added later
                  // if (isNavigationAvailable) ...[
                  //   const SizedBox(height: 24),
                  //   _buildSectionTitle(theme, Icons.map_outlined, 'Location on Map'),
                  //   const SizedBox(height: 12),
                  //   // Map widget here
                  // ],
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: hasAnyAction
          ? Container(
              color: theme.colorScheme.surface, // Match AppBar
              padding: const EdgeInsets.symmetric(vertical: 0), // Reduce padding
              child: SafeArea(
                top: false, // No top padding needed if AppBar exists
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0), // Add some vertical padding inside SafeArea
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      if (isPhoneAvailable)
                        _buildBottomActionItem(
                          theme,
                          icon: Icons.call_outlined,
                          label: 'Call',
                          onPressed: () => _launchDialer(phone),
                        ),
                      if (isNavigationAvailable)
                        _buildBottomActionItem(
                          theme,
                          icon: Icons.navigation_outlined,
                          label: 'Navigate',
                          onPressed: () => _launchNavigation(latitude, longitude),
                        ),
                      if (isWhatsappAvailable)
                        _buildBottomActionItem(
                          theme,
                          icon: FontAwesomeIcons.whatsapp, // Solid icon for FA
                          label: 'WhatsApp',
                          onPressed: () => _launchWhatsapp(whatsapp),
                        ),
                    ],
                  ),
                ),
              ),
            )
          : null, // Hide bottom bar if no actions are available
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false, VoidCallback? onTap}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink(); // Hide if value is empty
    }

    Widget content = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary, // Use primary color for icons in detail rows for subtle emphasis
          size: 20,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  // fontWeight: FontWeight.bold, // Title can be normal weight if icon is prominent
                  fontSize: 13,
                  color: theme.colorScheme.onSurfaceVariant, // Softer title color
                ),
              ),
              const SizedBox(height: 2), // Reduced space
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value.toString(),
                      style: TextStyle(
                        color: theme.colorScheme.onSurface, // Main value color
                        fontSize: 15,
                        // No underline, color change handled by onTap if needed
                      ),
                    ),
                  ),
                  if (canCopy)
                    SizedBox( // Make copy icon tappable area larger
                      width: 36,
                      height: 36,
                      child: IconButton(
                        icon: Icon(
                          Icons.copy_outlined, // Outlined copy icon
                          size: 18,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy $title',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0), // Consistent spacing
      child: onTap != null
          ? InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(8), // Add ripple effect for tappable rows
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0), // Padding for InkWell
                child: content,
              ),
            )
          : Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: content,
            ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0), // Add top padding for separation
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.primary, size: 22),
          const SizedBox(width: 10),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 17, // Slightly larger section title
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionItem(ThemeData theme, {required IconData icon, required String label, required VoidCallback onPressed}) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // Adjust padding
        child: Column(
          mainAxisSize: MainAxisSize.min, // Important for Column in Row
          children: [
            Icon(icon, color: theme.colorScheme.onSurface, size: 22), // Slightly smaller icon
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(color: theme.colorScheme.onSurface, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}