// startups_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'startup_detail_page.dart';

class StartupsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedStartups;
  final bool isFromDetailPage;

  const StartupsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedStartups,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<StartupsPage> createState() => _StartupsPageState();
}

class _StartupsPageState extends State<StartupsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('startups_list');
  List<Map<String, dynamic>> _startups = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("StartupsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant StartupsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("StartupsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("StartupsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("StartupsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedStartups != null && widget.preloadedStartups!.isNotEmpty) {
      print("Preloaded startups found, using them.");
      setState(() {
        _startups = List<Map<String, dynamic>>.from(widget.preloadedStartups!);
        _startups.forEach((startup) {
          startup['_isImageLoading'] = false;
        });
        _startups.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedStartups!.length == _pageSize;
      });
      _loadStartupsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded startups or empty list, loading from database.");
      _loadStartupsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadStartupsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadStartupsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final startupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startups';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(startupsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedStartups =
          await _updateStartupImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _startups = updatedStartups;
        } else {
          _startups.addAll(updatedStartups);
        }
        _startups.forEach((startup) {
          startup['_isImageLoading'] = false;
        });
        _startups.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the startups
      _cacheStartups(_startups);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching startups: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateStartupImageUrls(
      List<Map<String, dynamic>> startups) async {
    List<Future<void>> futures = [];
    for (final startup in startups) {
      if (startup['image_url'] == null ||
          startup['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(startup));
      }
    }
    await Future.wait(futures);
    return startups;
  }

  Future<void> _cacheStartups(List<Map<String, dynamic>> startups) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String startupsJson = jsonEncode(startups);
      await prefs.setString(
          'startups_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          startupsJson);
    } catch (e) {
      print('Error caching startups: $e');
    }
  }
  
  void _setupRealtime() {
    final startupsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_startups';
    _realtimeChannel = Supabase.instance.client
        .channel('startups')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: startupsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStartupId = payload.newRecord['id'];
          final newStartupResponse = await Supabase.instance.client
              .from(startupsTableName)
              .select('*')
              .eq('id', newStartupId)
              .single();
          if (mounted) {
            Map<String, dynamic> newStartup = Map.from(newStartupResponse);
            final updatedStartup = await _updateStartupImageUrls([newStartup]);
            setState(() {
              _startups = [..._startups, updatedStartup.first];
              updatedStartup.first['_isImageLoading'] = false;
              _startups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStartupId = payload.newRecord['id'];
          final updatedStartupResponse = await Supabase.instance.client
              .from(startupsTableName)
              .select('*')
              .eq('id', updatedStartupId)
              .single();
          if (mounted) {
            final updatedStartup = Map<String, dynamic>.from(updatedStartupResponse);
            setState(() {
              _startups = _startups.map((startup) {
                return startup['id'] == updatedStartup['id'] ? updatedStartup : startup;
              }).toList();
              _startups.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStartupId = payload.oldRecord['id'];
          setState(() {
            _startups.removeWhere((startup) => startup['id'] == deletedStartupId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreStartups();
    }
  }

  Future<void> _loadMoreStartups() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadStartupsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> startup) async {
    if (startup['_isImageLoading'] == true) {
      print('Image loading already in progress for ${startup['fullname']}, skipping.');
      return;
    }
    if (startup['image_url'] != null &&
        startup['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${startup['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      startup['_isImageLoading'] = true;
    });

    final fullname = startup['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeStartupBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/startups';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeStartupBucket');
    print('Image URL before fetch: ${startup['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeStartupBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeStartupBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        startup['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        startup['_isImageLoading'] = false;
        print('Setting image_url for ${startup['fullname']} to: ${startup['image_url']}');
      });
    } else {
      startup['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> startup) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StartupDetailPage(
            startup: startup,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchWebsite(String websiteLink) async {
    if (websiteLink.isEmpty) return;
    
    Uri url;
    if (websiteLink.startsWith('http://') || websiteLink.startsWith('https://')) {
      url = Uri.parse(websiteLink);
    } else {
      url = Uri.parse('https://$websiteLink');
    }
    
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $websiteLink')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("StartupsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Startups',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _startups.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadStartupsFromSupabase(initialLoad: true),
              child: _startups.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No startups available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : GridView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.75,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 10,
                      ),
                      padding: const EdgeInsets.all(16),
                      itemCount: _startups.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _startups.length) {
                          final startup = _startups[index];
                          final websiteLink = startup['websitelink'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('startup_${startup['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (startup['image_url'] == null ||
                                      startup['image_url'] == 'assets/placeholder_image.png') &&
                                  !startup['_isImageLoading']) {
                                _fetchImageUrl(startup);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              clipBehavior: Clip.antiAlias,
                              child: InkWell(
                                onTap: () => _navigateToDetail(context, startup),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Stack(
                                        children: [
                                          SizedBox(
                                            width: double.infinity,
                                            height: double.infinity,
                                            child: CachedNetworkImage(
                                              imageUrl: startup['image_url'] ??
                                                  'assets/placeholder_image.png',
                                              errorWidget: (context, url, error) =>
                                                  Image.asset('assets/placeholder_image.png'),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                          if (websiteLink.isNotEmpty)
                                            Positioned(
                                              top: 8,
                                              right: 8,
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: theme.colorScheme.primary.withOpacity(0.8),
                                                  borderRadius: BorderRadius.circular(16),
                                                ),
                                                child: IconButton(
                                                  icon: const Icon(
                                                    Icons.language,
                                                    color: Colors.white,
                                                    size: 16,
                                                  ),
                                                  onPressed: () => _launchWebsite(websiteLink),
                                                  tooltip: 'Visit website',
                                                  constraints: const BoxConstraints(
                                                    minWidth: 32,
                                                    minHeight: 32,
                                                  ),
                                                  padding: EdgeInsets.zero,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            startup['fullname'] ?? 'Unnamed Startup',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: theme.colorScheme.onSurface,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            startup['about'] ?? '',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: theme.colorScheme.secondary,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
