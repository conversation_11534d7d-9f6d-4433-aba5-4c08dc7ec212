import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'helpdesk_people_detail_page.dart';

class HelpdeskPeopleListPage extends StatefulWidget {
  final String helpdeskName;
  final List<Map<String, dynamic>> people;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const HelpdeskPeopleListPage({
    Key? key,
    required this.helpdeskName,
    required this.people,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<HelpdeskPeopleListPage> createState() => _HelpdeskPeopleListPageState();
}

class _HelpdeskPeopleListPageState extends State<HelpdeskPeopleListPage> {
  late List<Map<String, dynamic>> _people;
  bool _isLoading = false;
  late RealtimeChannel _peopleRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _people = List<Map<String, dynamic>>.from(widget.people);
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _peopleRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    final peopleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_people';
    _peopleRealtimeChannel = Supabase.instance.client
        .channel('people_list_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: peopleTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert ||
            payload.eventType == PostgresChangeEvent.update ||
            payload.eventType == PostgresChangeEvent.delete) {
          _refreshPeople();
        }
      },
    ).subscribe();
  }

  Future<void> _refreshPeople() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final peopleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_people';
    final helpdeskName = widget.helpdeskName;

    try {
      final response = await Supabase.instance.client
          .from(peopleTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _people = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      }
    } catch (error) {
      print('Error refreshing people: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToPersonDetail(Map<String, dynamic> person) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskPeopleDetailPage(
          personName: person['fullname'] ?? '',
          personTitle: person['title'] ?? '',
          detailPageName: person['id']?.toString() ?? '',
          helpdeskName: widget.helpdeskName,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          person: person,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${widget.helpdeskName} People',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshPeople,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _people.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No people available for ${widget.helpdeskName}.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _people.length,
                    itemBuilder: (context, index) {
                      final person = _people[index];
                      return Card(
                        color: theme.colorScheme.surface,
                        surfaceTintColor: Colors.transparent,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: InkWell(
                          onTap: () => _navigateToPersonDetail(person),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CircleAvatar(
                                  radius: 24,
                                  backgroundColor: currentIsDarkMode
                                      ? Colors.white.withOpacity(0.1)
                                      : Colors.black.withOpacity(0.1),
                                  child: Icon(
                                    Icons.person,
                                    color: currentIsDarkMode ? Colors.white : Colors.black,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        person['fullname'] ?? 'Unnamed Person',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                      if (person['title'] != null && person['title'].toString().isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 4),
                                          child: Text(
                                            person['title'],
                                            style: TextStyle(
                                              color: theme.colorScheme.onSurfaceVariant,
                                              fontSize: 14,
                                            ),
                                          ),
                                        ),
                                      if (person['department'] != null && person['department'].toString().isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 4),
                                          child: Text(
                                            person['department'],
                                            style: TextStyle(
                                              color: theme.colorScheme.onSurfaceVariant,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16,
                                  color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}