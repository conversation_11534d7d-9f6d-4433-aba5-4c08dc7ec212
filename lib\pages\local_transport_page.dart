import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'local_transport_detail_page.dart';
import 'login_page.dart';

class LocalTransportPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedLocalTransport;
  final bool isFromDetailPage;

  const LocalTransportPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedLocalTransport,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _LocalTransportPageState createState() => _LocalTransportPageState();
}

class _LocalTransportPageState extends State<LocalTransportPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('local_transport_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _localTransport = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("LocalTransportPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedLocalTransport != null &&
        widget.preloadedLocalTransport!.isNotEmpty) {
      setState(() {
        _localTransport = List.from(widget.preloadedLocalTransport!);
        _isLoading = false;
      });
    } else {
      _loadLocalTransportFromDatabase();
    }
  }

  void _setupRealtime() {
    final localTransportTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localtransport';
    _realtimeChannel = Supabase.instance.client
        .channel('local_transport_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localTransportTableName,
      callback: (payload) async {
        print("Realtime update received for local transport: ${payload.eventType}");
        _loadLocalTransportFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadLocalTransportFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _localTransport = [];
    });

    await _loadMoreLocalTransport();
  }

  Future<void> _loadMoreLocalTransport() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final localTransportTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localtransport';
      final response = await Supabase.instance.client
          .from(localTransportTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _localTransport.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading local transport: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading local transport: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreLocalTransport();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Local Transport',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('local_transport_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _localTransport.isEmpty && !_isLoading) {
            _loadLocalTransportFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadLocalTransportFromDatabase,
          child: _localTransport.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No local transport options found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _localTransport.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _localTransport.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildLocalTransportCard(
                      _localTransport[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildLocalTransportCard(
    Map<String, dynamic> transport,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = transport['fullname'] ?? 'Unknown';
    final String payment = transport['payment'] ?? '';
    final String about = transport['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LocalTransportDetailPage(
                localTransport: transport,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.commute,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
