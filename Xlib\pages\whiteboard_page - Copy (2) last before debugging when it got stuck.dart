import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter/rendering.dart';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' as io;
import 'dart:typed_data';
import 'package:permission_handler/permission_handler.dart';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';

@immutable
class WhiteboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const WhiteboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<WhiteboardPage> createState() => _WhiteboardPageState();
}

class _WhiteboardPageState extends State<WhiteboardPage> {
  List<List<DrawingPoint?>> _boards = [[]];
  int _currentBoardIndex = 0;
  Color selectedColor = Colors.black;
  double strokeWidth = 2.0; // Set default pen size to 2.0
  final GlobalKey _repaintKey = GlobalKey();
  bool _isErasing = false;

  static const String _boardsDataKey = 'whiteboard_boards_data';

  @override
  void initState() {
    super.initState();
    _loadBoards();
  }

  @override
  void dispose() {
    super.dispose();
  }

  List<DrawingPoint?> get currentBoard => _boards[_currentBoardIndex];

  void _showCombinedPenSettings() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        Color pickerColor = selectedColor;
        double sliderStrokeWidth = strokeWidth;
        return AlertDialog(
          title: const Text('Pen Settings'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    const Text('Select Color:'),
                    ColorPicker(
                      pickerColor: pickerColor,
                      onColorChanged: (Color color) {
                        pickerColor = color;
                      },
                      pickerAreaHeightPercent: 0.8,
                    ),
                    const SizedBox(height: 20),
                    const Text('Select Size:'),
                    Slider(
                      value: sliderStrokeWidth,
                      min: 1,
                      max: 20,
                      onChanged: (double newValue) {
                        setState(() {
                          sliderStrokeWidth = newValue;
                        });
                      },
                    ),
                    Text('Pen size: ${sliderStrokeWidth.toStringAsFixed(1)}'),
                  ],
                ),
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Done'),
              onPressed: () {
                setState(() {
                  selectedColor = pickerColor;
                  strokeWidth = sliderStrokeWidth;
                  _isErasing = false;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _addNewBoard() {
    setState(() {
      _boards.add([]);
      _currentBoardIndex = _boards.length - 1;
      _saveBoards();
    });
  }

  void _clearCurrentBoard() {
    setState(() {
      _boards[_currentBoardIndex].clear();
      _saveBoards();
    });
  }

  void _deleteCurrentBoard() {
    if (_boards.length > 1) {
      setState(() {
        _boards.removeAt(_currentBoardIndex);
        _currentBoardIndex =
            _currentBoardIndex > 0 ? _currentBoardIndex - 1 : 0;
        _saveBoards();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cannot delete the only board.')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      final status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Storage permission is required to save PDF.')),
        );
        return false;
      }
    }
    return true;
  }

  Future<void> _saveAsPdf() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      if (!await _requestStoragePermission()) {
        return;
      }
    }

    String? outputFile;
    try {
      outputFile = await FilePicker.platform.getDirectoryPath();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open file picker.')),
      );
      return;
    }

    if (outputFile == null) {
      // User cancelled the picker
      return;
    }

    final pdf = pw.Document();

    for (int i = 0; i < _boards.length; i++) {
      if (_boards[i].any((point) => point != null)) {
        ByteData? imageByteData = await _captureBoardAsImage(boardIndex: i);
        if (imageByteData != null) {
          final pdfImage = pw.MemoryImage(imageByteData.buffer.asUint8List());
          pdf.addPage(pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(pdfImage),
              );
            },
          ));
        }
      }
    }

    if (pdf.document.pdfPageList.pages.isNotEmpty) {
      try {
        final pdfBytes = await pdf.save();
        final fileName = 'notes_${DateTime.now().millisecondsSinceEpoch}.pdf';
        final filePath = path.join(outputFile, fileName);
        final file = io.File(filePath);
        await file.writeAsBytes(pdfBytes);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF saved to ${file.path}')),
        );
      } catch (e) {
        print("Error saving PDF: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save PDF.')),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No notes to save as PDF.')),
      );
    }
  }

  Future<ByteData?> _captureBoardAsImage({required int boardIndex}) async {
    final painter = _WhiteboardPainter(_boards[boardIndex]);
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder,
        Rect.fromPoints(const Offset(0.0, 0.0), const Offset(800.0, 1131.0)));
    painter.paint(canvas, const Size(800, 1131));
    final picture = recorder.endRecording();
    final img = await picture.toImage(800, 1131);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData;
  }

  Future<void> _saveBoards() async {
    final prefs = await SharedPreferences.getInstance();
    final List<dynamic> serializableBoards = _boards.map((board) {
      return board.map((point) => point?.toJson()).toList();
    }).toList();
    final String boardsJson = jsonEncode(serializableBoards);
    await prefs.setString(_boardsDataKey, boardsJson);
  }

  Future<void> _loadBoards() async {
    final prefs = await SharedPreferences.getInstance();
    final String? boardsJson = prefs.getString(_boardsDataKey);
    if (boardsJson != null) {
      try {
        final dynamic decodedData = jsonDecode(boardsJson);
        if (decodedData is List) {
          List<List<DrawingPoint?>> loadedBoards =
              decodedData.map<List<DrawingPoint?>?>((boardData) {
            if (boardData is List) {
              return boardData.map<DrawingPoint?>((pointData) {
                if (pointData is Map<String, dynamic>) {
                  return DrawingPoint.fromJson(pointData);
                }
                return null;
              }).toList();
            }
            return null;
          }).whereType<List<DrawingPoint?>>().toList();
          setState(() {
            _boards = loadedBoards;
          });
        } else {
          print("Error: Decoded data is not a List");
          setState(() {
            _boards = [[]];
          });
        }
      } catch (e) {
        print("Error loading boards: $e");
        setState(() {
          _boards = [[]];
        });
      }
    }
  }

  // Store the points temporarily during drawing
  List<DrawingPoint?> _currentDrawingPoints = [];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          'Whiteboard ${_boards.length > 1 ? '(${_currentBoardIndex + 1}/${_boards.length})' : ''}',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.palette, color: theme.colorScheme.onSurface),
            tooltip: 'Pen Settings',
            onPressed: _showCombinedPenSettings,
          ),
          IconButton(
            icon: Icon(_isErasing ? Icons.edit : Icons.draw, color: theme.colorScheme.onSurface),
            tooltip: _isErasing ? 'Pen' : 'Eraser',
            onPressed: () {
              setState(() {
                _isErasing = !_isErasing;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Board',
            onPressed: _addNewBoard,
          ),
          if (_boards.length > 1)
            IconButton(
              icon: const Icon(Icons.chevron_left),
              tooltip: 'Previous Board',
              onPressed: _currentBoardIndex > 0
                  ? () => setState(() => _currentBoardIndex--)
                  : null,
            ),
          if (_boards.length > 1)
            IconButton(
              icon: const Icon(Icons.chevron_right),
              tooltip: 'Next Board',
              onPressed: _currentBoardIndex < _boards.length - 1
                  ? () => setState(() => _currentBoardIndex++)
                  : null,
            ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: theme.colorScheme.onSurface),
            onSelected: (value) {
              if (value == 'pdf') {
                _saveAsPdf();
              } else if (value == 'clear') {
                _clearCurrentBoard();
              } else if (value == 'delete') {
                _deleteCurrentBoard();
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'pdf',
                child: Text('Save as PDF'),
              ),
              const PopupMenuItem<String>(
                value: 'clear',
                child: Text('Clear Board'),
              ),
              const PopupMenuItem<String>(
                value: 'delete',
                child: Text('Delete Board'),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height - AppBar().preferredSize.height - kBottomNavigationBarHeight, // Adjust height for bottom bar
          width: MediaQuery.of(context).size.width,
          child: RepaintBoundary(
            key: _repaintKey,
            child: GestureDetector(
              onPanStart: (details) {
                setState(() {
                  _currentDrawingPoints.clear();
                  _currentDrawingPoints.add(
                    DrawingPoint(
                      details.localPosition,
                      Paint()
                        ..color = _isErasing ? const Color(0xFFFFFFFF) : selectedColor
                        ..strokeWidth = _isErasing ? strokeWidth + 10 : strokeWidth
                        ..strokeCap = StrokeCap.round,
                      isErasing: _isErasing,
                    ),
                  );
                });
              },
              onPanUpdate: (details) {
                setState(() {
                  _currentDrawingPoints.add(
                    DrawingPoint(
                      details.localPosition,
                      Paint()
                        ..color = _isErasing ? const Color(0xFFFFFFFF) : selectedColor
                        ..strokeWidth = _isErasing ? strokeWidth + 10 : strokeWidth
                        ..strokeCap = StrokeCap.round,
                      isErasing: _isErasing,
                    ),
                  );
                });
              },
              onPanEnd: (details) {
                setState(() {
                  if (_currentDrawingPoints.isNotEmpty) {
                    currentBoard.addAll(_currentDrawingPoints);
                    currentBoard.add(null); // Add null to indicate the end of a stroke
                    _currentDrawingPoints.clear();
                    _saveBoards();
                  }
                });
              },
              child: CustomPaint(
                painter: _WhiteboardPainter([...currentBoard, ..._currentDrawingPoints]), // Combine existing and current drawing points
                size: Size.infinite,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

@immutable
class DrawingPoint {
  final Offset offset;
  final Paint paint;
  final bool isErasing;

  DrawingPoint(this.offset, this.paint, {this.isErasing = false});

  Map<String, dynamic> toJson() => {
        'dx': offset.dx,
        'dy': offset.dy,
        'color': paint.color.value,
        'strokeWidth': paint.strokeWidth,
        'isErasing': isErasing,
      };

  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      Offset((json['dx'] as num).toDouble(), (json['dy'] as num).toDouble()),
      Paint()
        ..color = Color(json['color'] as int)
        ..strokeWidth = (json['strokeWidth'] as num).toDouble()
        ..strokeCap = StrokeCap.round,
      isErasing: json['isErasing'] as bool? ?? false,
    );
  }
}

class _WhiteboardPainter extends CustomPainter {
  final List<DrawingPoint?> points;

  _WhiteboardPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.white,
    );

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        canvas.drawLine(
            points[i]!.offset, points[i + 1]!.offset, points[i]!.paint);
      } else if (points[i] != null && points[i + 1] == null) {
        canvas.drawPoints(
          ui.PointMode.points,
          [points[i]!.offset],
          points[i]!.paint,
        );
      }
    }

    final Paint linePaint = Paint()
      ..color = Colors.grey.shade300
      ..strokeWidth = 0.5;
    const double spacing = 30.0;
    for (double i = spacing; i < size.height; i += spacing) {
      canvas.drawLine(Offset(0, i), Offset(size.width, i), linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}