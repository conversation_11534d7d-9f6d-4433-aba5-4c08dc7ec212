import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class DateTimeCalculatorPage extends StatefulWidget {
  const DateTimeCalculatorPage({Key? key}) : super(key: key);

  @override
  _DateTimeCalculatorPageState createState() => _DateTimeCalculatorPageState();
}

class _DateTimeCalculatorPageState extends State<DateTimeCalculatorPage> {
  DateTime? _startDate;
  DateTime? _endDate;
  String _duration = '';

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (pickedDate != null && pickedDate != _startDate) {
      setState(() {
        _startDate = pickedDate;
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (pickedDate != null && pickedDate != _endDate) {
      setState(() {
        _endDate = pickedDate;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Date & Time Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectStartDate(context),
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Start Date',
                              border: const OutlineInputBorder(),
                              labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                              enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                              focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                _startDate == null ? 'Select Start Date' : DateFormat('yyyy-MM-dd').format(_startDate!),
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      const Icon(Icons.calendar_today),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectEndDate(context),
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'End Date',
                              border: const OutlineInputBorder(),
                              labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                              enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                              focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                _endDate == null ? 'Select End Date' : DateFormat('yyyy-MM-dd').format(_endDate!),
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      const Icon(Icons.calendar_today),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateDuration();
                    },
                    child: const Text('Calculate Duration'),
                  ),
                  const SizedBox(height: 20),
                  Text('Duration: $_duration', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateDuration() {
    if (_startDate != null && _endDate != null) {
      Duration difference = _endDate!.difference(_startDate!);
      int days = difference.inDays;
      setState(() {
        _duration = '$days days';
      });
    } else {
      setState(() {
        _duration = 'Select both start and end dates';
      });
    }
  }
}