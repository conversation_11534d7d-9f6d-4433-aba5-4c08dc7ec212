import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MealPlanDetailPage extends StatefulWidget {
  final Map<String, dynamic> mealPlan;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const MealPlanDetailPage({
    Key? key,
    required this.mealPlan,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<MealPlanDetailPage> createState() => _MealPlanDetailPageState();
}

class _MealPlanDetailPageState extends State<MealPlanDetailPage> {
  late RealtimeChannel _mealPlanRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _mealPlanRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _mealPlanRealtimeChannel = Supabase.instance.client
        .channel('meal_plan_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'mealplans',
      callback: (payload) async {
        // Manual filtering for the specific meal plan
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.mealPlan['id']) {
          print("Realtime update received for meal plan detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshMealPlan();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshMealPlan() async {
    try {
      final response = await Supabase.instance.client
          .from('mealplans')
          .select('*')
          .eq('id', widget.mealPlan['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's mealPlan with the new data
          widget.mealPlan.clear();
          widget.mealPlan.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing meal plan: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.mealPlan['fullname'] ?? 'Unknown';
    final String about = widget.mealPlan['about'] ?? '';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Meal plan details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.restaurant_menu,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About this Meal Plan:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
