import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'news_page.dart';
import 'periodicals_page.dart';
import 'radio_page.dart';
import 'television_page.dart';
import 'photos_page.dart';
import 'videos_page.dart';

class TertiaryMediaPage extends StatefulWidget {
  final Map<String, dynamic>? collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage; // Added isFromDetailPage

  const TertiaryMediaPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false, // Initialize isFromDetailPage
  }) : super(key: key);

  @override
  State<TertiaryMediaPage> createState() => _TertiaryMediaPageState();
}

class _TertiaryMediaPageState extends State<TertiaryMediaPage> {
  // Data for each section
  List<Map<String, dynamic>> _news = [];
  List<Map<String, dynamic>> _periodicals = [];
  List<Map<String, dynamic>> _radio = [];
  List<Map<String, dynamic>> _television = [];
  List<Map<String, dynamic>> _photos = [];
  List<Map<String, dynamic>> _videos = [];

  // Loading states
  bool _isLoadingNews = false;
  bool _isLoadingPeriodicals = false;
  bool _isLoadingRadio = false;
  bool _isLoadingTelevision = false;
  bool _isLoadingPhotos = false;
  bool _isLoadingVideos = false;

  // Realtime channels
  late final RealtimeChannel _newsChannel;
  late final RealtimeChannel _periodicalsChannel;
  late final RealtimeChannel _radioChannel;
  late final RealtimeChannel _televisionChannel;
  late final RealtimeChannel _photosChannel;
  late final RealtimeChannel _videosChannel;

  @override
  void initState() {
    super.initState();
    _preloadAndCacheData();
    _setupRealtimeChannels();
  }

  @override
  void dispose() {
    _newsChannel.unsubscribe();
    _periodicalsChannel.unsubscribe();
    _radioChannel.unsubscribe();
    _televisionChannel.unsubscribe();
    _photosChannel.unsubscribe();
    _videosChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _preloadAndCacheData() async {
    await Future.wait([
      _preloadNews(),
      _preloadPeriodicals(),
      _preloadRadio(),
      _preloadTelevision(),
      _preloadPhotos(),
      _preloadVideos(),
    ]);
  }

  Future<void> _preloadNews() async {
    if (_isLoadingNews) return;
    setState(() => _isLoadingNews = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('news');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _news = cachedData;
          _isLoadingNews = false;
        });
      }

      // Then fetch from Supabase
      final newsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_news';
      final response = await Supabase.instance.client
          .from(newsTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .limit(20);

      final news = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('news', news);

      setState(() {
        _news = news;
        _isLoadingNews = false;
      });
    } catch (e) {
      print('Error preloading news: $e');
      setState(() => _isLoadingNews = false);
    }
  }

  Future<void> _preloadPeriodicals() async {
    if (_isLoadingPeriodicals) return;
    setState(() => _isLoadingPeriodicals = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('periodicals');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _periodicals = cachedData;
          _isLoadingPeriodicals = false;
        });
      }

      // Then fetch from Supabase
      final periodicalsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_periodicals';
      final response = await Supabase.instance.client
          .from(periodicalsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final periodicals = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('periodicals', periodicals);

      setState(() {
        _periodicals = periodicals;
        _isLoadingPeriodicals = false;
      });
    } catch (e) {
      print('Error preloading periodicals: $e');
      setState(() => _isLoadingPeriodicals = false);
    }
  }

  Future<void> _preloadRadio() async {
    if (_isLoadingRadio) return;
    setState(() => _isLoadingRadio = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('radio');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _radio = cachedData;
          _isLoadingRadio = false;
        });
      }

      // Then fetch from Supabase
      final radioTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_radio';
      final response = await Supabase.instance.client
          .from(radioTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final radio = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('radio', radio);

      setState(() {
        _radio = radio;
        _isLoadingRadio = false;
      });
    } catch (e) {
      print('Error preloading radio: $e');
      setState(() => _isLoadingRadio = false);
    }
  }

  Future<void> _preloadTelevision() async {
    if (_isLoadingTelevision) return;
    setState(() => _isLoadingTelevision = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('television');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _television = cachedData;
          _isLoadingTelevision = false;
        });
      }

      // Then fetch from Supabase
      final televisionTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_television';
      final response = await Supabase.instance.client
          .from(televisionTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final television = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('television', television);

      setState(() {
        _television = television;
        _isLoadingTelevision = false;
      });
    } catch (e) {
      print('Error preloading television: $e');
      setState(() => _isLoadingTelevision = false);
    }
  }

  Future<void> _preloadPhotos() async {
    if (_isLoadingPhotos) return;
    setState(() => _isLoadingPhotos = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('photos');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _photos = cachedData;
          _isLoadingPhotos = false;
        });
      }

      // Then fetch from Supabase
      final photosTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_photos';
      final response = await Supabase.instance.client
          .from(photosTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .limit(20);

      final photos = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('photos', photos);

      setState(() {
        _photos = photos;
        _isLoadingPhotos = false;
      });
    } catch (e) {
      print('Error preloading photos: $e');
      setState(() => _isLoadingPhotos = false);
    }
  }

  Future<void> _preloadVideos() async {
    if (_isLoadingVideos) return;
    setState(() => _isLoadingVideos = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('videos');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _videos = cachedData;
          _isLoadingVideos = false;
        });
      }

      // Then fetch from Supabase
      final videosTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_videos';
      final response = await Supabase.instance.client
          .from(videosTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .limit(20);

      final videos = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('videos', videos);

      setState(() {
        _videos = videos;
        _isLoadingVideos = false;
      });
    } catch (e) {
      print('Error preloading videos: $e');
      setState(() => _isLoadingVideos = false);
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache(String tableName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading $tableName from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(String tableName, List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving $tableName to cache: $e');
    }
  }

  void _setupRealtimeChannels() {
    _setupNewsRealtimeChannel();
    _setupPeriodicalsRealtimeChannel();
    _setupRadioRealtimeChannel();
    _setupTelevisionRealtimeChannel();
    _setupPhotosRealtimeChannel();
    _setupVideosRealtimeChannel();
  }

  void _setupNewsRealtimeChannel() {
    final newsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_news';
    _newsChannel = Supabase.instance.client
        .channel('news_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: newsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadNews();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadNews();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadNews();
            }
          },
        )
        .subscribe();
  }

  void _setupPeriodicalsRealtimeChannel() {
    final periodicalsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_periodicals';
    _periodicalsChannel = Supabase.instance.client
        .channel('periodicals_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: periodicalsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadPeriodicals();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadPeriodicals();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadPeriodicals();
            }
          },
        )
        .subscribe();
  }

  void _setupRadioRealtimeChannel() {
    final radioTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_radio';
    _radioChannel = Supabase.instance.client
        .channel('radio_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: radioTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadRadio();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadRadio();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadRadio();
            }
          },
        )
        .subscribe();
  }

  void _setupTelevisionRealtimeChannel() {
    final televisionTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_television';
    _televisionChannel = Supabase.instance.client
        .channel('television_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: televisionTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadTelevision();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadTelevision();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadTelevision();
            }
          },
        )
        .subscribe();
  }

  void _setupPhotosRealtimeChannel() {
    final photosTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_photos';
    _photosChannel = Supabase.instance.client
        .channel('photos_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: photosTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadPhotos();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadPhotos();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadPhotos();
            }
          },
        )
        .subscribe();
  }

  void _setupVideosRealtimeChannel() {
    final videosTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_videos';
    _videosChannel = Supabase.instance.client
        .channel('videos_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: videosTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadVideos();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadVideos();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadVideos();
            }
          },
        )
        .subscribe();
  }

  Widget _buildGridItem(
    BuildContext context,
    String title,
    IconData icon,
    ThemeData theme,
    bool isFromDetailPage,
  ) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    void navigateToPage() {
      if (title == 'News & Blog Posts') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NewsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedNews: _news,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Periodicals') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PeriodicalsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPeriodicals: _periodicals,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Radio') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RadioPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedRadioStations: _radio,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Television') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TelevisionPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedTelevisionStations: _television,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Photos') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PhotosPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPhotos: _photos,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Videos') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideosPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedVideos: _videos,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: navigateToPage,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'News & Blog Posts', 'icon': Icons.post_add},
      {'title': 'Periodicals', 'icon': Icons.newspaper},
      {'title': 'Radio', 'icon': Icons.radio},
      {'title': 'Television', 'icon': Icons.live_tv},
      {'title': 'Photos', 'icon': Icons.photo},
      {'title': 'Videos', 'icon': Icons.video_library},
    ];

    // Filter out 'Radio' and 'Television' when not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage &&
          (item['title'] == 'Radio' || item['title'] == 'Television')) {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Media',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}