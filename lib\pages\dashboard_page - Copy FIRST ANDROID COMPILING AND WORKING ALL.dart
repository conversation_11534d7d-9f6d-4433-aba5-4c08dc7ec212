import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}


class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  double _uploadProgress = 0.0;
  String? _geminiOutput;
  PlatformFile? _pickedFile;
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s';
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';

  /// Getter for general text color: black in light mode, white in dark mode.
  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'txt', 'doc', 'docx'],
        withData: true,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        if (mounted) {
          setState(() {
            _pickedFile = result.files.first;
            _isUploading = true;
            _uploadProgress = 0.0;
            _geminiOutput = null;
            _flashcards = [];
            _quizQuestions = [];
          });
        }

        for (int i = 0; i <= 100; i += 10) {
          await Future.delayed(const Duration(milliseconds: 50));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('File ready: ${_pickedFile!.name}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final extractor = sf_pdf.PdfTextExtractor(document);
      final text = extractor.extractText();
      document.dispose();
      return text;
    } catch (e) {
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<void> _processFile() async {
    if (_pickedFile == null) return;

    if (mounted) {
      setState(() {
        _isProcessing = true;
        _geminiOutput = null;
        _flashcards = [];
        _quizQuestions = [];
      });
    }

    try {
      String fileContent = '';
      final fileName = _pickedFile!.name.toLowerCase();

      if (fileName.endsWith('.pdf')) {
        if (_pickedFile!.bytes != null) {
          fileContent =
              await _extractTextFromPdf(_pickedFile!.bytes!) ?? '';
        } else if (_pickedFile!.path != null) {
          final bytes = await File(_pickedFile!.path!).readAsBytes();
          fileContent = await _extractTextFromPdf(bytes) ?? '';
        }
      } else if (fileName.endsWith('.txt') ||
          fileName.endsWith('.doc') ||
          fileName.endsWith('.docx')) {
        fileContent = utf8.decode(_pickedFile!.bytes!);
      }

      if (fileContent.isEmpty) throw Exception('Content extraction failed');

      if (mounted) {
        setState(() => _fileContent = fileContent);
      }

      final prompt = _buildPrompt(fileContent);
      final response =
          await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        _handleResponse(response.text!);
      } else {
        throw Exception('AI response empty');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Processing error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  String _buildPrompt(String content) {
    final prompts = {
      'notes': '''Generate detailed notes with:
- Clear headings
- Bullet points
- Key terms in **bold**
- Examples in *italic*

Content: $content''',

      'cheatsheet': '''Generate a concise cheatsheet for the content.
Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.

Content: $content''',

      'flashcards': '''Generate flashcards (Q: question / A: answer):

Q: [Question]
A: [Answer]

Content: $content''',

      'quiz': '''Generate an interactive quiz in TEXT format. Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz.

Example Format:
1. What is the capital of France?
A) London
B) Paris
C) Berlin
D) Rome
Answer: B

2. What is the chemical symbol for water?
A) H2O
B) CO2
C) NaCl
D) O2
Answer: A

Now generate a quiz based on the following content, using the EXACT format above:

Content: $content''',

      'transcript': '''Create transcript:
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',

      'chat': '''Use as context:
$content
Provide summary and discussion question''',

      'summary': '''Generate a brief summary of the content. Keep it concise and to the point.

Content: $content''',

    'exam': '''Generate a comprehensive exam with at least 30 questions covering all aspects of the content.
Use this EXACT format for each question:
[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Correct Letter]

Do NOT include any markdown formatting, headers, or explanatory text.
Content: $content'''
  };

  return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        break;
      case 'chat':
        _startChatSession(response);
        break;
      default:
        if (mounted) {
          setState(() => _geminiOutput = response);
        }
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final lines = response.split('\n');
    String? currentQuestion;

    for (String line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('Q:')) {
        currentQuestion = trimmedLine.substring(2).trim();
      } else if (trimmedLine.startsWith('A:') && currentQuestion != null) {
        flashcards.add(Flashcard(
          question: currentQuestion,
          answer: trimmedLine.substring(2).trim(),
        ));
        currentQuestion = null;
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final questions = <QuizQuestion>[];

    // Improved regex to handle various question formats and spacing
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)[\.\)]\s+([^\n]+?)\s*(?:(?:[A-Da-d][\.\)]\s+[^\n]+?\s*)+)?(?:Answer|Correct):\s*([A-Da-d])',
        dotAll: true);
    final optionRegex = RegExp(r'([A-Da-d])[\.\)]\s+([^\n]+)');

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response'); // Print the raw response for debugging

    for (final match in matches) {
      print('\n--- Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Letter Group: ${match.group(3)}');

      final questionText = match.group(2)?.trim() ?? '';
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      final correctLetter = match.group(3)?.toUpperCase() ?? '';
      print('Correct Letter: $correctLetter');

      if (options.isNotEmpty && correctLetter.isNotEmpty) {
        final correctIndex = optionLetters.indexOf(correctLetter);

        if (correctIndex != -1) {
          questions.add(QuizQuestion(
            question: questionText,
            options: options,
            correctAnswerIndex: correctIndex,
          ));
        } else {
          print('Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');
        }
      } else {
        print('Warning: No options or correct letter found for question.');
      }
    }

    print('\nParsed Quiz Questions: ${questions.length}');
    questions.forEach((q) {
      print('  Question: ${q.question}');
      print('  Options: ${q.options}');
      print('  Correct Index: ${q.correctAnswerIndex}');
    });


    if (mounted) {
      setState(() {
        _quizQuestions = questions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
      });
    }
  }


Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
  if (_quizQuestions.isEmpty) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          _isProcessing
              ? 'Generating quiz questions...'
              : 'No quiz questions generated. Try processing the file first.',
          style: GoogleFonts.roboto(color: generalTextColor)
        ),
      ),
    );
  }

  final question = _quizQuestions[_currentQuestionIndex];
  final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
  final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor
                ),
              ),
              Text(
                'Score: $_quizScore',
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: theme.dividerColor),
            ),
            child: Text(
              question.question,
              style: GoogleFonts.roboto(fontSize: 16, color: generalTextColor)
            ),
          ),
          const SizedBox(height: 20),
          ...List.generate(question.options.length, (index) {
            // Only include options that actually have content
            if (question.options[index].isEmpty) return const SizedBox.shrink();

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Material(
                color: _userAnswers[_currentQuestionIndex] == index
                    ? theme.colorScheme.primary.withOpacity(0.2)
                    : theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  onTap: () {
                    setState(() => _userAnswers[_currentQuestionIndex] = index);
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Text(
                          '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                          style: GoogleFonts.roboto(
                            fontWeight: FontWeight.bold,
                            color: generalTextColor
                          ),
                        ),
                        Expanded(
                          child: Text(
                            question.options[index],
                            style: GoogleFonts.roboto(color: generalTextColor)
                          ),
                        ),
                        Radio<int>(
                          value: index,
                          groupValue: _userAnswers[_currentQuestionIndex],
                          onChanged: (value) {
                            setState(() => _userAnswers[_currentQuestionIndex] = value);
                          },
                          activeColor: theme.colorScheme.primary,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _userAnswers[_currentQuestionIndex] != null
                ? () {
                    // Check if the answer is correct before moving to next question
                    if (_userAnswers[_currentQuestionIndex] == question.correctAnswerIndex) {
                      setState(() => _quizScore++);
                    }

                    // Either go to next question or finish the quiz
                    _submitQuiz();
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: buttonBackground,
              padding: const EdgeInsets.symmetric(vertical: 16),
              disabledBackgroundColor: Colors.grey,
            ),
            child: Text(
              _currentQuestionIndex < _quizQuestions.length - 1
                  ? 'Next Question'
                  : 'Finish Quiz',
              style: GoogleFonts.roboto(
                color: _userAnswers[_currentQuestionIndex] != null
                    ? buttonTextColor
                    : Colors.grey[400],
                fontWeight: FontWeight.bold
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

void _submitQuiz() {
  if (_currentQuestionIndex < _quizQuestions.length - 1) {
    setState(() => _currentQuestionIndex++);
    return;
  }

  // Calculate final results
  final results = <QuizResult>[];
  final weakCategories = <String, int>{};

  for (int i = 0; i < _quizQuestions.length; i++) {
    final question = _quizQuestions[i];
    final correctIndex = question.correctAnswerIndex;
    final userAnswer = _userAnswers[i];
    final isCorrect = userAnswer == correctIndex;

    if (!isCorrect) {
      // Analyze question for weak categories
      final questionText = question.question.toLowerCase();
      if (questionText.contains('definition')) weakCategories['Definitions'] = (weakCategories['Definitions'] ?? 0) + 1;
      if (questionText.contains('formula')) weakCategories['Formulas'] = (weakCategories['Formulas'] ?? 0) + 1;
      if (questionText.contains('date')) weakCategories['Dates'] = (weakCategories['Dates'] ?? 0) + 1;
      if (questionText.contains('theory')) weakCategories['Theories'] = (weakCategories['Theories'] ?? 0) + 1;
      // Add more categories as needed
      weakCategories['General Knowledge'] = (weakCategories['General Knowledge'] ?? 0) + 1;
    }

    // Create a result entry for each question
    results.add(QuizResult(
      question: question.question,
      userAnswer: userAnswer != null && userAnswer < question.options.length
          ? question.options[userAnswer]
          : 'No answer',
      correctAnswer: correctIndex != null && correctIndex < question.options.length
          ? question.options[correctIndex]
          : 'Unknown',
      isCorrect: isCorrect,
    ));
  }

  // Get top 3 weak areas
  final weakList = weakCategories.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));
  final topWeaknesses = weakList.take(3).map((e) => e.key).toList();

  // Show results dialog
  showDialog(
    context: context,
    builder: (context) => QuizResultsDialog(
      score: _quizScore,
      total: _quizQuestions.length,
      results: results,
      weaknesses: topWeaknesses,
      textColor: generalTextColor,
    ),
  );
}

  void _startChatSession(String response) {
    try {
      _chatSession = _geminiModel.startChat();

      _chatSession.sendMessage(
          Content.text("Here's the document content for context:\n$_fileContent"));

      if (mounted) {
        setState(() {
          _chatMessages = [
            ChatMessage(
                "AI Assistant: I've analyzed your document. How can I help you with it?",
                false),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
        );
      }
    }
  }

  /// Converts markdown text into styled PDF widgets.
  List<pw.Widget> _buildPdfContent(String markdownText) {
  // Remove any introductory text and markdown code blocks
  String cleanedText = markdownText
      .replaceAll(RegExp(r'^```.*?^```', multiLine: true), '')
      .replaceAll(RegExp(r'^Here is .+?:', multiLine: true), '')
      .trim();

  final lines = cleanedText.split('\n');
    final widgets = <pw.Widget>[];

    for (var line in lines) {
      if (line.startsWith('# ')) {
        widgets.add(pw.Text(line.substring(2),
            style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold)));
      } else if (line.startsWith('## ')) {
        widgets.add(pw.Text(line.substring(3),
            style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)));
      } else {
        widgets.add(_buildRichTextFromMarkdown(line));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  pw.Widget _buildRichTextFromMarkdown(String text) {
    List<pw.TextSpan> spans = [];
    final pattern = RegExp(r'(\*\*.*?\*\*|\*.*?\*|[^*]+)');
    final matches = pattern.allMatches(text);

    for (var match in matches) {
      String segment = match.group(0)!;
      if (segment.startsWith('**') && segment.endsWith('**')) {
        spans.add(pw.TextSpan(
            text: segment.substring(2, segment.length - 2),
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold)));
      } else if ((segment.startsWith('*') && segment.endsWith('*')) ||
          (segment.startsWith('_') && segment.endsWith('_'))) {
        spans.add(pw.TextSpan(
            text: segment.substring(1, segment.length - 1),
            style: pw.TextStyle(fontStyle: pw.FontStyle.italic)));
      } else {
        spans.add(pw.TextSpan(text: segment));
      }
    }

    return pw.RichText(
        text: pw.TextSpan(children: spans, style: pw.TextStyle(fontSize: 14)));
  }

  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => pw.Container(
            alignment: pw.Alignment.centerLeft,
            margin: const pw.EdgeInsets.only(bottom: 10),
            child: pw.Text('Refactr AI',
                style: pw.TextStyle(
                    fontSize: 24, 
                    fontWeight: pw.FontWeight.bold
                )),
          ),
          build: (context) => _buildPdfContent(_geminiOutput!),
        ),
      );

      final bytes = await pdf.save();

      if (kIsWeb) {
        final blob = html.Blob([bytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.AnchorElement(href: url)
          ..download = 'learning_materials.pdf'
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        final dir = await getApplicationDocumentsDirectory();
        final path = '${dir.path}/learning_materials.pdf';
        await File(path).writeAsBytes(bytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to $path')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF export failed: $e')),
        );
      }
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages.add(
            ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Button styling as requested.
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'AI Learning Dashboard',
          style: GoogleFonts.roboto(color: generalTextColor),
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: generalTextColor),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, generalTextColor),
                ],
              ),
            ),
    );
  }

  Widget _buildFileSection(ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.upload, color: buttonTextColor),
              label: Text('Select Learning Material',
                  style: GoogleFonts.roboto(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              ),
              onPressed: _isUploading ? null : _pickFile,
            ),
            if (_pickedFile != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text('Selected: ${_pickedFile!.name}',
                    style: GoogleFonts.roboto(
                        color: generalTextColor.withOpacity(0.8))),
              ),
            if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _uploadProgress),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.roboto(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.roboto(color: generalTextColor),
              items: const [
                DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                DropdownMenuItem(value: 'chat', child: Text('Chat with Content')),
                DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
              ],
              onChanged: _isProcessing ? null : (value) => setState(() => _processType = value!),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.auto_awesome, color: buttonTextColor),
              label: Text(
                  _isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.roboto(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: _isProcessing ? null : _processFile,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    if (_processType == 'flashcards') {
      return _buildFlashcardsView(theme, generalTextColor);
    } else if (_processType == 'quiz') {
      return _buildQuizView(theme, generalTextColor);
    } else if (_processType == 'chat') {
      return _buildChatView(theme, generalTextColor);
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Generated Content',
                    style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor)),
                IconButton(
                  icon: Icon(Icons.download, color: generalTextColor),
                  onPressed: _exportToPdf,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: MarkdownBody(
                data: _geminiOutput ?? 'No content generated',
                styleSheet: MarkdownStyleSheet(
                  h1: GoogleFonts.roboto(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                  p: GoogleFonts.roboto(fontSize: 16, color: generalTextColor),
                  code: GoogleFonts.robotoMono(
                      fontSize: 14,
                      backgroundColor: theme.cardColor,
                      color: generalTextColor),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SizedBox(
          height: 400,
          child: PageView.builder(
            itemCount: _flashcards.length,
            itemBuilder: (context, index) => FlashcardWidget(
              flashcard: _flashcards[index],
              textColor: generalTextColor,
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.roboto(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FlipCard(
      front: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.question,
                style: GoogleFonts.roboto(fontSize: 20, color: textColor)),
          ),
        ),
      ),
      back: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.answer,
                style: GoogleFonts.roboto(fontSize: 18, color: textColor)),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use general text color based on mode.
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(message.text,
            style: GoogleFonts.roboto(color: bubbleTextColor)),
      ),
    );
  }
}

class QuizResultsDialog extends StatelessWidget {
  final int score;
  final int total;
  final List<QuizResult> results;
  final List<String> weaknesses;
  final Color textColor;

  const QuizResultsDialog({
    required this.score,
    required this.total,
    required this.results,
    required this.weaknesses,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Quiz Results', style: GoogleFonts.roboto(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: $score/$total (${(score/total*100).toStringAsFixed(1)}%)',
                style: GoogleFonts.roboto(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _getScoreColor(score/total))),

            if (weaknesses.isNotEmpty) ...[
              const SizedBox(height: 20),
              Text('Areas to Improve:',
                  style: GoogleFonts.roboto(
                      fontWeight: FontWeight.bold)),
              ...weaknesses.map((w) => Text('• $w',
                  style: GoogleFonts.roboto())),
            ],

            const SizedBox(height: 20),
            ExpansionTile(
              title: Text('Detailed Results',
                  style: GoogleFonts.roboto(fontWeight: FontWeight.bold)),
              children: [
                ...results.map((result) => ListTile(
                  title: Text(result.question,
                      style: GoogleFonts.roboto(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Your answer: ${result.userAnswer}',
                          style: GoogleFonts.roboto(
                              color: result.isCorrect ? Colors.green : Colors.red)),
                      Text('Correct answer: ${result.correctAnswer}'),
                      const Divider(),
                    ],
                  ),
                )),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.roboto(color: textColor)),
        ),
      ],
    );
  }

  Color _getScoreColor(double percentage) {
    if (percentage >= 0.9) return Colors.green;
    if (percentage >= 0.7) return Colors.orange;
    return Colors.red;
  }
}