import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'programs_page.dart';
import 'signature_events_page.dart';
import 'traditions_page.dart';
import 'partnership_opportunities_page.dart';

class TertiaryProgramsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryProgramsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryProgramsPage> createState() => _TertiaryProgramsPageState();
}

class _TertiaryProgramsPageState extends State<TertiaryProgramsPage> {
  List<Map<String, dynamic>>? _cachedPrograms;
  List<Map<String, dynamic>>? _cachedSignatureEvents;
  List<Map<String, dynamic>>? _cachedTraditions;
  List<Map<String, dynamic>>? _cachedPartnershipOpportunities;
  String? _lastCollegeName;
  bool _isLoadingPrograms = false;
  bool _isLoadingSignatureEvents = false;
  bool _isLoadingTraditions = false;
  bool _isLoadingPartnershipOpportunities = false;
  late RealtimeChannel _programsRealtimeChannel;
  late RealtimeChannel _signatureEventsRealtimeChannel;
  late RealtimeChannel _traditionsRealtimeChannel;
  late RealtimeChannel _partnershipOpportunitiesRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryProgramsPage initState called for ${widget.institutionName}");
    _loadCachedData();
    _loadDataFromDatabaseAndCache();
    _setupRealtimeListeners();
  }

  @override
  void dispose() {
    _programsRealtimeChannel.unsubscribe();
    _signatureEventsRealtimeChannel.unsubscribe();
    _traditionsRealtimeChannel.unsubscribe();
    _partnershipOpportunitiesRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadCachedData() async {
    await _loadCachedPrograms();
    await _loadCachedSignatureEvents();
    await _loadCachedTraditions();
    await _loadCachedPartnershipOpportunities();
  }

  void _loadDataFromDatabaseAndCache() async {
    await _loadProgramsFromDatabaseAndCache();
    await _loadSignatureEventsFromDatabaseAndCache();
    await _loadTraditionsFromDatabaseAndCache();
    await _loadPartnershipOpportunitiesFromDatabaseAndCache();
  }

  void _setupRealtimeListeners() {
    _setupProgramsRealtimeListener();
    _setupSignatureEventsRealtimeListener();
    _setupTraditionsRealtimeListener();
    _setupPartnershipOpportunitiesRealtimeListener();
  }

  // Programs methods
  Future<List<Map<String, dynamic>>?> _getCachedPrograms(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? programsJson = prefs.getString(
        'programs_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (programsJson != null) {
      List<dynamic> decodedList = jsonDecode(programsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cachePrograms(String collegeName, List<Map<String, dynamic>> programs) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String programsJson = jsonEncode(programs);
    await prefs.setString(
        'programs_${collegeName.toLowerCase().replaceAll(' ', '')}', programsJson);
    print('Programs cached for $collegeName.');
  }

  Future<void> _loadCachedPrograms() async {
    final cachedData = await _getCachedPrograms(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedPrograms = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded programs from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadProgramsFromDatabaseAndCache() async {
    if (_isLoadingPrograms) {
      return;
    }

    setState(() {
      _isLoadingPrograms = true;
    });

    print("Fetching programs for ${widget.institutionName} from database");
    final programsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_programs';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(programsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedPrograms = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingPrograms = false;
          _cachePrograms(widget.institutionName, response);
          print("Programs fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingPrograms = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingPrograms = false;
          _cachedPrograms = [];
          print("Error fetching programs for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingPrograms = false;
      }
    }
  }

  void _setupProgramsRealtimeListener() {
    final programsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_programs';
    _programsRealtimeChannel = Supabase.instance.client
        .channel('programs_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: programsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for programs of ${widget.institutionName}: ${payload.eventType}");
        _loadProgramsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Signature Events methods
  Future<List<Map<String, dynamic>>?> _getCachedSignatureEvents(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? signatureEventsJson = prefs.getString(
        'signatureevents_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (signatureEventsJson != null) {
      List<dynamic> decodedList = jsonDecode(signatureEventsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheSignatureEvents(String collegeName, List<Map<String, dynamic>> signatureEvents) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String signatureEventsJson = jsonEncode(signatureEvents);
    await prefs.setString(
        'signatureevents_${collegeName.toLowerCase().replaceAll(' ', '')}', signatureEventsJson);
    print('Signature Events cached for $collegeName.');
  }

  Future<void> _loadCachedSignatureEvents() async {
    final cachedData = await _getCachedSignatureEvents(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedSignatureEvents = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded signature events from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadSignatureEventsFromDatabaseAndCache() async {
    if (_isLoadingSignatureEvents) {
      return;
    }

    setState(() {
      _isLoadingSignatureEvents = true;
    });

    print("Fetching signature events for ${widget.institutionName} from database");
    final signatureEventsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_signatureevents';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(signatureEventsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedSignatureEvents = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingSignatureEvents = false;
          _cacheSignatureEvents(widget.institutionName, response);
          print("Signature Events fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingSignatureEvents = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingSignatureEvents = false;
          _cachedSignatureEvents = [];
          print("Error fetching signature events for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingSignatureEvents = false;
      }
    }
  }

  void _setupSignatureEventsRealtimeListener() {
    final signatureEventsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_signatureevents';
    _signatureEventsRealtimeChannel = Supabase.instance.client
        .channel('signatureevents_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: signatureEventsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for signature events of ${widget.institutionName}: ${payload.eventType}");
        _loadSignatureEventsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Traditions methods
  Future<List<Map<String, dynamic>>?> _getCachedTraditions(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? traditionsJson = prefs.getString(
        'traditions_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (traditionsJson != null) {
      List<dynamic> decodedList = jsonDecode(traditionsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheTraditions(String collegeName, List<Map<String, dynamic>> traditions) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String traditionsJson = jsonEncode(traditions);
    await prefs.setString(
        'traditions_${collegeName.toLowerCase().replaceAll(' ', '')}', traditionsJson);
    print('Traditions cached for $collegeName.');
  }

  Future<void> _loadCachedTraditions() async {
    final cachedData = await _getCachedTraditions(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedTraditions = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded traditions from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadTraditionsFromDatabaseAndCache() async {
    if (_isLoadingTraditions) {
      return;
    }

    setState(() {
      _isLoadingTraditions = true;
    });

    print("Fetching traditions for ${widget.institutionName} from database");
    final traditionsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_traditions';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(traditionsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedTraditions = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingTraditions = false;
          _cacheTraditions(widget.institutionName, response);
          print("Traditions fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingTraditions = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingTraditions = false;
          _cachedTraditions = [];
          print("Error fetching traditions for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingTraditions = false;
      }
    }
  }

  void _setupTraditionsRealtimeListener() {
    final traditionsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_traditions';
    _traditionsRealtimeChannel = Supabase.instance.client
        .channel('traditions_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: traditionsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for traditions of ${widget.institutionName}: ${payload.eventType}");
        _loadTraditionsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Partnership Opportunities methods
  Future<List<Map<String, dynamic>>?> _getCachedPartnershipOpportunities(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? partnershipOpportunitiesJson = prefs.getString(
        'partnershipopportunities_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (partnershipOpportunitiesJson != null) {
      List<dynamic> decodedList = jsonDecode(partnershipOpportunitiesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cachePartnershipOpportunities(String collegeName, List<Map<String, dynamic>> partnershipOpportunities) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String partnershipOpportunitiesJson = jsonEncode(partnershipOpportunities);
    await prefs.setString(
        'partnershipopportunities_${collegeName.toLowerCase().replaceAll(' ', '')}', partnershipOpportunitiesJson);
    print('Partnership Opportunities cached for $collegeName.');
  }

  Future<void> _loadCachedPartnershipOpportunities() async {
    final cachedData = await _getCachedPartnershipOpportunities(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedPartnershipOpportunities = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded partnership opportunities from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadPartnershipOpportunitiesFromDatabaseAndCache() async {
    if (_isLoadingPartnershipOpportunities) {
      return;
    }

    setState(() {
      _isLoadingPartnershipOpportunities = true;
    });

    print("Fetching partnership opportunities for ${widget.institutionName} from database");
    final partnershipOpportunitiesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(partnershipOpportunitiesTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedPartnershipOpportunities = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingPartnershipOpportunities = false;
          _cachePartnershipOpportunities(widget.institutionName, response);
          print("Partnership Opportunities fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingPartnershipOpportunities = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingPartnershipOpportunities = false;
          _cachedPartnershipOpportunities = [];
          print("Error fetching partnership opportunities for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingPartnershipOpportunities = false;
      }
    }
  }

  void _setupPartnershipOpportunitiesRealtimeListener() {
    final partnershipOpportunitiesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';
    _partnershipOpportunitiesRealtimeChannel = Supabase.instance.client
        .channel('partnershipopportunities_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: partnershipOpportunitiesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for partnership opportunities of ${widget.institutionName}: ${payload.eventType}");
        _loadPartnershipOpportunitiesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    void navigateToPage() {
      if (title == 'Programs') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProgramsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPrograms: _cachedPrograms,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Signature Events') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SignatureEventsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedSignatureEvents: _cachedSignatureEvents,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Traditions') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TraditionsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedTraditions: _cachedTraditions,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Partnership Opportunities') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PartnershipOpportunitiesPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedPartnershipOpportunities: _cachedPartnershipOpportunities,
              isFromDetailPage: widget.isFromDetailPage,
            ),
          ),
        );
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: navigateToPage,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Programs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Programs', Icons.list_alt, theme, isFromDetailPage),
                _buildGridItem(context, 'Signature Events', Icons.event_available, theme, isFromDetailPage),
                _buildGridItem(context, 'Traditions', Icons.history_edu, theme, isFromDetailPage),
                _buildGridItem(context, 'Partnership Opportunities', Icons.handshake, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}