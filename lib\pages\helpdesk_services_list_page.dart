import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'helpdesk_services_detail_page.dart';

class HelpdeskServicesListPage extends StatefulWidget {
  final String helpdeskName;
  final List<Map<String, dynamic>> services;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;

  const HelpdeskServicesListPage({
    Key? key,
    required this.helpdeskName,
    required this.services,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<HelpdeskServicesListPage> createState() => _HelpdeskServicesListPageState();
}

class _HelpdeskServicesListPageState extends State<HelpdeskServicesListPage> {
  late List<Map<String, dynamic>> _services;
  bool _isLoading = false;
  late RealtimeChannel _servicesRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _services = List<Map<String, dynamic>>.from(widget.services);
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _servicesRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    final servicesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_services';
    _servicesRealtimeChannel = Supabase.instance.client
        .channel('services_list_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: servicesTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert ||
            payload.eventType == PostgresChangeEvent.update ||
            payload.eventType == PostgresChangeEvent.delete) {
          _refreshServices();
        }
      },
    ).subscribe();
  }

  Future<void> _refreshServices() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final servicesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_services';
    final helpdeskName = widget.helpdeskName;

    try {
      final response = await Supabase.instance.client
          .from(servicesTableName)
          .select('*')
          .eq('department', helpdeskName)
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _services = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      }
    } catch (error) {
      print('Error refreshing services: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToServiceDetail(Map<String, dynamic> service) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HelpdeskServicesDetailPage(
          serviceDetailName: service['fullname'] ?? '',
          detailPageName: service['id']?.toString() ?? '',
          helpdeskName: widget.helpdeskName,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          service: service,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${widget.helpdeskName} Services',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshServices,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _services.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No services available for ${widget.helpdeskName}.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _services.length,
                    itemBuilder: (context, index) {
                      final service = _services[index];
                      return Card(
                        color: theme.colorScheme.surface,
                        surfaceTintColor: Colors.transparent,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: InkWell(
                          onTap: () => _navigateToServiceDetail(service),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CircleAvatar(
                                  radius: 24,
                                  backgroundColor: currentIsDarkMode
                                      ? Colors.white.withOpacity(0.1)
                                      : Colors.black.withOpacity(0.1),
                                  child: Icon(
                                    Icons.settings_suggest_outlined,
                                    color: currentIsDarkMode ? Colors.white : Colors.black,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        service['fullname'] ?? 'Unnamed Service',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                      if (service['about'] != null && service['about'].toString().isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 4),
                                          child: Text(
                                            service['about'],
                                            style: TextStyle(
                                              color: theme.colorScheme.onSurfaceVariant,
                                              fontSize: 14,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      if (service['department'] != null && service['department'].toString().isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 4),
                                          child: Text(
                                            service['department'],
                                            style: TextStyle(
                                              color: theme.colorScheme.onSurfaceVariant,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16,
                                  color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}