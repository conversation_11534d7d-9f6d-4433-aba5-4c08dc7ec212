import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MenuDetailPage extends StatefulWidget {
  final Map<String, dynamic> menu;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String selectedDay;

  const MenuDetailPage({
    Key? key,
    required this.menu,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.selectedDay,
  }) : super(key: key);

  @override
  State<MenuDetailPage> createState() => _MenuDetailPageState();
}

class _MenuDetailPageState extends State<MenuDetailPage> {
  late RealtimeChannel _menuRealtimeChannel;
  String _dayColumn = '_mon';

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
    _setDayColumn();
  }

  void _setDayColumn() {
    if (widget.selectedDay == 'Today') {
      // Use the current day
      final now = DateTime.now();
      int dayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday
      
      switch (dayOfWeek) {
        case 1: _dayColumn = '_mon'; break;
        case 2: _dayColumn = '_tue'; break;
        case 3: _dayColumn = '_wed'; break;
        case 4: _dayColumn = '_thur'; break;
        case 5: _dayColumn = '_fri'; break;
        case 6: _dayColumn = '_sat'; break;
        case 7: _dayColumn = '_sun'; break;
        default: _dayColumn = '_mon';
      }
    } else {
      // Use the selected day
      _dayColumn = '_${widget.selectedDay.toLowerCase().substring(0, 3)}';
    }
  }

  @override
  void dispose() {
    _menuRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _menuRealtimeChannel = Supabase.instance.client
        .channel('menu_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'menus',
      callback: (payload) async {
        // Manual filtering for the specific menu
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.menu['id']) {
          print("Realtime update received for menu detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshMenu();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshMenu() async {
    try {
      final response = await Supabase.instance.client
          .from('menus')
          .select('*')
          .eq('id', widget.menu['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's menu with the new data
          widget.menu.clear();
          widget.menu.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing menu: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.menu['fullname'] ?? 'Unknown';
    final String diningLocation = widget.menu['dininglocation'] ?? '';
    final String diningStation = widget.menu['diningstation'] ?? '';
    final double price = widget.menu['price'] != null ? double.tryParse(widget.menu['price'].toString()) ?? 0.0 : 0.0;
    final String breakfast = widget.menu['breakfast'] ?? '';
    final String lunch = widget.menu['lunch'] ?? '';
    final String dinner = widget.menu['dinner'] ?? '';
    final String about = widget.menu['about'] ?? '';
    final String allergens = widget.menu['allergens'] ?? '';
    final String ingredients = widget.menu['ingredients'] ?? '';
    
    // Get availability for each day of the week
    final bool availableMonday = widget.menu['_mon'] != null && widget.menu['_mon'].toString().isNotEmpty;
    final bool availableTuesday = widget.menu['_tue'] != null && widget.menu['_tue'].toString().isNotEmpty;
    final bool availableWednesday = widget.menu['_wed'] != null && widget.menu['_wed'].toString().isNotEmpty;
    final bool availableThursday = widget.menu['_thur'] != null && widget.menu['_thur'].toString().isNotEmpty;
    final bool availableFriday = widget.menu['_fri'] != null && widget.menu['_fri'].toString().isNotEmpty;
    final bool availableSaturday = widget.menu['_sat'] != null && widget.menu['_sat'].toString().isNotEmpty;
    final bool availableSunday = widget.menu['_sun'] != null && widget.menu['_sun'].toString().isNotEmpty;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Menu details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.restaurant_menu,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (diningLocation.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      diningLocation,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                                if (diningStation.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      diningStation,
                                      style: TextStyle(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                if (price > 0)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      'Price: \$${price.toStringAsFixed(2)}',
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Meal information
                      if (breakfast.isNotEmpty)
                        _buildDetailRow(theme, Icons.breakfast_dining, 'Breakfast', breakfast),
                      if (lunch.isNotEmpty)
                        _buildDetailRow(theme, Icons.lunch_dining, 'Lunch', lunch),
                      if (dinner.isNotEmpty)
                        _buildDetailRow(theme, Icons.dinner_dining, 'Dinner', dinner),
                      
                      // Availability information
                      const SizedBox(height: 16),
                      Text(
                        'Available on:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          _buildDayChip(theme, 'Monday', availableMonday),
                          _buildDayChip(theme, 'Tuesday', availableTuesday),
                          _buildDayChip(theme, 'Wednesday', availableWednesday),
                          _buildDayChip(theme, 'Thursday', availableThursday),
                          _buildDayChip(theme, 'Friday', availableFriday),
                          _buildDayChip(theme, 'Saturday', availableSaturday),
                          _buildDayChip(theme, 'Sunday', availableSunday),
                        ],
                      ),
                      
                      // Allergens and ingredients
                      if (allergens.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Allergens:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          allergens,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      
                      if (ingredients.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Ingredients:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          ingredients,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayChip(ThemeData theme, String day, bool isAvailable) {
    return Chip(
      label: Text(
        day,
        style: TextStyle(
          color: isAvailable 
              ? theme.colorScheme.onPrimary 
              : theme.colorScheme.onSurfaceVariant,
          fontSize: 12,
        ),
      ),
      backgroundColor: isAvailable 
          ? theme.colorScheme.primary 
          : theme.colorScheme.surfaceVariant,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}
