import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'login_page.dart';
import 'room_equipment_page.dart';
import 'room_assignments_page.dart';
import 'building_detail_page.dart';

class RoomDetailPage extends StatefulWidget {
  final Map<String, dynamic> room;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const RoomDetailPage({
    Key? key,
    required this.room,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<RoomDetailPage> createState() => _RoomDetailPageState();
}

class _RoomDetailPageState extends State<RoomDetailPage> {
  late RealtimeChannel _roomRealtimeChannel;
  List<Map<String, dynamic>> _roomEquipment = [];
  List<Map<String, dynamic>> _roomAssignments = [];
  bool _isLoadingRoomEquipment = false;
  bool _isLoadingRoomAssignments = false;
  Map<String, dynamic>? _buildingData;
  bool _isLoadingBuilding = false;

  @override
  void initState() {
    super.initState();
    _setupRoomRealtimeListener();
    _loadRoomEquipment();
    _loadRoomAssignments();
    _loadBuildingData();
  }

  @override
  void dispose() {
    _roomRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupRoomRealtimeListener() {
    final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
    print('Setting up realtime listener for room detail: $roomsTableName');
    _roomRealtimeChannel = Supabase.instance.client
        .channel('room_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomsTableName,
          callback: (payload) {
            // Check if this change is for the current room
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.room['id']) {
              print('Received update for current room: ${widget.room['fullname']}');
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedRoomData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the room is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedRoomData() async {
    try {
      final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching updated data from table: $roomsTableName for room ID: ${widget.room['id']}');

      final response = await Supabase.instance.client
          .from(roomsTableName)
          .select('*')
          .eq('id', widget.room['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedRoom = Map.from(response);
        print('Successfully fetched updated room data');

        // Update the widget.room with the new data
        setState(() {
          widget.room.clear(); // Clear old data
          widget.room.addAll(updatedRoom); // Add updated data
          print("Room data updated in detail page for ${widget.room['fullname']}");
          _updateRoomsCache(updatedRoom); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated room data: $e');
    }
  }

  Future<void> _updateRoomsCache(Map<String, dynamic> updatedRoom) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rooms_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Updating cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedRooms = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific room in the cache
        bool found = false;
        for (var room in cachedRooms) {
          if (room['id'] == updatedRoom['id']) {
            updatedCache.add(updatedRoom);
            found = true;
            print('Updated room in cache: ${updatedRoom['fullname']}');
          } else {
            updatedCache.add(Map<String, dynamic>.from(room));
          }
        }

        if (!found) {
          print('Room not found in cache, adding it');
          updatedCache.add(updatedRoom);
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
        print('Successfully updated rooms cache');
      } else {
        // If cache doesn't exist, create it with just this room
        print('Cache not found, creating new cache with this room');
        await prefs.setString(cacheKey, jsonEncode([updatedRoom]));
      }
    } catch (e) {
      print('Error updating rooms cache: $e');
    }
  }

  Future<void> _loadRoomEquipment() async {
    if (_isLoadingRoomEquipment) return;

    setState(() {
      _isLoadingRoomEquipment = true;
    });

    try {
      final roomEquipmentTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
      print('Fetching room equipment for room: ${widget.room['fullname']}');

      final response = await Supabase.instance.client
          .from(roomEquipmentTableName)
          .select('*')
          .eq('room', widget.room['fullname'])
          .order('fullname', ascending: true);

      final roomEquipment = List<Map<String, dynamic>>.from(response);
      print('Fetched ${roomEquipment.length} equipment items for room: ${widget.room['fullname']}');

      setState(() {
        _roomEquipment = roomEquipment;
        _isLoadingRoomEquipment = false;
      });
    } catch (e) {
      print('Error fetching room equipment: $e');
      setState(() {
        _isLoadingRoomEquipment = false;
      });
    }
  }

  Future<void> _loadRoomAssignments() async {
    if (_isLoadingRoomAssignments) return;

    setState(() {
      _isLoadingRoomAssignments = true;
    });

    try {
      final roomAssignmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomassignments';
      print('Fetching room assignments for room: ${widget.room['fullname']}');

      final response = await Supabase.instance.client
          .from(roomAssignmentsTableName)
          .select('*')
          .eq('room', widget.room['fullname'])
          .order('fullname', ascending: true);

      final roomAssignments = List<Map<String, dynamic>>.from(response);
      print('Fetched ${roomAssignments.length} assignments for room: ${widget.room['fullname']}');

      setState(() {
        _roomAssignments = roomAssignments;
        _isLoadingRoomAssignments = false;
      });
    } catch (e) {
      print('Error fetching room assignments: $e');
      setState(() {
        _isLoadingRoomAssignments = false;
      });
    }
  }

  Future<void> _loadBuildingData() async {
    if (_isLoadingBuilding) return;

    setState(() {
      _isLoadingBuilding = true;
    });

    try {
      final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_buildings';
      print('Fetching building data for: ${widget.room['building']}');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .eq('fullname', widget.room['building'])
          .single();

      setState(() {
        _buildingData = response;
        _isLoadingBuilding = false;
      });
      print('Fetched building data for: ${widget.room['building']}');
    } catch (e) {
      print('Error fetching building data: $e');
      setState(() {
        _isLoadingBuilding = false;
      });
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch URL')),
        );
      }
    }
  }

  Future<void> _launchMaps() async {
    final double latitude = widget.room['latitude'] ?? 0.0;
    final double longitude = widget.room['longitude'] ?? 0.0;

    if (latitude == 0.0 && longitude == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location coordinates not available')),
      );
      return;
    }

    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch maps')),
        );
      }
    }
  }

  Future<void> _launchPhone(String phone) async {
    if (phone.isEmpty) return;

    final Uri url = Uri.parse('tel:$phone');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch phone')),
        );
      }
    }
  }

  void _viewRoomEquipment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RoomEquipmentPage(
          collegeNameForTable: widget.collegeNameForTable,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          preloadedRoomEquipment: _roomEquipment,
          isFromDetailPage: true,
          roomFilter: widget.room['fullname'],
        ),
      ),
    );
  }

  void _viewRoomAssignments() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RoomAssignmentsPage(
          collegeNameForTable: widget.collegeNameForTable,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          preloadedRoomAssignments: _roomAssignments,
          isFromDetailPage: true,
          roomFilter: widget.room['fullname'],
        ),
      ),
    );
  }

  void _viewBuilding() {
    if (_buildingData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuildingDetailPage(
            building: _buildingData!,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.room['fullname'] ?? 'Unnamed Room';
    final String building = widget.room['building'] ?? '';
    final String roomType = widget.room['roomtype'] ?? '';
    final String dimensions = widget.room['dimensions'] ?? '';
    final int capacity = widget.room['capacity'] ?? 0;
    final String phone = widget.room['phone'] ?? '';
    final String fax = widget.room['fax'] ?? '';
    final String about = widget.room['about'] ?? '';
    final double latitude = widget.room['latitude'] ?? 0.0;
    final double longitude = widget.room['longitude'] ?? 0.0;

    final bool hasLocation = latitude != 0.0 && longitude != 0.0;
    final bool hasPhone = phone.isNotEmpty;
    final bool hasFax = fax.isNotEmpty;
    final bool hasEquipment = _roomEquipment.isNotEmpty;
    final bool hasAssignments = _roomAssignments.isNotEmpty;
    final bool hasBuilding = building.isNotEmpty && _buildingData != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasLocation)
              SizedBox(
                height: 200,
                child: FlutterMap(
                  options: MapOptions(
                    initialCenter: LatLng(latitude, longitude),
                    initialZoom: 16.0,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: currentIsDarkMode
                          ? 'https://tile.jawg.io/jawg-dark/{z}/{x}/{y}{r}.png?access-token=your-access-token'
                          : 'https://tile.jawg.io/jawg-light/{z}/{x}/{y}{r}.png?access-token=your-access-token',
                      subdomains: const ['a', 'b', 'c'],
                    ),
                    MarkerLayer(
                      markers: [
                        Marker(
                          width: 40.0,
                          height: 40.0,
                          point: LatLng(latitude, longitude),
                          child: const Icon(
                            Icons.location_on,
                            color: Colors.red,
                            size: 40.0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (building.isNotEmpty) ...[
                    const Text(
                      'Building:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(building),
                        if (hasBuilding)
                          TextButton(
                            onPressed: _viewBuilding,
                            child: const Text('View Building'),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (roomType.isNotEmpty) ...[
                    const Text(
                      'Room Type:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(roomType),
                    const SizedBox(height: 16),
                  ],
                  if (dimensions.isNotEmpty) ...[
                    const Text(
                      'Dimensions:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(dimensions),
                    const SizedBox(height: 16),
                  ],
                  if (capacity > 0) ...[
                    const Text(
                      'Capacity:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('$capacity'),
                    const SizedBox(height: 16),
                  ],
                  if (phone.isNotEmpty) ...[
                    const Text(
                      'Phone:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(phone),
                        IconButton(
                          icon: const Icon(Icons.phone),
                          onPressed: () => _launchPhone(phone),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (fax.isNotEmpty) ...[
                    const Text(
                      'Fax:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(fax),
                    const SizedBox(height: 16),
                  ],
                  if (about.isNotEmpty) ...[
                    const Text(
                      'About:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(about),
                    const SizedBox(height: 16),
                  ],
                  if (hasEquipment) ...[
                    const Text(
                      'Equipment:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('This room has ${_roomEquipment.length} equipment items'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _viewRoomEquipment,
                      child: const Text('View Equipment'),
                    ),
                    const SizedBox(height: 16),
                  ] else if (_isLoadingRoomEquipment) ...[
                    const Text(
                      'Equipment:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Center(child: CircularProgressIndicator()),
                    const SizedBox(height: 16),
                  ],
                  if (hasAssignments) ...[
                    const Text(
                      'Assignments:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('This room has ${_roomAssignments.length} assignments'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _viewRoomAssignments,
                      child: const Text('View Assignments'),
                    ),
                    const SizedBox(height: 16),
                  ] else if (_isLoadingRoomAssignments) ...[
                    const Text(
                      'Assignments:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Center(child: CircularProgressIndicator()),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                if (hasLocation)
                  IconButton(
                    icon: Icon(
                      Icons.directions,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchMaps,
                  ),
                if (hasPhone)
                  IconButton(
                    icon: Icon(
                      Icons.phone,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () => _launchPhone(phone),
                  ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
