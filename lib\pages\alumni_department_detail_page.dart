// alumni_department_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';

import 'login_page.dart';

class AlumniDepartmentDetailPage extends StatelessWidget {
  final Map<String, dynamic> department;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AlumniDepartmentDetailPage({
    Key? key,
    required this.department,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      // If we can't launch the email app, copy to clipboard
      await Clipboard.setData(ClipboardData(text: email));
    }
  }

  Future<void> _launchPhone(String phone) async {
    final Uri phoneUri = Uri(
      scheme: 'tel',
      path: phone,
    );
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      // If we can't launch the phone app, copy to clipboard
      await Clipboard.setData(ClipboardData(text: phone));
    }
  }

  Future<void> _launchWebsite(String website) async {
    if (website.isEmpty) return;
    
    final Uri uri = Uri.parse(website.startsWith('http') ? website : 'https://$website');
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _shareDepartment() async {
    final String fullname = department['fullname'] ?? 'Unnamed Department';
    final String school = department['school'] ?? '';
    final String about = department['about'] ?? '';
    final String contactEmail = department['contactemail'] ?? '';
    final String contactPhone = department['contactphone'] ?? '';
    final String website = department['website'] ?? '';
    
    final StringBuffer shareText = StringBuffer();
    shareText.writeln('Alumni Department: $fullname');
    if (school.isNotEmpty) {
      shareText.writeln('School: $school');
    }
    if (about.isNotEmpty) {
      shareText.writeln('\n$about');
    }
    if (contactEmail.isNotEmpty || contactPhone.isNotEmpty || website.isNotEmpty) {
      shareText.writeln('\nContact Information:');
      if (contactEmail.isNotEmpty) {
        shareText.writeln('Email: $contactEmail');
      }
      if (contactPhone.isNotEmpty) {
        shareText.writeln('Phone: $contactPhone');
      }
      if (website.isNotEmpty) {
        shareText.writeln('Website: $website');
      }
    }
    
    await Share.share(shareText.toString());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    final String fullname = department['fullname'] ?? 'Unnamed Department';
    final String about = department['about'] ?? '';
    final String school = department['school'] ?? '';
    final String imageUrl = department['image_url'] ?? '';
    final String contactEmail = department['contactemail'] ?? '';
    final String contactPhone = department['contactphone'] ?? '';
    final String website = department['website'] ?? '';
    final String alumniCoordinator = department['alumnicoordinator'] ?? '';
    final String alumniEvents = department['alumnievents'] ?? '';
    final String alumniOpportunities = department['alumniopportunities'] ?? '';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Department Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.share,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _shareDepartment,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Department image
            if (imageUrl.isNotEmpty)
              Container(
                width: double.infinity,
                height: 200,
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Center(child: CircularProgressIndicator()),
                  errorWidget: (context, url, error) => Container(
                    color: theme.colorScheme.surfaceVariant,
                    child: Icon(
                      Icons.business,
                      size: 50,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              )
            else
              Container(
                width: double.infinity,
                height: 150,
                color: theme.colorScheme.surfaceVariant,
                child: Center(
                  child: Icon(
                    Icons.business,
                    size: 50,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            
            // Department details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and school
                  Text(
                    fullname,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (school.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      school,
                      style: TextStyle(
                        fontSize: 18,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 16),
                  
                  // About section
                  if (about.isNotEmpty)
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 20,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'About',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              about,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Alumni coordinator
                  if (alumniCoordinator.isNotEmpty)
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.person,
                                  size: 20,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Alumni Coordinator',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              alumniCoordinator,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Alumni events
                  if (alumniEvents.isNotEmpty)
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.event,
                                  size: 20,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Alumni Events',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              alumniEvents,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Alumni opportunities
                  if (alumniOpportunities.isNotEmpty)
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.volunteer_activism,
                                  size: 20,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Alumni Opportunities',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              alumniOpportunities,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Contact information
                  if (contactEmail.isNotEmpty || contactPhone.isNotEmpty || website.isNotEmpty)
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.contact_phone,
                                  size: 20,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Contact Information',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            if (contactEmail.isNotEmpty) ...[
                              InkWell(
                                onTap: () => _launchEmail(contactEmail),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.email,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          contactEmail,
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: theme.colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.content_copy,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                            if (contactPhone.isNotEmpty) ...[
                              InkWell(
                                onTap: () => _launchPhone(contactPhone),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.phone,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        contactPhone,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: theme.colorScheme.primary,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Icon(
                                        Icons.content_copy,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                            if (website.isNotEmpty) ...[
                              InkWell(
                                onTap: () => _launchWebsite(website),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.language,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          website,
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: theme.colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.open_in_new,
                                        size: 16,
                                        color: theme.colorScheme.primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
