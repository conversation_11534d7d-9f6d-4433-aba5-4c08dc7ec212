import 'dart:io' as io;
import 'dart:typed_data';
import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;

class TextRecognitionHelper {
  // Create a singleton instance
  static final TextRecognitionHelper _instance = TextRecognitionHelper._internal();
  factory TextRecognitionHelper() => _instance;
  TextRecognitionHelper._internal();

  // Map to store text recognizers for different scripts
  final Map<TextRecognitionScript, TextRecognizer> _textRecognizers = {};

  // Get or create a text recognizer for a specific script
  TextRecognizer getRecognizer(TextRecognitionScript script) {
    if (!_textRecognizers.containsKey(script)) {
      _textRecognizers[script] = TextRecognizer(script: script);
    }
    return _textRecognizers[script]!;
  }

  // Default recognizer is Latin
  TextRecognizer get _textRecognizer => getRecognizer(TextRecognitionScript.latin);

  // Process an image file and extract text with enhanced handwriting recognition
  Future<String> processImageForText(Uint8List? imageBytes, String? filePath) async {
    InputImage? inputImage;
    io.File? tempImageFile;
    io.File? enhancedImageFile;

    try {
      Uint8List? processedImageBytes = imageBytes;

      // Preprocess the image to improve handwriting recognition
      if (imageBytes != null) {
        processedImageBytes = await _preprocessImage(imageBytes);
      }

      // Create InputImage from bytes or file path
      if (processedImageBytes != null) {
        if (kIsWeb) {
          // Web: Use fromBytes with estimated dimensions
          try {
            // Try to get actual image dimensions
            final codec = await instantiateImageCodec(processedImageBytes);
            final frameInfo = await codec.getNextFrame();
            final image = frameInfo.image;

            inputImage = InputImage.fromBytes(
              bytes: processedImageBytes,
              metadata: InputImageMetadata(
                size: Size(image.width.toDouble(), image.height.toDouble()),
                rotation: InputImageRotation.rotation0deg,
                format: InputImageFormat.bgra8888,
                bytesPerRow: image.width * 4
              )
            );

            // Dispose the image to free resources
            image.dispose();
          } catch (e) {
            // Fallback to estimated dimensions
            final estimatedWidth = 1000;  // Higher resolution for better recognition
            final estimatedHeight = 800;

            inputImage = InputImage.fromBytes(
              bytes: processedImageBytes,
              metadata: InputImageMetadata(
                size: Size(estimatedWidth.toDouble(), estimatedHeight.toDouble()),
                rotation: InputImageRotation.rotation0deg,
                format: InputImageFormat.bgra8888,
                bytesPerRow: estimatedWidth * 4
              )
            );
          }
        } else {
          // Mobile: Save to temp file for better processing
          final tempDir = await getTemporaryDirectory();
          final tempFilePath = path.join(tempDir.path, "temp_image_${DateTime.now().millisecondsSinceEpoch}.jpg");
          tempImageFile = io.File(tempFilePath);
          await tempImageFile.writeAsBytes(processedImageBytes);
          inputImage = InputImage.fromFilePath(tempFilePath);
        }
      } else if (filePath != null && !kIsWeb) {
        // Mobile: Use file path directly but preprocess the image first
        final originalFile = io.File(filePath);
        final originalBytes = await originalFile.readAsBytes();
        final processedBytes = await _preprocessImage(originalBytes);

        final tempDir = await getTemporaryDirectory();
        final enhancedFilePath = path.join(tempDir.path, "enhanced_${DateTime.now().millisecondsSinceEpoch}.jpg");
        enhancedImageFile = io.File(enhancedFilePath);
        await enhancedImageFile.writeAsBytes(processedBytes);
        inputImage = InputImage.fromFilePath(enhancedFilePath);
      } else {
        throw Exception('No image data available');
      }

      // Try multiple scripts for better handwriting recognition
      final results = await _recognizeWithMultipleScripts(inputImage);

      // Clean up temp files if created
      await _cleanupTempFiles([tempImageFile, enhancedImageFile]);

      return results;
    } catch (e) {
      // Clean up temp files if created
      await _cleanupTempFiles([tempImageFile, enhancedImageFile]);

      throw Exception('Text recognition failed: $e');
    }
  }

  // Preprocess image to improve handwriting recognition
  Future<Uint8List> _preprocessImage(Uint8List imageBytes) async {
    try {
      // Decode the image
      final decodedImage = img.decodeImage(imageBytes);
      if (decodedImage == null) return imageBytes;

      // Apply preprocessing steps
      var processedImage = decodedImage;

      // 1. Resize if too large (maintain aspect ratio)
      final maxDimension = 2000; // Max width or height
      if (processedImage.width > maxDimension || processedImage.height > maxDimension) {
        final aspectRatio = processedImage.width / processedImage.height;
        if (processedImage.width > processedImage.height) {
          processedImage = img.copyResize(
            processedImage,
            width: maxDimension,
            height: (maxDimension / aspectRatio).round(),
            interpolation: img.Interpolation.linear,
          );
        } else {
          processedImage = img.copyResize(
            processedImage,
            width: (maxDimension * aspectRatio).round(),
            height: maxDimension,
            interpolation: img.Interpolation.linear,
          );
        }
      }

      // 2. Convert to grayscale for better text recognition
      processedImage = img.grayscale(processedImage);

      // 3. Increase contrast to make handwriting more visible
      processedImage = img.contrast(processedImage, contrast: 1.5);

      // 4. Apply adaptive thresholding to separate text from background
      processedImage = _adaptiveThreshold(processedImage, 15, 5);

      // 5. Apply slight blur to reduce noise
      processedImage = img.gaussianBlur(processedImage, radius: 1);

      // Encode back to bytes
      return Uint8List.fromList(img.encodeJpg(processedImage, quality: 90));
    } catch (e) {
      print('Image preprocessing failed: $e');
      // Return original if preprocessing fails
      return imageBytes;
    }
  }

  // Apply adaptive thresholding to improve text contrast
  img.Image _adaptiveThreshold(img.Image src, int kernelSize, int constant) {
    final result = img.Image(
      width: src.width,
      height: src.height,
      numChannels: src.numChannels,
    );

    for (int y = 0; y < src.height; y++) {
      for (int x = 0; x < src.width; x++) {
        // Calculate local mean
        int sum = 0;
        int count = 0;

        for (int ky = math.max(0, y - kernelSize ~/ 2);
            ky <= math.min(src.height - 1, y + kernelSize ~/ 2);
            ky++) {
          for (int kx = math.max(0, x - kernelSize ~/ 2);
              kx <= math.min(src.width - 1, x + kernelSize ~/ 2);
              kx++) {
            final pixel = src.getPixel(kx, ky);
            // Convert luminance to int
            sum += img.getLuminance(pixel).toInt();
            count++;
          }
        }

        final mean = sum ~/ count;
        final pixel = src.getPixel(x, y);
        final luminance = img.getLuminance(pixel).toInt();

        // Apply threshold
        if (luminance < mean - constant) {
          result.setPixel(x, y, img.ColorRgb8(0, 0, 0)); // Black
        } else {
          result.setPixel(x, y, img.ColorRgb8(255, 255, 255)); // White
        }
      }
    }

    return result;
  }

  // Try multiple scripts to improve handwriting recognition
  Future<String> _recognizeWithMultipleScripts(InputImage inputImage) async {
    // First try with Latin script (most common)
    final latinRecognizer = getRecognizer(TextRecognitionScript.latin);
    final latinText = await latinRecognizer.processImage(inputImage);

    // If we got a reasonable amount of text, return it
    if (latinText.text.trim().length > 10) {
      return latinText.text;
    }

    // Try with other scripts if Latin didn't yield good results
    final scripts = [
      TextRecognitionScript.chinese,
      // TextRecognitionScript.devanagari, // Not available in current version
      TextRecognitionScript.japanese,
      TextRecognitionScript.korean,
    ];

    String bestResult = latinText.text;
    int bestLength = latinText.text.trim().length;

    // Try each script and keep the one with the most text
    for (final script in scripts) {
      try {
        final recognizer = getRecognizer(script);
        final recognizedText = await recognizer.processImage(inputImage);
        final text = recognizedText.text.trim();

        // If this script produced more text, it might be better
        if (text.length > bestLength) {
          bestResult = recognizedText.text;
          bestLength = text.length;
        }
      } catch (e) {
        // Skip if this script recognizer fails
        print('Recognition with script $script failed: $e');
      }
    }

    return bestResult;
  }

  // Clean up temporary files
  Future<void> _cleanupTempFiles(List<io.File?> files) async {
    for (final file in files) {
      if (file != null && !kIsWeb && await file.exists()) {
        try {
          await file.delete();
        } catch (_) {}
      }
    }
  }

  // Process multiple images and combine the text
  Future<String> processMultipleImages(List<Uint8List> imageBytesList) async {
    final StringBuffer combinedText = StringBuffer();

    for (int i = 0; i < imageBytesList.length; i++) {
      try {
        final text = await processImageForText(imageBytesList[i], null);
        combinedText.writeln('--- Page ${i + 1} ---');
        combinedText.writeln(text);
        combinedText.writeln();
      } catch (e) {
        combinedText.writeln('--- Page ${i + 1} (Error) ---');
        combinedText.writeln('[Error processing image: $e]');
        combinedText.writeln();
      }
    }

    return combinedText.toString();
  }

  // Dispose resources
  void dispose() {
    for (final recognizer in _textRecognizers.values) {
      recognizer.close();
    }
    _textRecognizers.clear();
  }
}
