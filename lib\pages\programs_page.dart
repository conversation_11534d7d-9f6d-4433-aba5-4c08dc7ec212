import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'program_detail_page.dart';

class ProgramsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPrograms;
  final bool isFromDetailPage;

  const ProgramsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPrograms,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ProgramsPage> createState() => _ProgramsPageState();
}

class _ProgramsPageState extends State<ProgramsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('programs_list');
  List<Map<String, dynamic>> _programs = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    print("ProgramsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant ProgramsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("ProgramsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("ProgramsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("ProgramsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPrograms != null && widget.preloadedPrograms!.isNotEmpty) {
      print("Preloaded programs found, using them.");
      setState(() {
        _programs = List<Map<String, dynamic>>.from(widget.preloadedPrograms!);
        _programs.forEach((program) {
          program['_isImageLoading'] = false;
        });
        _programs.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedPrograms!.length == _pageSize;
      });
      _loadProgramsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded programs or empty list, loading from database.");
      _loadProgramsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadProgramsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadProgramsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final programsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_programs';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(programsTableName)
          .select('*');

      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.ilike('fullname', '%$_searchQuery%');
      }

      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedPrograms =
          await _updateProgramImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _programs = updatedPrograms;
        } else {
          _programs.addAll(updatedPrograms);
        }
        _programs.forEach((program) {
          program['_isImageLoading'] = false;
        });
        _programs.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });

      // Cache the programs
      _cachePrograms(_programs);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching programs: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateProgramImageUrls(
      List<Map<String, dynamic>> programs) async {
    List<Future<void>> futures = [];
    for (final program in programs) {
      if (program['image_url'] == null ||
          program['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(program));
      }
    }
    await Future.wait(futures);
    return programs;
  }

  void _setupRealtime() {
    final programsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_programs';
    _realtimeChannel = Supabase.instance.client
        .channel('programs')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: programsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newProgramId = payload.newRecord['id'];
          final newProgramResponse = await Supabase.instance.client
              .from(programsTableName)
              .select('*')
              .eq('id', newProgramId)
              .single();
          if (mounted) {
            Map<String, dynamic> newProgram = Map.from(newProgramResponse);
            final updatedProgram = await _updateProgramImageUrls([newProgram]);
            setState(() {
              _programs = [..._programs, updatedProgram.first];
              updatedProgram.first['_isImageLoading'] = false;
              _programs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedProgramId = payload.newRecord['id'];
          final updatedProgramResponse = await Supabase.instance.client
              .from(programsTableName)
              .select('*')
              .eq('id', updatedProgramId)
              .single();
          if (mounted) {
            final updatedProgram = Map<String, dynamic>.from(updatedProgramResponse);
            setState(() {
              _programs = _programs.map((program) {
                return program['id'] == updatedProgram['id'] ? updatedProgram : program;
              }).toList();
              _programs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedProgramId = payload.oldRecord['id'];
          setState(() {
            _programs.removeWhere((program) => program['id'] == deletedProgramId);
          });
        }
      },
    ).subscribe();
  }

  Future<void> _cachePrograms(List<Map<String, dynamic>> programs) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String programsJson = jsonEncode(programs);
      await prefs.setString(
          'programs_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          programsJson);
    } catch (e) {
      print('Error caching programs: $e');
    }
  }

  void _launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _launchPhone(String phone) async {
    final url = 'tel:$phone';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch phone app')),
      );
    }
  }

  void _launchWhatsApp(String whatsapp) async {
    final url = 'https://wa.me/$whatsapp';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch WhatsApp')),
      );
    }
  }

  void _launchEmail(String email) async {
    final url = 'mailto:$email';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch email app')),
      );
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMorePrograms();
    }
  }

  Future<void> _loadMorePrograms() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadProgramsFromSupabase(initialLoad: false);
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> program) async {
    if (program['_isImageLoading'] == true) {
      print('Image loading already in progress for ${program['fullname']}, skipping.');
      return;
    }
    if (program['image_url'] != null &&
        program['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${program['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      program['_isImageLoading'] = true;
    });

    final fullname = program['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeProgramBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/programs';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeProgramBucket');
    print('Image URL before fetch: ${program['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeProgramBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeProgramBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        program['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        program['_isImageLoading'] = false;
        print('Setting image_url for ${program['fullname']} to: ${program['image_url']}');
      });
    } else {
      program['_isImageLoading'] = false;
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> program) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProgramDetailPage(
            program: program,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("ProgramsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Programs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('Search Programs'),
                  content: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Enter program name...',
                      prefixIcon: const Icon(Icons.search),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _page = 0;
                        _loadProgramsFromSupabase(initialLoad: true);
                      },
                      child: Text('Search'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading && _programs.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadProgramsFromSupabase(initialLoad: true),
              child: _programs.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No programs available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _programs.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _programs.length) {
                          final program = _programs[index];
                          return VisibilityDetector(
                            key: Key('program_${program['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (program['image_url'] == null ||
                                      program['image_url'] == 'assets/placeholder_image.png') &&
                                  !program['_isImageLoading']) {
                                _fetchImageUrl(program);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: program['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  program['fullname'] ?? 'Unnamed Program',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    program['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, program),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),

    );
  }
}
