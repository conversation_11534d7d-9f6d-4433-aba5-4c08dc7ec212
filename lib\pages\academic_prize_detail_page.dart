import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AcademicPrizeDetailPage extends StatefulWidget {
  final Map<String, dynamic> prize;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AcademicPrizeDetailPage({
    Key? key,
    required this.prize,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AcademicPrizeDetailPage> createState() => _AcademicPrizeDetailPageState();
}

class _AcademicPrizeDetailPageState extends State<AcademicPrizeDetailPage> {
  late RealtimeChannel _prizeRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _prizeRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _prizeRealtimeChannel = Supabase.instance.client
        .channel('academic_prize_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'academicprizes',
      callback: (payload) async {
        // Manual filtering for the specific prize
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.prize['id']) {
          print("Realtime update received for academic prize detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshPrize();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshPrize() async {
    try {
      final response = await Supabase.instance.client
          .from('academicprizes')
          .select('*')
          .eq('id', widget.prize['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's prize with the new data
          widget.prize.clear();
          widget.prize.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing academic prize: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.prize['fullname'] ?? 'Unknown';
    final String about = widget.prize['about'] ?? '';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Prize details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.emoji_events,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
