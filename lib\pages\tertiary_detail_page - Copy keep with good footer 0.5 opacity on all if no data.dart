// tertiary_detail_page.dart
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart'; // Import shared_preferences
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart'; // Import intl for date formatting


import 'tertiary_start_page.dart';
import 'tertiary_updates_page.dart';
import 'tertiary_path_page.dart';
import 'tertiary_people_page.dart';
import 'tertiary_lodging_page.dart';
import 'tertiary_shop_eat_page.dart';
import 'tertiary_transport_page.dart';
import 'tertiary_core_page.dart';
import 'tertiary_academics_page.dart';
import 'tertiary_programs_page.dart';
import 'tertiary_athletics_groups_page.dart';
import 'tertiary_media_page.dart';
import 'tertiary_startups_page.dart';
import 'tertiary_projects_publications_page.dart';
import 'tertiary_buildings_spaces_page.dart';
import 'tertiary_calendar_page.dart';
import 'tertiary_statistics_page.dart';
import 'tertiary_map_page.dart';
import 'tertiary_feedback_page.dart';
import 'tertiary_timeline_page.dart';
import 'tertiary_connectivity_page.dart';
import 'tertiary_giving_page.dart';
import 'tertiary_virtual_tour_page.dart';
import 'tertiary_today_page.dart';
import 'tertiary_info_page.dart';
import '../main.dart'; // Import main.dart for MyApp.preloadedHelpdesks (if needed later)

class TertiaryDetailPage extends StatefulWidget {
  final Map<String, dynamic> college;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryDetailPage({
    Key? key,
    required this.college,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryDetailPage> createState() => _TertiaryDetailPageState();
}

class _TertiaryDetailPageState extends State<TertiaryDetailPage> {
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;

  final String _adUnitId = 'ca-app-pub-3940256099942544/5224354917';
  List<Map<String, dynamic>>? _preloadedHelpdesks; // To store preloaded helpdesks
  List<Map<String, dynamic>>? _preloadedTodayEvents; // To store preloaded today events

  Future<void> _prefetchInfoPageData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // 1. Clear specific cached data (if it exists)
    await prefs.remove('about_${widget.college['id']}');
    await prefs.remove('vision_${widget.college['id']}');
    // ... (add other fields you want to clear and prefetch)
    await prefs.remove('mission_${widget.college['id']}');
    await prefs.remove('address_${widget.college['id']}');
    await prefs.remove('daysnhours_${widget.college['id']}');
    await prefs.remove('postaladdress_${widget.college['id']}');
    await prefs.remove('corevalues_${widget.college['id']}');
    await prefs.remove('motto_${widget.college['id']}');
    await prefs.remove('goals_${widget.college['id']}');
    await prefs.remove('mandate_${widget.college['id']}');
    await prefs.remove('founded_${widget.college['id']}');
    await prefs.remove('accreditation_${widget.college['id']}');
    await prefs.remove('freewifi_${widget.college['id']}');
    await prefs.remove('objectives_${widget.college['id']}');
    await prefs.remove('aims_${widget.college['id']}');
    await prefs.remove('pledge_${widget.college['id']}');
    await prefs.remove('statementoffaith_${widget.college['id']}');
    await prefs.remove('religiousaffiliation_${widget.college['id']}');
    await prefs.remove('whychooseus_${widget.college['id']}');
    await prefs.remove('institutiontype_${widget.college['id']}');
    await prefs.remove('campussetting_${widget.college['id']}');
    await prefs.remove('highestqualificationoffered_${widget.college['id']}');
    await prefs.remove('studentpopulation_${widget.college['id']}');
    await prefs.remove('academicyearcalendar_${widget.college['id']}');
    await prefs.remove('website_${widget.college['id']}');
    await prefs.remove('drivingdirections_${widget.college['id']}');
    await prefs.remove('busdirections_${widget.college['id']}');
    await prefs.remove('airdirections_${widget.college['id']}');
    await prefs.remove('raildirections_${widget.college['id']}');
    await prefs.remove('boatdirections_${widget.college['id']}');

    // 2. Fetch updated data (example using a hypothetical function)
    Map<String, dynamic> updatedData =
        await fetchUpdatedCollegeData(widget.college['id']);

    // 3. Cache the updated data using SharedPreferences
    await prefs.setString('about_${widget.college['id']}', updatedData['about'] ?? '');
    await prefs.setString(
        'vision_${widget.college['id']}', updatedData['vision'] ?? '');
    // ... (cache other fields)
    await prefs.setString(
        'mission_${widget.college['id']}', updatedData['mission'] ?? '');
    await prefs.setString(
        'address_${widget.college['id']}', updatedData['address'] ?? '');
    await prefs.setString(
        'daysnhours_${widget.college['id']}', updatedData['daysnhours'] ?? '');
    await prefs.setString(
        'postaladdress_${widget.college['id']}', updatedData['postaladdress'] ?? '');
    await prefs.setString(
        'corevalues_${widget.college['id']}', updatedData['corevalues'] ?? '');
    await prefs.setString('motto_${widget.college['id']}', updatedData['motto'] ?? '');
    await prefs.setString('goals_${widget.college['id']}', updatedData['goals'] ?? '');
    await prefs.setString(
        'mandate_${widget.college['id']}', updatedData['mandate'] ?? '');
    await prefs.setString(
        'founded_${widget.college['id']}', updatedData['founded'] ?? '');
    await prefs.setString(
        'accreditation_${widget.college['id']}', updatedData['accreditation'] ?? '');
    // Convert freewifi to String before caching
    await prefs.setString(
        'freewifi_${widget.college['id']}', updatedData['freewifi']?.toString() ?? '');
    await prefs.setString(
        'objectives_${widget.college['id']}', updatedData['objectives'] ?? '');
    await prefs.setString('aims_${widget.college['id']}', updatedData['aims'] ?? '');
    await prefs.setString(
        'pledge_${widget.college['id']}', updatedData['pledge'] ?? '');
    await prefs.setString('statementoffaith_${widget.college['id']}',
        updatedData['statementoffaith'] ?? '');
    await prefs.setString('religiousaffiliation_${widget.college['id']}',
        updatedData['religiousaffiliation'] ?? '');
    await prefs.setString(
        'whychooseus_${widget.college['id']}', updatedData['whychooseus'] ?? '');
    await prefs.setString('institutiontype_${widget.college['id']}',
        updatedData['institutiontype'] ?? '');
    await prefs.setString(
        'campussetting_${widget.college['id']}', updatedData['campussetting'] ?? '');
    await prefs.setString(
        'highestqualificationoffered_${widget.college['id']}',
        updatedData['highestqualificationoffered'] ?? '');
    await prefs.setString('studentpopulation_${widget.college['id']}',
        updatedData['studentpopulation'] ?? '');
    await prefs.setString('academicyearcalendar_${widget.college['id']}',
        updatedData['academicyearcalendar'] ?? '');
    await prefs.setString(
        'website_${widget.college['id']}', updatedData['website'] ?? '');
    await prefs.setString('drivingdirections_${widget.college['id']}',
        updatedData['drivingdirections'] ?? '');
    await prefs.setString(
        'busdirections_${widget.college['id']}', updatedData['busdirections'] ?? '');
    await prefs.setString('airdirections_${widget.college['id']}',
        updatedData['airdirections'] ?? '');
    await prefs.setString('raildirections_${widget.college['id']}',
        updatedData['raildirections'] ?? '');
    await prefs.setString('boatdirections_${widget.college['id']}',
        updatedData['boatdirections'] ?? '');

    print("Data prefetched and cached for TertiaryInfoPage.");
  }

  // Hypothetical function to simulate fetching updated data from an API or database
  Future<Map<String, dynamic>> fetchUpdatedCollegeData(int collegeId) async {
    // Replace this with your actual data fetching logic
    await Future.delayed(Duration(seconds: 2)); // Simulate network delay

    // Fetch the current data
    Map<String, dynamic> currentData = widget.college;

    // Return the current data (no updates in this example)
    return {
      'about': currentData['about'],
      'vision': currentData['vision'],
      // ... (other fields)
      'mission': currentData['mission'],
      'address': currentData['address'],
      'daysnhours': currentData['daysnhours'],
      'postaladdress': currentData['postaladdress'],
      'corevalues': currentData['corevalues'],
      'motto': currentData['motto'],
      'goals': currentData['goals'],
      'mandate': currentData['mandate'],
      'founded': currentData['founded'],
      'accreditation': currentData['accreditation'],
      'freewifi': currentData['freewifi'],
      'objectives': currentData['objectives'],
      'aims': currentData['aims'],
      'pledge': currentData['pledge'],
      'statementoffaith': currentData['statementoffaith'],
      'religiousaffiliation': currentData['religiousaffiliation'],
      'whychooseus': currentData['whychooseus'],
      'institutiontype': currentData['institutiontype'],
      'campussetting': currentData['campussetting'],
      'highestqualificationoffered': currentData['highestqualificationoffered'],
      'studentpopulation': currentData['studentpopulation'],
      'academicyearcalendar': currentData['academicyearcalendar'],
      'website': currentData['website'],
      'drivingdirections': currentData['drivingdirections'],
      'busdirections': currentData['busdirections'],
      'airdirections': currentData['airdirections'],
      'raildirections': currentData['raildirections'],
      'boatdirections': currentData['boatdirections'],
    };
  }

  Future<List<Map<String, dynamic>>> _preloadTodaysEvents() async {
    final now = DateTime.now();
    final todayDay = DateFormat('d').format(now);
    final todayMonth = DateFormat('MMMM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events';

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .order('starttime', ascending: true);

      if (response == null) {
        return []; // Return empty list if no events
      }

      List<Map<String, dynamic>> eventsData = List<Map<String, dynamic>>
          .from(response as List<dynamic>);

      List<Map<String, dynamic>> filteredEvents = [];
      for (var event in eventsData) {
        if (!_isEventOver(event, now)) { // Reuse isEventOver logic from TertiaryTodayPage
          filteredEvents.add(event);
        }
      }

      List<Future<void>> futures = [];
      for (final event in filteredEvents) {
        futures.add(_fetchEventImageUrlForPreload(event)); // Use preload image fetch
      }
      await Future.wait(futures);

      filteredEvents.sort((a, b) {
        final startTimeA = _parseTimeOfDayForPreload(a['starttime'] ?? '00:00') ?? TimeOfDay.now(); // Use preload time parse
        final startTimeB = _parseTimeOfDayForPreload(b['starttime'] ?? '00:00') ?? TimeOfDay.now(); // Use preload time parse
        if (startTimeA.hour != startTimeB.hour) {
          return startTimeA.hour.compareTo(startTimeB.hour);
        }
        return startTimeA.minute.compareTo(startTimeB.minute);
      });

      String collegeNameForEvents = widget.college['fullname'] ?? '';
      await _cacheTodayEvents(collegeNameForEvents, filteredEvents); // Cache today's events

      return filteredEvents;
    } catch (error) {
      print('Error preloading today events: $error');
      return []; // Return empty list in case of error
    }
  }

  Future<void> _clearTodayEventsCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Today\'s events cache cleared for $collegeName.');
  }


  Future<void> _cacheTodayEvents(String collegeName, List<Map<String, dynamic>> events) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String eventsJson = jsonEncode(events);
    await prefs.setString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}', eventsJson);
    print('Today\'s events cached for $collegeName.');
  }

  Future<List<Map<String, dynamic>>?> _getCachedTodayEvents(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? eventsJson = prefs.getString('today_events_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (eventsJson != null) {
      List<dynamic> decodedList = jsonDecode(eventsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }


  bool _isEventOver(Map<String, dynamic> event, DateTime now) {
    final eventStartTime = event['starttime'] as String? ?? '00:00';
    final eventEndTime = event['endtime'] as String? ?? '23:59';
    final eventDay = int.tryParse(event['startday'] as String? ?? '0') ?? 0;
    final eventMonthName = event['startmonth'] as String? ?? '';
    final eventYear = int.tryParse(event['startyear'] as String? ?? '0') ?? 0;

    if (eventDay == 0 || eventMonthName.isEmpty || eventYear == 0) return false;

    final monthNumber = DateFormat('MMMM').parse(eventMonthName).month;

    final startDate = DateTime(eventYear, monthNumber, eventDay);

    TimeOfDay? endTimeOfDay;
    try {
      endTimeOfDay = _parseTimeOfDayForPreload(eventEndTime);
    } catch (e) {
      print("Error parsing endtime: $eventEndTime for event ${event['fullname']}");
      return false;
    }


    if (endTimeOfDay != null) {
      final eventEndDate = DateTime(startDate.year, startDate.month, startDate.day, endTimeOfDay.hour, endTimeOfDay.minute);
      print("Event End Date: $eventEndDate, Current Time: $now");
      return now.isAfter(eventEndDate.add(const Duration(minutes: 1))); // ADDED 1 minute delay HERE - Correct placement
    }
    return false;
  }

  TimeOfDay? _parseTimeOfDayForPreload(String timeString) {
    try {
      // Try parsing with AM/PM format first
      DateTime parsedTime12H = DateFormat('h:mm a').parse(timeString.trim());
      return TimeOfDay.fromDateTime(parsedTime12H);
    } catch (e1) {
      try {
        // If AM/PM parsing fails, try 24-hour format
        DateTime parsedTime24H = DateFormat('HH:mm').parse(timeString.trim());
        return TimeOfDay.fromDateTime(parsedTime24H);
      } catch (e2) {
        print("Error parsing time string (preload): $timeString, errors: 12H: $e1, 24H: $e2");
        return null;
      }
    }
  }

  Future<void> _fetchEventImageUrlForPreload(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) {
      return;
    }
    if (event['image_url'] != null && event['image_url'] != 'assets/placeholder_image.png') {
      return;
    }
    event['_isImageLoading'] = true; // Set loading state directly on the map for preloading

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventsBucket = '${widget.college['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}/events';

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventsBucket)
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventsBucket)
            .getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      // Handle error if image download fails during preload, maybe keep placeholder
    }
    event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
    event['_isImageLoading'] = false; // Reset loading state after preloading
  }


  @override
  void initState() {
    super.initState();
    _loadRewardedAd();
    _prefetchInfoPageData();
    _preloadAndCacheHelpdesks(); // Preload helpdesks on detail page load

    // Clear and Preload Today's Events Cache on TertiaryDetailPage Init
    _clearTodayEventsCache(widget.college['fullname']); // Clear cache first
    _loadPreloadTodayEvents(); // Then preload and cache fresh data
  }

  Future<void> _loadPreloadTodayEvents() async {
    List<Map<String, dynamic>> todayEvents = await _preloadTodaysEvents();
    setState(() {
      _preloadedTodayEvents = todayEvents;
    });
  }


  Future<void> _preloadAndCacheHelpdesks() async {
    String collegeNameForTable = widget.college['fullname'] ?? '';
    if (collegeNameForTable.isEmpty) return;

    await _clearHelpdeskCache(collegeNameForTable); // Clear existing cache
    List<Map<String, dynamic>> helpdesks = await _fetchHelpdesksFromDatabase(collegeNameForTable); // Fetch latest
    await _cacheHelpdesks(collegeNameForTable, helpdesks); // Cache the fetched data
    setState(() {
      _preloadedHelpdesks = helpdesks; // Store for passing to start page
    });
    print('Helpdesks preloaded and cached for ${widget.college['fullname']}.');
  }

  Future<void> _clearHelpdeskCache(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    print('Helpdesk cache cleared for $collegeName.');
  }

  Future<List<Map<String, dynamic>>> _fetchHelpdesksFromDatabase(String collegeNameForTable) async {
    final helpdesksTableName = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (response == null || response.isEmpty) {
        return []; // Return empty list if no helpdesks found
      }
      List<Map<String, dynamic>> updatedHelpdesks = await _updateHelpdeskImageUrls(response, collegeNameForTable);
      return updatedHelpdesks;
    } catch (error) {
      print('Error fetching helpdesks from database: $error');
      return []; // Return empty list in case of error
    }
  }

  Future<List<Map<String, dynamic>>> _updateHelpdeskImageUrls(List<Map<String, dynamic>> helpdesks, String collegeNameForTable) async {
    final collegeHelpdeskBucket = '${collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';
    for (final helpdesk in helpdesks) {
      final fullname = helpdesk['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = 'assets/placeholder_image.png';

      try {
        await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          } catch (e) {
            // Image not found, keep default placeholder
          }
        }
      }
      helpdesk['image_url'] = imageUrl;
    }
    return helpdesks;
  }


  Future<void> _cacheHelpdesks(String collegeName, List<Map<String, dynamic>> helpdesks) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String helpdesksJson = jsonEncode(helpdesks);
    await prefs.setString('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}', helpdesksJson);
    print('Helpdesks cached for $collegeName.');
  }

  Future<List<Map<String, dynamic>>?> _getCachedHelpdesks(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? helpdesksJson = prefs.getString('helpdesks_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (helpdesksJson != null) {
      List<dynamic> decodedList = jsonDecode(helpdesksJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }


  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (RewardedAd ad) {
              ad.dispose();
              _loadRewardedAd();
            },
            onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
              print('Failed to show rewarded ad: $error');
              ad.dispose();
            },
          );
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
        },
      ),
    );
  }

  void _showRewardedAd() {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('User earned reward: ${reward.amount} ${reward.type}');
        },
      );
    } else {
      print('Rewarded ad is not ready yet.');
      Navigator.pop(context);
    }
  }

  Future<void> _launchDialer() async {
    final String phoneNumber = widget.college['phone'] ?? '';
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchWhatsapp() async {
    final String whatsappNumber = widget.college['whatsapp'] ?? '';
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchNavigation() async {
    final double latitude = widget.college['latitude'] ?? 0.0;
    final double longitude = widget.college['longitude'] ?? 0.0;
    final Uri url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.college['phone'];
    final hasPhone = phone != null && phone.isNotEmpty;
    final latitude = widget.college['latitude'];
    final longitude = widget.college['longitude'];
    final hasLocation = latitude != null && longitude != null;
    final whatsapp = widget.college['whatsapp'];
    final hasWhatsapp = whatsapp != null && whatsapp.isNotEmpty;
    final hasTodayEvents = _preloadedTodayEvents != null && _preloadedTodayEvents!.isNotEmpty;


    return WillPopScope(
      onWillPop: () async {
        _showRewardedAd();
        return true;
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd();
            },
          ),
          title: Text(
            widget.college['fullname'] ?? '',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          actions: [
            IconButton(
              icon: Icon(
                Icons.support_agent,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryConnectivityPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.wifi,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryConnectivityPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.volunteer_activism,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryGivingPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.view_in_ar,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TertiaryVirtualTourPage(
                      isDarkMode: widget.isDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = 2;
              double aspectRatio = 1.3;

              if (constraints.maxWidth > 1200) {
                crossAxisCount = 6;
                aspectRatio = 1.4;
              } else if (constraints.maxWidth > 900) {
                crossAxisCount = 4;
                aspectRatio = 1.3;
              } else if (constraints.maxWidth > 600) {
                crossAxisCount = 3;
                aspectRatio = 1.2;
              }

              return GridView.count(
                crossAxisCount: crossAxisCount,
                padding: const EdgeInsets.all(16),
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: aspectRatio,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildGridItem(context, 'Start', Icons.play_arrow, theme),
                  _buildGridItem(context, 'Updates', Icons.update, theme),
                  _buildGridItem(context, 'Path', Icons.timeline, theme),
                  _buildGridItem(context, 'People', Icons.people, theme),
                  _buildGridItem(context, 'Lodging', Icons.hotel, theme),
                  _buildGridItem(
                      context, 'Shop & Eat', Icons.restaurant_menu, theme),
                  _buildGridItem(
                      context, 'Transport', Icons.directions_bus, theme),
                  _buildGridItem(context, 'Core', Icons.stars, theme),
                  _buildGridItem(context, 'Academics', Icons.school, theme),
                  _buildGridItem(context, 'Programs', Icons.list_alt, theme),
                  _buildGridItem(
                      context, 'Athletics & Groups', Icons.sports_soccer, theme),
                  _buildGridItem(context, 'Media', Icons.perm_media, theme),
                  _buildGridItem(context, 'Startups', Icons.rocket_launch, theme),
                  _buildGridItem(context, 'Projects & Publications',
                      Icons.article, theme),
                  _buildGridItem(
                      context, 'Buildings & Spaces', Icons.apartment, theme),
                  _buildGridItem(context, 'Statistics', Icons.bar_chart, theme),
                  _buildGridItem(
                      context, 'Calendar', Icons.calendar_month, theme),
                  _buildGridItem(context, 'Map', Icons.map, theme),
                  _buildGridItem(context, 'Feedback', Icons.feedback, theme),
                  _buildGridItem(
                      context, 'Historical Timeline', Icons.history_edu, theme),
                  _buildGridItem(context, 'Rentals', Icons.swap_horiz, theme),
                  _buildGridItem(context, 'Jobs', Icons.work, theme),
                  _buildGridItem(
                      context, 'Services', Icons.miscellaneous_services, theme),
                  _buildGridItem(context, 'Money', Icons.payments, theme),
                  _buildGridItem(context, 'Health', Icons.local_hospital, theme),
                  _buildGridItem(context, 'Weather', Icons.cloud_queue, theme),
                  _buildGridItem(context, 'Safety', Icons.security, theme),
                  _buildGridItem(context, 'Nearby', Icons.near_me, theme),
                ],
              );
            },
          ),
        ),
        bottomNavigationBar: Container(
          color: theme.colorScheme.surface,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface.withOpacity(hasPhone ? 1.0 : 0.5),
                    ),
                    onPressed: hasPhone ? _launchDialer : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.navigation,
                      color: theme.colorScheme.onSurface.withOpacity(hasLocation ? 1.0 : 0.5),
                      ),
                    onPressed: hasLocation ? _launchNavigation : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.watch,
                      color: theme.colorScheme.onSurface.withOpacity(hasTodayEvents ? 1.0 : 0.5),
                    ),
                    onPressed: hasTodayEvents ? () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryTodayPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            collegeData: widget.college, // Pass collegeData here
                            preloadedEvents: _preloadedTodayEvents, // Pass preloaded events here
                          ),
                        ),
                      );
                    } : null,
                  ),
                  IconButton(
                    icon: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface.withOpacity(hasWhatsapp ? 1.0 : 0.5),
                    ),
                    onPressed: hasWhatsapp ? _launchWhatsapp : null,
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.info,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      // No need to clear image cache here anymore

                      // Navigate to TertiaryInfoPage
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TertiaryInfoPage(
                            isDarkMode: widget.isDarkMode,
                            toggleTheme: widget.toggleTheme,
                            schoolName: widget.college['fullname'] ?? '',
                            collegeData: widget.college,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(
      BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Start') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStartPage(
                  institutionName: widget.college['fullname'] ?? '',
                  collegeData: widget.college, // Pass collegeData here
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  // preloadedHelpdesks: _preloadedHelpdesks, // REMOVE this line - causing error
                ),
              ),
            );
          } else if (title == 'Updates') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryUpdatesPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Path') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryPathPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } // ... (rest of your navigation cases for other grid items)
          else if (title == 'People') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryPeoplePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Lodging') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryLodgingPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Shop & Eat') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryShopEatPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Transport') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryTransportPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Core') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryCorePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Academics') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryAcademicsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Programs') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryProgramsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Athletics & Groups') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryAthleticsGroupsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Media') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryMediaPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Startups') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStartupsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Projects & Publications') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryProjectsPublicationsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Buildings & Spaces') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryBuildingsSpacesPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Calendar') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryCalendarPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Statistics') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryStatisticsPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Map') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryMapPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Feedback') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryFeedbackPage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          } else if (title == 'Historical Timeline') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TertiaryTimelinePage(
                  institutionName: widget.college['fullname'] ?? '',
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    super.dispose();
  }
}