// web_panorama.dart
// This file is only used on the web (dart.library.html is available).
import 'dart:html' as html;
import 'dart:ui' as ui;
import 'package:flutter/widgets.dart';

/// Builds a panorama widget for the web using an iframe.
Widget buildWebPanorama(String htmlContent) {
  // Create a unique viewId for this HTML content.
  final String viewId = 'panorama-html-${htmlContent.hashCode}';

  // Register the view factory. (It’s safe to call this multiple times with the same viewId.)
  ui.platformViewRegistry.registerViewFactory(viewId, (int id) {
    final html.IFrameElement element = html.IFrameElement()
      ..srcdoc = htmlContent
      ..style.border = 'none'
      ..style.width = '100%'
      ..style.height = '100%';
    return element;
  });
  return HtmlElementView(viewType: viewId);
}
