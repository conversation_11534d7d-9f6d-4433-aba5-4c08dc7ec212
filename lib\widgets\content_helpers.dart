import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// Build a table widget using Flutter's native Table widget
Widget buildTableWidget(String tableContent, Color textColor, double fontSize) {
  final lines = tableContent.trim().split('\n');
  if (lines.length < 3) return Container(); // Need at least header, separator, and one row

  // Parse the table
  final List<List<String>> rows = [];
  for (var line in lines) {
    if (line.trim().isEmpty) continue;
    if (!line.startsWith('|') || !line.endsWith('|')) continue;

    // Remove first and last pipe
    line = line.substring(1, line.length - 1);

    // Split by pipe but handle escaped pipes
    final cells = line.split('|').map((cell) => cell.trim()).toList();
    rows.add(cells);
  }

  if (rows.isEmpty) return Container();

  // Check if second row is separator
  final isSeparator = rows.length > 1 &&
                     rows[1].every((cell) => cell.isEmpty || RegExp(r'^[-:\s]+$').hasMatch(cell));

  final headerRow = rows[0];
  final dataRows = isSeparator ? rows.sublist(2) : rows.sublist(1);

  // Ensure all rows have the same number of columns
  final columnCount = headerRow.length;
  for (var row in dataRows) {
    while (row.length < columnCount) {
      row.add(''); // Add empty cells if needed
    }
    if (row.length > columnCount) {
      row = row.sublist(0, columnCount); // Trim extra cells
    }
  }

  // Create table rows
  List<TableRow> tableRows = [];

  // Header row
  tableRows.add(
    TableRow(
      decoration: BoxDecoration(
        color: textColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      children: headerRow.map((cell) => Padding(
        padding: const EdgeInsets.all(8.0),
        child: SelectableText(
          cell,
          style: GoogleFonts.notoSans(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          textAlign: TextAlign.center,
        ),
      )).toList(),
    ),
  );

  // Data rows
  for (var row in dataRows) {
    tableRows.add(
      TableRow(
        decoration: BoxDecoration(
          border: Border(top: BorderSide(color: Colors.grey.withOpacity(0.3))),
        ),
        children: row.map((cell) => Padding(
          padding: const EdgeInsets.all(8.0),
          child: SelectableText(
            cell,
            style: GoogleFonts.notoSans(
              fontSize: fontSize,
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        )).toList(),
      ),
    );
  }

  return Container(
    margin: const EdgeInsets.symmetric(vertical: 16),
    decoration: BoxDecoration(
      border: Border.all(color: Colors.grey.withOpacity(0.3)),
      borderRadius: BorderRadius.circular(8),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Table(
        border: TableBorder.all(
          color: Colors.grey.withOpacity(0.3),
          width: 0.5,
        ),
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        columnWidths: Map.fromIterable(
          List.generate(columnCount, (index) => index),
          key: (index) => index,
          value: (_) => const FlexColumnWidth(1),
        ),
        children: tableRows,
      ),
    ),
  );
}

// Remove preliminary text from LLM output
String removePreliminaryText(String content) {
  // Common patterns for preliminary text
  final patterns = [
    RegExp(r"^(Sure|Okay|Alright|Here|I'll|I will)[^\n]*\n+", caseSensitive: false),
    RegExp(r"^(Let me|I'd be happy to|I'd love to|I can)[^\n]*\n+", caseSensitive: false),
    RegExp(r"^(Here's|Here are|Below is|Following is)[^\n]*\n+", caseSensitive: false),
  ];

  String result = content;
  for (final pattern in patterns) {
    if (pattern.hasMatch(result)) {
      result = result.replaceFirst(pattern, '');
      break; // Only remove one preliminary text pattern
    }
  }

  return result;
}
