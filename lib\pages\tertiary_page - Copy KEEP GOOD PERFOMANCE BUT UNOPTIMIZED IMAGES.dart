import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 2; // Number of colleges to load per page
  bool _hasMore = true; // Flag to indicate if there are more colleges to load

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadPreloadedColleges() {
    if (MyApp.preloadedColleges != null) {
      setState(() {
        // Directly use preloaded data if available
        _colleges = List<Map<String, dynamic>>.from(MyApp.preloadedColleges!);
        _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = MyApp.preloadedColleges!.length == _pageSize; // Adjust _hasMore based on preloaded data size
      });
      // Fetch image URLs only for the initial preloaded set if needed
      for (var college in _colleges) {
        if (college['image_url'] == null || college['image_url'] == 'assets/placeholder_image.png') {
          _fetchImageUrl(college);
        }
      }
    } else {
      _loadCollegesFromSupabase();
    }
  }

  Future<void> _loadCollegesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    setState(() {
      _isLoading = true;
    });
    try {
      final startIndex = _page * _pageSize;
      final endIndex = startIndex + _pageSize - 1;

      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('colleges')
          .select('*')
          .order('fullname', ascending: true)
          .range(startIndex, endIndex);

      final updatedColleges = await _updateCollegeImageUrls(response);

      setState(() {
        if (initialLoad) {
          _colleges = updatedColleges;
        } else {
          _colleges.addAll(updatedColleges);
        }
        _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize; // Determine if there are more pages based on response size
      });

      // Update preloaded data only on initial load and if it's currently null
      if (initialLoad || MyApp.preloadedColleges == null) {
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(_colleges);
      } else if (!initialLoad && MyApp.preloadedColleges != null) {
        MyApp.preloadedColleges!.addAll(updatedColleges);
        MyApp.preloadedColleges!.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      }


    } catch (error) {
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching colleges: $error')),
        );
        setState(() {
          _isLoading = false;
          _hasMore = false; // Stop trying to load more on error
        });
      }
    }
  }


  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(List<Map<String, dynamic>> colleges) async {
    List<Future<void>> futures = [];
    for (final college in colleges) {
      if (college['image_url'] == null || college['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(college));
      }
    }
    await Future.wait(futures);
    return colleges;
  }


  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'colleges',
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .eq('id', newCollegeId);
          if (newCollegeResponse.isNotEmpty) {
            final newCollege = newCollegeResponse.first;
            final updatedCollege = await _updateCollegeImageUrls([newCollege]);
            setState(() {
              _colleges = [..._colleges, updatedCollege.first];
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data
            MyApp.preloadedColleges = [...MyApp.preloadedColleges ?? [], updatedCollege.first];
            MyApp.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .eq('id', updatedCollegeId);
          if (updatedCollegeResponse.isNotEmpty) {
            final updatedCollege = updatedCollegeResponse.first;
            setState(() {
              _colleges = _colleges.map((college) {
                return college['id'] == updatedCollege['id'] ? updatedCollege : college;
              }).toList();
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data
            MyApp.preloadedColleges = MyApp.preloadedColleges?.map((college) {
              return college['id'] == updatedCollege['id'] ? updatedCollege : college;
            }).toList();
            MyApp.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCollegeId = payload.oldRecord['id'];
          setState(() {
            _colleges.removeWhere((college) => college['id'] == deletedCollegeId);
          });
          // Update preloaded data
          MyApp.preloadedColleges?.removeWhere((college) => college['id'] == deletedCollegeId);
        }
      },
    )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed && _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreColleges();
    }
  }

  Future<void> _loadMoreColleges() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadCollegesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TertiaryDetailPage(
            college: college,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Tertiary',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => _loadCollegesFromSupabase(initialLoad: true),
        child: _colleges.isEmpty && _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _colleges.isEmpty && !_isLoading
                ? LayoutBuilder(
                    builder: (BuildContext context, BoxConstraints constraints) {
                      return SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: constraints.maxHeight,
                          child: const Center(
                            child: Text('No colleges available.'),
                          ),
                        ),
                      );
                    })
                : ListView.builder(
                    key: _listKey,
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    itemCount: _colleges.length + (_hasMore ? 1 : 0), // Add 1 for loading indicator
                    itemBuilder: (context, index) {
                      if (index < _colleges.length) {
                        final university = _colleges[index];
                        return VisibilityDetector(
                          key: Key('college_${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 && (university['image_url'] == null || university['image_url'] == 'assets/placeholder_image.png')) {
                              _fetchImageUrl(university);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: ClipOval( // Wrap with ClipOval
                                child: SizedBox(
                                  width: 40, // Adjust size as needed
                                  height: 40,
                                  child: CachedNetworkImage(
                                    imageUrl: university['image_url'] ?? 'assets/placeholder_image.png',
                                    errorWidget: (context, url, error) => Image.asset('assets/placeholder_image.png'), // Show placeholder on error
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              title: Text(
                                university['fullname'] ?? 'Unnamed College',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '${university['city'] ?? ''}${university['state'] != null && university['city'] != null ? ', ' : ''}${university['state'] ?? ''}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      } else if (_hasMore) {
                        return const Center(child: Padding(padding: EdgeInsets.all(16), child: CircularProgressIndicator()));
                      } else {
                        return Container(); // No more items
                      }
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    if (college['image_url'] != null && college['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${college['fullname']}, skipping fetch.'); // Debugging line
      return; // Already has a valid URL, no need to fetch again
    }
    final fullname = college['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp'; // Directly construct .webp file name
    String imageUrl = '';

    print('Fetching image URL for: $fullname, filename: $imageNameWebp'); // Debugging line

    try {
      final file = await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
      print('Download attempt for $imageNameWebp completed. File size: ${file.length}'); // Debugging line
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
        print('Public URL generated: $imageUrl'); // Debugging line
      } else {
        print('Downloaded file is empty for $imageNameWebp'); // Debugging line
      }
    } catch (e) {
      // If .webp image is not found or any error occurs, use placeholder
      // We are *only* trying to load .webp, so any error means use placeholder.
      print('Error downloading or WebP image not found for $fullname: $e'); // Debugging line
      // imageUrl remains empty, so the placeholder will be used
    }

    if (mounted) {
      setState(() {
        college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        print('Setting image_url for ${college['fullname']} to: ${college['image_url']}'); // Debugging line
      });
    }
  }
}