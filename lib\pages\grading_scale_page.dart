import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

class GradingScalePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedGradingScale;
  final bool isFromDetailPage;

  const GradingScalePage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedGradingScale,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _GradingScalePageState createState() => _GradingScalePageState();
}

class _GradingScalePageState extends State<GradingScalePage> {
  bool _isDisposed = false;
  List<Map<String, dynamic>> _gradingScale = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  String _selectedProgramLevel = 'All Levels';
  List<String> _programLevels = ['All Levels'];

  @override
  void initState() {
    super.initState();
    print("GradingScalePage initState called");
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() {
    if (widget.preloadedGradingScale != null &&
        widget.preloadedGradingScale!.isNotEmpty) {
      setState(() {
        _gradingScale = List.from(widget.preloadedGradingScale!);
        _isLoading = false;
        _extractProgramLevels();
      });
    } else {
      _loadGradingScaleFromDatabase();
    }
  }

  void _extractProgramLevels() {
    Set<String> programLevelsSet = {'All Levels'};

    for (var grade in _gradingScale) {
      if (grade['programlevel'] != null && grade['programlevel'].toString().isNotEmpty) {
        programLevelsSet.add(grade['programlevel']);
      }
    }

    setState(() {
      _programLevels = programLevelsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final gradingScaleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_gradingscale';
    _realtimeChannel = Supabase.instance.client
        .channel('grading_scale_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: gradingScaleTableName,
      callback: (payload) async {
        print("Realtime update received for grading scale: ${payload.eventType}");
        _loadGradingScaleFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadGradingScaleFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _gradingScale = [];
    });

    try {
      final gradingScaleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_gradingscale';
      // Build the query string parts
      List<String> conditions = [];

      // Apply program level filter
      if (_selectedProgramLevel != 'All Levels') {
        conditions.add("programlevel.eq.${_selectedProgramLevel}");
      }

      // Execute the query
      final response = await Supabase.instance.client
          .from(gradingScaleTableName)
          .select('*')
          .or(conditions.join(','))
          .order('mark', ascending: false);

      if (_isDisposed) return;

      setState(() {
        _gradingScale = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      _extractProgramLevels();
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading grading scale: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading grading scale: $e')),
      );
    }
  }

  void _filterByProgramLevel(String programLevel) {
    setState(() {
      _selectedProgramLevel = programLevel;
    });
    _loadGradingScaleFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Grading Scale',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Program level filter
          if (_programLevels.length > 1)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Program Level',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: theme.colorScheme.surface,
                ),
                value: _selectedProgramLevel,
                items: _programLevels.map((String level) {
                  return DropdownMenuItem<String>(
                    value: level,
                    child: Text(
                      level,
                      style: TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    _filterByProgramLevel(newValue);
                  }
                },
              ),
            ),

          // Grading scale table
          Expanded(
            child: VisibilityDetector(
              key: const Key('grading_scale_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _gradingScale.isEmpty && !_isLoading) {
                  _loadGradingScaleFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadGradingScaleFromDatabase,
                child: _isLoading
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _gradingScale.isEmpty
                        ? Center(
                            child: Text(
                              'No grading scale found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (_selectedProgramLevel != 'All Levels')
                                      Padding(
                                        padding: const EdgeInsets.only(bottom: 16.0),
                                        child: Text(
                                          'Grading Scale for $_selectedProgramLevel',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ),
                                    Expanded(
                                      child: SingleChildScrollView(
                                        child: Table(
                                          border: TableBorder.all(
                                            color: theme.colorScheme.outline.withOpacity(0.3),
                                            width: 1,
                                          ),
                                          columnWidths: const {
                                            0: FlexColumnWidth(1),
                                            1: FlexColumnWidth(1),
                                            2: FlexColumnWidth(2),
                                          },
                                          children: [
                                            // Table header
                                            TableRow(
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.primaryContainer,
                                              ),
                                              children: [
                                                _buildTableHeaderCell(theme, 'Mark'),
                                                _buildTableHeaderCell(theme, 'Grade'),
                                                _buildTableHeaderCell(theme, 'Remark'),
                                              ],
                                            ),
                                            // Table rows
                                            ..._gradingScale.map((grade) {
                                              return TableRow(
                                                children: [
                                                  _buildTableCell(theme, grade['mark'] ?? ''),
                                                  _buildTableCell(theme, grade['lettergrade'] ?? ''),
                                                  _buildTableCell(theme, grade['graderemark'] ?? ''),
                                                ],
                                              );
                                            }).toList(),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeaderCell(ThemeData theme, String text) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onPrimaryContainer,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(ThemeData theme, String text) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Text(
        text,
        style: TextStyle(
          color: theme.colorScheme.onSurface,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
