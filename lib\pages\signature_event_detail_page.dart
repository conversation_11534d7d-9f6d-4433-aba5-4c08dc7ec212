// signature_event_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class SignatureEventDetailPage extends StatefulWidget {
  final Map<String, dynamic> event;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const SignatureEventDetailPage({
    Key? key,
    required this.event,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<SignatureEventDetailPage> createState() => _SignatureEventDetailPageState();
}

class _SignatureEventDetailPageState extends State<SignatureEventDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _eventRealtimeChannel; // Realtime channel for event updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupEventRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _eventRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.event['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupEventRealtimeListener() {
    final eventsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_signatureevents';
    _eventRealtimeChannel = Supabase.instance.client
        .channel('event_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: eventsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current event's ID
        if (payload.newRecord['id'] == widget.event['id']) {
          print("Realtime UPDATE event received for THIS event (manual filter applied): ${widget.event['fullname']}");
          _fetchUpdatedEventData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER event, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedEventData() async {
    final eventsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_signatureevents';
    try {
      final updatedEventResponse = await Supabase.instance.client
          .from(eventsTableName)
          .select('*')
          .eq('id', widget.event['id'])
          .single();

      if (mounted && updatedEventResponse != null) {
        Map<String, dynamic> updatedEvent = Map.from(updatedEventResponse);
        // Update the widget.event with the new data
        setState(() {
          widget.event.clear(); // Clear old data
          widget.event.addAll(updatedEvent); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Event data updated in detail page for ${widget.event['fullname']}");
          _updateEventsCache(updatedEvent); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated event data: $error");
    }
  }

  Future<void> _updateEventsCache(Map<String, dynamic> updatedEvent) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'signatureevents_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedEventsJson = prefs.getString(cacheKey);

    if (cachedEventsJson != null) {
      List<Map<String, dynamic>> cachedEvents = (jsonDecode(cachedEventsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the event in the cached list
      for (int i = 0; i < cachedEvents.length; i++) {
        if (cachedEvents[i]['id'] == updatedEvent['id']) {
          cachedEvents[i] = updatedEvent;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedEvents));
      print("Events cache updated with realtime change for ${updatedEvent['fullname']}");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final about = widget.event['about'] as String? ?? '';
    final isAlumniEvent = widget.event['alumnisignatureevent'] ?? false;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.event['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (isAlumniEvent)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Chip(
                label: Text(
                  'Alumni',
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
                backgroundColor: theme.colorScheme.primary,
                padding: EdgeInsets.zero,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
