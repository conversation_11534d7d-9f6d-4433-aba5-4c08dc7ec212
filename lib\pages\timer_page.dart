import 'package:flutter/material.dart';
import 'dart:async';

class TimerScreen extends StatefulWidget {
  const TimerScreen({Key? key}) : super(key: key);

  @override
  _TimerScreenState createState() => _TimerScreenState();
}

class _TimerScreenState extends State<TimerScreen> with WidgetsBindingObserver {
  int _seconds = 0;
  Timer? _timer;
  bool _isRunning = false;
  int _tempMinutes = 0;
  int _tempSeconds = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _timer?.cancel();
    } else if (state == AppLifecycleState.resumed && _isRunning) {
      _startTimer();
    }
  }

  void _startTimer() {
    _isRunning = true;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds > 0) {
        setState(() {
          _seconds--;
        });
      } else {
        _pauseTimer();
      }
    });
  }

  void _pauseTimer() {
    setState(() {
      _isRunning = false;
    });
    _timer?.cancel();
  }

  void _resetTimer() {
    _pauseTimer();
    setState(() {
      _seconds = 0;
    });
  }

  Future<void> _showSetTimerDialog(BuildContext context) async {
    final theme = Theme.of(context);
    _tempMinutes = _seconds ~/ 60;
    _tempSeconds = _seconds % 60;

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Set Timer', style: TextStyle(color: theme.colorScheme.onSurface)),
          contentPadding: const EdgeInsets.all(0.0),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: _NumberPicker(
                        initialValue: _tempMinutes,
                        maxValue: 59,
                        onChanged: (val) {
                          _tempMinutes = val;
                        },
                      ),
                    ),
                    Text(':', style: TextStyle(fontSize: 24, color: theme.colorScheme.onSurface)),
                    Expanded(
                      child: _NumberPicker(
                        initialValue: _tempSeconds,
                        maxValue: 59,
                        onChanged: (val) {
                          _tempSeconds = val;
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Set', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                setState(() {
                  _seconds = _tempMinutes * 60 + _tempSeconds;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  String _formatTime(int totalSeconds) {
    final minutes = (totalSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (totalSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Timer',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  _formatTime(_seconds),
                  style: TextStyle(fontSize: 60, color: theme.colorScheme.onSurface),
                ),
                const SizedBox(height: 30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildCircleButton(
                      heroTag: 'timer_play_pause',
                      icon: _isRunning ? Icons.pause : Icons.play_arrow,
                      onPressed: _seconds > 0 ? (_isRunning ? _pauseTimer : _startTimer) : null,
                      backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
                    ),
                    _buildCircleButton(
                      heroTag: 'timer_reset',
                      icon: Icons.refresh,
                      onPressed: _resetTimer,
                      backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "timer_fab",
        onPressed: () => _showSetTimerDialog(context),
        tooltip: 'Set Timer',
        backgroundColor: isDarkMode ? Colors.black : theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        shape: const CircleBorder(),
        child: const Icon(Icons.timer),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildCircleButton({required String heroTag, required IconData icon, VoidCallback? onPressed, Color? backgroundColor}) {
    final theme = Theme.of(context);
    return FloatingActionButton.small(
      heroTag: heroTag,
      onPressed: onPressed,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      shape: const CircleBorder(),
      child: Icon(icon),
    );
  }
}

class _NumberPicker extends StatefulWidget {
  final int maxValue;
  final ValueChanged<int> onChanged;
  final int initialValue;

  const _NumberPicker({
    required this.maxValue,
    required this.onChanged,
    required this.initialValue,
    Key? key,
  }) : super(key: key);

  @override
  State<_NumberPicker> createState() => _NumberPickerState();
}

class _NumberPickerState extends State<_NumberPicker> {
  late int _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(Icons.remove, color: theme.colorScheme.onSurface),
          onPressed: () {
            if (_currentValue > 0) {
              setState(() {
                _currentValue--;
              });
              widget.onChanged(_currentValue);
            }
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text(
            '${_currentValue.toString().padLeft(2, '0')}',
            style: TextStyle(fontSize: 20, color: theme.colorScheme.onSurface),
          ),
        ),
        IconButton(
          icon: Icon(Icons.add, color: theme.colorScheme.onSurface),
          onPressed: () {
            if (_currentValue < widget.maxValue) {
              setState(() {
                _currentValue++;
              });
              widget.onChanged(_currentValue);
            }
          },
        ),
      ],
    );
  }
}