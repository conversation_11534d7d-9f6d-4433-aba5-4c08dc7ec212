import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

import 'login_page.dart';

class TertiaryStatisticsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryStatisticsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _TertiaryStatisticsPageState createState() => _TertiaryStatisticsPageState();
}

class _TertiaryStatisticsPageState extends State<TertiaryStatisticsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('statistics_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _documents = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  String _selectedTypeFilter = 'All';
  String _selectedYearFilter = 'All Years';
  List<String> _typeFilterOptions = ['All', 'Financial'];
  List<String> _yearFilterOptions = ['All Years'];
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    print("StatisticsPage initState called");
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() {
    _loadDocumentsFromDatabase();
  }

  void _setupRealtime() {
    final documentsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_documents';
    _realtimeChannel = Supabase.instance.client
        .channel('statistics_documents_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: documentsTableName,
      callback: (payload) async {
        print("Realtime update received for statistics documents: ${payload.eventType}");
        _loadDocumentsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadDocumentsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _documents = [];
    });

    try {
      final documentsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_documents';
      var query = Supabase.instance.client
          .from(documentsTableName)
          .select('*')
          .eq('statsdocument', true); // Only get documents where statsdocument is true

      // Apply type filter
      if (_selectedTypeFilter == 'Financial') {
        query = query.eq('statsfinancial', true);
      }

      // Apply year filter if it's not 'All Years'
      if (_selectedYearFilter != 'All Years') {
        int year = int.parse(_selectedYearFilter);
        query = query.eq('year', year);
      }

      // Apply search
      if (_searchQuery.isNotEmpty) {
        query = query.ilike('fullname', '%$_searchQuery%');
      }

      final response = await query.order('year', ascending: false);

      if (_isDisposed) return;

      // Extract unique years for the filter
      Set<String> years = {'All Years'};
      for (var doc in response) {
        if (doc['year'] != null) {
          years.add(doc['year'].toString());
        }
      }

      setState(() {
        _documents = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
        _yearFilterOptions = years.toList()..sort((a, b) {
          if (a == 'All Years') return -1;
          if (b == 'All Years') return 1;
          return int.parse(b).compareTo(int.parse(a)); // Sort years in descending order
        });
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading statistics documents: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading statistics documents: $e')),
      );
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadDocumentsFromDatabase();
  }

  Future<void> _openDocument(String link) async {
    if (link.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document link is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(link);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open document: $link')),
      );
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Statistics',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search statistics documents...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters row
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                // Type filter
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Type:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                          ),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedTypeFilter,
                            isExpanded: true,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: theme.colorScheme.onSurface,
                            ),
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontSize: 16,
                            ),
                            dropdownColor: theme.colorScheme.surface,
                            items: _typeFilterOptions.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null && newValue != _selectedTypeFilter) {
                                setState(() {
                                  _selectedTypeFilter = newValue;
                                });
                                _loadDocumentsFromDatabase();
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Year filter
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Year:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                          ),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedYearFilter,
                            isExpanded: true,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: theme.colorScheme.onSurface,
                            ),
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontSize: 16,
                            ),
                            dropdownColor: theme.colorScheme.surface,
                            items: _yearFilterOptions.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null && newValue != _selectedYearFilter) {
                                setState(() {
                                  _selectedYearFilter = newValue;
                                });
                                _loadDocumentsFromDatabase();
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Documents list
          Expanded(
            child: VisibilityDetector(
              key: const Key('statistics_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _documents.isEmpty && !_isLoading) {
                  _loadDocumentsFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadDocumentsFromDatabase,
                child: _isLoading
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _documents.isEmpty
                        ? Center(
                            child: Text(
                              'No statistics documents found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _documents.length,
                            itemBuilder: (context, index) {
                              return _buildDocumentCard(
                                _documents[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentCard(
    Map<String, dynamic> document,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = document['fullname'] ?? 'Unknown';
    final String link = document['link'] ?? '';
    final bool isStatsDoc = document['statsdocument'] == true;
    final bool isFinancialDoc = document['statsfinancial'] == true;
    final int? year = document['year'];
    final String major = document['major'] ?? '';
    final String department = document['department'] ?? '';
    final String school = document['school'] ?? '';

    // Build document details
    List<Widget> details = [];

    if (year != null) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Year: $year',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    if (major.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Major: $major',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    if (department.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Department: $department',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    if (school.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'School: $school',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    // Document type tags
    List<Widget> tags = [];

    if (isStatsDoc) {
      tags.add(_buildTag(theme, 'Statistics'));
    }

    if (isFinancialDoc) {
      tags.add(_buildTag(theme, 'Financial'));
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openDocument(link),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.1),
                    child: Icon(
                      isFinancialDoc ? Icons.attach_money : Icons.bar_chart,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fullname,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        ...details,
                      ],
                    ),
                  ),
                  Icon(
                    Icons.open_in_new,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),

              if (tags.isNotEmpty) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: tags,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTag(ThemeData theme, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: theme.colorScheme.onPrimaryContainer,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
