// article_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class ArticleDetailPage extends StatefulWidget {
  final Map<String, dynamic> article;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ArticleDetailPage({
    Key? key,
    required this.article,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ArticleDetailPage> createState() => _ArticleDetailPageState();
}

class _ArticleDetailPageState extends State<ArticleDetailPage> {
  late RealtimeChannel _articleRealtimeChannel; // Realtime channel for article updates

  @override
  void initState() {
    super.initState();
    _setupArticleRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _articleRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupArticleRealtimeListener() {
    final articlesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_articles';
    _articleRealtimeChannel = Supabase.instance.client
        .channel('article_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: articlesTableName,
          callback: (payload) {
            // Check if this change is for the current article
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.article['id']) {
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedArticleData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the article is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedArticleData() async {
    try {
      final articlesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_articles';
      final response = await Supabase.instance.client
          .from(articlesTableName)
          .select('*')
          .eq('id', widget.article['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedArticle = Map.from(response);
        // Update the widget.article with the new data
        setState(() {
          widget.article.clear(); // Clear old data
          widget.article.addAll(updatedArticle); // Add updated data
          print("Article data updated in detail page for ${widget.article['fullname']}");
          _updateArticlesCache(updatedArticle); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated article data: $e');
    }
  }

  Future<void> _updateArticlesCache(Map<String, dynamic> updatedArticle) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'articles_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedArticles = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific article in the cache
        bool found = false;
        for (var article in cachedArticles) {
          if (article['id'] == updatedArticle['id']) {
            updatedCache.add(updatedArticle);
            found = true;
          } else {
            updatedCache.add(Map<String, dynamic>.from(article));
          }
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
      }
    } catch (e) {
      print('Error updating articles cache: $e');
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = widget.article['fullname'] as String? ?? 'Article';
    final String year = widget.article['year']?.toString() ?? 'N/A';
    final String author = widget.article['author'] as String? ?? '';
    final String author2 = widget.article['author2'] as String? ?? '';
    final String author3 = widget.article['author3'] as String? ?? '';
    final bool facultyOrStaffArticle = widget.article['facultyorstaffarticle'] as bool? ?? false;
    final bool studentArticle = widget.article['studentarticle'] as bool? ?? false;
    final String about = widget.article['about'] as String? ?? '';
    final String link = widget.article['link'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Article Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Year: $year',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Author(s):',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (author.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (author2.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author2,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (author3.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            author3,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        children: [
                          if (facultyOrStaffArticle)
                            Chip(
                              label: const Text('Faculty/Staff Article'),
                              backgroundColor: theme.colorScheme.primaryContainer,
                            ),
                          if (studentArticle)
                            Chip(
                              label: const Text('Student Article'),
                              backgroundColor: theme.colorScheme.secondaryContainer,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (about.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Abstract',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (link.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Link',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.primary,
                                  decoration: TextDecoration.underline,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _copyToClipboard(link),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _launchURL(link),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
