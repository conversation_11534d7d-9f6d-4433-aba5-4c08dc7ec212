Future<void> _addToCalendar() async {
    tzdata.initializeTimeZones(); // Initialize timezone data at the beginning of the function
    final event = widget.event;
    if (event == null) return;

    String? eventName = event['fullname'] as String? ?? 'Event';
    String? eventDescription = event['about'] as String? ?? '';
    String? eventLocation = (event['building'] as String? ?? '') + ' ' + (event['room'] as String? ?? '');

    String? startDateString = event['startday'] as String?;
    String? startMonthString = event['startmonth'] as String?;
    String? startYearString = event['startyear'] as String?;
    String? startTimeString = event['starttime'] as String?;
    String? endTimeString = event['endtime'] as String?;

    if (startDateString == null || startMonthString == null || startYearString == null || startTimeString == null || endTimeString == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not add to calendar, date/time information missing.')),
      );
      return;
    }

    DateTime? startTime;
    DateTime? endTime;

    try {
      DateFormat monthFormat = DateFormat('MMMM');
      int monthNumber = monthFormat.parse(startMonthString).month;
      String monthStringPadded = monthNumber.toString().padLeft(2, '0');
      String dayStringPadded = startDateString.padLeft(2, '0');

      String startDateTimeString = '${startYearString}-${monthStringPadded}-${dayStringPadded} ${startTimeString}';
      String endDateTimeString = '${startYearString}-${monthStringPadded}-${dayStringPadded} ${endTimeString}';

      DateFormat inputFormat = DateFormat('yyyy-MM-dd HH:mm');
      startTime = inputFormat.parse(startDateTimeString);
      endTime = inputFormat.parse(endDateTimeString);


    } catch (e) {
      print('Error parsing date/time: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error parsing date/time for calendar.')),
      );
      return;
    }


    if (startTime == null || endTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not parse event start or end time.')),
      );
      return;
    }

    // 1. Check for calendar permissions
    var permissionsGranted = await _deviceCalendarPlugin.hasPermissions();
    if (permissionsGranted.isSuccess && !permissionsGranted.data!) {
      permissionsGranted = await _deviceCalendarPlugin.requestPermissions();
      if (!permissionsGranted.isSuccess || !permissionsGranted.data!) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Calendar permissions not granted.')),
        );
        return;
      }
    }

    // 2. Get calendars
    final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();
    if (!calendarsResult.isSuccess || calendarsResult.data == null || calendarsResult.data!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No calendars found.')),
      );
      return;
    }

    Calendar? selectedCalendar;
    if (calendarsResult.data!.length > 1) {
      // 3. Show calendar selection dialog if multiple calendars
      selectedCalendar = await _showCalendarPickerDialog(calendarsResult.data!);
      if (selectedCalendar == null) {
        return; // User cancelled selection
      }
    } else {
      selectedCalendar = calendarsResult.data!.first; // Use the only calendar
    }

    if (selectedCalendar == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No calendar selected.')),
      );
      return;
    }

    // 4. Create event in selected calendar
    final eventToCreate = Event(selectedCalendar.id.toString()); // Calendar ID is String
    eventToCreate.title = eventName;
    eventToCreate.description = eventDescription;
    eventToCreate.location = eventLocation;

    // Convert DateTime to TZDateTime (using UTC timezone for example)
    final tz.TZDateTime startTZDateTime = tz.TZDateTime.utc(startTime!.year, startTime.month, startTime.day, startTime.hour, startTime.minute);
    final tz.TZDateTime endTZDateTime = tz.TZDateTime.utc(endTime!.year, endTime.month, endTime.day, endTime.hour, endTime.minute);

    eventToCreate.start = startTZDateTime; // Assign TZDateTime
    eventToCreate.end = endTZDateTime;     // Assign TZDateTime
    eventToCreate.allDay = false; // Assuming events are not all-day

    final createEventResult = await _deviceCalendarPlugin.createOrUpdateEvent(eventToCreate);
    if (createEventResult?.isSuccess == true && createEventResult?.data != null) { // Use ?. and check for true explicitly
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Event added to calendar successfully.')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to add event to calendar: ${createEventResult?.exception?.toString() ?? "Unknown error"}')), // Use ?. and null-coalescing operator ??
      );
    }
  }