import 'package:flutter/material.dart';
import 'package:math_expressions/math_expressions.dart';

class AlgebraSolverPage extends StatefulWidget {
  const AlgebraSolverPage({Key? key}) : super(key: key);

  @override
  _AlgebraSolverPageState createState() => _AlgebraSolverPageState();
}

class _AlgebraSolverPageState extends State<AlgebraSolverPage> {
  String _equation = '';
  String _solution = '';

  /// <PERSON>'s method solver.
  double newtonSolve(Expression expr, Expression dExpr, double guess) {
    ContextModel cm = ContextModel();
    double x = guess;
    const int maxIterations = 100;
    const double tolerance = 1e-6;

    for (int i = 0; i < maxIterations; i++) {
      cm.bindVariable(Variable('x'), Number(x));
      double f = expr.evaluate(EvaluationType.REAL, cm);
      double fPrime = dExpr.evaluate(EvaluationType.REAL, cm);

      // If the derivative is too small, break out.
      if (fPrime.abs() < 1e-8) {
        return double.nan;
      }
      double newX = x - f / fPrime;
      if ((newX - x).abs() < tolerance) {
        return newX;
      }
      x = newX;
    }
    return double.nan;
  }

  /// Solves the equation given as a string.
  String solveEquation(String equation) {
    if (!equation.contains('=')) {
      return 'Invalid equation: missing "=".';
    }
    // Preprocess the equation:
    // 1. Remove spaces.
    String processed = equation.replaceAll(' ', '');
    // 2. Insert explicit multiplication (e.g. "2x" becomes "2*x").
    processed = processed.replaceAllMapped(
      RegExp(r'(\d)([a-zA-Z])'),
      (match) => '${match.group(1)}*${match.group(2)}',
    );
    processed = processed.replaceAllMapped(
      RegExp(r'(\d)\('),
      (match) => '${match.group(1)}*(',
    );
    // 3. Replace "log(" with "ln(" to avoid parser issues.
    processed = processed.replaceAll("log(", "ln(");

    // Split the equation at "=".
    List<String> parts = processed.split('=');
    if (parts.length != 2) {
      return 'Invalid equation: must contain exactly one "=".';
    }
    String leftStr = parts[0];
    String rightStr = parts[1];

    try {
      Parser p = Parser();
      Expression leftExp = p.parse(leftStr);
      Expression rightExp = p.parse(rightStr);
      // Form an expression representing 0 = left - right.
      Expression expr = leftExp - rightExp;
      // Get the derivative of the expression with respect to x.
      Expression dExpr = expr.derive('x');

      // Use an initial guess of 0.
      double result = newtonSolve(expr, dExpr, 0.0);
      if (result.isNaN) {
        return 'No solution found or did not converge';
      }
      return 'x = ${result.toString()}';
    } catch (e) {
      return 'Error solving equation: $e';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Algebra Solver',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    decoration: InputDecoration(
                      labelText: 'Enter Equation (e.g., 2x+3=7)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _equation = value;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      setState(() {
                        _solution = solveEquation(_equation);
                      });
                    },
                    child: const Text('Solve Equation'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Solution: $_solution',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
