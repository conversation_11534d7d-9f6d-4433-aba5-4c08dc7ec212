// patents_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'patent_detail_page.dart';

class PatentsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedPatents;
  final bool isFromDetailPage;

  const PatentsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedPatents,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PatentsPage> createState() => _PatentsPageState();
}

class _PatentsPageState extends State<PatentsPage> {
  List<Map<String, dynamic>> _patents = [];
  List<Map<String, dynamic>> _filteredPatents = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _patentsChannel;

  @override
  void initState() {
    super.initState();
    _loadPatents();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _patentsChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterPatents();
    });
  }

  void _filterPatents() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredPatents = List.from(_patents);
      return;
    }

    _filteredPatents = _patents.where((patent) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          patent['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (patent['inventor']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (patent['inventor2']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (patent['inventor3']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (patent['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Faculty/Staff') {
          matchesFilter = patent['facultyorstaffpatent'] == true;
        } else if (_selectedFilter == 'Student') {
          matchesFilter = patent['studentpatent'] == true;
        } else if (_selectedFilter == 'Year') {
          matchesFilter = patent['year'].toString() == _selectedFilter;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadPatents() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedPatents.isNotEmpty) {
        setState(() {
          _patents = widget.preloadedPatents;
          _filteredPatents = widget.preloadedPatents;
          _isLoading = false;
        });
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _patents = cachedData;
          _filteredPatents = cachedData;
          _isLoading = false;
        });
      }

      // Then fetch from Supabase
      final patentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
      final response = await Supabase.instance.client
          .from(patentsTableName)
          .select('*')
          .order('year', ascending: false);

      final patents = List<Map<String, dynamic>>.from(response);
      
      // Cache the data
      await _saveToCache(patents);
      
      setState(() {
        _patents = patents;
        _filteredPatents = patents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading patents: $e';
      });
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'patents_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading patents from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'patents_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving patents to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final patentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
    _patentsChannel = Supabase.instance.client
        .channel('patents_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: patentsTableName,
          callback: (payload) {
            _loadPatents();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> years = {};
    
    for (final patent in _patents) {
      if (patent['year'] != null) {
        years.add(patent['year'].toString());
      }
    }
    
    final List<String> filters = ['All', 'Faculty/Staff', 'Student'];
    filters.addAll(years);
    
    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Patents',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('patents-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _patents.isEmpty && !_isLoading) {
            _loadPatents();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search patents...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterPatents();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadPatents,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredPatents.isEmpty
                          ? const Center(
                              child: Text('No patents found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredPatents.length,
                              itemBuilder: (context, index) {
                                final patent = _filteredPatents[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      patent['fullname'] ?? 'Untitled Patent',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Year: ${patent['year'] ?? 'N/A'}'),
                                        Text('Inventor: ${_formatInventors(patent)}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PatentDetailPage(
                                            patent: patent,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatInventors(Map<String, dynamic> patent) {
    final List<String> inventors = [];
    
    if (patent['inventor'] != null && patent['inventor'].toString().isNotEmpty) {
      inventors.add(patent['inventor'].toString());
    }
    
    if (patent['inventor2'] != null && patent['inventor2'].toString().isNotEmpty) {
      inventors.add(patent['inventor2'].toString());
    }
    
    if (patent['inventor3'] != null && patent['inventor3'].toString().isNotEmpty) {
      inventors.add(patent['inventor3'].toString());
    }
    
    if (inventors.isEmpty) {
      return 'N/A';
    }
    
    return inventors.join(', ');
  }
}
