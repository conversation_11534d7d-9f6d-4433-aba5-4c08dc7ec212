// organizations_clubs_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'organization_club_detail_page.dart';

class OrganizationsClubsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedOrgsClubs;
  final bool isFromDetailPage;

  const OrganizationsClubsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedOrgsClubs,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<OrganizationsClubsPage> createState() => _OrganizationsClubsPageState();
}

class _OrganizationsClubsPageState extends State<OrganizationsClubsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('orgs_clubs_list');
  List<Map<String, dynamic>> _orgsClubs = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("OrganizationsClubsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant OrganizationsClubsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("OrganizationsClubsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("OrganizationsClubsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("OrganizationsClubsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedOrgsClubs != null && widget.preloadedOrgsClubs!.isNotEmpty) {
      print("Preloaded organizations/clubs found, using them.");
      setState(() {
        _orgsClubs = List<Map<String, dynamic>>.from(widget.preloadedOrgsClubs!);
        _orgsClubs.forEach((orgClub) {
          orgClub['_isImageLoading'] = false;
        });
        _orgsClubs.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedOrgsClubs!.length == _pageSize;
      });
      _loadOrgsClubsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded organizations/clubs or empty list, loading from database.");
      _loadOrgsClubsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadOrgsClubsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadOrgsClubsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final orgsClubsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_orgsorclubs';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(orgsClubsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedOrgsClubs =
          await _updateOrgClubImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _orgsClubs = updatedOrgsClubs;
        } else {
          _orgsClubs.addAll(updatedOrgsClubs);
        }
        _orgsClubs.forEach((orgClub) {
          orgClub['_isImageLoading'] = false;
        });
        _orgsClubs.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the organizations/clubs
      _cacheOrgsClubs(_orgsClubs);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching organizations/clubs: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateOrgClubImageUrls(
      List<Map<String, dynamic>> orgsClubs) async {
    List<Future<void>> futures = [];
    for (final orgClub in orgsClubs) {
      if (orgClub['image_url'] == null ||
          orgClub['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(orgClub));
      }
    }
    await Future.wait(futures);
    return orgsClubs;
  }

  Future<void> _cacheOrgsClubs(List<Map<String, dynamic>> orgsClubs) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String orgsClubsJson = jsonEncode(orgsClubs);
      await prefs.setString(
          'orgsorclubs_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          orgsClubsJson);
    } catch (e) {
      print('Error caching organizations/clubs: $e');
    }
  }
  
  void _setupRealtime() {
    final orgsClubsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_orgsorclubs';
    _realtimeChannel = Supabase.instance.client
        .channel('orgs_clubs')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: orgsClubsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newOrgClubId = payload.newRecord['id'];
          final newOrgClubResponse = await Supabase.instance.client
              .from(orgsClubsTableName)
              .select('*')
              .eq('id', newOrgClubId)
              .single();
          if (mounted) {
            Map<String, dynamic> newOrgClub = Map.from(newOrgClubResponse);
            final updatedOrgClub = await _updateOrgClubImageUrls([newOrgClub]);
            setState(() {
              _orgsClubs = [..._orgsClubs, updatedOrgClub.first];
              updatedOrgClub.first['_isImageLoading'] = false;
              _orgsClubs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedOrgClubId = payload.newRecord['id'];
          final updatedOrgClubResponse = await Supabase.instance.client
              .from(orgsClubsTableName)
              .select('*')
              .eq('id', updatedOrgClubId)
              .single();
          if (mounted) {
            final updatedOrgClub = Map<String, dynamic>.from(updatedOrgClubResponse);
            setState(() {
              _orgsClubs = _orgsClubs.map((orgClub) {
                return orgClub['id'] == updatedOrgClub['id'] ? updatedOrgClub : orgClub;
              }).toList();
              _orgsClubs.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedOrgClubId = payload.oldRecord['id'];
          setState(() {
            _orgsClubs.removeWhere((orgClub) => orgClub['id'] == deletedOrgClubId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreOrgsClubs();
    }
  }

  Future<void> _loadMoreOrgsClubs() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadOrgsClubsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> orgClub) async {
    if (orgClub['_isImageLoading'] == true) {
      print('Image loading already in progress for ${orgClub['fullname']}, skipping.');
      return;
    }
    if (orgClub['image_url'] != null &&
        orgClub['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${orgClub['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      orgClub['_isImageLoading'] = true;
    });

    final fullname = orgClub['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeOrgClubBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/orgsorclubs';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeOrgClubBucket');
    print('Image URL before fetch: ${orgClub['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeOrgClubBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeOrgClubBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        orgClub['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        orgClub['_isImageLoading'] = false;
        print('Setting image_url for ${orgClub['fullname']} to: ${orgClub['image_url']}');
      });
    } else {
      orgClub['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> orgClub) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => OrganizationClubDetailPage(
            orgClub: orgClub,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("OrganizationsClubsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Organizations/Clubs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _orgsClubs.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadOrgsClubsFromSupabase(initialLoad: true),
              child: _orgsClubs.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No organizations/clubs available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _orgsClubs.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _orgsClubs.length) {
                          final orgClub = _orgsClubs[index];
                          return VisibilityDetector(
                            key: Key('orgclub_${orgClub['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (orgClub['image_url'] == null ||
                                      orgClub['image_url'] == 'assets/placeholder_image.png') &&
                                  !orgClub['_isImageLoading']) {
                                _fetchImageUrl(orgClub);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: orgClub['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  orgClub['fullname'] ?? 'Unnamed Organization/Club',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    orgClub['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, orgClub),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
