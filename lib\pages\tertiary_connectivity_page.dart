// tertiary_connectivity_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';

class Connectivity {
  final int id;
  final String fullname;
  final String about;

  Connectivity({
    required this.id,
    required this.fullname,
    required this.about,
  });

  factory Connectivity.fromJson(Map<String, dynamic> json) {
    return Connectivity(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Connectivity',
      about: json['about'] ?? 'No description available',
    );
  }

  // Helper method to get an appropriate icon based on the connectivity type
  IconData getIcon() {
    final String name = fullname.toLowerCase();
    if (name.contains('wi-fi') || name.contains('wifi') || name.contains('wireless')) {
      return Icons.wifi;
    } else if (name.contains('mobile') || name.contains('cellular') || name.contains('network')) {
      return Icons.signal_cellular_alt;
    } else if (name.contains('computer') || name.contains('lab')) {
      return Icons.computer;
    } else if (name.contains('print') || name.contains('printing')) {
      return Icons.print;
    } else if (name.contains('support') || name.contains('help') || name.contains('it')) {
      return Icons.support_agent;
    } else if (name.contains('ethernet') || name.contains('lan')) {
      return Icons.lan;
    } else if (name.contains('vpn')) {
      return Icons.vpn_key;
    } else if (name.contains('email') || name.contains('mail')) {
      return Icons.email;
    } else {
      return Icons.settings_ethernet;
    }
  }
}

class TertiaryConnectivityPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;

  const TertiaryConnectivityPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
  }) : super(key: key);

  @override
  State<TertiaryConnectivityPage> createState() => _TertiaryConnectivityPageState();
}

class _TertiaryConnectivityPageState extends State<TertiaryConnectivityPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Connectivity> _connectivityItems = [];

  @override
  void initState() {
    super.initState();
    _fetchConnectivityData();
  }

  Future<void> _fetchConnectivityData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_connectivity';

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final List<Connectivity> connectivityItems = List<Map<String, dynamic>>.from(response)
          .map((json) => Connectivity.fromJson(json))
          .toList();

      setState(() {
        _connectivityItems = connectivityItems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading connectivity information: $e';
      });
      print('Error fetching connectivity data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Connectivity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchConnectivityData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchConnectivityData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _connectivityItems.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.settings_ethernet,
                            size: 64,
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No connectivity information available',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _fetchConnectivityData,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: _connectivityItems.map((item) {
                                  return _buildDetailRow(theme, item.getIcon(), item.fullname, item.about);
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String titleKey, String value) {
    String title = _capitalizeWords(titleKey);
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _capitalizeWords(String text) {
    return text.replaceAllMapped(RegExp(r'\b\w+\b'), (match) {
      final word = match.group(0)!;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    });
  }
}