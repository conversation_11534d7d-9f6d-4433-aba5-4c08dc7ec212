// periodicals_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'periodical_detail_page.dart';

class PeriodicalsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPeriodicals;
  final bool isFromDetailPage;

  const PeriodicalsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPeriodicals,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PeriodicalsPage> createState() => _PeriodicalsPageState();
}

class _PeriodicalsPageState extends State<PeriodicalsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('periodicals_list');
  List<Map<String, dynamic>> _periodicals = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PeriodicalsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PeriodicalsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PeriodicalsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PeriodicalsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PeriodicalsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPeriodicals != null && widget.preloadedPeriodicals!.isNotEmpty) {
      print("Preloaded periodicals found, using them.");
      setState(() {
        _periodicals = List<Map<String, dynamic>>.from(widget.preloadedPeriodicals!);
        _periodicals.forEach((periodical) {
          periodical['_isImageLoading'] = false;
        });
        _periodicals.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedPeriodicals!.length == _pageSize;
      });
      _loadPeriodicalsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded periodicals or empty list, loading from database.");
      _loadPeriodicalsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadPeriodicalsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadPeriodicalsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final periodicalsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_periodicals';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(periodicalsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedPeriodicals =
          await _updatePeriodicalImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _periodicals = updatedPeriodicals;
        } else {
          _periodicals.addAll(updatedPeriodicals);
        }
        _periodicals.forEach((periodical) {
          periodical['_isImageLoading'] = false;
        });
        _periodicals.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the periodicals
      _cachePeriodicals(_periodicals);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching periodicals: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updatePeriodicalImageUrls(
      List<Map<String, dynamic>> periodicals) async {
    List<Future<void>> futures = [];
    for (final periodical in periodicals) {
      if (periodical['image_url'] == null ||
          periodical['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(periodical));
      }
    }
    await Future.wait(futures);
    return periodicals;
  }

  Future<void> _cachePeriodicals(List<Map<String, dynamic>> periodicals) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String periodicalsJson = jsonEncode(periodicals);
      await prefs.setString(
          'periodicals_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          periodicalsJson);
    } catch (e) {
      print('Error caching periodicals: $e');
    }
  }
  
  void _setupRealtime() {
    final periodicalsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_periodicals';
    _realtimeChannel = Supabase.instance.client
        .channel('periodicals')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: periodicalsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newPeriodicalId = payload.newRecord['id'];
          final newPeriodicalResponse = await Supabase.instance.client
              .from(periodicalsTableName)
              .select('*')
              .eq('id', newPeriodicalId)
              .single();
          if (mounted) {
            Map<String, dynamic> newPeriodical = Map.from(newPeriodicalResponse);
            final updatedPeriodical = await _updatePeriodicalImageUrls([newPeriodical]);
            setState(() {
              _periodicals = [..._periodicals, updatedPeriodical.first];
              updatedPeriodical.first['_isImageLoading'] = false;
              _periodicals.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedPeriodicalId = payload.newRecord['id'];
          final updatedPeriodicalResponse = await Supabase.instance.client
              .from(periodicalsTableName)
              .select('*')
              .eq('id', updatedPeriodicalId)
              .single();
          if (mounted) {
            final updatedPeriodical = Map<String, dynamic>.from(updatedPeriodicalResponse);
            setState(() {
              _periodicals = _periodicals.map((periodical) {
                return periodical['id'] == updatedPeriodical['id'] ? updatedPeriodical : periodical;
              }).toList();
              _periodicals.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedPeriodicalId = payload.oldRecord['id'];
          setState(() {
            _periodicals.removeWhere((periodical) => periodical['id'] == deletedPeriodicalId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMorePeriodicals();
    }
  }

  Future<void> _loadMorePeriodicals() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadPeriodicalsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> periodical) async {
    if (periodical['_isImageLoading'] == true) {
      print('Image loading already in progress for ${periodical['fullname']}, skipping.');
      return;
    }
    if (periodical['image_url'] != null &&
        periodical['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${periodical['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      periodical['_isImageLoading'] = true;
    });

    final fullname = periodical['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegePeriodicalBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/periodicals';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegePeriodicalBucket');
    print('Image URL before fetch: ${periodical['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegePeriodicalBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegePeriodicalBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        periodical['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        periodical['_isImageLoading'] = false;
        print('Setting image_url for ${periodical['fullname']} to: ${periodical['image_url']}');
      });
    } else {
      periodical['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> periodical) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PeriodicalDetailPage(
            periodical: periodical,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("PeriodicalsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Periodicals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _periodicals.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadPeriodicalsFromSupabase(initialLoad: true),
              child: _periodicals.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No periodicals available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _periodicals.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _periodicals.length) {
                          final periodical = _periodicals[index];
                          final phone = periodical['phone'] as String? ?? '';
                          final email = periodical['email'] as String? ?? '';
                          final whatsapp = periodical['whatsapp'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('periodical_${periodical['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (periodical['image_url'] == null ||
                                      periodical['image_url'] == 'assets/placeholder_image.png') &&
                                  !periodical['_isImageLoading']) {
                                _fetchImageUrl(periodical);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: periodical['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      periodical['fullname'] ?? 'Unnamed Periodical',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        periodical['about'] ?? '',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: theme.colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                    onTap: () => _navigateToDetail(context, periodical),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                          if (whatsapp.isNotEmpty)
                                            IconButton(
                                              icon: FaIcon(
                                                FontAwesomeIcons.whatsapp,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchWhatsapp(whatsapp),
                                              tooltip: 'WhatsApp',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
