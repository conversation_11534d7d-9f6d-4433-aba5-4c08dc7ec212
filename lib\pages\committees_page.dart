// committees_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'committee_detail_page.dart';

class CommitteesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedCommittees;
  final bool isFromDetailPage;

  const CommitteesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedCommittees,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<CommitteesPage> createState() => _CommitteesPageState();
}

class _CommitteesPageState extends State<CommitteesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('committees_list');
  List<Map<String, dynamic>> _committees = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("CommitteesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant CommitteesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("CommitteesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("CommitteesPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("CommitteesPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedCommittees != null && widget.preloadedCommittees!.isNotEmpty) {
      print("Preloaded committees found, using them.");
      setState(() {
        _committees = List<Map<String, dynamic>>.from(widget.preloadedCommittees!);
        _committees.forEach((committee) {
          committee['_isImageLoading'] = false;
        });
        _committees.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedCommittees!.length == _pageSize;
      });
      _loadCommitteesFromSupabase(initialLoad: false);
    } else {
      print("No preloaded committees or empty list, loading from database.");
      _loadCommitteesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadCommitteesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadCommitteesFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final committeesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_committees';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(committeesTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedCommittees =
          await _updateCommitteeImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _committees = updatedCommittees;
        } else {
          _committees.addAll(updatedCommittees);
        }
        _committees.forEach((committee) {
          committee['_isImageLoading'] = false;
        });
        _committees.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the committees
      _cacheCommittees(_committees);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching committees: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateCommitteeImageUrls(
      List<Map<String, dynamic>> committees) async {
    List<Future<void>> futures = [];
    for (final committee in committees) {
      if (committee['image_url'] == null ||
          committee['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(committee));
      }
    }
    await Future.wait(futures);
    return committees;
  }

  Future<void> _cacheCommittees(List<Map<String, dynamic>> committees) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String committeesJson = jsonEncode(committees);
      await prefs.setString(
          'committees_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          committeesJson);
    } catch (e) {
      print('Error caching committees: $e');
    }
  }
  
  void _setupRealtime() {
    final committeesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_committees';
    _realtimeChannel = Supabase.instance.client
        .channel('committees')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: committeesTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCommitteeId = payload.newRecord['id'];
          final newCommitteeResponse = await Supabase.instance.client
              .from(committeesTableName)
              .select('*')
              .eq('id', newCommitteeId)
              .single();
          if (mounted) {
            Map<String, dynamic> newCommittee = Map.from(newCommitteeResponse);
            final updatedCommittee = await _updateCommitteeImageUrls([newCommittee]);
            setState(() {
              _committees = [..._committees, updatedCommittee.first];
              updatedCommittee.first['_isImageLoading'] = false;
              _committees.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCommitteeId = payload.newRecord['id'];
          final updatedCommitteeResponse = await Supabase.instance.client
              .from(committeesTableName)
              .select('*')
              .eq('id', updatedCommitteeId)
              .single();
          if (mounted) {
            final updatedCommittee = Map<String, dynamic>.from(updatedCommitteeResponse);
            setState(() {
              _committees = _committees.map((committee) {
                return committee['id'] == updatedCommittee['id'] ? updatedCommittee : committee;
              }).toList();
              _committees.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCommitteeId = payload.oldRecord['id'];
          setState(() {
            _committees.removeWhere((committee) => committee['id'] == deletedCommitteeId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreCommittees();
    }
  }

  Future<void> _loadMoreCommittees() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadCommitteesFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> committee) async {
    if (committee['_isImageLoading'] == true) {
      print('Image loading already in progress for ${committee['fullname']}, skipping.');
      return;
    }
    if (committee['image_url'] != null &&
        committee['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${committee['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      committee['_isImageLoading'] = true;
    });

    final fullname = committee['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeCommitteeBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/committees';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeCommitteeBucket');
    print('Image URL before fetch: ${committee['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeCommitteeBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeCommitteeBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        committee['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        committee['_isImageLoading'] = false;
        print('Setting image_url for ${committee['fullname']} to: ${committee['image_url']}');
      });
    } else {
      committee['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> committee) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CommitteeDetailPage(
            committee: committee,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("CommitteesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Committees',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _committees.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadCommitteesFromSupabase(initialLoad: true),
              child: _committees.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No committees available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _committees.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _committees.length) {
                          final committee = _committees[index];
                          return VisibilityDetector(
                            key: Key('committee_${committee['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (committee['image_url'] == null ||
                                      committee['image_url'] == 'assets/placeholder_image.png') &&
                                  !committee['_isImageLoading']) {
                                _fetchImageUrl(committee);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: committee['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  committee['fullname'] ?? 'Unnamed Committee',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    committee['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, committee),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
