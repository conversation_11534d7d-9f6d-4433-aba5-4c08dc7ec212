// boundary_tracker_page.dart
import 'package:flutter/material.dart';
import 'package:ar_flutter_plugin/ar_flutter_plugin.dart';
import 'package:ar_flutter_plugin/datatypes/config_planedetection.dart';
import 'package:ar_flutter_plugin/datatypes/hittest_result.dart';
import 'package:ar_flutter_plugin/models/ar_node.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math';

class BoundaryTrackerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BoundaryTrackerPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<BoundaryTrackerPage> createState() => _BoundaryTrackerPageState();
}

class _BoundaryTrackerPageState extends State<BoundaryTrackerPage> {
  late ArCoreController arCoreController;
  List<ARNode> boundaryNodes = [];
  List<LatLng> plotBoundaries = []; // Define your plot boundaries here
  LatLng? _currentLocation;

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();
    _loadPlotBoundaries(); // Load your plot boundary coordinates
    _getCurrentLocation();
  }

  Future<void> _requestCameraPermission() async {
    var status = await Permission.camera.status;
    if (!status.isGranted) {
      await Permission.camera.request();
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
        _updateARBoundaryOverlay();
      });
    } catch (e) {
      print("Error getting location: $e");
    }
  }

  // Placeholder for loading your actual plot boundary coordinates
  void _loadPlotBoundaries() {
    // Example: Define your plot boundaries as LatLng coordinates
    plotBoundaries = [
      const LatLng(your_latitude_1, your_longitude_1),
      const LatLng(your_latitude_2, your_longitude_2),
      const LatLng(your_latitude_3, your_longitude_3),
      // ... more points
    ];
  }

  void _onArCoreViewCreated(ArCoreController controller) {
    arCoreController = controller;
    _updateARBoundaryOverlay();
  }

  void _updateARBoundaryOverlay() {
    if (arCoreController == null || _currentLocation == null || plotBoundaries.isEmpty) {
      return;
    }

    // First, clear existing nodes
    for (var node in boundaryNodes) {
      arCoreController.removeNode(node.name!);
    }
    boundaryNodes.clear();

    // Approximate conversion from LatLng to AR world space (very simplified)
    const double scaleFactor = 10.0; // Adjust for scaling in AR

    for (int i = 0; i < plotBoundaries.length; i++) {
      final boundaryPoint = plotBoundaries[i];
      final prevBoundaryPoint = plotBoundaries[(i - 1 + plotBoundaries.length) % plotBoundaries.length];

      // Calculate a simple offset based on the difference in coordinates
      final dx = (boundaryPoint.longitude - _currentLocation!.longitude) * 111320 * cos(_currentLocation!.latitude * pi / 180) * scaleFactor;
      final dy = (boundaryPoint.latitude - _currentLocation!.latitude) * 111320 * scaleFactor;

      final prevDx = (prevBoundaryPoint.longitude - _currentLocation!.longitude) * 111320 * cos(_currentLocation!.latitude * pi / 180) * scaleFactor;
      final prevDy = (prevBoundaryPoint.latitude - _currentLocation!.latitude) * 111320 * scaleFactor;

      // Create a line between consecutive boundary points
      final material = ARKitMaterial(
        color: Colors.red,
      );

      final vector.Vector3 start = vector.Vector3(prevDx, 0, -prevDy);
      final vector.Vector3 end = vector.Vector3(dx, 0, -dy);
      final vector.Vector3 direction = (end - start).normalized();
      final double lineLength = (end - start).length;

      final node = ARKitNode(
        name: 'boundary_line_$i',
        geometry: ARKitCylinder(
          height: lineLength,
          radius: 0.1,
          materials: [material],
        ),
        position: start + direction * (lineLength / 2),
        eulerAngles: vector.Vector3(0, atan2(direction.x, direction.z) + pi / 2, 0),
      );

      arCoreController.addARKitNode(node);
      boundaryNodes.add(node);
    }
  }

  @override
  void dispose() {
    arCoreController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Boundary Tracker'),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: Stack(
        children: [
          ArCoreView(
            onArCoreViewCreated: _onArCoreViewCreated,
            enablePlaneDetection: PlaneDetectionConfig.horizontalAndVertical,
          ),
          // Add UI elements to indicate status or provide instructions
          const Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: EdgeInsets.only(bottom: 50.0),
              child: Text(
                'Point your camera to visualize plot boundaries.',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}