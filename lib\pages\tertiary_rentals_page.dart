import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'facility_rentals_page.dart';
import 'equipment_rentals_page.dart';

class Rental {
  final int id;
  final String fullname;
  final String dimensions;
  final int capacity;
  final String phone;
  final String whatsapp;
  final String payment;
  final String department;
  final bool facilityrental;
  final bool equipmentrental;
  final String about;
  final double? latitude;
  final double? longitude;
  final String pricing;

  Rental({
    required this.id,
    required this.fullname,
    required this.dimensions,
    required this.capacity,
    required this.phone,
    required this.whatsapp,
    required this.payment,
    required this.department,
    required this.facilityrental,
    required this.equipmentrental,
    required this.about,
    this.latitude,
    this.longitude,
    required this.pricing,
  });

  factory Rental.fromJson(Map<String, dynamic> json) {
    return Rental(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Rental',
      dimensions: json['dimensions'] ?? 'Not specified',
      capacity: json['capacity'] ?? 0,
      phone: json['phone'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      payment: json['payment'] ?? 'Not specified',
      department: json['department'] ?? '',
      facilityrental: json['facilityrental'] ?? false,
      equipmentrental: json['equipmentrental'] ?? false,
      about: json['about'] ?? 'No description available',
      latitude: json['latitude'],
      longitude: json['longitude'],
      pricing: json['pricing'] ?? 'Not specified',
    );
  }
}

class TertiaryRentalsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isFromDetailPage;

  const TertiaryRentalsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryRentalsPage> createState() => _TertiaryRentalsPageState();
}

class _TertiaryRentalsPageState extends State<TertiaryRentalsPage> {

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Card(
      key: Key('rentals_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          switch (title) {
            case 'Facilities':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FacilityRentalsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
            case 'Equipment':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EquipmentRentalsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Facilities', 'icon': Icons.home_work},
      {'title': 'Equipment', 'icon': Icons.business_outlined},
    ];

    // Filter out 'Equipment' when not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage && item['title'] == 'none') {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Rentals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}