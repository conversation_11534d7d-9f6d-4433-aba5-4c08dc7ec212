import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CourseDetailPage extends StatefulWidget {
  final Map<String, dynamic> course;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const CourseDetailPage({
    Key? key,
    required this.course,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<CourseDetailPage> createState() => _CourseDetailPageState();
}

class _CourseDetailPageState extends State<CourseDetailPage> {
  late RealtimeChannel _courseRealtimeChannel;
  List<Map<String, dynamic>> _prerequisites = [];
  List<Map<String, dynamic>> _corequisites = [];
  bool _isLoadingPrerequisites = false;
  bool _isLoadingCorequisites = false;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
    _loadPrerequisites();
    _loadCorequisites();
  }

  @override
  void dispose() {
    _courseRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _courseRealtimeChannel = Supabase.instance.client
        .channel('course_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'coursecatalog',
      callback: (payload) async {
        // Manual filtering for the specific course
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.course['id']) {
          print("Realtime update received for course detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshCourse();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshCourse() async {
    try {
      final response = await Supabase.instance.client
          .from('coursecatalog')
          .select('*')
          .eq('id', widget.course['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's course with the new data
          widget.course.clear();
          widget.course.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing course: $e");
    }
  }

  Future<void> _loadPrerequisites() async {
    if (_isLoadingPrerequisites) return;

    setState(() {
      _isLoadingPrerequisites = true;
    });

    try {
      final prerequisites = widget.course['prerequisites']?.toString() ?? '';
      if (prerequisites.isEmpty) {
        setState(() {
          _isLoadingPrerequisites = false;
        });
        return;
      }

      // Split prerequisites by comma and trim whitespace
      final prerequisiteCodes = prerequisites.split(',').map((e) => e.trim()).toList();

      if (prerequisiteCodes.isEmpty) {
        setState(() {
          _isLoadingPrerequisites = false;
        });
        return;
      }

      // Build query to find all prerequisite courses
      var query = Supabase.instance.client
          .from('coursecatalog')
          .select('*');

      // Use a filter for the module codes
      if (prerequisiteCodes.length == 1) {
        query = query.eq('modulecode', prerequisiteCodes[0]);
      } else {
        String filter = '';
        for (int i = 0; i < prerequisiteCodes.length; i++) {
          if (i > 0) filter += ',';
          filter += 'modulecode.eq.' + prerequisiteCodes[i];
        }
        query = query.or(filter);
      }

      final response = await query;

      if (mounted) {
        setState(() {
          _prerequisites = List<Map<String, dynamic>>.from(response);
          _isLoadingPrerequisites = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPrerequisites = false;
        });
      }
      print("Error loading prerequisites: $e");
    }
  }

  Future<void> _loadCorequisites() async {
    if (_isLoadingCorequisites) return;

    setState(() {
      _isLoadingCorequisites = true;
    });

    try {
      final corequisites = widget.course['corequisites']?.toString() ?? '';
      if (corequisites.isEmpty) {
        setState(() {
          _isLoadingCorequisites = false;
        });
        return;
      }

      // Split corequisites by comma and trim whitespace
      final corequisiteCodes = corequisites.split(',').map((e) => e.trim()).toList();

      if (corequisiteCodes.isEmpty) {
        setState(() {
          _isLoadingCorequisites = false;
        });
        return;
      }

      // Build query to find all corequisite courses
      var query = Supabase.instance.client
          .from('coursecatalog')
          .select('*');

      // Use a filter for the module codes
      if (corequisiteCodes.length == 1) {
        query = query.eq('modulecode', corequisiteCodes[0]);
      } else {
        String filter = '';
        for (int i = 0; i < corequisiteCodes.length; i++) {
          if (i > 0) filter += ',';
          filter += 'modulecode.eq.' + corequisiteCodes[i];
        }
        query = query.or(filter);
      }

      final response = await query;

      if (mounted) {
        setState(() {
          _corequisites = List<Map<String, dynamic>>.from(response);
          _isLoadingCorequisites = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCorequisites = false;
        });
      }
      print("Error loading corequisites: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String moduleCode = widget.course['modulecode'] ?? '';
    final String moduleName = widget.course['modulename'] ?? 'Unknown';
    final String about = widget.course['about'] ?? '';
    final String major = widget.course['major'] ?? '';
    final String minor = widget.course['minor'] ?? '';
    final String department = widget.course['department'] ?? '';
    final String department2 = widget.course['department2'] ?? '';
    final String department3 = widget.course['department3'] ?? '';
    final String school = widget.course['school'] ?? '';
    final String school2 = widget.course['school2'] ?? '';
    final String school3 = widget.course['school3'] ?? '';
    final String prerequisites = widget.course['prerequisites'] ?? '';
    final String corequisites = widget.course['corequisites'] ?? '';
    final bool isCore = widget.course['corecourse'] == true;
    final bool isMinorRequirement = widget.course['minorrequirement'] == true;
    final bool isElective = widget.course['electivecourse'] == true;
    final int? year = widget.course['year'];
    final String term = widget.course['term'] ?? '';
    final int? credits = widget.course['credits'];
    final int? classesPerWeek = widget.course['classesperweek'];
    final int? labHoursPerWeek = widget.course['labhoursperweek'];
    final int? tutorialHoursPerWeek = widget.course['tutorialhoursperweek'];
    final String aim = widget.course['aim'] ?? '';
    final String learningOutcomes = widget.course['learningoutcomes'] ?? '';
    final String assessment = widget.course['assessment'] ?? '';
    final String substituteModuleCode = widget.course['substitutemodulecode'] ?? '';
    final String substituteModuleName = widget.course['substitutemodulename'] ?? '';
    final bool isOffered = widget.course['offered'] == true;

    // Departments information
    List<String> departments = [];
    if (department.isNotEmpty) departments.add(department);
    if (department2.isNotEmpty) departments.add(department2);
    if (department3.isNotEmpty) departments.add(department3);

    // Schools information
    List<String> schools = [];
    if (school.isNotEmpty) schools.add(school);
    if (school2.isNotEmpty) schools.add(school2);
    if (school3.isNotEmpty) schools.add(school3);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          moduleCode.isNotEmpty ? '$moduleCode: $moduleName' : moduleName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (!isOffered)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Not Offered',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Course details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.book,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  moduleCode.isNotEmpty ? '$moduleCode: $moduleName' : moduleName,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: [
                                    if (isCore)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.primaryContainer,
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'Core',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    if (isMinorRequirement)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.tertiaryContainer,
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'Minor Requirement',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onTertiaryContainer,
                                          ),
                                        ),
                                      ),
                                    if (isElective)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.secondaryContainer,
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'Elective',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSecondaryContainer,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Basic information
                      if (departments.isNotEmpty)
                        _buildDetailRow(theme, Icons.account_balance, 'Department(s)', departments.join(', ')),

                      if (schools.isNotEmpty)
                        _buildDetailRow(theme, Icons.school, 'School(s)', schools.join(', ')),

                      if (major.isNotEmpty)
                        _buildDetailRow(theme, Icons.school_outlined, 'Major', major),

                      if (minor.isNotEmpty)
                        _buildDetailRow(theme, Icons.class_outlined, 'Minor', minor),

                      if (year != null)
                        _buildDetailRow(theme, Icons.calendar_today, 'Year', year.toString()),

                      if (term.isNotEmpty)
                        _buildDetailRow(theme, Icons.event, 'Term', term),

                      if (credits != null)
                        _buildDetailRow(theme, Icons.credit_card, 'Credits', credits.toString()),

                      if (classesPerWeek != null)
                        _buildDetailRow(theme, Icons.schedule, 'Classes per Week', classesPerWeek.toString()),

                      if (labHoursPerWeek != null)
                        _buildDetailRow(theme, Icons.science, 'Lab Hours per Week', labHoursPerWeek.toString()),

                      if (tutorialHoursPerWeek != null)
                        _buildDetailRow(theme, Icons.people, 'Tutorial Hours per Week', tutorialHoursPerWeek.toString()),

                      if (prerequisites.isNotEmpty)
                        _buildDetailRow(theme, Icons.arrow_forward, 'Prerequisites', prerequisites),

                      if (corequisites.isNotEmpty)
                        _buildDetailRow(theme, Icons.compare_arrows, 'Corequisites', corequisites),

                      if (substituteModuleCode.isNotEmpty || substituteModuleName.isNotEmpty)
                        _buildDetailRow(
                          theme,
                          Icons.swap_horiz,
                          'Substitute Module',
                          substituteModuleCode.isNotEmpty && substituteModuleName.isNotEmpty
                            ? '$substituteModuleCode: $substituteModuleName'
                            : substituteModuleCode.isNotEmpty
                                ? substituteModuleCode
                                : substituteModuleName,
                        ),

                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Aim section
              if (aim.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Aim', aim),
              ],

              // Learning outcomes section
              if (learningOutcomes.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Learning Outcomes', learningOutcomes),
              ],

              // Assessment section
              if (assessment.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Assessment', assessment),
              ],

              // Prerequisites section
              if (_prerequisites.isNotEmpty) ...[
                const SizedBox(height: 24),
                Text(
                  'Prerequisite Courses',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _prerequisites.length,
                  itemBuilder: (context, index) {
                    final prerequisite = _prerequisites[index];
                    return Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: currentIsDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(
                            Icons.book,
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        title: Text(
                          '${prerequisite['modulecode'] ?? ''}: ${prerequisite['modulename'] ?? 'Unknown'}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CourseDetailPage(
                                course: prerequisite,
                                isDarkMode: currentIsDarkMode,
                                toggleTheme: widget.toggleTheme,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],

              // Corequisites section
              if (_corequisites.isNotEmpty) ...[
                const SizedBox(height: 24),
                Text(
                  'Corequisite Courses',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _corequisites.length,
                  itemBuilder: (context, index) {
                    final corequisite = _corequisites[index];
                    return Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: currentIsDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(
                            Icons.book,
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        title: Text(
                          '${corequisite['modulecode'] ?? ''}: ${corequisite['modulename'] ?? 'Unknown'}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CourseDetailPage(
                                course: corequisite,
                                isDarkMode: currentIsDarkMode,
                                toggleTheme: widget.toggleTheme,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],

              // Loading indicators
              if (_isLoadingPrerequisites || _isLoadingCorequisites)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, String value) {
    if (value.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(ThemeData theme, String title, String content) {
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: TextStyle(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
