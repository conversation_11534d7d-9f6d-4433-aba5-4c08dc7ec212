import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum Gender { male, female }

enum ActivityLevel { sedentary, light, moderate, active, veryActive, extraActive }

class BmrCalculatorPage extends StatefulWidget {
  const BmrCalculatorPage({Key? key}) : super(key: key);

  @override
  _BmrCalculatorPageState createState() => _BmrCalculatorPageState();
}

class _BmrCalculatorPageState extends State<BmrCalculatorPage> {
  double _weight = 0.0; // in kg
  double _height = 0.0; // in cm
  int _age = 0;
  Gender _gender = Gender.male;
  ActivityLevel _activityLevel = ActivityLevel.sedentary;
  String _bmrResult = '';
  String _dailyCalories = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'BMR & Calorie Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Weight input
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Weight (kg)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurface),
                      ),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _weight = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  // Height input
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Height (cm)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurface),
                      ),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _height = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  // Age input
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Age',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: theme.colorScheme.onSurface),
                      ),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _age = int.tryParse(value) ?? 0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  const SizedBox(height: 20),
                  // Gender radio buttons
                  Row(
                    children: [
                      Radio<Gender>(
                        value: Gender.male,
                        groupValue: _gender,
                        onChanged: (Gender? value) {
                          setState(() {
                            _gender = value!;
                          });
                        },
                      ),
                      Text('Male', style: TextStyle(color: theme.colorScheme.onSurface)),
                      Radio<Gender>(
                        value: Gender.female,
                        groupValue: _gender,
                        onChanged: (Gender? value) {
                          setState(() {
                            _gender = value!;
                          });
                        },
                      ),
                      Text('Female', style: TextStyle(color: theme.colorScheme.onSurface)),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Activity level dropdown
                  DropdownButton<ActivityLevel>(
                    isExpanded: true, // Prevents overflow by using full available width
                    value: _activityLevel,
                    dropdownColor: theme.colorScheme.surface,
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    items: ActivityLevel.values.map((ActivityLevel level) {
                      String levelText;
                      switch (level) {
                        case ActivityLevel.sedentary:
                          levelText = 'Sedentary (little to no exercise)';
                          break;
                        case ActivityLevel.light:
                          levelText = 'Lightly active (light exercise/sports 1-3 days/week)';
                          break;
                        case ActivityLevel.moderate:
                          levelText = 'Moderately active (moderate exercise/sports 3-5 days/week)';
                          break;
                        case ActivityLevel.active:
                          levelText = 'Very active (hard exercise/sports 6-7 days/week)';
                          break;
                        case ActivityLevel.veryActive:
                          levelText = 'Very active (very hard exercise & physical job)';
                          break;
                        case ActivityLevel.extraActive:
                          levelText = 'Extra active (very hard exercise/sports & 2x training)';
                          break;
                      }
                      return DropdownMenuItem<ActivityLevel>(
                        value: level,
                        child: Text(
                          levelText,
                          style: TextStyle(color: theme.colorScheme.onSurface),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (ActivityLevel? value) {
                      setState(() {
                        _activityLevel = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  // Calculate button with white text in light mode.
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateBMRAndCalories();
                    },
                    child: const Text('Calculate BMR & Calories'),
                  ),
                  const SizedBox(height: 20),
                  Text('BMR: $_bmrResult', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Daily Calories (approx): $_dailyCalories', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateBMRAndCalories() {
    if (_weight <= 0 || _height <= 0 || _age <= 0) {
      setState(() {
        _bmrResult = 'Invalid Input';
        _dailyCalories = 'Invalid Input';
      });
      return;
    }

    double bmrValue;
    if (_gender == Gender.male) {
      bmrValue = 10 * _weight + 6.25 * _height - 5 * _age + 5;
    } else {
      bmrValue = 10 * _weight + 6.25 * _height - 5 * _age - 161;
    }

    double activityMultiplier;
    switch (_activityLevel) {
      case ActivityLevel.sedentary:
        activityMultiplier = 1.2;
        break;
      case ActivityLevel.light:
        activityMultiplier = 1.375;
        break;
      case ActivityLevel.moderate:
        activityMultiplier = 1.55;
        break;
      case ActivityLevel.active:
        activityMultiplier = 1.725;
        break;
      case ActivityLevel.veryActive:
        activityMultiplier = 1.9;
        break;
      case ActivityLevel.extraActive:
        activityMultiplier = 2.2;
        break;
    }

    double dailyCaloriesValue = bmrValue * activityMultiplier;

    setState(() {
      _bmrResult = bmrValue.toStringAsFixed(0);
      _dailyCalories = dailyCaloriesValue.toStringAsFixed(0);
    });
  }
}
