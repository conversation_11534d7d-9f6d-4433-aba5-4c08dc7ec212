import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadPreloadedColleges() {
    setState(() {
      _isLoading = true;
    });
    if (MyApp.preloadedColleges != null && MyApp.preloadedColleges!.isNotEmpty) {
      _updateCollegeImageUrls(MyApp.preloadedColleges!).then((updatedColleges) {
        setState(() {
          _colleges = updatedColleges;
          _isLoading = false;
        });
      });
    } else {
      print('Warning: Preloaded colleges are not available.');
      _fetchCollegesFallback();
    }
  }

  Future<void> _fetchCollegesFallback() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('colleges')
          .select('id, fullname, city, state')
          .order('fullname', ascending: true);

      final updatedColleges = response.map((college) => {...college, 'isImageVisible': false}).toList();

      setState(() {
        _colleges = updatedColleges;
        _isLoading = false;
      });
    } catch (error) {
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching colleges: $error')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(List<Map<String, dynamic>> colleges) async {
    for (final college in colleges) {
      if (!college.containsKey('image_url')) {
        final fullname = college['fullname'] as String? ?? '';
        final imageNamePng = '$fullname.png';
        final imageNameJpg = '$fullname.jpg';
        final imageNameWebp = '$fullname.webp';
        String imageUrl = '';

        try {
          await Supabase.instance.client.storage.from('colleges').download(imageNamePng);
          imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNamePng);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from('colleges').download(imageNameJpg);
            imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameJpg);
          } catch (e) {
            try {
              await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
              imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
            } catch (e) {
              // Image not found, use placeholder
            }
          }
        }
        college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      }
    }
    colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
    return colleges;
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'colleges',
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('id, fullname, city, state')
              .eq('id', newCollegeId);
          if (newCollegeResponse.isNotEmpty) {
            final newCollege = newCollegeResponse.first;
            newCollege['isImageVisible'] = false;
            setState(() {
              _colleges = [..._colleges, newCollege];
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedCollegeResponse = await Supabase.instance.client
              .from('colleges')
              .select('id, fullname, city, state')
              .eq('id', updatedCollegeId);
          if (updatedCollegeResponse.isNotEmpty) {
            final updatedCollege = updatedCollegeResponse.first;
            setState(() {
              _colleges = _colleges.map((college) {
                return college['id'] == updatedCollege['id'] ? {...college, ...updatedCollege} : college;
              }).toList();
              _colleges.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCollegeId = payload.oldRecord['id'];
          setState(() {
            _colleges.removeWhere((college) => college['id'] == deletedCollegeId);
          });
        }
      },
    )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed) {
      setState(() {});
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TertiaryDetailPage(
            college: college,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Tertiary',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () async {
                _fetchCollegesFallback();
              },
              child: _colleges.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No colleges available.'),
                            ),
                          ),
                        );
                      })
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _colleges.length,
                      itemBuilder: (context, index) {
                        final university = _colleges[index];
                        return VisibilityDetector(
                          key: Key('college_${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 && !(university['isImageVisible'] ?? false)) {
                              if (university['image_url'] == null && !(university['isFetchingImage'] ?? false)) {
                                _fetchImageUrl(university);
                              }
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.secondary.withOpacity(0.1),
                                backgroundImage: (university['image_url'] != null)
                                    ? CachedNetworkImageProvider(university['image_url']!) as ImageProvider
                                    : const AssetImage('assets/placeholder_image.png'),
                              ),
                              title: Text(
                                university['fullname'] ?? 'Unnamed College',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '${university['city'] ?? ''}${university['state'] != null && university['city'] != null ? ', ' : ''}${university['state'] ?? ''}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    if (college['isFetchingImage'] == true) return;
    setState(() {
      college['isFetchingImage'] = true;
    });
    final fullname = college['fullname'] as String? ?? '';
    final imageNamePng = '$fullname.png';
    final imageNameJpg = '$fullname.jpg';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';

    try {
      await Supabase.instance.client.storage.from('colleges').download(imageNamePng);
      imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNamePng);
    } catch (e) {
      try {
        await Supabase.instance.client.storage.from('colleges').download(imageNameJpg);
        imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameJpg);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from('colleges').download(imageNameWebp);
          imageUrl = Supabase.instance.client.storage.from('colleges').getPublicUrl(imageNameWebp);
        } catch (e) {
          // Image not found, use placeholder
        }
      }
    }

    setState(() {
      college['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      college['isImageVisible'] = true;
      college['isFetchingImage'] = false;
    });
  }
}