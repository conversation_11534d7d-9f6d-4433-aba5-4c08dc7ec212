// orientations_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class OrientationsDetailPage extends StatefulWidget {
  final Map<String, dynamic> orientation;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const OrientationsDetailPage({
    Key? key,
    required this.orientation,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<OrientationsDetailPage> createState() => _OrientationsDetailPageState();
}

class _OrientationsDetailPageState extends State<OrientationsDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _orientationRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupOrientationRealtimeListener();
  }

  @override
  void dispose() {
    _orientationRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.orientation['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupOrientationRealtimeListener() {
    final orientationsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_orientations';
    _orientationRealtimeChannel = Supabase.instance.client
        .channel('orientation_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: orientationsTableName,
      callback: (payload) async {
        if (payload.newRecord['id'] == widget.orientation['id']) {
          print("Realtime UPDATE event received for THIS orientation: ${widget.orientation['fullname']}");
          _fetchUpdatedOrientationData();
        } else {
          print("Realtime UPDATE event received for OTHER orientation, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedOrientationData() async {
    final orientationsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_orientations';
    try {
      final updatedOrientationResponse = await Supabase.instance.client
          .from(orientationsTableName)
          .select('*')
          .eq('id', widget.orientation['id'])
          .single();

      if (mounted && updatedOrientationResponse != null) {
        Map<String, dynamic> updatedOrientation = Map.from(updatedOrientationResponse);
        setState(() {
          widget.orientation.clear();
          widget.orientation.addAll(updatedOrientation);
          _loadImageFromPreloadedData();
          print("Orientation data updated in detail page for ${widget.orientation['fullname']}");
          _updateOrientationsCache(updatedOrientation);
        });
      }
    } catch (error) {
      print("Error fetching updated orientation data: $error");
    }
  }

  Future<void> _updateOrientationsCache(Map<String, dynamic> updatedOrientation) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'orientations_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedOrientationsJson = prefs.getString(cacheKey);

    if (cachedOrientationsJson != null) {
      List<Map<String, dynamic>> cachedOrientations = (jsonDecode(cachedOrientationsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      for (int i = 0; i < cachedOrientations.length; i++) {
        if (cachedOrientations[i]['id'] == updatedOrientation['id']) {
          cachedOrientations[i] = updatedOrientation;
          break;
        }
      }

      await prefs.setString(cacheKey, jsonEncode(cachedOrientations));
      print("Orientations cache updated with realtime change for ${updatedOrientation['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email.')),
      );
    }
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$label copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool currentIsDarkMode = widget.isDarkMode;
    final phone = widget.orientation['phone'] as String? ?? '';
    final email = widget.orientation['email'] as String? ?? '';
    final fax = widget.orientation['fax'] as String? ?? '';
    final about = widget.orientation['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isFaxAvailable = fax.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.orientation['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section - always display
            _isLoadingImage
                ? Container(
                    height: 200,
                    color: theme.colorScheme.surfaceVariant,
                    child: Center(child: CircularProgressIndicator()),
                  )
                : (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                    ? CachedNetworkImage(
                        imageUrl: _imageUrl,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => Container(
                          height: 200,
                          color: theme.colorScheme.surfaceVariant,
                          child: Center(
                            child: Icon(
                              Icons.people_outline,
                              size: 50,
                              color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      )
                    : Container(
                        height: 200,
                        color: theme.colorScheme.surfaceVariant,
                        child: Center(
                          child: Icon(
                            Icons.people_outline,
                            size: 50,
                            color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Orientation details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                backgroundImage: (_imageUrl.isNotEmpty && _imageUrl != 'assets/placeholder_image.png')
                                    ? NetworkImage(_imageUrl) as ImageProvider
                                    : null,
                                child: (_imageUrl.isEmpty || _imageUrl == 'assets/placeholder_image.png')
                                    ? Icon(
                                        Icons.people_outline,
                                        size: 30,
                                        color: currentIsDarkMode ? Colors.white : Colors.black,
                                      )
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.orientation['fullname'] ?? '',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    if (about.isNotEmpty) ...[
                                      Text(
                                        'About',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        about,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: theme.colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Contact information card
                  if (isPhoneAvailable || isEmailAvailable || isFaxAvailable) ...[
                    const SizedBox(height: 16),
                    Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Contact Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            if (isPhoneAvailable) ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.phone,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      phone,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.content_copy,
                                      color: theme.colorScheme.onSurfaceVariant,
                                      size: 20,
                                    ),
                                    onPressed: () => _copyToClipboard(phone, 'Phone number'),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.call,
                                      color: theme.colorScheme.primary,
                                      size: 20,
                                    ),
                                    onPressed: () => _launchDialer(phone),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                            ],
                            if (isEmailAvailable) ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.email,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      email,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.content_copy,
                                      color: theme.colorScheme.onSurfaceVariant,
                                      size: 20,
                                    ),
                                    onPressed: () => _copyToClipboard(email, 'Email'),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.email_outlined,
                                      color: theme.colorScheme.primary,
                                      size: 20,
                                    ),
                                    onPressed: () => _launchEmail(email),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                            ],
                            if (isFaxAvailable) ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.fax,
                                    color: theme.colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      fax,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.content_copy,
                                      color: theme.colorScheme.onSurfaceVariant,
                                      size: 20,
                                    ),
                                    onPressed: () => _copyToClipboard(fax, 'Fax number'),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
