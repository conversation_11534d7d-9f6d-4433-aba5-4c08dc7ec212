import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'login_page.dart';
import 'room_detail_page.dart';
import 'building_detail_page.dart';

class PublicArtDetailPage extends StatefulWidget {
  final Map<String, dynamic> art;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const PublicArtDetailPage({
    Key? key,
    required this.art,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<PublicArtDetailPage> createState() => _PublicArtDetailPageState();
}

class _PublicArtDetailPageState extends State<PublicArtDetailPage> {
  late RealtimeChannel _artRealtimeChannel;
  Map<String, dynamic>? _roomData;
  Map<String, dynamic>? _buildingData;
  bool _isLoadingRoom = false;
  bool _isLoadingBuilding = false;

  @override
  void initState() {
    super.initState();
    _setupArtRealtimeListener();
    _loadRoomData();
    _loadBuildingData();
  }

  @override
  void dispose() {
    _artRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupArtRealtimeListener() {
    final publicArtTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_publicart';
    print('Setting up realtime listener for art detail: $publicArtTableName');
    _artRealtimeChannel = Supabase.instance.client
        .channel('art_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: publicArtTableName,
          callback: (payload) {
            // Check if this change is for the current art
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.art['id']) {
              print('Received update for current art: ${widget.art['fullname']}');
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedArtData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the art is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedArtData() async {
    try {
      final publicArtTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_publicart';
      print('Fetching updated data from table: $publicArtTableName for art ID: ${widget.art['id']}');

      final response = await Supabase.instance.client
          .from(publicArtTableName)
          .select('*')
          .eq('id', widget.art['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedArt = Map.from(response);
        print('Successfully fetched updated art data');

        // Update the widget.art with the new data
        setState(() {
          widget.art.clear(); // Clear old data
          widget.art.addAll(updatedArt); // Add updated data
          print("Art data updated in detail page for ${widget.art['fullname']}");
          _updateArtCache(updatedArt); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated art data: $e');
    }
  }

  Future<void> _updateArtCache(Map<String, dynamic> updatedArt) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'publicart_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Updating cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedArt = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific art in the cache
        bool found = false;
        for (var art in cachedArt) {
          if (art['id'] == updatedArt['id']) {
            updatedCache.add(updatedArt);
            found = true;
            print('Updated art in cache: ${updatedArt['fullname']}');
          } else {
            updatedCache.add(Map<String, dynamic>.from(art));
          }
        }

        if (!found) {
          print('Art not found in cache, adding it');
          updatedCache.add(updatedArt);
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
        print('Successfully updated art cache');
      } else {
        // If cache doesn't exist, create it with just this art
        print('Cache not found, creating new cache with this art');
        await prefs.setString(cacheKey, jsonEncode([updatedArt]));
      }
    } catch (e) {
      print('Error updating art cache: $e');
    }
  }

  Future<void> _loadRoomData() async {
    if (_isLoadingRoom || widget.art['room'] == null) return;

    setState(() {
      _isLoadingRoom = true;
    });

    try {
      final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching room data for: ${widget.art['room']}');

      final response = await Supabase.instance.client
          .from(roomsTableName)
          .select('*')
          .eq('fullname', widget.art['room'])
          .single();

      setState(() {
        _roomData = response;
        _isLoadingRoom = false;
      });
      print('Fetched room data for: ${widget.art['room']}');
    } catch (e) {
      print('Error fetching room data: $e');
      setState(() {
        _isLoadingRoom = false;
      });
    }
  }

  Future<void> _loadBuildingData() async {
    if (_isLoadingBuilding || widget.art['building'] == null) return;

    setState(() {
      _isLoadingBuilding = true;
    });

    try {
      final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_buildings';
      print('Fetching building data for: ${widget.art['building']}');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .eq('fullname', widget.art['building'])
          .single();

      setState(() {
        _buildingData = response;
        _isLoadingBuilding = false;
      });
      print('Fetched building data for: ${widget.art['building']}');
    } catch (e) {
      print('Error fetching building data: $e');
      setState(() {
        _isLoadingBuilding = false;
      });
    }
  }

  Future<void> _launchEmail(String email) async {
    if (email.isEmpty) return;

    final Uri url = Uri.parse('mailto:$email');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch email')),
        );
      }
    }
  }

  Future<void> _launchMaps() async {
    final double latitude = widget.art['latitude'] ?? 0.0;
    final double longitude = widget.art['longitude'] ?? 0.0;

    if (latitude == 0.0 && longitude == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Location coordinates not available')),
      );
      return;
    }

    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch maps')),
        );
      }
    }
  }

  void _viewRoom() {
    if (_roomData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RoomDetailPage(
            room: _roomData!,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _viewBuilding() {
    if (_buildingData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuildingDetailPage(
            building: _buildingData!,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.art['fullname'] ?? 'Unnamed Art';
    final String building = widget.art['building'] ?? '';
    final String room = widget.art['room'] ?? '';
    final String email = widget.art['email'] ?? '';
    final String about = widget.art['about'] ?? '';
    final double latitude = widget.art['latitude'] ?? 0.0;
    final double longitude = widget.art['longitude'] ?? 0.0;

    final bool hasLocation = latitude != 0.0 && longitude != 0.0;
    final bool hasEmail = email.isNotEmpty;
    final bool hasRoom = room.isNotEmpty && _roomData != null;
    final bool hasBuilding = building.isNotEmpty && _buildingData != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasLocation)
              SizedBox(
                height: 200,
                child: FlutterMap(
                  options: MapOptions(
                    initialCenter: LatLng(latitude, longitude),
                    initialZoom: 16.0,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: currentIsDarkMode
                          ? 'https://tile.jawg.io/jawg-dark/{z}/{x}/{y}{r}.png?access-token=your-access-token'
                          : 'https://tile.jawg.io/jawg-light/{z}/{x}/{y}{r}.png?access-token=your-access-token',
                      subdomains: const ['a', 'b', 'c'],
                    ),
                    MarkerLayer(
                      markers: [
                        Marker(
                          width: 40.0,
                          height: 40.0,
                          point: LatLng(latitude, longitude),
                          child: const Icon(
                            Icons.palette,
                            color: Colors.red,
                            size: 40.0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (building.isNotEmpty) ...[
                    const Text(
                      'Building:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(building),
                        if (hasBuilding)
                          TextButton(
                            onPressed: _viewBuilding,
                            child: const Text('View Building'),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (room.isNotEmpty) ...[
                    const Text(
                      'Room:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(room),
                        if (hasRoom)
                          TextButton(
                            onPressed: _viewRoom,
                            child: const Text('View Room'),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (email.isNotEmpty) ...[
                    const Text(
                      'Contact:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(email),
                        IconButton(
                          icon: const Icon(Icons.email),
                          onPressed: () => _launchEmail(email),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (about.isNotEmpty) ...[
                    const Text(
                      'About:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(about),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                if (hasLocation)
                  IconButton(
                    icon: Icon(
                      Icons.directions,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: _launchMaps,
                  ),
                if (hasEmail)
                  IconButton(
                    icon: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () => _launchEmail(email),
                  ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
