import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'athletics_page.dart';
import 'organizations_clubs_page.dart';
import 'research_groups_page.dart';
import 'committees_page.dart';

class TertiaryAthleticsGroupsPage extends StatefulWidget {

  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage; // Added isFromDetailPage

  const TertiaryAthleticsGroupsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false, // Initialize isFromDetailPage
  }) : super(key: key);

  @override
  State<TertiaryAthleticsGroupsPage> createState() => _TertiaryAthleticsGroupsPageState();



}

class _TertiaryAthleticsGroupsPageState extends State<TertiaryAthleticsGroupsPage> {
  // Data for each section
  List<Map<String, dynamic>> _athletics = [];
  List<Map<String, dynamic>> _orgsClubs = [];
  List<Map<String, dynamic>> _researchGroups = [];
  List<Map<String, dynamic>> _committees = [];

  // Loading states
  bool _isLoadingAthletics = false;
  bool _isLoadingOrgsClubs = false;
  bool _isLoadingResearchGroups = false;
  bool _isLoadingCommittees = false;

  // Realtime channels
  late final RealtimeChannel _athleticsChannel;
  late final RealtimeChannel _orgsClubsChannel;
  late final RealtimeChannel _researchGroupsChannel;
  late final RealtimeChannel _committeesChannel;

  @override
  void initState() {
    super.initState();
    _preloadAndCacheData();
    _setupRealtimeChannels();
  }

  @override
  void dispose() {
    _athleticsChannel.unsubscribe();
    _orgsClubsChannel.unsubscribe();
    _researchGroupsChannel.unsubscribe();
    _committeesChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _preloadAndCacheData() async {
    await Future.wait([
      _preloadAthletics(),
      _preloadOrgsClubs(),
      _preloadResearchGroups(),
      _preloadCommittees(),
    ]);
  }

  Future<void> _preloadAthletics() async {
    if (_isLoadingAthletics) return;
    setState(() => _isLoadingAthletics = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('athletics');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _athletics = cachedData;
          _isLoadingAthletics = false;
        });
      }

      // Then fetch from Supabase
      final athleticsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_athletics';
      final response = await Supabase.instance.client
          .from(athleticsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final athletics = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('athletics', athletics);

      setState(() {
        _athletics = athletics;
        _isLoadingAthletics = false;
      });
    } catch (e) {
      print('Error preloading athletics: $e');
      setState(() => _isLoadingAthletics = false);
    }
  }

  Future<void> _preloadOrgsClubs() async {
    if (_isLoadingOrgsClubs) return;
    setState(() => _isLoadingOrgsClubs = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('orgsorclubs');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _orgsClubs = cachedData;
          _isLoadingOrgsClubs = false;
        });
      }

      // Then fetch from Supabase
      final orgsClubsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_orgsorclubs';
      final response = await Supabase.instance.client
          .from(orgsClubsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final orgsClubs = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('orgsorclubs', orgsClubs);

      setState(() {
        _orgsClubs = orgsClubs;
        _isLoadingOrgsClubs = false;
      });
    } catch (e) {
      print('Error preloading organizations/clubs: $e');
      setState(() => _isLoadingOrgsClubs = false);
    }
  }

  Future<void> _preloadResearchGroups() async {
    if (_isLoadingResearchGroups) return;
    setState(() => _isLoadingResearchGroups = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('researchgroups');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _researchGroups = cachedData;
          _isLoadingResearchGroups = false;
        });
      }

      // Then fetch from Supabase
      final researchGroupsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_researchgroups';
      final response = await Supabase.instance.client
          .from(researchGroupsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final researchGroups = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('researchgroups', researchGroups);

      setState(() {
        _researchGroups = researchGroups;
        _isLoadingResearchGroups = false;
      });
    } catch (e) {
      print('Error preloading research groups: $e');
      setState(() => _isLoadingResearchGroups = false);
    }
  }

  Future<void> _preloadCommittees() async {
    if (_isLoadingCommittees) return;
    setState(() => _isLoadingCommittees = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('committees');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _committees = cachedData;
          _isLoadingCommittees = false;
        });
      }

      // Then fetch from Supabase
      final committeesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_committees';
      final response = await Supabase.instance.client
          .from(committeesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final committees = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('committees', committees);

      setState(() {
        _committees = committees;
        _isLoadingCommittees = false;
      });
    } catch (e) {
      print('Error preloading committees: $e');
      setState(() => _isLoadingCommittees = false);
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache(String tableName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading $tableName from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(String tableName, List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving $tableName to cache: $e');
    }
  }

  void _setupRealtimeChannels() {
    _setupAthleticsRealtimeChannel();
    _setupOrgsClubsRealtimeChannel();
    _setupResearchGroupsRealtimeChannel();
    _setupCommitteesRealtimeChannel();
  }

  void _setupAthleticsRealtimeChannel() {
    final athleticsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_athletics';
    _athleticsChannel = Supabase.instance.client
        .channel('athletics_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: athleticsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadAthletics();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadAthletics();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadAthletics();
            }
          },
        )
        .subscribe();
  }

  void _setupOrgsClubsRealtimeChannel() {
    final orgsClubsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_orgsorclubs';
    _orgsClubsChannel = Supabase.instance.client
        .channel('orgsclubs_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: orgsClubsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadOrgsClubs();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadOrgsClubs();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadOrgsClubs();
            }
          },
        )
        .subscribe();
  }

  void _setupResearchGroupsRealtimeChannel() {
    final researchGroupsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_researchgroups';
    _researchGroupsChannel = Supabase.instance.client
        .channel('researchgroups_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: researchGroupsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadResearchGroups();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadResearchGroups();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadResearchGroups();
            }
          },
        )
        .subscribe();
  }

  void _setupCommitteesRealtimeChannel() {
    final committeesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_committees';
    _committeesChannel = Supabase.instance.client
        .channel('committees_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: committeesTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadCommittees();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadCommittees();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadCommittees();
            }
          },
        )
        .subscribe();
  }

  Widget _buildGridItem(
    BuildContext context,
    String title,
    IconData icon,
    ThemeData theme,
    bool isFromDetailPage,
  ) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    void navigateToPage() {
      if (title == 'Athletics/Sports') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AthleticsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedAthletics: _athletics,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Organizations/Clubs') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrganizationsClubsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedOrgsClubs: _orgsClubs,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Research Groups') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ResearchGroupsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedResearchGroups: _researchGroups,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Committees') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CommitteesPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedCommittees: _committees,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      }
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: navigateToPage,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Athletics/Sports', 'icon': Icons.sports},
      {'title': 'Organizations/Clubs', 'icon': Icons.groups},
      {'title': 'Research Groups', 'icon': Icons.science},
      {'title': 'Committees', 'icon': Icons.people_outline},
    ];

    // Filter out 'Research Groups' if not coming from a detail page.
    final filteredGridItems = gridItems.where((item) {
      if (!isFromDetailPage && item['title'] == 'Research Groups') {
        return false;
      }
      return true;
    }).toList();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Athletics & Groups',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}