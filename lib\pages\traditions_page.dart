import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'tradition_detail_page.dart';

class TraditionsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedTraditions;
  final bool isFromDetailPage;

  const TraditionsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedTraditions,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TraditionsPage> createState() => _TraditionsPageState();
}

class _TraditionsPageState extends State<TraditionsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('traditions_list');
  List<Map<String, dynamic>> _traditions = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("TraditionsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant TraditionsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("TraditionsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("TraditionsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("TraditionsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedTraditions != null && widget.preloadedTraditions!.isNotEmpty) {
      print("Preloaded traditions found, using them.");
      setState(() {
        _traditions = List<Map<String, dynamic>>.from(widget.preloadedTraditions!);
        _traditions.forEach((tradition) {
          tradition['_isImageLoading'] = false;
        });
        _traditions.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedTraditions!.length == _pageSize;
      });
      _loadTraditionsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded traditions or empty list, loading from database.");
      _loadTraditionsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadTraditionsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadTraditionsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final traditionsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_traditions';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(traditionsTableName)
          .select('*');

      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedTraditions =
          await _updateTraditionImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _traditions = updatedTraditions;
        } else {
          _traditions.addAll(updatedTraditions);
        }
        _traditions.forEach((tradition) {
          tradition['_isImageLoading'] = false;
        });
        _traditions.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });

      // Cache the traditions
      _cacheTraditions(_traditions);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching traditions: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateTraditionImageUrls(
      List<Map<String, dynamic>> traditions) async {
    List<Future<void>> futures = [];
    for (final tradition in traditions) {
      if (tradition['image_url'] == null ||
          tradition['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(tradition));
      }
    }
    await Future.wait(futures);
    return traditions;
  }

  Future<void> _cacheTraditions(List<Map<String, dynamic>> traditions) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String traditionsJson = jsonEncode(traditions);
      await prefs.setString(
          'traditions_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          traditionsJson);
    } catch (e) {
      print('Error caching traditions: $e');
    }
  }

  void _setupRealtime() {
    final traditionsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_traditions';
    _realtimeChannel = Supabase.instance.client
        .channel('traditions')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: traditionsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newTraditionId = payload.newRecord['id'];
          final newTraditionResponse = await Supabase.instance.client
              .from(traditionsTableName)
              .select('*')
              .eq('id', newTraditionId)
              .single();
          if (mounted) {
            Map<String, dynamic> newTradition = Map.from(newTraditionResponse);
            final updatedTradition = await _updateTraditionImageUrls([newTradition]);
            setState(() {
              _traditions = [..._traditions, updatedTradition.first];
              updatedTradition.first['_isImageLoading'] = false;
              _traditions.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedTraditionId = payload.newRecord['id'];
          final updatedTraditionResponse = await Supabase.instance.client
              .from(traditionsTableName)
              .select('*')
              .eq('id', updatedTraditionId)
              .single();
          if (mounted) {
            final updatedTradition = Map<String, dynamic>.from(updatedTraditionResponse);
            setState(() {
              _traditions = _traditions.map((tradition) {
                return tradition['id'] == updatedTradition['id'] ? updatedTradition : tradition;
              }).toList();
              _traditions.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedTraditionId = payload.oldRecord['id'];
          setState(() {
            _traditions.removeWhere((tradition) => tradition['id'] == deletedTraditionId);
          });
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreTraditions();
    }
  }

  Future<void> _loadMoreTraditions() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadTraditionsFromSupabase(initialLoad: false);
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> tradition) async {
    if (tradition['_isImageLoading'] == true) {
      print('Image loading already in progress for ${tradition['fullname']}, skipping.');
      return;
    }
    if (tradition['image_url'] != null &&
        tradition['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${tradition['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      tradition['_isImageLoading'] = true;
    });

    final fullname = tradition['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeTraditionBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/traditions';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeTraditionBucket');
    print('Image URL before fetch: ${tradition['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeTraditionBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeTraditionBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        tradition['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        tradition['_isImageLoading'] = false;
        print('Setting image_url for ${tradition['fullname']} to: ${tradition['image_url']}');
      });
    } else {
      tradition['_isImageLoading'] = false;
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> tradition) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TraditionDetailPage(
            tradition: tradition,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("TraditionsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Traditions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _traditions.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadTraditionsFromSupabase(initialLoad: true),
              child: _traditions.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No traditions available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _traditions.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _traditions.length) {
                          final tradition = _traditions[index];
                          return VisibilityDetector(
                            key: Key('tradition_${tradition['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (tradition['image_url'] == null ||
                                      tradition['image_url'] == 'assets/placeholder_image.png') &&
                                  !tradition['_isImageLoading']) {
                                _fetchImageUrl(tradition);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: tradition['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  tradition['fullname'] ?? 'Unnamed Tradition',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    tradition['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, tradition),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
