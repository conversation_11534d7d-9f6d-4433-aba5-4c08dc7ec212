// At the top of the file:
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf_render/pdf_render.dart' as pdf_render;
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter_windowmanager_plus/flutter_windowmanager_plus.dart';
import 'package:secure_application/secure_application.dart';
import 'package:secure_application/secure_gate.dart';
import '../widgets/content_helpers.dart';
import '../widgets/pdf_helpers.dart';
import '../widgets/content_segment.dart';
import 'package:path/path.dart' as path;
import 'dart:io' as io;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdf/pdf.dart' as pdf_package;
import 'package:flutter_math_fork/flutter_math.dart';
import '../widgets/latex_helpers.dart' as latex_helpers;
import '../widgets/latex_image_renderer.dart';
import '../utils/text_recognition_helper.dart';
import 'ai_credits_wallet_page.dart';
import '../utils/youtube_transcript_extractor.dart';
import '../utils/url_text_extractor.dart';
import '../utils/docx_extractor.dart';
import '../utils/pptx_extractor.dart';
import '../utils/excel_extractor.dart';
import '../utils/csv_extractor.dart';
import '../utils/audio_extractor.dart';
import '../utils/data_analyzer.dart';
import '../services/supabase_storage_service.dart';
import 'saved_content_page.dart';
import 'package:fl_chart/fl_chart.dart';



class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  // Add text recognition and input controllers here in the STATE class
  final TextEditingController _textInputController = TextEditingController();
  final textRecognizer = TextRecognizer();

  // Combined Markdown and LaTeX renderer widget
  static Widget buildLatexContent(String content, bool isDarkMode, double fontSize) {
    return latex_helpers.buildLatexContent(content, isDarkMode, fontSize);
  }

  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  bool _isResponseComplete = true; // Whether the response is complete or cutoff
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<FileWithPageRange> _pickedFiles = [];
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  List<ExamQuestion> _examQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';
  List<String> _lessonSteps = [];
  int _currentLessonStepIndex = 0;
  bool _lessonPlaying = false;
  double _lessonSpeed = 1.0;
  double _lessonFontSize = 14.0;
  bool _isNarrationMuted = false;

  // Data analysis state removed

  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  get isPlaying => ttsState == TtsState.playing;
  get isStopped => ttsState == TtsState.stopped;
  get isPaused => ttsState == TtsState.paused;
  get isContinued => ttsState == TtsState.continued;
  String _ttsLanguage = 'en-US';
  String? _readingGradeLevel;
  String? _difficultyLevel;
  String? _outputLanguage = 'English';
  String? _numberOfDays = '1 Week';

  String _displayText = '';
  int _currentCharIndex = 0;
  Timer? _textAnimationTimer;
  bool _isTextAnimationActive = false;

  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;



  int? _quizTimeLimitMinutes; // Quiz time limit in minutes
  Timer? _quizTimer; // Timer for quiz
  Duration _timeRemaining = Duration.zero; // Time remaining for quiz

  pw.Font? notoSansRegular;
  pw.Font? notoSansBold;
  pw.Font? notoSansItalic;
  pw.Font? notoSansBoldItalic;
  pw.Font? notoSansSymbols;
  pw.Font? stixTwoMathRegular;
  pw.Font? bravura;
  pw.Font? jetBrainsMonoRegular;
  pw.Font? isocpRegular;
  pw.Font? symbola;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
    _loadFonts();
    _secureScreen();
  }

  Future<void> _secureScreen() async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      // Prevent screenshots and screen recording
      await FlutterWindowManagerPlus.addFlags(FlutterWindowManagerPlus.FLAG_SECURE);

      // Listen for screen recording attempts (iOS)
      if (Platform.isIOS) {
        // iOS specific code for screen recording detection
        // This would typically use native code through method channels
      }
    }
  }

  Future<void> _loadFonts() async {
    // Load Noto Sans variants
    notoSansRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Regular.ttf'));
    notoSansBold = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Bold.ttf'));
    notoSansItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Italic.ttf'));
    notoSansBoldItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf'));
    notoSansSymbols = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSansSymbols-Regular.ttf'));

    // Load STIX Two Math
    stixTwoMathRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/STIXTwoMath-Regular.ttf'));

    // Load special fonts
    bravura = pw.Font.ttf(await rootBundle.load('assets/fonts/Bravura.ttf'));
    jetBrainsMonoRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/JetBrainsMono-Regular.ttf'));
    isocpRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/ISOCP-Regular.ttf')); // Matches pubspec name
    symbola = pw.Font.ttf(await rootBundle.load('assets/fonts/Symbola.ttf'));
  }


  @override
  void didUpdateWidget(covariant DashboardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void dispose() {
    _textAnimationTimer?.cancel();
    _stopTts();
    _cancelQuizTimer();
    textRecognizer.close(); // Close text recognizer here
    super.dispose();
  }



// Camera and problem solving methods removed




  List<InlineSpan> parseText(String text, TextStyle defaultStyle) {
    // Check if the text contains any LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    if (latexRegExp.hasMatch(text)) {
      // If LaTeX is found, use the buildLatexContent method to render it
      return [WidgetSpan(
        child: _DashboardPageState.buildLatexContent(
          text,
          defaultStyle.color == Colors.white, // Determine if dark mode based on text color
          defaultStyle.fontSize ?? 16.0,
        ),
      )];
    }

    // If no LaTeX, continue with the original parsing
    List<InlineSpan> spans = [];
    StringBuffer buffer = StringBuffer();
    int i = 0;

    while (i < text.length) {
      if (text.startsWith('<sup>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sup>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, -5), // Move superscript up
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sup>');
          i += 5;
        }
      } else if (text.startsWith('<sub>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sub>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, 3), // Move subscript down
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sub>');
          i += 5;
        }
      } else if (text[i] == '^') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf(' ', i + 1);
        if (end == -1) end = text.length;
        String exponent = text.substring(i + 1, end);
        spans.add(WidgetSpan(
          child: Transform.translate(
            offset: const Offset(0, -5), // Move exponent up
            child: Text(
              exponent,
              style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
            ),
          ),
        ));
        i = end;
      } else {
        buffer.write(text[i]);
        i++;
      }
    }

    if (buffer.isNotEmpty) {
      spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
    }

    return spans;
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash-exp-image-generation',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf', 'mp3', 'txt', 'doc', 'docx', 'jpg', 'jpeg', 'png',
          'pptx', 'ppt', 'xlsx', 'xls', 'csv'
        ],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<PlatformFile> validFiles = result.files
            .where((file) =>
            (file.bytes != null && file.bytes!.isNotEmpty) ||
                file.path != null)
            .toList();

        if (validFiles.isEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'The selected files could not be processed - no content or path available'),
              ),
            );
          }
          return;
        }

        if (mounted) {
          setState(() {
            _pickedFiles
                .addAll(validFiles.map((file) => FileWithPageRange(file: file)));
            _isUploading = true;
            _uploadProgress = 0.0;
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Files ready: ${_pickedFiles.map((fileRange) => fileRange.file.name).join(", ")}'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  void _deleteFile(FileWithPageRange fileToDeleteRange) {
    setState(() {
      _pickedFiles.remove(fileToDeleteRange);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${fileToDeleteRange.file.name} removed')),
    );
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes,
      {int? startPage, int? endPage}) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final pageCount = document.pages.count;
      final extractor = sf_pdf.PdfTextExtractor(document);

      StringBuffer resultText = StringBuffer();

      int start = startPage != null ? startPage - 1 : 0;
      int end = endPage != null ? endPage - 1 : pageCount - 1;

      start = start.clamp(0, pageCount - 1);
      end = end.clamp(0, pageCount - 1);

      for (int i = start; i <= end; i++) {
        try {
          final pageText =
              extractor.extractText(startPageIndex: i, endPageIndex: i);
          if (pageText.isNotEmpty) {
            resultText.writeln(pageText);
            resultText.writeln();
          }
        } catch (e) {
          print('Error extracting text from page $i: $e');
        }
      }

      document.dispose();
      return resultText.toString();
    } catch (e) {
      print('PDF extraction failed: $e');
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<String?> _extractTextFromAudio(PlatformFile audioFile) async {
    if (!_speechAvailable) {
      _initSpeech();
      if (!_speechAvailable) {
        throw Exception('Speech recognition not available');
      }
    }

    if (audioFile.bytes != null) {
      final tempDir = await getTemporaryDirectory();
      final tempPath = path.join(tempDir.path, audioFile.name);
      final file = File(tempPath);
      await file.writeAsBytes(audioFile.bytes!);

      bool available = await _speech.initialize();
      if (!available) {
        throw Exception('Speech service not initialized');
      }

      String textResult = '';
// In _extractTextFromAudio function:
      try {
        await _speech.listen(
          onResult: (result) {
            textResult = result.recognizedWords;
          },
          listenMode: stt.ListenMode.dictation,
          pauseFor: const Duration(seconds: 3),
          localeId: 'en_US',
          partialResults: false,
          cancelOnError: true,
          // Remove the onAudioBuffer parameter
        );
      } catch (e) {
        print('Speech recognition error: $e');
        throw Exception('Speech recognition error: $e');
      } finally {
        _speech.stop();
        file.delete();
      }
      return textResult;
    } else {
      throw Exception('Audio file bytes are null');
    }
  }


Future<void> _processInput() async {
  if (_textInputController.text.isEmpty && _pickedFiles.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Please select files or enter text')),
    );
    return;
  }

  // Handle chat process type immediately
  if (_processType == 'chat') {
    if (mounted) {
      setState(() {
        _isProcessing = false;
        _processingProgress = 1.0;
      });
    }
    _startChatSession();
    return;
  }

  // Immediate UI update for instant feedback
  setState(() {
    _isProcessing = true;
    _processingProgress = 0.0;
    _geminiOutput = null;
    _flashcards = [];
    _quizQuestions = [];
    _examQuestions = [];
    _lessonSteps = [];
    _isTextAnimationActive = false;
    _cancelQuizTimer();
    _timeRemaining = Duration.zero;
  });

  // Show immediate visual feedback
  final progressController = StreamController<double>();
  progressController.stream.listen((progress) {
    if (mounted) setState(() => _processingProgress = progress);
  });

  // Start progress animation immediately
  for (int i = 0; i <= 10; i++) {
    await Future.delayed(const Duration(milliseconds: 50));
    progressController.add(i / 100);
  }

  try {
    // Check if input is a URL (article or YouTube video)
    final inputText = _textInputController.text.trim();
    if (inputText.isNotEmpty) {
      // Check if it's a URL
      final urlRegex = RegExp(r'^(http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(/\S*)?$');
      final youtubeRegex = RegExp(r'^(https?://)?(www\.)?(youtube\.com|youtu\.?be)/.+$');

      if (urlRegex.hasMatch(inputText)) {
        // Show immediate feedback
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Detected URL. Extracting content...')),
        );

        setState(() {
          _processingProgress = 0.1;
        });

        try {
          String extractedContent = '';

          // Check if it's a YouTube URL
          if (youtubeRegex.hasMatch(inputText)) {
            // Extract YouTube transcript
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Extracting YouTube transcript... This may take a moment.')),
            );

            setState(() {
              _processingProgress = 0.3;
            });

            try {
              // Use the imported YouTube transcript extractor
              extractedContent = await YoutubeTranscriptExtractor.extractTranscript(inputText);
              extractedContent = "YouTube Transcript:\n\n$extractedContent";
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error extracting YouTube transcript: $e')),
              );
              // Continue with regular URL extraction as fallback
              try {
                // Use the imported URL text extractor
                extractedContent = await UrlTextExtractor.extractTextFromUrl(inputText);
                extractedContent = "Article Content:\n\n$extractedContent";
              } catch (e2) {
                throw Exception('Failed to extract content: $e2');
              }
            }
          } else {
            // Regular URL extraction
            setState(() {
              _processingProgress = 0.3;
            });

            try {
              // Use the imported URL text extractor
              extractedContent = await UrlTextExtractor.extractTextFromUrl(inputText);
              extractedContent = "Article Content:\n\n$extractedContent";
            } catch (e) {
              throw Exception('Failed to extract content from URL: $e');
            }
          }

          // Update the text input with the extracted content
          setState(() {
            _textInputController.text = extractedContent;
            _processingProgress = 0.6;
          });

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Content extracted successfully!')),
          );
        } catch (e) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error extracting content: $e')),
          );
          setState(() {
            _isProcessing = false;
          });
          await progressController.close();
          return;
        }
      }
    }

    await Future.microtask(() async {
      String content = '';
      if (_textInputController.text.isNotEmpty) {
        content = _textInputController.text;
        _fileContent = content;
        for (int i = 0; i <= 10; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          progressController.add(i / 10 * 0.6);
        }
      } else if (_pickedFiles.isNotEmpty) {
        content = await _extractContentFromFiles();
        _fileContent = content;
        for (int i = 0; i <= 10; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          progressController.add(i / 10 * 0.6);
        }
      }

      for (int i = 0; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 30));
        progressController.add(0.6 + (i / 10 * 0.3));
      }

      final prompt = _buildPrompt(content);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            _handleResponse(response.text!);
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    });
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Processing failed: $e')),
      );
    }
  } finally {
    await progressController.close();
    if (mounted) {
      setState(() {
        _isProcessing = false;
        if (_processingProgress < 1.0) _processingProgress = 1.0;
      });
    }
  }
}



Future<String> _extractContentFromFiles() async {
  String combinedFileContent = '';
  final totalFiles = _pickedFiles.length;

  for (int fileIndex = 0; fileIndex < totalFiles; fileIndex++) {
    final fileRange = _pickedFiles[fileIndex];
    final pickedFile = fileRange.file;
    String fileContent = '';
    final fileName = pickedFile.name.toLowerCase();
    Uint8List? fileBytes = pickedFile.bytes;
    // Only access path if not on web platform
    String? filePath = kIsWeb ? null : pickedFile.path;
    bool contentExtracted = false;

    print("Processing file: ${pickedFile.name}, Has Bytes: ${fileBytes != null}, Has Path: ${filePath != null}, Is Web: $kIsWeb");

    // --- Ensure we have bytes if possible (Mobile/Desktop) - Only if bytes are null initially ---
    if (fileBytes == null && filePath != null && !kIsWeb) {
      try {
        print("Reading bytes from path (non-web): $filePath");
        fileBytes = await io.File(filePath).readAsBytes();
        print("Bytes read from path successfully: ${fileBytes?.length ?? 0} bytes");
      } catch (e) {
        print("Error reading file from path $filePath (non-web): $e");
        // Don't set error content yet, let subsequent logic handle it
      }
    }

    // --- PDF Handling ---
    if (fileName.endsWith('.pdf')) {
      String extractedText = '';
      bool attemptOcr = false;
      Uint8List? pdfDataToUse = fileBytes; // Prefer bytes first

      // If bytes are still null and we are NOT on web, try the path again (redundant but safe)
      if (pdfDataToUse == null && filePath != null && !kIsWeb) {
           try {
               pdfDataToUse = await io.File(filePath).readAsBytes();
               print("Re-read bytes from path for PDF (non-web): ${pdfDataToUse?.length ?? 0} bytes");
           } catch (e) {
                print("Error re-reading PDF from path $filePath (non-web): $e");
                pdfDataToUse = null;
           }
      }

      // Check if we have data to process the PDF
      if (pdfDataToUse == null) {
          print("Error: No data (bytes or readable non-web path) available for PDF: ${pickedFile.name}");
          fileContent = "[Error: PDF data unavailable or not readable]";
      } else {
          // Attempt direct text extraction first using the available data
          try {
              print("Attempting direct text extraction for PDF: ${pickedFile.name} using ${pdfDataToUse.length} bytes.");
              extractedText = await _extractTextFromPdf(
                      pdfDataToUse,
                      startPage: fileRange.startPage,
                      endPage: fileRange.endPage) ?? '';

              if (extractedText.trim().length < 100) {
                 print("PDF ${pickedFile.name}: Minimal text extracted directly (${extractedText.trim().length} chars), will attempt OCR.");
                 attemptOcr = true;
              } else {
                 print("PDF ${pickedFile.name}: Direct text extraction successful.");
                 fileContent = extractedText;
                 contentExtracted = true;
              }
          } catch (e) {
              print("Direct PDF text extraction failed for ${pickedFile.name}, attempting OCR. Error: $e");
              attemptOcr = true;
              extractedText = ''; // Reset text if direct failed
          }

          // Attempt OCR if needed, using the same data source
          if (attemptOcr) {
              try {
                 print("Processing PDF ${pickedFile.name} with OCR using ${pdfDataToUse.length} bytes...");
                 // Pass BYTES to OCR function. Path is only relevant inside if NOT on web and bytes failed initially.
                 extractedText = await _extractTextFromImagePdf(
                    pdfDataToUse, // ALWAYS pass the bytes we have confirmed are available
                    kIsWeb ? null : filePath, // Only pass path if not on web
                    startPage: fileRange.startPage,
                    endPage: fileRange.endPage);
                 print("OCR processing finished for ${pickedFile.name}. Text length: ${extractedText.length}");
                 fileContent = extractedText;
                 // Consider OCR successful even if text is short
                 contentExtracted = true;
              } catch (e) {
                 print("OCR PDF extraction failed for ${pickedFile.name}: $e");
                 if (fileContent.isEmpty) { // Only set error if no previous content
                   fileContent = '[Error during OCR PDF processing: $e]';
                 }
              }
          }
      }
    }
    // --- PowerPoint Handling ---
    else if (fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
      if (fileBytes != null) {
        try {
          print("Processing PowerPoint ${pickedFile.name} with PptxExtractor...");
          if (fileName.endsWith('.pptx')) {
            fileContent = await PptxExtractor.extractText(fileBytes);
          } else {
            fileContent = PptxExtractor.extractTextFromPpt(fileBytes);
          }

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("PowerPoint extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("PowerPoint extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from PowerPoint ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from PowerPoint: $e]';
        }
      } else {
        print("Cannot process PowerPoint ${pickedFile.name} without bytes.");
        fileContent = '[PowerPoint file content not available]';
      }
    }
    // --- Excel Handling ---
    else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      if (fileBytes != null) {
        try {
          print("Processing Excel ${pickedFile.name} with ExcelExtractor...");
          fileContent = await ExcelExtractor.extractText(fileBytes);

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("Excel extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("Excel extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from Excel ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from Excel: $e]';
        }
      } else {
        print("Cannot process Excel ${pickedFile.name} without bytes.");
        fileContent = '[Excel file content not available]';
      }
    }
    // --- CSV Handling ---
    else if (fileName.endsWith('.csv')) {
      if (fileBytes != null) {
        try {
          print("Processing CSV ${pickedFile.name} with CsvExtractor...");
          fileContent = await CsvExtractor.extractText(fileBytes);

          if (!fileContent.startsWith('[Error')) {
            contentExtracted = true;
            print("CSV extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } else {
            print("CSV extraction returned an error: $fileContent");
          }
        } catch (e) {
          print("Error extracting text from CSV ${pickedFile.name}: $e");
          fileContent = '[Error extracting text from CSV: $e]';
        }
      } else {
        print("Cannot process CSV ${pickedFile.name} without bytes.");
        fileContent = '[CSV file content not available]';
      }
    }
    // --- Image Handling (JPG, PNG, etc.) ---
    else if (fileName.endsWith('.jpg') ||
             fileName.endsWith('.jpeg') ||
             fileName.endsWith('.png') ||
             fileName.endsWith('.bmp') ||
             fileName.endsWith('.gif'))
    {
       print("Processing Image ${pickedFile.name} with OCR...");

       // Check if we have bytes to process
       if (fileBytes != null) {
         try {
           // Create an instance of the TextRecognitionHelper
           final textRecognitionHelper = TextRecognitionHelper();

           // Process the image for text recognition
           fileContent = await textRecognitionHelper.processImageForText(fileBytes, filePath);
           contentExtracted = true;
           print("Enhanced OCR successful for ${pickedFile.name}. Text length: ${fileContent.length}");
         } catch (e) {
           print("Enhanced OCR failed for ${pickedFile.name}: $e");
           fileContent = '[Error during enhanced OCR processing: $e]';
         }
       } else if (filePath != null && !kIsWeb) {
         // Fallback: try to read bytes from file path for non-web platforms
         try {
           print("Reading image bytes from path for ${pickedFile.name}: $filePath");
           fileBytes = await io.File(filePath).readAsBytes();

           final textRecognitionHelper = TextRecognitionHelper();
           fileContent = await textRecognitionHelper.processImageForText(fileBytes, filePath);
           contentExtracted = true;
           print("Enhanced OCR successful from path for ${pickedFile.name}. Text length: ${fileContent.length}");
         } catch (e) {
           print("Enhanced OCR failed from path for ${pickedFile.name}: $e");
           fileContent = '[Error during enhanced OCR processing from path: $e]';
         }
       } else {
         print("Cannot process image ${pickedFile.name} - no bytes or path available");
         fileContent = '[Image file content not available for OCR]';
       }
    }
    // --- TXT Handling ---
    else if (fileName.endsWith('.txt')) {
      try {
          if (fileBytes != null) {
            fileContent = utf8.decode(fileBytes, allowMalformed: true);
            contentExtracted = true;
          } else if (filePath != null && !kIsWeb) {
            fileContent = await io.File(filePath).readAsString();
            contentExtracted = true;
          } else {
             print("TXT file content not available for ${pickedFile.name}");
             fileContent = '[TXT file content not available]';
          }
      } catch (e) {
          print("Error reading TXT file ${pickedFile.name}: $e");
          fileContent = '[Error reading text file: $e]';
      }
    }
    // --- DOC/DOCX Handling with improved extraction ---
    else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
       if (fileBytes != null) {
         try {
           // Use the DocxExtractor for better extraction
           try {
             // Use the imported DOCX extractor
             if (fileName.endsWith('.docx')) {
               fileContent = await DocxExtractor.extractText(fileBytes);
             } else {
               // For older .doc format
               fileContent = DocxExtractor.extractTextFromDoc(fileBytes);
             }

             contentExtracted = true;
             print("Extracted DOC/DOCX ${pickedFile.name} using DocxExtractor");
           } catch (e) {
             print("Error using DocxExtractor for ${pickedFile.name}: $e");

             // Fallback to basic decoding if extractor fails
             try {
               // Try UTF-8 first
               fileContent = utf8.decode(fileBytes, allowMalformed: true);
               // Basic check if decoding likely failed (lots of replacement chars)
               if (fileContent.contains('\uFFFD') && fileContent.length < 100 && fileContent.replaceAll('\uFFFD', '').trim().isEmpty) {
                  throw Exception('Likely invalid UTF-8, trying latin1');
               }
               contentExtracted = true;
               print("Decoded DOC/DOCX ${pickedFile.name} as UTF-8");
             } catch (_) {
               try {
                 // Fallback to Latin-1
                 fileContent = latin1.decode(fileBytes);
                 contentExtracted = true;
                 print("Decoded DOC/DOCX ${pickedFile.name} as Latin-1");
               } catch (e2) {
                 print("Error decoding DOC/DOCX file ${pickedFile.name} with UTF-8 and Latin-1: $e2");
                 fileContent = '[Could not decode DOC/DOCX content - requires specific library for complex formats]';
               }
             }
           }
         } catch (e) {
           print("All DOC/DOCX extraction methods failed for ${pickedFile.name}: $e");
           fileContent = '[Failed to extract content from DOC/DOCX file]';
         }
       } else {
           print("Cannot process DOC/DOCX ${pickedFile.name} without bytes.");
           fileContent = '[Cannot process DOC/DOCX from path or without bytes]';
       }
    }
    // --- MP3 Handling ---
    else if (fileName.endsWith('.mp3')) {
      // Check for bytes first, as path might not be usable on web for audio either
      if (fileBytes != null) {
          try {
              print("Processing MP3 ${pickedFile.name} with AudioExtractor...");
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) { // Check if it's not an error message
                 contentExtracted = true;
                 print("MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 print("MP3 extraction returned an error message: $fileContent");
                 // Fallback to the old method if the new extractor fails
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     bytes: fileBytes);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) { // Check if it's not an error message
                    contentExtracted = true;
                    print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
          } catch (e) {
              print("Audio extraction failed for ${pickedFile.name} (using bytes): $e");
              // Fallback to the old method if the new extractor fails
              try {
                final tempAudioFile = PlatformFile(
                    name: pickedFile.name,
                    size: fileBytes.length,
                    bytes: fileBytes);
                fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                if (!fileContent.startsWith('[')) { // Check if it's not an error message
                   contentExtracted = true;
                   print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                } else {
                   fileContent = '[Error extracting audio content: $e]';
                }
              } catch (e2) {
                print("Both audio extraction methods failed for ${pickedFile.name}: $e2");
                fileContent = '[Error extracting audio content: $e2]';
              }
          }
      } else if (filePath != null && !kIsWeb) {
         // Fallback for non-web if bytes weren't available
         print("Attempting MP3 extraction from path (non-web): $filePath");
         try {
              // Read bytes from file path
              final fileBytes = await io.File(filePath).readAsBytes();
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) {
                 contentExtracted = true;
                 print("MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 // Fallback to old method
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     path: filePath);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) {
                    contentExtracted = true;
                    print("Fallback MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
         } catch (e) {
             print("Audio extraction failed for ${pickedFile.name} (using path): $e");
             fileContent = '[Error extracting audio content from path: $e]';
         }
      } else {
          print("MP3 file ${pickedFile.name} has no bytes or non-web path.");
          fileContent = '[MP3 data unavailable]';
      }
    }
     // --- Unsupported File Type ---
    else {
       if (fileContent.isEmpty) { // Only set if no prior error message exists
         print("Unsupported file type: ${pickedFile.name}");
         fileContent = '[Unsupported file type: ${fileName.split('.').last}]';
       }
    }

    // --- Append to Combined Content ---
    // Include file name header regardless of success to indicate which file was processed
    combinedFileContent += "## ${pickedFile.name}\n\n";
    if (contentExtracted && fileContent.trim().isNotEmpty) {
      combinedFileContent += "$fileContent\n\n";
    } else if (fileContent.isNotEmpty) { // Include error messages/placeholders
      combinedFileContent += "$fileContent\n\n";
    } else { // Fallback if fileContent remained empty for some reason
      combinedFileContent += "[No content extracted or extraction failed without specific error]\n\n";
    }

    print("Finished processing file: ${pickedFile.name}. Content Extracted: $contentExtracted. Appended Length: ${fileContent.length}");

  } // End of file loop

  print("Finished extracting content from all files.");
  return combinedFileContent;
}




// Helper function to convert raw pixels (RGBA) to PNG bytes
Future<Uint8List?> _encodePixelsToPng(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888, // Assuming pdf_render provides RGBA
    (ui.Image img) {
      completer.complete(img);
    },
    // Add onError callback if desired
  );

  try {
    final ui.Image decodedImage = await completer.future;
    final ByteData? byteData = await decodedImage.toByteData(format: ui.ImageByteFormat.png);
    decodedImage.dispose(); // Dispose the ui.Image
    return byteData?.buffer.asUint8List();
  } catch (e) {
    print("Error encoding pixels to PNG: $e");
    return null;
  }
}


Future<String> _extractTextFromImagePdf(
    Uint8List? pdfBytes,
    String? pdfPath,
    {int? startPage, int? endPage}) async {

  StringBuffer combinedText = StringBuffer();
  pdf_render.PdfDocument? doc;
  final currentTextRecognizer = TextRecognizer();

  io.Directory? tempDir; // Mobile only
  String? tempDirPath; // Mobile only

  if (!kIsWeb) {
      try {
          tempDir = await getTemporaryDirectory();
          tempDirPath = tempDir.path;
          print("Using temp directory for PDF OCR (non-web): $tempDirPath");
      } catch (e) {
          print("Error getting temp directory for PDF OCR (non-web): $e");
          await currentTextRecognizer.close();
          return "[Error: Cannot access temp storage for PDF OCR.]";
      }
  } else {
      print("Running PDF OCR on Web - no temp directory needed.");
  }

  try {
    // --- Open the PDF document ---
    if (pdfBytes != null) {
      print("Opening PDF from bytes for OCR.");
      doc = await pdf_render.PdfDocument.openData(pdfBytes);
    } else if (pdfPath != null) {
      if (!kIsWeb) {
         print("Opening PDF from path for OCR (non-web): $pdfPath");
         doc = await pdf_render.PdfDocument.openFile(pdfPath);
      } else {
          print("Error: Cannot open PDF from path on Web for OCR. Bytes required.");
           await currentTextRecognizer.close();
          return "[Error: Cannot open PDF from path on Web.]";
      }
    } else {
      throw Exception("No PDF bytes or path provided for OCR.");
    }

    if (doc == null) {
      throw Exception("Failed to open PDF document for OCR.");
    }

    // --- Determine Page Range ---
    final pageCount = doc.pageCount;
    int start = (startPage != null && startPage > 0) ? startPage - 1 : 0;
    int end = (endPage != null && endPage <= pageCount) ? endPage - 1 : pageCount - 1;
    start = start.clamp(0, pageCount - 1);
    end = end.clamp(start, pageCount - 1);

    print("Processing PDF pages ${start + 1} to ${end + 1} with OCR.");

    // --- Process each page ---
    for (int i = start; i <= end; i++) {
      pdf_render.PdfPage? page;
      io.File? tempImageFile; // Mobile only

      try {
        print("Processing page ${i + 1}...");
        page = await doc.getPage(i + 1);

        // Render page to raw pixels
final pdf_render.PdfPageImage? pageImage = await page.render(
  width: (page.width * 2).toInt(), // Higher resolution
  height: (page.height * 2).toInt(),
  // Format parameter removed if not required by your version
);

        // **** CORRECTED CHECK: Use .pixels ****
        if (pageImage == null || pageImage.pixels == null) {
          print('Warning: Skipping page ${i + 1} due to rendering failure or null pixels.');
          continue;
        }

        // **** CORRECTED ACCESS: Use .pixels ****
        final Uint8List rawPixels = pageImage.pixels;
        final int imageWidth = pageImage.width;
        final int imageHeight = pageImage.height;

        InputImage? inputImage;

        // --- Create InputImage (Platform Specific) ---
if (kIsWeb) {
  // Web: Use InputImage.fromBytes with raw pixel data
  print("Creating InputImage from PDF page raw pixels (Web) for page ${i+1}");
  try {
    inputImage = InputImage.fromBytes(
      bytes: rawPixels,
      metadata: InputImageMetadata(
        size: Size(imageWidth.toDouble(), imageHeight.toDouble()),
        rotation: InputImageRotation.rotation0deg, // Assume no rotation
        format: InputImageFormat.bgra8888, // Changed to match available enum value
        bytesPerRow: imageWidth * 4 // RGBA = 4 bytes/pixel
      )
    );
    print("InputImage created from raw pixels for page ${i + 1} (Web).");
  } catch (e) {
    print("Error creating InputImage from raw pixels on web (Page ${i+1}): $e");
    combinedText.writeln("--- Page ${i + 1} (OCR Prep Error - Raw Pixels Web: $e) ---");
    inputImage = null;
  }
} else {
            // Mobile: Encode pixels to PNG, save PNG to temp file, use fromFilePath
            if (tempDirPath == null) {
                 print("Error: Temp directory path is null on mobile for PDF page ${i + 1}. Skipping OCR.");
                 combinedText.writeln("--- Page ${i + 1} (Error: No Temp Dir) ---");
                 inputImage = null;
            } else {
                print("Encoding page ${i+1} pixels to PNG (Mobile)...");
                final Uint8List? pngBytes = await _encodePixelsToPng(rawPixels, imageWidth, imageHeight);

                if (pngBytes != null) {
                    final tempFileName = 'pdf_page_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.png';
                    final tempFilePath = path.join(tempDirPath, tempFileName);
                    tempImageFile = io.File(tempFilePath);
                    try {
                        await tempImageFile.writeAsBytes(pngBytes);
                        print("Creating InputImage from path for PDF page ${i + 1} (Mobile): $tempFilePath");
                        inputImage = InputImage.fromFilePath(tempFilePath);
                    } catch (e) {
                         print("Error writing/reading temp PNG file on mobile (Page ${i+1}): $e");
                         combinedText.writeln("--- Page ${i + 1} (Temp PNG File Error: $e) ---");
                         inputImage = null;
                         if (await tempImageFile.exists()) {
                             try { await tempImageFile.delete(); } catch (_) {}
                         }
                    }
                } else {
                     print("Error encoding page ${i + 1} pixels to PNG. Skipping OCR.");
                     combinedText.writeln("--- Page ${i + 1} (Error: PNG Encoding Failed) ---");
                     inputImage = null;
                }
            }
        }

        // --- Perform OCR ---
        if (inputImage != null) {
            try {
                print("Performing OCR on page ${i + 1}...");
                final RecognizedText recognizedText = await currentTextRecognizer.processImage(inputImage);
                combinedText.writeln("--- Page ${i + 1} ---");
                combinedText.writeln(recognizedText.text);
                combinedText.writeln();
                print("OCR successful for page ${i + 1}. Text length: ${recognizedText.text.length}");
            } catch (ocrError) {
                 print("Error during OCR processing for page ${i + 1}: $ocrError");
                 combinedText.writeln("--- Page ${i + 1} (OCR Execution Error: $ocrError) ---");
            }
        } else {
             print("Skipping OCR for page ${i + 1} as InputImage creation failed.");
             if (!combinedText.toString().contains("Page ${i + 1} (Error")) {
                 combinedText.writeln("--- Page ${i + 1} (Skipped OCR due to preparation error) ---");
             }
        }

      } catch (pageProcessingError) {
        print('Error processing page ${i + 1}: $pageProcessingError');
        combinedText.writeln("--- Page ${i + 1} (Error: $pageProcessingError) ---");
      } finally {
         // --- Clean up resources for the current page ---
         // *** REMOVED page.close() as it doesn't exist ***

         // Delete mobile temp file if it was created
         if (tempImageFile != null && await tempImageFile.exists()) {
            try {
               await tempImageFile.delete();
               print("Deleted temp file for page ${i + 1}.");
            } catch (deleteError) {
               print("Error deleting temp pdf page file for page ${i + 1}: $deleteError");
            }
         }
      }
    } // End of page loop

  } catch (e) {
    print('Fatal error during PDF OCR process: $e');
    combinedText.writeln("\n[Extraction failed due to error: $e]");
  } finally {
    // --- Final Cleanup ---
    if (doc != null) {
        try {
           doc.dispose(); // Dispose the main PDF document
           print("PDF document disposed.");
        } catch (disposeError) {
           print("Error disposing PDF document: $disposeError");
        }
    }
    try {
       await currentTextRecognizer.close(); // Close the TextRecognizer
       print("TextRecognizer closed.");
    } catch (closeError) {
       print("Error closing TextRecognizer: $closeError");
    }
  }

  return combinedText.toString();
}

// Helper function
Future<ui.Image> _createImageFromPixels(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888,
    (ui.Image img) {
      completer.complete(img);
    },
  );
  return completer.future;
}





// 6. Optional Helper to create InputImage from bytes via temp file
Future<InputImage?> _createInputImageFromBytes(Uint8List imageBytes, String tempDirPath, String tempFileName) async {
  File? tempImageFile;
  try {
      final tempFilePath = path.join(tempDirPath, tempFileName);
      tempImageFile = File(tempFilePath);
      await tempImageFile.writeAsBytes(imageBytes);
      return InputImage.fromFilePath(tempFilePath);
      // Note: The caller is responsible for deleting the temp file after ML Kit processing
  } catch (e) {
      print("Error creating temp file for InputImage: $e");
      // Clean up if file creation failed partially
       try {
            if (tempImageFile != null && await tempImageFile.exists()) {
               await tempImageFile.delete();
            }
       } catch (deleteError) {
           print("Error deleting temp image file during error handling: $deleteError");
       }
      return null;
  }
}




  String _buildPrompt(String content) {
    String gradeLevelText = '';
    if (_readingGradeLevel != null && _readingGradeLevel!.isNotEmpty) {
      gradeLevelText = ' Tailor the content to a ${_readingGradeLevel!.toLowerCase()} reading level.';
    }
    String difficultyLevelText = '';
    if (_difficultyLevel != null && _difficultyLevel!.isNotEmpty) {
      difficultyLevelText = ' Difficulty level: ${_difficultyLevel!.toLowerCase()}.';
    }
    String languageText = '';
    if (_outputLanguage != null && _outputLanguage != 'English') {
      languageText = ' Write the output in ${_outputLanguage!} language. All responses must be in ${_outputLanguage!} language only.';
    }

    final prompts = {
      'notes': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES atleast 50% the length of the original document(s) that teach the subject matter directly:$gradeLevelText$languageText
- Clear headings
- no bullet points infront of number like 1. or (1)
- Bullet points
- Key terms in **bold**
- Examples in *italic*
- no lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
- no lines like *Example:* but Example:
- Use dash (-) for bullet points in markdown
- do not mention the source name
- in pdf export, the header text of every section of the document should be bold, make topic titles bold, headings bold"
- in pdf export properly italicize and make text bold in the right places, dont use * or surround text with ** or lead any text with #. clean output
- include tables whenever necessary
- For tables, use markdown pipe syntax with headers
- DO NOT attempt to generate or include images
- DO NOT use pollinations.ai or any other image generation service
- Use # prefixes for all section headers
- Never use unicode symbols or special characters
- First create a study guide based on the material(s) then Cover EVERY concept in the given file or files exhaustively without omission in atleast 5 pages with unique content on each page and without repeating anything thats been covered before. leave no stone unturned and the notes should be the condensed but comprehensive and exhaustive version of the material, no omissions
- Include ALL foundational elements needed for complete understanding
- Never use "the document states" or similar meta-references - present as primary knowledge
- **Format ALL mathematical equations, formulas, variables, and symbols using LaTeX syntax.**


Content: $content''',
      'chat': '''Use this document as your ONLY knowledge source:
$content
Respond to ALL subsequent questions using ONLY this information but you can infer from the document to give a response to a question.$languageText''',
      'interactive_lesson': '''Generate an interactive lesson from the content provided, suitable for a smartboard presentation for ${gradeLevelText.isNotEmpty ? gradeLevelText : 'a general audience'}.$languageText
Structure the lesson in sequential steps, as if teaching the material step-by-step on a whiteboard. Each step should be a concise point or explanation.
Incorporate placeholders where appropriate to indicate where visual or multimedia elements should be added. Use these placeholders:
- [chart] for charts or graphs
- [image] for images
- [video] for videos
- [audio] for audio clips
- [pdf] for PDF documents
- [docx] for Word documents
- [link: URL] for external URLs, replace URL with actual URL
- draw images when asked

Ensure the lesson is easy to parse into individual steps. Focus on clarity and conciseness for each step, suitable for display on a digital whiteboard.
Example output format (step by step, each on a new line):

Dont include the words Step and number when writing on the board e.g Step 1. Malaria example but should simply be written as Malaria example

Introduction to Supply and Demand

What is Demand? - Demand is how much of a product consumers are willing to buy at different prices.
[chart] - Demand Curve showing price vs quantity demanded.

What is Supply? - Supply is how much of a product producers are willing to sell at different prices.
[chart] - Supply Curve showing price vs quantity supplied.

Market Equilibrium - Equilibrium is where supply and demand meet.
[chart] - Equilibrium Point on Supply and Demand Curves.

Factors Affecting Demand - Discuss factors like income, consumer preferences, etc.

Factors Affecting Supply - Discuss factors like cost of production, technology, etc.

Conclusion - Summary of Supply and Demand concepts.

Content: $content''',
      'cheatsheet': '''Generate a concise yet comprehensive cheatsheet for the content.$gradeLevelText$languageText
Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.
Use dash (-) for bullet points in markdown

Content: $content''',
      'flashcards': '''Generate at least 50 comprehensive and no repeats when we change reading grade level flashcards (Q: question / A: answer) or generate a comprehensive set of flashcards if the content is less but can be comprehensively covered:$gradeLevelText$languageText

Q: [Question]
A: [Answer]

- Each card must contain:
  Front:
  ||Core Concept||: Concise question
  ||Type||: [Definition/Application/Analysis/Connection]

  Full Explanation: (1 sentence)

  Requirements:
  1. Cover EVERY concept from source material
  2. 15-50 cards per major topic
  3. Progressive difficulty within topics
  4. Cross-link cards through connection points
  5. before the full explanation in the back dont put any text like :0 A:

Content: $content''',
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 questions comprehensive and no repeats when we change difficulty level.$difficultyLevelText$languageText Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz. the questions should be different on every difficulty level so there is no repetition of questions

Example Format:
1. What is the capital of France?
A) London
B) Paris
C) Berlin
D) Rome
Answer: B

2. What is the chemical symbol for water?
A) H2O
B) CO2
C) NaCl
D) O2
Answer: A
Now generate a quiz based on the following content, using the EXACT format above:

Content: $content''',
      'transcript': '''Create comprehensive and no repeats when we change reading grade level transcript:$languageText
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',
      'summary': '''Generate a summary/brief of the following content.$languageText

Instruction: Dynamically adapt the summary based on the content type.

If the content is a research paper or academic paper, provide a detailed summary with these sections:
- Background
- Research Question
- Study Method
- Study Limitations
- Global Alignment (if applicable)
- Findings
- include quantitative information where necessary
- Policy Recommendations
- Stakeholder Implications (for donors, government, public, decision-makers, private sector, students, academics)

Otherwise, if the content is not a research paper, provide a concise general summary of the main points.

Content: $content''',
      'exam': '''Generate a comprehensive practice exam with at least 50 NEW questions (different from a quiz, more detailed, paper-based exam style) covering all aspects of the content.$difficultyLevelText$languageText
Use this EXACT format for each question and answer:

[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Full Correct Answer Text - not just the letter, explain the answer in detail]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'minutes': '''Generate comprehensive meeting minutes based on the content provided.$gradeLevelText$languageText
Include:
- Meeting Title
- Date and Time
- Attendees
- Agenda Items
- Discussions
- Action Items
- Decisions

Content: $content''',
      'lesson_plan': '''Generate a detailed lesson plan based on the content.$gradeLevelText$languageText Include:
- Learning objectives
- Required materials
- Step-by-step teaching instructions
- Classroom activities
- Assessment methods
Content: $content''',
      'worksheet': '''Generate a worksheet with practice questions and exercises based on the content designed to be completed in ${_numberOfDays ?? '1 Week'}.$languageText

Content: $content''',
      'homework_guide': '''Generate a homework guide that includes practice problems and solutions based on the content.$gradeLevelText$languageText

Content: $content''',
      'project_ideas': '''Generate project ideas based on the content, suitable for students.$gradeLevelText$languageText

Content: $content''',
      'exam_free': '''Generate a comprehensive free-response exam with essay questions based on the content.$difficultyLevelText$languageText
Use this EXACT format for each question and answer:

[Question Number]. [Question Text]
Answer: [Sample answer or solution to the question]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'exam_case': '''Generate a comprehensive case-based exam with scenario questions based on the content.$difficultyLevelText$languageText
Use this EXACT format for each question and answer:

[Question Number]. [Case Scenario]
[Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Full explanation of the correct answer]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'grammar': '''Check the grammar and suggest improvements for the following text. Provide corrections and explanations.$gradeLevelText$languageText

Content: $content''',
      'paper_grader': '''Provide detailed writing feedback on this paper. Include:$languageText
- Grammar corrections
- Structural improvements
- Argument strength analysis
      - Style suggestions
- Citation feedback
Content: $content''',
      'case_studies': '''Generate relevant case studies or real-life applications based on the content.$gradeLevelText$languageText

Content: $content''',
      'experiment': '''Generate an experiment or lab activity based on the content.$gradeLevelText$languageText

Content: $content''',
      'scheme_of_work': '''Generate a comprehensive scheme of work based on the content for a duration of ${_numberOfDays ?? '1 Week'}.$languageText
Include:
- Learning objectives
- Daily/Weekly breakdown of topics
- Required resources
- Assessment methods
- Suggested activities
- Cross-curricular links

Content: $content''',

      'data_analysis': '''Conduct a comprehensive data analysis on the provided data.$languageText
Include:
- Summary statistics (mean, median, mode, standard deviation, etc.)
- Key trends and patterns
- Correlations between variables
- Visualizations (describe charts that would be useful)
- Insights and recommendations
- Anomalies or outliers
- Potential business implications

Format the output with clear sections, tables for statistics, and detailed descriptions of what visualizations would show.

Content: $content'''
    };


    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    // Check if the response is complete or cutoff
    _isResponseComplete = !response.endsWith('...') &&
                         !response.endsWith('…') &&
                         !response.endsWith('to be continued') &&
                         !response.contains("I'll continue in the next response") &&
                         !response.contains('(continued)') &&
                         !response.contains('To be continued');

    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        _startQuizTimer(); // Start timer when quiz is generated and displayed
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        _parseExam(response);
        break;
      case 'chat':
        _startChatSession();
        break;
      case 'interactive_lesson':
        _parseInteractiveLesson(response);
        break;
      case 'data_analysis':
        // Data analysis parsing removed
        break;
      default:
        break;
    }
  }

  // Continue generating content when the response is cutoff
  Future<void> _continueGenerating() async {
    if (_geminiOutput == null || _isResponseComplete) return;

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
    });

    final progressController = StreamController<double>();
    try {
      progressController.stream.listen((progress) {
        if (mounted) setState(() => _processingProgress = progress);
      });

      // Simulate progress for better UX
      for (int i = 0; i <= 20; i++) {
        progressController.add(i / 100);
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Create a prompt asking to continue from where it left off
      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = ' Continue in ${_outputLanguage!} language only.';
      }
      final prompt = 'Please continue from where you left off. Here is the previous response:\n\n${_geminiOutput}${languageText}';
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            // Append the new response to the existing one
            _geminiOutput = '${_geminiOutput!}\n\n${response.text!}';
            // Check if the response is now complete
            _isResponseComplete = !response.text!.endsWith('...') &&
                               !response.text!.endsWith('…') &&
                               !response.text!.contains("I'll continue in the next response") &&
                               !response.text!.contains('(continued)') &&
                               !response.text!.contains('To be continued');
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to continue generating: $e')),
        );
      }
    } finally {
      await progressController.close();
      if (mounted) {
        setState(() {
          _isProcessing = false;
          if (_processingProgress < 1.0) _processingProgress = 1.0;
        });
      }
    }
  }

  void _parseInteractiveLesson(String response) {
    // Split by double newlines but filter out empty steps
    final rawSteps = response.split('\n\n');
    final steps = <String>[];

    // Process steps to ensure no empty boards
    for (final step in rawSteps) {
      final trimmedStep = step.trim();
      if (trimmedStep.isNotEmpty) {
        steps.add(trimmedStep);
      }
    }

    setState(() {
      _lessonSteps = steps;
      _currentLessonStepIndex = 0;
      _lessonPlaying = true; // Start playing automatically
      _displayText = '';
      _currentCharIndex = 0;
    });

    if (_lessonSteps.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _startLessonStepDisplay();
        }
      });
    }
  }

  // Data analysis method removed but Excel and CSV support maintained

  void _startChatSession() {
    try {
      _chatSession = _geminiModel.startChat();

      // Combine file content and pasted text if available
      String contextContent = _fileContent;
      String pastedText = _textInputController.text.trim();

      if (pastedText.isNotEmpty) {
        contextContent = contextContent.isEmpty ?
            pastedText :
            "$contextContent\n\n$pastedText";
      }

      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = "Respond only in ${_outputLanguage!} language. ";
      }

      final systemMessage = "Document content:\n$contextContent\n\n"
          "You are an assistant for this content. "
          "Base all responses strictly on this information. "
          "If asked about something not in the document be sure to infer and give an answer but be sure to, say "
          "'That information is not in the provided document'. "
          "$languageText";

      _chatSession.sendMessage(Content.text(systemMessage));

      if (mounted) {
        setState(() {
          _chatMessages = [
            ChatMessage(
                "AI: I've analyzed the document and am ready to answer questions",
                false),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
        );
      }
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final blocks = response.split(RegExp(r'\n\s*(?=Q:|Question|\d+\.)'));

    final qaRegex = RegExp(
      r'^(?:Q:|Question|\d+\.?)\s*(.*?)\s*(?:A:|Answer:?|\n)(.*)',
      caseSensitive: false,
      dotAll: true,
    );

    for (final block in blocks) {
      final match = qaRegex.firstMatch(block);
      if (match != null) {
        String question = match.group(1)?.trim() ?? '';
        String answer = match.group(2)?.trim() ?? '';

        // Clean question: Remove leading colons and spaces
        question = question
            .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]|^[:\s]+|[:\s]+$'), '')
            .trim();

        // Clean answer: Remove "::0" and other patterns
        answer = answer
            .replaceAll(RegExp(r'(Definition|Full Explanation|Application|Analysis|Connection|::.*?::|\|\|.*?\|\||[-*#`]|^A:?\s*|::0\s*)'), '')
            .trim();

        // Final trimming: Remove any remaining leading or trailing colons
        question = question.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();
        answer = answer.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();

        if (question.isNotEmpty && answer.isNotEmpty) {
          flashcards.add(Flashcard(
            question: question,
            answer: answer,
          ));
        }
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];
    // Updated regex to be more flexible with different languages
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?=(?:\n\s*[A-D]\))|$)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([\s\S]+?)(?=(?:\n\s*[A-D]\))|(?:\n\s*(?:Answer|Answers|Correct|Réponse|Respuesta|Jawaban|Odpowiedź|Antwoord|Antwort|Risposta|Svar|Vastaus|Svaret|Odpověď|Válasz|Răspuns|Απάντηση|Отговор|Відповідь|Ответ|答案|回答|정답|उत्तर|जवाब|پاسخ|الإجابة|الجواب):))',
        dotAll: true);
    final answerRegex = RegExp(r'(?:Answer|Answers|Correct|Réponse|Respuesta|Jawaban|Odpowiedź|Antwoord|Antwort|Risposta|Svar|Vastaus|Svaret|Odpověď|Válasz|Răspuns|Απάντηση|Отговор|Відповідь|Ответ|答案|回答|정답|उत्तर|जवाब|پاسخ|الإجابة|الجواب):\s*([A-D]?)',
        caseSensitive: false);

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response');

    for (final match in matches) {
      print('\n--- Quiz Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      // Get the full text from this question to the next question
      int startPos = match.start;
      int endPos = response.length;

      // Calculate the next question number
      int currentQuestionNumber = int.parse(match.group(1) ?? '0');
      int nextQuestionNumber = currentQuestionNumber + 1;

      // Find the next question to determine the end of this question block
      final nextQuestionPattern = '(?:\\n|^)\\s*$nextQuestionNumber\\.\\s+';
      final nextQuestionMatch = RegExp(nextQuestionPattern).firstMatch(response.substring(match.end));

      if (nextQuestionMatch != null) {
        endPos = match.end + nextQuestionMatch.start;
      }

      final fullQuestionBlock = response.substring(startPos, endPos);
      print('Full Question Block: $fullQuestionBlock');

      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      // Find the answer in the full question block
      final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
      String correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
      print('Correct Letter: $correctLetter');

      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
        if (correctAnswerIndex == -1) {
          print(
              'Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');

          // Try to infer the correct answer by position (A=0, B=1, etc.)
          final letterIndex = correctLetter.codeUnitAt(0) - 'A'.codeUnitAt(0);
          if (letterIndex >= 0 && letterIndex < options.length) {
            correctAnswerIndex = letterIndex;
            print('Inferred correct answer index by letter position: $correctAnswerIndex');
          } else {
            correctAnswerIndex = null;
          }
        }
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
        _timeRemaining = _quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0
            ? Duration(minutes: _quizTimeLimitMinutes!)
            : Duration.zero; // Reset timer on new quiz
      });
    }
  }

  void _parseExam(String response) {
    final examQuestions = <ExamQuestion>[];

    // Different regex patterns for different exam types
    RegExp questionRegex;
    RegExp optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');

    if (_processType == 'exam_free') {
      // For free response exams, capture the question and sample answer/solution
      // This improved regex captures multi-line questions and answers
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?=(?:\n\s*\d+\.)|(?:\n\s*(?:Sample Answer|Sample Response|Solution|Answer):)|$)([\s\S]*?(?:Sample Answer|Sample Response|Solution|Answer):\s*([\s\S]+?))?(?=(?:\n\s*\d+\.)|$)', dotAll: true);
    } else if (_processType == 'exam_case') {
      // For case-based exams, capture the question and answer
      // This improved regex captures multi-line case questions and solutions
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?=(?:\n\s*\d+\.)|(?:\n\s*(?:Solution|Answer):)|$)([\s\S]*?(?:Solution|Answer):\s*([\s\S]+?))?(?=(?:\n\s*\d+\.)|$)', dotAll: true);
    } else {
      // For regular exams with multiple choice
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer):\s+([^\n]+)', dotAll: true);
    }

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Exam Response:\n$response');

    for (final match in matches) {
      print('\n--- Exam Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      String correctAnswerText = '';

      // Handle different exam types
      if (_processType == 'exam_free' || _processType == 'exam_case') {
        // For free response and case exams, the answer is in group 4
        correctAnswerText = match.group(4)?.trim() ?? '';
        print('Correct Answer Text Group 4: $correctAnswerText');

        // If no answer found in group 4, try group 3 (for backward compatibility)
        if (correctAnswerText.isEmpty) {
          correctAnswerText = match.group(3)?.trim() ?? '';
          print('Fallback to Answer Text Group 3: $correctAnswerText');
        }
      } else {
        // For regular exams, the answer is in group 3
        correctAnswerText = match.group(3)?.trim() ?? '';
        print('Correct Answer Text Group 3: $correctAnswerText');
      }

      examQuestions.add(ExamQuestion(
        question: questionText,
        options: options,
        correctAnswer: correctAnswerText,
      ));
    }

    if (mounted) {
      setState(() {
        _examQuestions = examQuestions;
      });
    }
  }

  void _startQuizTimer() {
    _cancelQuizTimer();
    if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0) {
      _timeRemaining = Duration(minutes: _quizTimeLimitMinutes!);
      _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 0) {
          setState(() => _timeRemaining -= const Duration(seconds: 1));
        } else {
          _cancelQuizTimer();
          _submitQuiz();
        }
      });
    }
  }

  void _cancelQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = null;
  }

  String get _formattedTimeRemaining {
    final minutes = _timeRemaining.inMinutes.remainder(60);
    final seconds = _timeRemaining.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    return _quizQuestions.isEmpty
        ? Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          _isProcessing
              ? 'Generating quiz questions...'
              : 'No quiz questions generated. Try processing the file first.',
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
      ),
    )
        : Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
                if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0)
                  Text(
                    'Time: ${_formattedTimeRemaining}',
                    style: GoogleFonts.notoSans(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.secondary),
                  ),
                Text(
                  'Score: $_quizScore',
                  style: GoogleFonts.notoSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.dividerColor),
              ),
              child: _DashboardPageState.buildLatexContent(
                _quizQuestions[_currentQuestionIndex].question,
                widget.isDarkMode,
                _lessonFontSize > 0 ? _lessonFontSize : 16.0,
              ),
            ),
            const SizedBox(height: 20),
            ..._buildQuizOptions(theme, generalTextColor),
            const SizedBox(height: 24),
            _buildQuizNavigationButtons(theme, generalTextColor),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildQuizOptions(ThemeData theme, Color generalTextColor) {
    return List.generate(
        _quizQuestions[_currentQuestionIndex].options.length, (index) {
      if (_quizQuestions[_currentQuestionIndex].options[index].isEmpty)
        return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Material(
          color: _userAnswers[_currentQuestionIndex] == index
              ? theme.colorScheme.primary.withOpacity(0.2)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: () {
              setState(() => _userAnswers[_currentQuestionIndex] = index);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                    style: GoogleFonts.notoSans(
                        fontWeight: FontWeight.bold, color: generalTextColor),
                  ),
                  Expanded(
                    child: _DashboardPageState.buildLatexContent(
                      _quizQuestions[_currentQuestionIndex].options[index],
                      widget.isDarkMode,
                      _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    ),
                  ),
                  Radio<int>(
                    value: index,
                    groupValue: _userAnswers[_currentQuestionIndex],
                    onChanged: (value) {
                      setState(() => _userAnswers[_currentQuestionIndex] = value);
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildQuizNavigationButtons(ThemeData theme, Color generalTextColor) {
    return ElevatedButton(
      onPressed: _userAnswers[_currentQuestionIndex] != null
          ? () {
        if (_userAnswers[_currentQuestionIndex] ==
            _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
          setState(() => _quizScore++);
        }
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          setState(() => _currentQuestionIndex++);
        } else {
          _submitQuiz();
        }
      }
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 16),
        disabledBackgroundColor: Colors.grey,
      ),
      child: Text(
        _currentQuestionIndex < _quizQuestions.length - 1
            ? 'Next Question'
            : 'Finish Quiz',
        style: GoogleFonts.notoSans(
            color: _userAnswers[_currentQuestionIndex] != null
                ? widget.isDarkMode
                ? Colors.black
                : Colors.white
                : Colors.grey[400],
            fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildExamView(ThemeData theme, Color generalTextColor) {
    // If no exam questions are available, show a placeholder
    if (_examQuestions.isEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            _isProcessing
                ? 'Generating exam questions...'
                : 'No exam questions generated. Process file as Exam.',
            style: GoogleFonts.notoSans(color: generalTextColor),
          ),
        ),
      );
    }

    // For all exam types, show the exam content
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _processType == 'exam_free'
                    ? 'Free Response Exam'
                    : _processType == 'exam_case'
                      ? 'Case-Based Exam'
                      : 'Multiple Choice Exam',
                  style: GoogleFonts.notoSans(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: generalTextColor
                  ),
                  textAlign: TextAlign.center
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.save, color: generalTextColor),
                      onPressed: () => _saveContent(),
                      tooltip: 'Save Content',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._examQuestions.asMap().entries.map((entry) {
              int index = entry.key;
              ExamQuestion question = entry.value;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question text
                  _DashboardPageState.buildLatexContent(
                    '${index + 1}. ${question.question}',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  ),
                  const SizedBox(height: 8),
                  // Options (if any)
                  if (question.options.isNotEmpty)
                    ...List.generate(question.options.length, (optionIndex) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: _DashboardPageState.buildLatexContent(
                          '${String.fromCharCode('A'.codeUnitAt(0) + optionIndex)}) ${question.options[optionIndex]}',
                          widget.isDarkMode,
                          _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                        ),
                      );
                    }),
                  const SizedBox(height: 8),
                  // Answer section
                  if (question.correctAnswer.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: theme.dividerColor),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _processType == 'exam_free' ? 'Sample Response:' :
                            _processType == 'exam_case' ? 'Solution:' : 'Answer:',
                            style: GoogleFonts.notoSans(
                              fontWeight: FontWeight.bold,
                              color: generalTextColor
                            )
                          ),
                          _DashboardPageState.buildLatexContent(
                            question.correctAnswer,
                            widget.isDarkMode,
                            _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 20),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _submitQuiz() {
    final incorrectQuestions = _quizQuestions.where((q) {
      final userAnswer = _userAnswers[_quizQuestions.indexOf(q)];
      return userAnswer != q.correctAnswerIndex;
    }).toList();

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        incorrectQuestions: incorrectQuestions,
        textColor: generalTextColor,
        onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
        userAnswers: _userAnswers,
      ),
    );
  }


  Future<void> _generateWeaknessNotes(List<QuizQuestion> incorrectQuestions) async {
    final incorrectContent = incorrectQuestions.map((q) => q.question).join('\n');
    final prompt = '''Generate comprehensive notes focused on the topics covered in these quiz questions that the user answered incorrectly. Use the following source material to create the notes. Focus specifically on areas where the user demonstrated weakness in the quiz.

Incorrect Questions:
$incorrectContent

Source Material:
$_fileContent''';

    final response = await _geminiModel.generateContent([Content.text(prompt)]);
    if (response.text != null) {
      final weaknessNotes = response.text!;

      // Close the previous dialog
      Navigator.of(context).pop();

      // Show the results dialog with the weakness notes
      showDialog(
        context: context,
        builder: (context) => ExamResultsDialog(
          questions: _quizQuestions,
          incorrectQuestions: incorrectQuestions,
          textColor: generalTextColor,
          onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
          userAnswers: _userAnswers,
          weaknessNotes: weaknessNotes,
        ),
      );

      // Also show the notes in a separate dialog for viewing
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Improvement Notes', style: GoogleFonts.notoSans(color: generalTextColor)),
          content: SingleChildScrollView(
            child: MarkdownBody(
              data: weaknessNotes,
              styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                p: GoogleFonts.notoSans(color: generalTextColor, fontSize: 16),
                h1: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 24),
                h2: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 20),
                h3: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 18),
                h4: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 16),
                h5: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 14),
                h6: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 12),
                code: GoogleFonts.sourceCodePro(backgroundColor: Theme.of(context).cardColor, color: generalTextColor),
                blockquote: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
                strong: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
                em: GoogleFonts.notoSans(fontStyle: FontStyle.italic, color: generalTextColor),
                listBullet: GoogleFonts.notoSans(color: generalTextColor),
                checkbox: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Save', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () async {
                Navigator.of(context).pop();

                // Generate a default title
                final defaultTitle = 'WEAKNESS NOTES - ${DateTime.now().toString().substring(0, 16)}';

                // Show dialog to get title
                final TextEditingController titleController = TextEditingController(text: defaultTitle);
                final title = await showDialog<String>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('Save Weakness Notes',
                      style: TextStyle(color: generalTextColor)),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextField(
                          controller: titleController,
                          decoration: InputDecoration(
                            labelText: 'Title',
                            labelStyle: TextStyle(color: generalTextColor),
                          ),
                          style: TextStyle(color: generalTextColor),
                        ),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Cancel', style: TextStyle(color: generalTextColor)),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context, titleController.text),
                        child: Text('Save', style: TextStyle(color: generalTextColor)),
                      ),
                    ],
                  ),
                );

                if (title == null || title.isEmpty) {
                  return;
                }

                try {
                  // Save content locally
                  final directory = await getApplicationDocumentsDirectory();
                  final fileName = '${title.replaceAll(' ', '_').replaceAll(':', '-')}.txt';
                  final filePath = path.join(directory.path, fileName);
                  final file = File(filePath);

                  // Write the content
                  await file.writeAsString(weaknessNotes);

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Weakness notes saved successfully')),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error saving weakness notes: ${e.toString()}')),
                  );
                }
              },
            ),
            TextButton(
              child: Text('Close', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to generate improvement notes.')),
      );
    }
  }


  Future<List<pw.Widget>> _buildPdfContent(String markdownText, String contentType) async {
    if (contentType == 'exam' || contentType == 'exam_free' || contentType == 'exam_case') {
      return await _buildPdfExamContent(markdownText);
    }
    else if (contentType == 'solution') {
      return _buildSolutionPdfContent(markdownText);
    }
    else {
      return _buildPdfNotesContent(markdownText, contentType); // Pass contentType here
    }
  }


List<pw.Widget> _buildSolutionPdfContent(String solutionText) {
  List<pw.Widget> widgets = [];

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Problem Solution',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold)),
  ));

  // Use the PDF helpers to convert content
  widgets.addAll(convertLatexToPdfWidgets(
    removePreliminaryText(solutionText),  // Remove preliminary text
    notoSansRegular!,
    notoSansBold!,
    notoSansItalic!,
    [
      stixTwoMathRegular!,
      notoSansSymbols!,
      bravura!,
      jetBrainsMonoRegular!,
      isocpRegular!,
      symbola!
    ]
  ));

  return widgets;
}



// New implementation of _buildPdfNotesContent that uses the PDF helpers
List<pw.Widget> _buildPdfNotesContent(String markdownText, String contentType) {
  List<pw.Widget> widgets = [];

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Refactr AI',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ])),
  ));

  // Use the PDF helpers to convert content
  widgets.addAll(convertLatexToPdfWidgets(
    removePreliminaryText(markdownText),  // Remove preliminary text
    notoSansRegular!,
    notoSansBold!,
    notoSansItalic!,
    [
      stixTwoMathRegular!,
      notoSansSymbols!,
      bravura!,
      jetBrainsMonoRegular!,
      isocpRegular!,
      symbola!
    ]
  ));

  return widgets;
}

// Table functionality removed as requested

  Future<List<pw.Widget>> _buildPdfExamContent(String markdownText) async {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];

    for (var line in lines) {
      if (line.trim().isEmpty) continue;

      // Check for LaTeX expressions
      final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
      if (latexRegExp.hasMatch(line)) {
        // Process line with LaTeX
        widgets.add(await _buildPdfLatexLine(line));
      } else {
        // Regular text line
        widgets.add(pw.Text(line,
            style: pw.TextStyle(
                fontSize: 12,
                font: stixTwoMathRegular!,
                fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!])));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  // Helper method to handle LaTeX in PDF export
  Future<pw.Widget> _buildPdfLatexLine(String line) async {
    // Check for LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    final matches = latexRegExp.allMatches(line);

    if (matches.isEmpty) {
      // No LaTeX expressions found, return regular text
      return pw.Text(
        line,
        style: pw.TextStyle(
          fontSize: 12,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      );
    }

    // Process line with LaTeX expressions
    List<pw.Widget> lineWidgets = [];
    int lastEnd = 0;

    for (final match in matches) {
      // Add text before LaTeX expression
      if (match.start > lastEnd) {
        lineWidgets.add(
          pw.Text(
            line.substring(lastEnd, match.start),
            style: pw.TextStyle(
              fontSize: 12,
              font: stixTwoMathRegular!,
              fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
            ),
          ),
        );
      }

      // Render LaTeX expression as image
      final latexExpression = match.group(0)!;
      try {
        // Render LaTeX as image
        final Uint8List? imageData = await LatexImageRenderer.renderLatexAsImage(
          latexExpression,
          14.0, // Font size for LaTeX
          false, // Use light mode for PDF
        );

        if (imageData != null) {
          // Add LaTeX image to PDF
          lineWidgets.add(
            pw.Image(
              pw.MemoryImage(imageData),
              fit: pw.BoxFit.contain,
              height: 24, // Adjust height as needed
            ),
          );
        } else {
          // Fallback to text if image rendering fails
          lineWidgets.add(
            pw.Text(
              latexExpression,
              style: pw.TextStyle(
                fontSize: 12,
                font: stixTwoMathRegular!,
                fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
              ),
            ),
          );
        }
      } catch (e) {
        // Fallback to text if image rendering fails
        lineWidgets.add(
          pw.Text(
            latexExpression,
            style: pw.TextStyle(
              fontSize: 12,
              font: stixTwoMathRegular!,
              fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
            ),
          ),
        );
      }

      lastEnd = match.end;
    }

    // Add any remaining text after the last LaTeX expression
    if (lastEnd < line.length) {
      lineWidgets.add(
        pw.Text(
          line.substring(lastEnd),
          style: pw.TextStyle(
            fontSize: 12,
            font: stixTwoMathRegular!,
            fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
          ),
        ),
      );
    }

    // Return a row with all the widgets
    return pw.Row(
      children: lineWidgets,
      crossAxisAlignment: pw.CrossAxisAlignment.center,
    );
  }

// Convert text to superscript Unicode characters
String _convertToSuperscript(String text) {
  final Map<String, String> superscriptMap = {
    '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
    '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
    'n': 'ⁿ', 'i': 'ⁱ', 'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ',
    'd': 'ᵈ', 'e': 'ᵉ', 'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ',
    'j': 'ʲ', 'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'o': 'ᵒ',
    'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ',
    'v': 'ᵛ', 'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += superscriptMap[char] ?? char;
  }
  return result;
}

// Convert text to subscript Unicode characters
String _convertToSubscript(String text) {
  final Map<String, String> subscriptMap = {
    '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
    '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
    '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
    'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ', 'i': 'ᵢ', 'j': 'ⱼ',
    'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ',
    'p': 'ₚ', 'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ',
    'v': 'ᵥ', 'x': 'ₓ',
  };

  String result = '';
  for (int i = 0; i < text.length; i++) {
    final char = text[i].toLowerCase();
    result += subscriptMap[char] ?? char;
  }
  return result;
}

List<pw.Widget> _parseLineToWidgets(String line) {
  line = line.replaceAllMapped(RegExp(r"'(.+?)'"), (match) => match.group(1)!);
  List<pw.Widget> widgets = [];
  final pattern = RegExp(
    r'(\*\*(.*?)\*\*|__(.*?)__)|(\*(.*?)\*|_(.*?)_)|(<sup>(.*?)</sup>|\^(.*?)(\s|$))|(<sub>(.*?)</sub>)|(~~(.*?)~~)|(`(.*?)`)',
    dotAll: true,
  );

  final matches = pattern.allMatches(line);

  for (final match in matches) {
    // Bold (both ** and __ syntax)
    if (match.group(1) != null) {
      final content = match.group(2) ?? match.group(3) ?? '';
      widgets.add(pw.Text(
        content,
        style: pw.TextStyle(
          fontWeight: pw.FontWeight.bold,
          font: notoSansBold,
        ),
      ));
    }
    // Italics (both * and _ syntax)
    else if (match.group(4) != null) {
      final content = match.group(5) ?? match.group(6) ?? '';
      widgets.add(pw.Text(
        content,
        style: pw.TextStyle(
          fontStyle: pw.FontStyle.italic,
          font: notoSansItalic,
        ),
      ));
    }
    // Superscript (both <sup> and ^ syntax)
    else if (match.group(7) != null) {
      final content = match.group(8) ?? match.group(9) ?? '';
      // Convert to Unicode superscript if possible
      String superscriptText = _convertToSuperscript(content);
      widgets.add(pw.Text(
        superscriptText,
        style: pw.TextStyle(
          fontSize: 10,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      ));
    }
    // Subscript (<sub> syntax)
    else if (match.group(11) != null) {
      final content = match.group(12) ?? '';
      // Convert to Unicode subscript if possible
      String subscriptText = _convertToSubscript(content);
      widgets.add(pw.Text(
        subscriptText,
        style: pw.TextStyle(
          fontSize: 10,
          font: stixTwoMathRegular!,
          fontFallback: [notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
        ),
      ));
    }
    // Strikethrough
    else if (match.group(13) != null) {
      widgets.add(pw.Text(
        match.group(14)!,
        style: pw.TextStyle(
          decoration: pw.TextDecoration.lineThrough,
        ),
      ));
    }
    // Code/monospace
    else if (match.group(15) != null) {
      widgets.add(pw.Text(
        match.group(16)!,
        style: pw.TextStyle(
          font: jetBrainsMonoRegular,
        ),
      ));
    }
  }

  return widgets;
}

MarkdownStyleSheet _getMarkdownStyleSheet(ThemeData theme, Color textColor) {
  return MarkdownStyleSheet(
    p: GoogleFonts.notoSans(color: textColor, fontSize: 16),
    h1: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 24),
    h2: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 20),
    h3: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 18),
    strong: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.w700),
    em: GoogleFonts.notoSans(color: textColor, fontStyle: FontStyle.italic),
    code: GoogleFonts.sourceCodePro(
        fontSize: 14, backgroundColor: theme.cardColor, color: textColor),
  );
}


  Future<void> _downloadContent() async {
    if (_geminiOutput == null) return;

    try {
      // Get default title based on process type
      String defaultTitle = '';
      switch (_processType) {
        case 'lesson_plan':
          defaultTitle = 'Lesson_Plan_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        case 'worksheet':
          defaultTitle = 'Worksheet_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        case 'homework_guide':
          defaultTitle = 'Homework_Guide_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        case 'project_ideas':
          defaultTitle = 'Project_Ideas_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        case 'case_studies':
          defaultTitle = 'Case_Studies_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        case 'scheme_of_work':
          defaultTitle = 'Scheme_of_Work_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
          break;
        default:
          defaultTitle = '${_processType.replaceAll('_', ' ')}_${DateTime.now().toString().substring(0, 16).replaceAll(' ', '_').replaceAll(':', '-')}';
      }

      // Create PDF document
      final pdf = pw.Document();
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (context) => _buildPdfContent(_geminiOutput!, _processType),
        ),
      );

      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        // Web download
        final blob = html.Blob([pdfBytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement
          ..href = url
          ..style.display = 'none'
          ..download = '$defaultTitle.pdf';

        html.document.body!.children.add(anchor);
        anchor.click();

        Future.delayed(const Duration(seconds: 2), () {
          html.Url.revokeObjectUrl(url);
          html.document.body!.children.remove(anchor);
        });
      } else {
        // Mobile/Desktop download
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        final String? outputPath = await FilePicker.platform.saveFile(
          dialogTitle: 'Save PDF Document',
          fileName: '$defaultTitle.pdf',
          allowedExtensions: ['pdf'],
          type: FileType.custom,
        );

        if (outputPath != null) {
          final file = io.File(outputPath);
          await file.writeAsBytes(pdfBytes);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF downloaded successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download failed: ${e.toString()}')),
        );
      }
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages
            .add(ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
        // Update lesson playing state when TTS completes
        if (_lessonPlaying && _currentLessonStepIndex >= _lessonSteps.length - 1) {
          _lessonPlaying = false;
        }
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty && !_isNarrationMuted) {
      await flutterTts.setVolume(volume);
      // Use the lesson speed to control the speech rate
      await flutterTts.setSpeechRate(_lessonSpeed * 0.5);
      await flutterTts.setPitch(pitch);
      await flutterTts.setLanguage(_ttsLanguage);

      String cleanedText = text
          .replaceAll(RegExp(r'<sup>(.*?)</sup>'), r'$1') // Keep superscript content
          .replaceAll(RegExp(r'<sub>(.*?)</sub>'), r'$1') // Keep subscript content
          .replaceAll(RegExp(r'(\w+)\^(\w+)'), r'$1 to the power of $2') // Handle exponents
          .replaceAll(RegExp(r'[*~`#-]'), '')
          .replaceAll(RegExp(r'[\n\r]'), ' ')
          .trim();

      if (ttsState == TtsState.playing) {
        var result = await flutterTts.pause();
        if (result == 1) setState(() => ttsState = TtsState.paused);
      } else {
        var result = await flutterTts.speak(cleanedText);
        if (result == 1) setState(() => ttsState = TtsState.playing);
      }
    }
  }

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }

  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: () => isPlaying
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Colors.grey,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.3),
                trackHeight: 4.0,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
                overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: rate,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: "Speed",
                activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                inactiveColor: Colors.grey,
                onChanged: (double value) {
                  setState(() => rate = value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
appBar: AppBar(
  backgroundColor: theme.colorScheme.surface,
  surfaceTintColor: Colors.transparent,
  title: Text(
    'Grasp It Faster',
    style: TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: theme.colorScheme.onSurface,
    ),
  ),
  actions: [
    IconButton(
      icon: Icon(Icons.save_alt, color: theme.colorScheme.onSurface),
      tooltip: 'Saved Content',
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SavedContentPage(
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
      },
    ),
    IconButton(
      icon: Icon(
        widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
        color: theme.colorScheme.onSurface,
      ),
      onPressed: widget.toggleTheme,
    ),
  ],
),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildFileSection(theme, generalTextColor,
                buttonTextColor, buttonBackground),
            const SizedBox(height: 20),
            _buildProcessingControls(theme, generalTextColor,
                buttonTextColor, buttonBackground),
            const SizedBox(height: 20),
            _buildContentDisplay(theme, generalTextColor),
          ],
        ),
      ),
      // Bottom navigation bar removed entirely
    );
  }

Widget _buildFileSection(ThemeData theme, Color generalTextColor,
    Color buttonTextColor, Color buttonBg) {

// Add this helper function to determine the file icon
Icon _getFileIcon(String fileName) {
  final extension = fileName.split('.').last.toLowerCase();
  final iconColor = generalTextColor.withOpacity(0.6);
  final iconSize = 18.0;
  switch (extension) {
    case 'pdf':
      return Icon(Icons.picture_as_pdf, color: iconColor, size: iconSize);
    case 'txt':
      return Icon(Icons.description, color: iconColor, size: iconSize);
    case 'doc':
    case 'docx':
      return Icon(Icons.article, color: iconColor, size: iconSize);
    case 'jpg':
    case 'jpeg':
    case 'png':
      return Icon(Icons.image, color: iconColor, size: iconSize);
    case 'mp3':
      return Icon(Icons.audiotrack, color: iconColor, size: iconSize);
    case 'ppt':
    case 'pptx':
      return Icon(Icons.slideshow, color: iconColor, size: iconSize);
    case 'xls':
    case 'xlsx':
      return Icon(Icons.table_chart, color: iconColor, size: iconSize);
    case 'csv':
      return Icon(Icons.grid_on, color: iconColor, size: iconSize);
    default:
      return Icon(Icons.insert_drive_file, color: iconColor, size: iconSize);
  }
}

  return Column(
    children: [
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Files',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                icon: Icon(Icons.cloud_upload, color: buttonTextColor),
                label: Text('Select Learning Material(s)',
                    style: GoogleFonts.notoSans(color: buttonTextColor)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonBg,
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                  minimumSize: const Size(double.infinity, 50),
                ),
                onPressed: _isUploading ? null : _pickFile,
              ),
              if (_pickedFiles.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Selected Files:',
                          style: GoogleFonts.notoSans(
                              color: generalTextColor.withOpacity(0.8))),
..._pickedFiles.map((fileRange) => Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _getFileIcon(fileRange.file.name),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  fileRange.file.name,
                  style: GoogleFonts.notoSans(
                      color: generalTextColor.withOpacity(0.6),
                      fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (fileRange.file.name.toLowerCase().endsWith('.pdf'))
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Start Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.startPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'End Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.endPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
              ],
            ),
        ],
      ),
    ),
    IconButton(
      icon: Icon(Icons.delete, color: Colors.red),
      onPressed: () => _deleteFile(fileRange),
    ),
  ],
)).toList(),
                    ],
                  ),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: LinearProgressIndicator(value: _uploadProgress),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                    style: GoogleFonts.notoSans(color: generalTextColor),
                  ),
                ),
            ],
          ),
        ),
      ),
      const SizedBox(height: 16),
      Text(
        'Or',
        style: GoogleFonts.notoSans(
          color: generalTextColor,
          fontSize: 16,
        ),
      ),
      const SizedBox(height: 16),
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enter Text',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _textInputController,
                maxLines: 2, // Reduced to half the original height
                decoration: InputDecoration(
                  hintText: 'Paste or type your text here...',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12), // Further reduced padding
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ],
          ),
        ),
      ),
    ],
  );
}

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor,
      Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: [
                const DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                const DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                const DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                const DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'minutes', child: Text('Create Meeting Minutes')),
                const DropdownMenuItem(value: 'chat', child: Text('AI Tutor / Chat with Content')),
                const DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                const DropdownMenuItem(value: 'lesson_plan', child: Text('Create Lesson Plan')),
                const DropdownMenuItem(value: 'worksheet', child: Text('Worksheet')),
                const DropdownMenuItem(value: 'homework_guide', child: Text('Homework Guide')),
                const DropdownMenuItem(value: 'project_ideas', child: Text('Project Ideas')),
                const DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
                const DropdownMenuItem(value: 'exam_free', child: Text('Exam (Free Response)')),
                const DropdownMenuItem(value: 'exam_case', child: Text('Exam (Case Question)')),
                const DropdownMenuItem(value: 'case_studies', child: Text('Case Studies')),
                const DropdownMenuItem(value: 'experiment', child: Text('Experiment/Lab')),
                const DropdownMenuItem(value: 'interactive_lesson', child: Text('Interactive Lesson')),
                const DropdownMenuItem(value: 'scheme_of_work', child: Text('Scheme of Work')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _processType = value;
                    _geminiOutput = null;
                    _flashcards = [];
                    _quizQuestions = [];
                    _examQuestions = [];
                    _chatMessages = [];
                    _readingGradeLevel = null;
                    _difficultyLevel = null;
                    _quizTimeLimitMinutes = null;
                    _cancelQuizTimer();
                    _timeRemaining = Duration.zero;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            // Language dropdown
            DropdownButtonFormField<String>(
              value: _outputLanguage,
              decoration: InputDecoration(
                labelText: 'Output Language',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: const [
                DropdownMenuItem(value: 'English', child: Text('English')),
                DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')),
                DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                DropdownMenuItem(value: 'Swahili', child: Text('Swahili')),
                DropdownMenuItem(value: 'Shona', child: Text('Shona')),
                DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
              ],
              onChanged: (value) {
                setState(() {
                  _outputLanguage = value;
                });
              },
            ),
            const SizedBox(height: 16),
            if (_processType != 'quiz' && _processType != 'exam' && _processType != 'exam_free' && _processType != 'exam_case' && _processType != 'scheme_of_work' && _processType != 'worksheet')
              DropdownButtonFormField<String>(
                value: _readingGradeLevel,
                decoration: InputDecoration(
                  labelText: 'Reading Grade Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Grade 1', child: Text('Grade 1')),
                  DropdownMenuItem(value: 'Grade 2', child: Text('Grade 2')),
                  DropdownMenuItem(value: 'Grade 3', child: Text('Grade 3')),
                  DropdownMenuItem(value: 'Grade 4', child: Text('Grade 4')),
                  DropdownMenuItem(value: 'Grade 5', child: Text('Grade 5')),
                  DropdownMenuItem(value: 'Grade 6', child: Text('Grade 6')),
                  DropdownMenuItem(value: 'Grade 7', child: Text('Grade 7')),
                  DropdownMenuItem(value: 'Grade 8', child: Text('Grade 8')),
                  DropdownMenuItem(value: 'Grade 9', child: Text('Grade 9')),
                  DropdownMenuItem(value: 'Grade 10', child: Text('Grade 10')),
                  DropdownMenuItem(value: 'Grade 11', child: Text('Grade 11')),
                  DropdownMenuItem(value: 'Grade 12', child: Text('Grade 12')),
                  DropdownMenuItem(value: 'College', child: Text('College')),
                  DropdownMenuItem(value: 'Professional', child: Text('Professional')),
                ],
                onChanged: (value) {
                  setState(() {
                    _readingGradeLevel = value;
                  });
                },
              ),
            if (_processType == 'scheme_of_work' || _processType == 'worksheet')
              DropdownButtonFormField<String>(
                value: _numberOfDays,
                decoration: InputDecoration(
                  labelText: 'Number of Days',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: '1 Day', child: Text('1 Day')),
                  DropdownMenuItem(value: '2 Days', child: Text('2 Days')),
                  DropdownMenuItem(value: '3 Days', child: Text('3 Days')),
                  DropdownMenuItem(value: '5 Days', child: Text('5 Days')),
                  DropdownMenuItem(value: '1 Week', child: Text('1 Week')),
                  DropdownMenuItem(value: '2 Weeks', child: Text('2 Weeks')),
                  DropdownMenuItem(value: '3 Weeks', child: Text('3 Weeks')),
                  DropdownMenuItem(value: '1 Month', child: Text('1 Month')),
                  DropdownMenuItem(value: '2 Months', child: Text('2 Months')),
                  DropdownMenuItem(value: '3 Months', child: Text('3 Months')),
                  DropdownMenuItem(value: '6 Months', child: Text('6 Months')),
                  DropdownMenuItem(value: '1 Year', child: Text('1 Year')),
                ],
                onChanged: (value) {
                  setState(() {
                    _numberOfDays = value;
                  });
                },
              ),
            if (_processType == 'quiz' || _processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case')
              DropdownButtonFormField<String>(
                value: _difficultyLevel,
                decoration: InputDecoration(
                  labelText: 'Difficulty Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                  DropdownMenuItem(value: 'Normal', child: Text('Normal')),
                  DropdownMenuItem(
                      value: 'Intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'Hard', child: Text('Hard')),
                  DropdownMenuItem(
                      value: 'Very Hard', child: Text('Very Hard')),
                ],
                onChanged: (value) {
                  setState(() {
                    _difficultyLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Quiz Time Limit (minutes)',
                          labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        ),
                        onChanged: (value) => setState(() => _quizTimeLimitMinutes = int.tryParse(value)),
                      ),
                    ),
                  ],
                ),
              ),

            SizedBox(height: 16), // Added consistent spacing here
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.memory, color: buttonTextColor),
              label: Text(_isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
				onPressed: _isProcessing ? null : _processInput,
            ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    String titleText = 'Generated Content';
    bool showDownloadButton = true;
    switch (_processType) {
      case 'notes':
        titleText = 'Notes';
        break;
      case 'cheatsheet':
        titleText = 'Cheatsheet';
        break;
      case 'flashcards':
        titleText = 'Flashcards';
        showDownloadButton = false;
        break;
      case 'quiz':
        titleText = 'Quiz';
        showDownloadButton = false;
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        titleText = 'Exam';
        break;
      case 'transcript':
        titleText = 'Transcript';
        showDownloadButton = false;
        break;
      case 'chat':
        titleText = 'Chat';
        showDownloadButton = false;
        break;
      case 'summary':
        titleText = 'Summary';
        break;
      case 'minutes':
        titleText = 'Meeting Minutes';
        break;
      case 'interactive_lesson':
        titleText = 'Lesson';
        showDownloadButton = false;
        break;
      case 'lesson_plan':
        titleText = 'Lesson Plan';
        break;
      case 'worksheet':
        titleText = 'Worksheet';
        break;
      case 'homework_guide':
        titleText = 'Homework Guide';
        break;
      case 'project_ideas':
        titleText = 'Project Ideas';
        break;
      case 'grammar':
        titleText = 'Grammar Check';
        break;
      case 'paper_grader':
        titleText = 'Paper Feedback';
        break;
      case 'case_studies':
        titleText = 'Case Studies';
        break;
      case 'experiment':
        titleText = 'Experiment/Lab';
        break;
      case 'scheme_of_work':
        titleText = 'Scheme of Work';
        break;
      case 'data_analysis':
        titleText = 'Data Analysis';
        break;
    }

if (_processType == 'flashcards') {
    return _buildFlashcardsView(theme, generalTextColor);
  } else if (_processType == 'quiz') {
    return _buildQuizView(theme, generalTextColor);
  } else if (_processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case') {
    return _buildExamView(theme, generalTextColor);
  } else if (_processType == 'chat') {
    return _buildChatView(theme, generalTextColor);
  } else if (_processType == 'interactive_lesson') {
    return _buildInteractiveLessonView(theme, generalTextColor);
  } else if (_processType == 'data_analysis') {
    // Data analysis view removed, use standard content display instead
  }

  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(titleText,
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor)),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Font size controls
                  IconButton(
                    icon: Icon(Icons.text_decrease, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize > 12) _lessonFontSize -= 2;
                      });
                    },
                    tooltip: 'Decrease font size',
                  ),
                  IconButton(
                    icon: Icon(Icons.text_increase, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize < 24) _lessonFontSize += 2;
                      });
                    },
                    tooltip: 'Increase font size',
                  ),
                  if (showDownloadButton)
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _downloadContent(),
                      tooltip: 'Download Content',
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              // Always use white background in light mode for better LaTeX rendering
              color: widget.isDarkMode ? theme.cardColor : Colors.white,
              borderRadius: BorderRadius.circular(8),
              // Add a subtle border in light mode
              border: widget.isDarkMode ? null : Border.all(color: Colors.grey.shade200),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _geminiOutput ?? 'No content generated',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (_isResponseComplete == false)
                      IconButton(
                        icon: Icon(Icons.play_arrow, color: widget.isDarkMode ? Colors.white : Colors.black),
                        tooltip: 'Continue generating',
                        onPressed: () {
                          // Continue generating content
                          _continueGenerating();
                        },
                      )
                    else
                      IconButton(
                        icon: Icon(Icons.check_circle, color: Colors.green),
                        tooltip: 'Response Complete',
                        onPressed: null,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}





  Widget _buildInteractiveLessonView(ThemeData theme, Color generalTextColor) {
    TextStyle defaultStyle = GoogleFonts.notoSans(
      fontSize: _lessonFontSize,
      color: Colors.white,
      height: 1.5,
    );

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Page ${_currentLessonStepIndex + 1}/${_lessonSteps.length}',
                  style: GoogleFonts.notoSans(
                    color: generalTextColor,
                    fontSize: 16,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(
                        _isNarrationMuted ? Icons.volume_off : Icons.volume_up,
                        color: generalTextColor,
                      ),
                      onPressed: () {
                        setState(() {
                          _isNarrationMuted = !_isNarrationMuted;
                          if (_isNarrationMuted) _pauseTts();
                        });
                      },
                      tooltip: 'Toggle narration',
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: 300,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black, // Always pure black for better contrast in lessons
                  borderRadius: BorderRadius.circular(10),
                  // Add a subtle border
                  border: Border.all(color: Colors.grey.shade800, width: 1),
                ),
                padding: const EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _displayText,
                    true, // Always dark mode for interactive lesson
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 80,
              child: _buildLessonControls(theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLessonControls(ThemeData theme) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Previous button
            IconButton(
              icon: Icon(Icons.skip_previous, color: generalTextColor),
              onPressed: _goToPreviousStep,
              tooltip: 'Previous step',
            ),
            // Play/Pause button
            IconButton(
              icon: Icon(
                  _lessonPlaying ? Icons.pause : Icons.play_arrow,
                  color: generalTextColor),
              onPressed: _toggleLessonPlay,
              tooltip: _lessonPlaying ? 'Pause' : 'Play',
            ),
            // Next button
            IconButton(
              icon: Icon(Icons.skip_next, color: generalTextColor),
              onPressed: _goToNextStep,
              tooltip: 'Next step',
            ),
          ],
        ),
      ],
    );
  }

  void _goToPreviousStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex > 0) {
        _currentLessonStepIndex--;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _goToNextStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _stopLessonStepDisplay() {
    _stopTts();
    _stopTextAnimation();
  }

  void _stopTextAnimation() {
    _textAnimationTimer?.cancel();
    _isTextAnimationActive = false;
    _displayText = '';
    _currentCharIndex = 0;
  }

  void _goToNextStepForTts() {
    if (_lessonSteps.isEmpty) return;

    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
        _displayText = '';
        _currentCharIndex = 0;
        _startLessonStepDisplay();
      } else {
        _lessonPlaying = false;
      }
    });
  }

  void _restartLesson() {
    setState(() {
      _currentLessonStepIndex = 0;
      _stopLessonStepDisplay();
      _startLessonStepDisplay();
    });
  }

  void _toggleLessonPlay() {
    setState(() {
      _lessonPlaying = !_lessonPlaying;
      if (_lessonPlaying) {
        _startLessonStepDisplay();
      } else {
        _pauseLessonStepDisplay();
      }
    });
  }

  void _pauseLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _pause();
    setState(() {
      _isTextAnimationActive = false;
    });
  }

  // Data analysis view removed but Excel and CSV support maintained

  void _startLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _currentCharIndex = 0;
    _displayText = '';
    _isTextAnimationActive = true;

    final fullText = _lessonSteps[_currentLessonStepIndex]
        .replaceAll(RegExp(r'\[.*?\]'), '')
        .trim();

    if (_lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);

      // Use _lessonSpeed to control animation speed (faster speed = shorter duration)
      _textAnimationTimer = Timer.periodic(Duration(milliseconds: (30 / _lessonSpeed).round()), (timer) {
        if (_currentCharIndex < fullText.length) {
          setState(() {
            _displayText += fullText[_currentCharIndex];
            _currentCharIndex++;
          });
        } else {
          timer.cancel();
          _isTextAnimationActive = false;

          // Auto-advance to next step after a delay if not the last step
          if (_currentLessonStepIndex < _lessonSteps.length - 1) {
            // Use _lessonSpeed to control auto-advance delay (faster speed = shorter delay)
            Future.delayed(Duration(milliseconds: (3000 / _lessonSpeed).round()), () {
              if (mounted && _lessonPlaying) {
                _goToNextStep();
              }
            });
          }
        }
      });
    }
  }

  void _startTtsForCurrentStep() {
    if (_lessonSteps.isNotEmpty &&
        _currentLessonStepIndex < _lessonSteps.length &&
        _lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);
    }
  }

  void _stopTts() {
    flutterTts.stop();
    setState(() => _lessonPlaying = false);
  }

  void _pauseTts() {
    flutterTts.pause();
    setState(() => _lessonPlaying = false);
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: generalTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool get _hasMP3 => _pickedFiles.any((f) => f.file.name.toLowerCase().endsWith('.mp3'));
}

class FileWithPageRange {
  PlatformFile file;
  int? startPage;
  int? endPage;

  FileWithPageRange({required this.file, this.startPage, this.endPage});
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class ExamQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;

  ExamQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FlipCard(
      front: Card(
        color: isDarkMode ? theme.cardColor : Colors.white, // Use white in light mode for better LaTeX rendering
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Center(
              child: _DashboardPageState.buildLatexContent(
                flashcard.question,
                isDarkMode,
                20.0,
              ),
            ),
          ),
        ),
      ),
      back: Card(
        color: isDarkMode ? theme.colorScheme.surface : Colors.white, // Use white in light mode for better LaTeX rendering
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Center(
              child: _DashboardPageState.buildLatexContent(
                flashcard.answer.replaceAll(RegExp(r'^::\w*\s*|^:\s*'), ''), // Clean answer text and remove leading colon
                isDarkMode,
                16.0,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.white), // Use white in light mode for better LaTeX rendering
          borderRadius: BorderRadius.circular(15),
          // Add a subtle border in light mode for non-user messages
          border: (!isDarkMode && !message.isUser) ? Border.all(color: Colors.grey.shade200) : null,
        ),
        child: _DashboardPageState.buildLatexContent(
          message.text,
          isDarkMode,
          16.0, // Default font size for chat
        ),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final VoidCallback onGenerateNotes;
  final List<QuizQuestion> incorrectQuestions;
  final List<QuizQuestion> questions;
  final Color textColor;
  final Map<int, int?> userAnswers;
  final String? weaknessNotes;

  const ExamResultsDialog({
    Key? key,
    required this.questions,
    required this.textColor,
    required this.incorrectQuestions,
    required this.onGenerateNotes,
    required this.userAnswers,
    this.weaknessNotes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int correctCount = 0;
    for (int i = 0; i < questions.length; i++) {
      if (userAnswers[i] == questions[i].correctAnswerIndex) {
        correctCount++;
      }
    }
    double totalQuestions = questions.length.toDouble();

    return AlertDialog(
      title: Text('Practice Exam Answer Key', style: GoogleFonts.notoSans(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: ${(correctCount / totalQuestions * 100).toStringAsFixed(1)}%',
                style: TextStyle(color: _getScoreColor(correctCount / totalQuestions), fontSize: 24)),
            const SizedBox(height: 20),
            Text('Answer Key:',
                style: GoogleFonts.notoSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...questions.asMap().entries.map((entry) {
              int index = entry.key;
              QuizQuestion question = entry.value;
              String correctAnswer = question.correctAnswerIndex != null && question.correctAnswerIndex! < question.options.length
                  ? question.options[question.correctAnswerIndex!]
                  : 'Unknown';
              String answerLetter = question.correctAnswerIndex != null ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!) : '?';
              bool isCorrect = userAnswers[index] == question.correctAnswerIndex;
              String userAnswerText = userAnswers[index] != null && userAnswers[index]! < question.options.length
                  ? question.options[userAnswers[index]!] : 'Not Answered';
              String userAnswerLetter = userAnswers[index] != null ? String.fromCharCode('A'.codeUnitAt(0) + userAnswers[index]!) : ' ';


              return ListTile(
                title: Text('${index + 1}. ${question.question}',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Correct answer: $answerLetter) $correctAnswer',
                        style: GoogleFonts.notoSans(color: Colors.green)),
                    Text('Your answer: $userAnswerLetter) $userAnswerText',
                        style: GoogleFonts.notoSans(color: isCorrect ? Colors.green : Colors.red)),
                    const Divider(),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        if (weaknessNotes == null)
          TextButton(
            onPressed: onGenerateNotes,
            child: Text('Generate Improvement Notes', style: GoogleFonts.notoSans(color: Colors.blue)),
          )
        else
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () => _downloadWeaknessNotes(context),
                child: Text('Download Notes as PDF', style: GoogleFonts.notoSans(color: Colors.blue)),
              ),
            ],
          ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }

  Future<void> _downloadWeaknessNotes(BuildContext context) async {
    if (weaknessNotes == null) return;

    try {
      // Create PDF document
      final pdf = pw.Document();

      // Add content to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: pdf_package.PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (pw.Context context) {
            return pw.Header(
              level: 0,
              child: pw.Text(
                'Quiz Improvement Notes',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            );
          },
          footer: (pw.Context context) {
            return pw.Footer(
              trailing: pw.Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: const pw.TextStyle(
                  fontSize: 12,
                ),
              ),
            );
          },
          build: (pw.Context context) => [
            pw.Paragraph(
              text: weaknessNotes!,
              style: const pw.TextStyle(
                fontSize: 14,
              ),
            ),
          ],
        ),
      );

      // Get directory for saving file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'Quiz_Improvement_Notes_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = path.join(directory.path, fileName);
      final file = File(filePath);

      // Save PDF to file
      await file.writeAsBytes(await pdf.save());

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Notes saved to: $filePath')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving notes: $e')),
        );
      }
    }
  }
}

Color _getScoreColor(double percentage) {
  if (percentage >= 0.9) return Colors.green;
  if (percentage >= 0.7) return Colors.orange;
  return Colors.red;
}
