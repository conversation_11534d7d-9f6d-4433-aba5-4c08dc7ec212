// admission_links_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class AdmissionLinksPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AdmissionLinksPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AdmissionLinksPageState createState() => _AdmissionLinksPageState();
}

class _AdmissionLinksPageState extends State<AdmissionLinksPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('admission_links_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _links = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadLinksFromCache();
    _loadLinksFromSupabase();
  }

  void _setupRealtime() {
    final linksTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_links';
    _realtimeChannel = Supabase.instance.client
        .channel('admission_links_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: linksTableName,
      callback: (payload) async {
        print("Realtime update received for links: ${payload.eventType}");
        _loadLinksFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadLinksFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_links_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> links = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && links.isNotEmpty) {
          setState(() {
            _links = links;
          });
          print("Loaded ${links.length} links from cache");
        }
      }
    } catch (e) {
      print("Error loading links from cache: $e");
    }
  }

  Future<void> _loadLinksFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final linksTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_links';
      var query = Supabase.instance.client
          .from(linksTableName)
          .select('*')
          .eq('admissionslink', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('fullname.ilike.%$_searchQuery%,description.ilike.%$_searchQuery%');
      }
      
      final response = await query.order('fullname', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _links = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Cache the data
      _cacheLinks(_links);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading links: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading links: $e')),
      );
    }
  }

  Future<void> _cacheLinks(List<Map<String, dynamic>> links) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_links_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(links));
      print("Cached ${links.length} links");
    } catch (e) {
      print("Error caching links: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadLinksFromSupabase();
  }

  Future<void> _openLink(String url) async {
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Link URL is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open link: $url')),
      );
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Admission Links',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search links...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Links list
          Expanded(
            child: VisibilityDetector(
              key: const Key('admission_links_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _links.isEmpty && !_isLoading) {
                  _loadLinksFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadLinksFromSupabase,
                child: _isLoading && _links.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _links.isEmpty
                        ? Center(
                            child: Text(
                              'No links found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _links.length,
                            itemBuilder: (context, index) {
                              return _buildLinkCard(
                                _links[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLinkCard(
    Map<String, dynamic> link,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = link['fullname'] ?? 'Unnamed Link';
    final String description = link['description'] ?? '';
    final String url = link['url'] ?? '';
    final String category = link['category'] ?? '';

    // Determine icon based on URL or category
    IconData linkIcon = Icons.link;
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      linkIcon = Icons.video_library;
    } else if (url.contains('facebook.com')) {
      linkIcon = Icons.facebook;
    } else if (url.contains('twitter.com')) {
      linkIcon = Icons.flutter_dash;
    } else if (url.contains('instagram.com')) {
      linkIcon = Icons.camera_alt;
    } else if (url.contains('linkedin.com')) {
      linkIcon = Icons.work;
    } else if (url.contains('pdf')) {
      linkIcon = Icons.picture_as_pdf;
    } else if (url.contains('doc') || url.contains('docx')) {
      linkIcon = Icons.description;
    } else if (url.contains('xls') || url.contains('xlsx')) {
      linkIcon = Icons.table_chart;
    } else if (url.contains('ppt') || url.contains('pptx')) {
      linkIcon = Icons.slideshow;
    } else if (category.toLowerCase().contains('form')) {
      linkIcon = Icons.assignment;
    } else if (category.toLowerCase().contains('application')) {
      linkIcon = Icons.app_registration;
    } else if (category.toLowerCase().contains('scholarship')) {
      linkIcon = Icons.school;
    } else if (category.toLowerCase().contains('financial')) {
      linkIcon = Icons.attach_money;
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openLink(url),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? theme.colorScheme.primaryContainer
                    : theme.colorScheme.primaryContainer,
                child: Icon(
                  linkIcon,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              // Link details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    if (category.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceVariant,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          category,
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Open icon
              Icon(
                Icons.open_in_new,
                color: theme.colorScheme.primary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
