name: Harmonizr360
description: A new Flutter project with an Instagram-like interface.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  video_player: ^2.8.1 # Or the latest version
  path_provider: ^2.1.1 # Or the latest version
  path: ^1.9.0 # Or the latest version
  shared_preferences: ^2.2.0 # Or the latest version
  math_expressions: ^2.3.0  # You can check pub.dev for the latest version
  image_picker: ^1.1.2  # Use the latest version available on pub.dev
  intl: ^0.17.0 # Or the latest version
  crypto: ^3.0.1
  photo_view: ^0.14.0
  flutter_tts: ^3.8.3 # Or the latest version
  qr_code_scanner: ^1.0.1   # Or the latest version
  fl_chart: ^0.66.0 # Use the latest version
  # google_ml_kit: ^0.16.2 # Add this line for OCR (example)
  pdf: ^3.10.4 # Add this line  
  #image_gallery_saver: ^2.0.0 # Add this line (check for the latest version on pub.dev)  
  file_picker: ^6.1.1
  csv: ^5.0.0
  flutter_pdfview: ^1.3.1
  geolocator: ^9.0.0 # Use the latest version
  #ar_flutter_plugin: ^0.7.0 # Use the latest version
  permission_handler: ^10.1.0 # For camera permissions
  #audioplayers: ^6.1.0 # Use the latest version you need
  cupertino_icons: ^1.0.2
  font_awesome_flutter: ^10.6.0
  flutter_colorpicker: ^1.0.3
  url_launcher: ^6.2.1
  # Firebase
  firebase_core: ^3.8.1
  firebase_core_web: ^2.10.0
  flutter_map: ^7.0.2
  latlong2: ^0.9.0
  flutter_map_cancellable_tile_provider: ^3.0.2
  js: ^0.6.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: "^0.13.1" # Add the latest version from pub.dev
  flutter_native_splash: "^2.3.8"  # Add the latest version from pub.dev

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png" # Replace with your icon path
  # min_sdk_android: 21 # Optional, for adaptive icons

flutter_native_splash:
  color: "#ffffff" # Customize your splash screen background color
  image: assets/splash/splash.png # Replace with your splash image path
  # android_12_background_color: "#ffffff" # Optional for Android 12+
  # android_12_splash_image: assets/splash/splash-android12.png # Optional for Android 12+

flutter:
  uses-material-design: true
  assets:
    - assets/alarm_sound.mp3
    - assets/alarm_sound.ogg # or - assets/alarm_sound.wav
    - assets/ads/ # Add this line to include the entire ads directory
    - assets/library/