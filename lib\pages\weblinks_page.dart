// weblinks_page.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'login_page.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart'; // ✅ Import foundation.dart for kIsWeb

class WeblinksPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const WeblinksPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<WeblinksPage> createState() => _WeblinksPageState();
}

class _WeblinksPageState extends State<WeblinksPage> {
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;
  WebViewController? _ministryOfEducationController;
  final Map<String, WebViewController> _preloadedControllers = {};
  bool _isPreloading = true;

  final String _adUnitId = 'YOUR_REWARDED_AD_UNIT_ID';

  NativeAd? _nativeAd;
  bool _isNativeAdLoaded = false;

  final Map<String, String> linkData = { // Make linkData accessible in _buildGridItem
    'MOEST': 'https://www.education.gov.mw/',
    'MIE': 'https://mie.edu.mw/',
    'MANEB': 'https://www.maneb.edu.mw/',
    'NCST': 'https://www.ncst.mw/',
    'NCHE': 'https://www.nche.ac.mw/',
    'TEVETA': 'https://tevetamw.com/',
    'IMMIGRATION': 'https://www.immigration.gov.mw/',
    'HESLGB': 'https://www.heslgb.mw/',
    'NRB': 'https://www.nrb.gov.mw/',
    'ROAD TRAFFIC': 'https://transport.gov.mw/index.php/departments/road-traffic',
    'MRA': 'https://www.mra.mw/',
    'MALAWI LAW SOCIETY': 'https://malawilawsociety.net/',
    'NURSING COUNCIL': 'http://nmcm.mbuna-inc.com/',
    'PHARMACY BOARD': 'https://www.pmra.mw/',
    'ICAM': 'https://www.icam.mw/',
    'POLICE': 'https://www.police.gov.mw/',
    'MIN. OF INFORMATION': 'https://www.ict.gov.mw/',
    'JUDICIARY': 'https://www.judiciary.mw/',
    //'POST OFFICE': 'https://www.malawipost.com/',
    //'MEDICAL COUNCIL': 'https://medicalcouncilmw.org/',
  };


  @override
  void initState() {
    super.initState();
    _initializePreloading(); // Call a new async function to handle initState
    _loadNativeAd();
    //_loadRewardedAd();
    //_preloadMinistryOfEducationWebsite();
  }

  Future<void> _initializePreloading() async {
    await _preloadWeblinks(); // Await the preloading process
  }

  @override
  void dispose() {
    _nativeAd?.dispose();
    _rewardedAd?.dispose();
    super.dispose();
  }

void _loadNativeAd() {
  if (kIsWeb) return;

  const adUnitId = 'ca-app-pub-3940256099942544/**********'; // Test ID

  _nativeAd = NativeAd(
    adUnitId: adUnitId,
    request: const AdRequest(),
    listener: NativeAdListener(
      onAdLoaded: (Ad ad) {
        print('Native ad loaded');
        setState(() => _isNativeAdLoaded = true);
      },
      onAdFailedToLoad: (Ad ad, LoadAdError error) {
        print('Native ad failed: $error');
        ad.dispose();
      },
    ),
  )..load();
}


  Future<void> _preloadWeblinks() async {
    for (final entry in linkData.entries) {
      final title = entry.key;
      final url = entry.value;
      // No need to await loadRequest here, preloading happens in background.
      WebViewController()
        ..loadRequest(Uri.parse(url)); // Keep preloading in background
    }
    setState(() {
      _isPreloading = false; // Set to false AFTER the loop (but not waiting for individual loadRequests)
    });
  }

  void _preloadMinistryOfEducationWebsite() {
    _ministryOfEducationController = WebViewController()
      ..loadRequest(Uri.parse('https://www.education.gov.mw/'));
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (AdAd) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              _loadRewardedAd();
            },
            onAdFailedToShowFullScreenContent: (AdAd, error) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              print('Failed to show rewarded ad: $error');
              _loadRewardedAd();
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          setState(() {
            _rewardedAd = null;
            _isAdLoaded = false;
          });
        },
      ),
    );
  }

  void _showRewardedAd(BuildContext context, String title) {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        print('User earned reward: ${reward.amount} ${reward.type}');
        _launchLink(linkData[title]!); // Launch link after reward
      });
    } else {
      print('Rewarded ad not ready, navigating directly.');
      _launchLink(linkData[title]!); // Launch link directly if no ad
    }
  }

  Future<void> _launchLink(String link) async {
    if (link.isNotEmpty) {
      final Uri uri = Uri.parse(link);
      if (await canLaunchUrl(uri)) {
        launchUrl(uri);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch link.')),
        );
      }
    }
  }


  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool currentIsDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          _launchLink(linkData[title]!); // Directly launch link on tap
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: currentIsDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

Widget _buildNativeAdCard(BuildContext context, ThemeData theme) {
  if (!_isNativeAdLoaded || _nativeAd == null) return const SizedBox.shrink();

  return Container(
    margin: const EdgeInsets.all(8.0),
    height: 320, // Adjusted height for better visibility
    alignment: Alignment.center,
    child: AdWidget(ad: _nativeAd!),
  );
}


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Weblinks',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isPreloading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Web Link Grid Items
                _buildGridItem(context, 'MOEST', Icons.school, theme),
                _buildGridItem(context, 'MIE', Icons.school, theme),
                _buildGridItem(context, 'MANEB', Icons.school, theme),
                _buildGridItem(context, 'NCST', Icons.science, theme),
                _buildGridItem(context, 'NCHE', Icons.school, theme),
                _buildGridItem(context, 'TEVETA', Icons.school, theme),
                _buildGridItem(context, 'IMMIGRATION', Icons.person, theme),
                _buildGridItem(context, 'HESLGB', Icons.monetization_on, theme),
                // ✅ Insert Native Ad after some grid items (e.g., after 8 items)
                if (_isNativeAdLoaded) _buildNativeAdCard(context, theme),
                _buildGridItem(context, 'NRB', Icons.badge, theme),
                _buildGridItem(context, 'ROAD TRAFFIC', Icons.directions_car, theme),
                // New Links
                _buildGridItem(context, 'MRA', Icons.account_balance, theme),
                _buildGridItem(context, 'POST OFFICE', Icons.local_post_office, theme),
                _buildGridItem(context, 'MALAWI LAW SOCIETY', Icons.gavel, theme),
                _buildGridItem(context, 'MEDICAL COUNCIL', Icons.medical_services, theme),
                _buildGridItem(context, 'NURSING COUNCIL', Icons.healing, theme),
                _buildGridItem(context, 'PHARMACY BOARD', Icons.medication, theme),
                _buildGridItem(context, 'COUNCIL FOR ACCOUNTANTS', Icons.analytics, theme),
                _buildGridItem(context, 'JUDICIARY', Icons.account_balance, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}