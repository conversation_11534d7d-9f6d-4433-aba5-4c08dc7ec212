import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'notices_page.dart';
import 'social_media_feeds_page.dart';

class TertiaryUpdatesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryUpdatesPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryUpdatesPage> createState() => _TertiaryUpdatesPageState();
}

class _TertiaryUpdatesPageState extends State<TertiaryUpdatesPage> {
  List<Map<String, dynamic>>? _cachedNotices;
  List<Map<String, dynamic>>? _cachedSocialMediaFeeds;
  String? _lastCollegeName;
  bool _isLoadingNotices = false;
  bool _isLoadingSocialMediaFeeds = false;
  late RealtimeChannel _noticesRealtimeChannel;
  late RealtimeChannel _socialMediaFeedsRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryUpdatesPage initState called for ${widget.institutionName}");
    _loadCachedNotices();
    _loadCachedSocialMediaFeeds();
    _loadNoticesFromDatabaseAndCache();
    _loadSocialMediaFeedsFromDatabaseAndCache();
    _setupNoticesRealtimeListener();
    _setupSocialMediaFeedsRealtimeListener();
  }

  @override
  void dispose() {
    _noticesRealtimeChannel.unsubscribe();
    _socialMediaFeedsRealtimeChannel.unsubscribe();
    super.dispose();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Noticeboard') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NoticesPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedNotices: _cachedNotices,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          } else if (title == 'Social Media Feeds') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SocialMediaFeedsPage(
                  collegeNameForTable: widget.institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: widget.toggleTheme,
                  preloadedSocialMediaFeeds: _cachedSocialMediaFeeds,
                  isFromDetailPage: widget.isFromDetailPage,
                ),
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Notices methods
  Future<List<Map<String, dynamic>>?> _getCachedNotices(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? noticesJson = prefs.getString(
        'notices_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (noticesJson != null) {
      List<dynamic> decodedList = jsonDecode(noticesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheNotices(String collegeName, List<Map<String, dynamic>> notices) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String noticesJson = jsonEncode(notices);
    await prefs.setString(
        'notices_${collegeName.toLowerCase().replaceAll(' ', '')}', noticesJson);
    print('Notices cached for $collegeName.');
  }

  Future<void> _loadCachedNotices() async {
    final cachedData = await _getCachedNotices(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedNotices = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded notices from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadNoticesFromDatabaseAndCache() async {
    if (_isLoadingNotices) {
      return;
    }

    setState(() {
      _isLoadingNotices = true;
    });

    print("Fetching notices for ${widget.institutionName} from database");
    final noticesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_notices';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(noticesTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false);

      if (mounted) {
        setState(() {
          _cachedNotices = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingNotices = false;
          _cacheNotices(widget.institutionName, response);
          print("Notices fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingNotices = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingNotices = false;
          _cachedNotices = [];
          print("Error fetching notices for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingNotices = false;
      }
    }
  }

  void _setupNoticesRealtimeListener() {
    final noticesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_notices';
    _noticesRealtimeChannel = Supabase.instance.client
        .channel('notices_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: noticesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for notices of ${widget.institutionName}: ${payload.eventType}");
        _loadNoticesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Social Media Feeds methods
  Future<List<Map<String, dynamic>>?> _getCachedSocialMediaFeeds(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? socialMediaFeedsJson = prefs.getString(
        'socialmediafeeds_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (socialMediaFeedsJson != null) {
      List<dynamic> decodedList = jsonDecode(socialMediaFeedsJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheSocialMediaFeeds(String collegeName, List<Map<String, dynamic>> feeds) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String socialMediaFeedsJson = jsonEncode(feeds);
    await prefs.setString(
        'socialmediafeeds_${collegeName.toLowerCase().replaceAll(' ', '')}', socialMediaFeedsJson);
    print('Social media feeds cached for $collegeName.');
  }

  Future<void> _loadCachedSocialMediaFeeds() async {
    final cachedData = await _getCachedSocialMediaFeeds(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedSocialMediaFeeds = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded social media feeds from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadSocialMediaFeedsFromDatabaseAndCache() async {
    if (_isLoadingSocialMediaFeeds) {
      return;
    }

    setState(() {
      _isLoadingSocialMediaFeeds = true;
    });

    print("Fetching social media feeds for ${widget.institutionName} from database");
    final socialMediaFeedsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(socialMediaFeedsTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedSocialMediaFeeds = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingSocialMediaFeeds = false;
          _cacheSocialMediaFeeds(widget.institutionName, response);
          print("Social media feeds fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingSocialMediaFeeds = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingSocialMediaFeeds = false;
          _cachedSocialMediaFeeds = [];
          print("Error fetching social media feeds for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingSocialMediaFeeds = false;
      }
    }
  }

  void _setupSocialMediaFeedsRealtimeListener() {
    final socialMediaFeedsTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_socialmediafeeds';
    _socialMediaFeedsRealtimeChannel = Supabase.instance.client
        .channel('socialmediafeeds_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: socialMediaFeedsTableName,
      callback: (payload) async {
        print(
            "Realtime update received for social media feeds of ${widget.institutionName}: ${payload.eventType}");
        _loadSocialMediaFeedsFromDatabaseAndCache();
      },
    ).subscribe();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Updates',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            // Update crossAxisCount and aspectRatio to match TertiaryStartPage
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Noticeboard', Icons.announcement, theme),
                _buildGridItem(context, 'Social Media Feeds', Icons.dynamic_feed, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}