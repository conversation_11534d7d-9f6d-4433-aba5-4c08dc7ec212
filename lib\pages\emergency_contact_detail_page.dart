import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'tertiary_safety_page.dart';

class EmergencyContactDetailPage extends StatefulWidget {
  final EmergencyContact contact;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EmergencyContactDetailPage({
    Key? key,
    required this.contact,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EmergencyContactDetailPage> createState() => _EmergencyContactDetailPageState();
}

class _EmergencyContactDetailPageState extends State<EmergencyContactDetailPage> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Check if location coordinates are available
    final bool hasLocation = widget.contact.hasLocation();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Emergency Contact',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: Colors.red.withOpacity(0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.red,
                        radius: 24,
                        child: Icon(
                          Icons.contact_emergency,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.contact.fullname,
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            if (widget.contact.title.isNotEmpty)
                              Text(
                                widget.contact.title,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontStyle: FontStyle.italic,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (widget.contact.department.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Row(
                        children: [
                          Icon(
                            Icons.business,
                            size: 16,
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              widget.contact.department,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            
            // Quick contact buttons
            if (widget.contact.phone.isNotEmpty || widget.contact.email.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (widget.contact.phone.isNotEmpty)
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.call),
                          label: const Text('CALL'),
                          onPressed: () => _launchUrl('tel:${widget.contact.phone}'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12.0),
                          ),
                        ),
                      ),
                    if (widget.contact.phone.isNotEmpty && widget.contact.email.isNotEmpty)
                      const SizedBox(width: 16),
                    if (widget.contact.email.isNotEmpty)
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.email),
                          label: const Text('EMAIL'),
                          onPressed: () => _launchUrl('mailto:${widget.contact.email}'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12.0),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            
            // Contact details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Contact Information',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Phone
                          if (widget.contact.phone.isNotEmpty)
                            ListTile(
                              leading: CircleAvatar(
                                backgroundColor: Colors.red.withOpacity(0.2),
                                child: Icon(
                                  Icons.phone,
                                  color: Colors.red,
                                ),
                              ),
                              title: Text('Phone'),
                              subtitle: Text(widget.contact.getFormattedPhone()),
                              trailing: IconButton(
                                icon: Icon(Icons.call, color: Colors.red),
                                onPressed: () => _launchUrl('tel:${widget.contact.phone}'),
                              ),
                            ),
                          
                          // Email
                          if (widget.contact.email.isNotEmpty)
                            ListTile(
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                child: Icon(
                                  Icons.email,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              title: Text('Email'),
                              subtitle: Text(widget.contact.email),
                              trailing: IconButton(
                                icon: Icon(Icons.email, color: theme.colorScheme.primary),
                                onPressed: () => _launchUrl('mailto:${widget.contact.email}'),
                              ),
                            ),
                          
                          // Fax
                          if (widget.contact.fax.isNotEmpty)
                            ListTile(
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                child: Icon(
                                  Icons.print,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              title: Text('Fax'),
                              subtitle: Text(widget.contact.fax),
                            ),
                          
                          // Office Hours
                          if (widget.contact.officehours.isNotEmpty)
                            ListTile(
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                child: Icon(
                                  Icons.access_time,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              title: Text('Office Hours'),
                              subtitle: Text(widget.contact.officehours),
                            ),
                          
                          // Hours
                          if (widget.contact.hours.isNotEmpty && widget.contact.hours != widget.contact.officehours)
                            ListTile(
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                child: Icon(
                                  Icons.schedule,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              title: Text('Hours'),
                              subtitle: Text(widget.contact.hours),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Location
            if (widget.contact.building.isNotEmpty || widget.contact.room.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Building
                            if (widget.contact.building.isNotEmpty)
                              ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                  child: Icon(
                                    Icons.business,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                title: Text('Building'),
                                subtitle: Text(widget.contact.building),
                              ),
                            
                            // Room
                            if (widget.contact.room.isNotEmpty)
                              ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                  child: Icon(
                                    Icons.meeting_room,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                title: Text('Room'),
                                subtitle: Text(widget.contact.room),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Map (if location is available)
            if (hasLocation)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Map',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: FlutterMap(
                          options: MapOptions(
                            initialCenter: LatLng(widget.contact.latitude!, widget.contact.longitude!),
                            initialZoom: 16.0,
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.harmonizr.app',
                            ),
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: LatLng(widget.contact.latitude!, widget.contact.longitude!),
                                  width: 40,
                                  height: 40,
                                  child: Icon(
                                    Icons.location_on,
                                    color: Colors.red,
                                    size: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // About section
            if (widget.contact.about.isNotEmpty && widget.contact.about != 'No description available')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          widget.contact.about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Services section
            if (widget.contact.services.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Services',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          widget.contact.services,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: widget.contact.phone.isNotEmpty
          ? FloatingActionButton(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              onPressed: () => _launchUrl('tel:${widget.contact.phone}'),
              child: const Icon(Icons.call),
              tooltip: 'Call ${widget.contact.fullname}',
            )
          : null,
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
