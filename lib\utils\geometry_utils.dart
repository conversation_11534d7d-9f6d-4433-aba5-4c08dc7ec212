import 'package:flutter/material.dart';
import 'package:superdeclarative_geometry/superdeclarative_geometry.dart';

class GeometryUtils {
  // Draw a circle
  static Widget drawCircle({
    required double radius,
    Color color = Colors.blue,
    Color borderColor = Colors.black,
    double borderWidth = 1.0,
    bool filled = true,
  }) {
    return CustomPaint(
      size: Size(radius * 2, radius * 2),
      painter: _CirclePainter(
        radius: radius,
        color: color,
        borderColor: borderColor,
        borderWidth: borderWidth,
        filled: filled,
      ),
    );
  }

  // Draw a rectangle
  static Widget drawRectangle({
    required double width,
    required double height,
    Color color = Colors.blue,
    Color borderColor = Colors.black,
    double borderWidth = 1.0,
    bool filled = true,
    double cornerRadius = 0.0,
  }) {
    return CustomPaint(
      size: Size(width, height),
      painter: _RectanglePainter(
        width: width,
        height: height,
        color: color,
        borderColor: borderColor,
        borderWidth: borderWidth,
        filled: filled,
        cornerRadius: cornerRadius,
      ),
    );
  }

  // Draw a triangle
  static Widget drawTriangle({
    required double width,
    required double height,
    Color color = Colors.blue,
    Color borderColor = Colors.black,
    double borderWidth = 1.0,
    bool filled = true,
  }) {
    return CustomPaint(
      size: Size(width, height),
      painter: _TrianglePainter(
        width: width,
        height: height,
        color: color,
        borderColor: borderColor,
        borderWidth: borderWidth,
        filled: filled,
      ),
    );
  }

  // Draw a line
  static Widget drawLine({
    required Offset start,
    required Offset end,
    Color color = Colors.black,
    double strokeWidth = 2.0,
    StrokeCap strokeCap = StrokeCap.round,
  }) {
    final width = (end.dx - start.dx).abs() + strokeWidth * 2;
    final height = (end.dy - start.dy).abs() + strokeWidth * 2;
    
    return CustomPaint(
      size: Size(width, height),
      painter: _LinePainter(
        start: Offset(
          start.dx < end.dx ? strokeWidth : width - strokeWidth,
          start.dy < end.dy ? strokeWidth : height - strokeWidth,
        ),
        end: Offset(
          end.dx > start.dx ? width - strokeWidth : strokeWidth,
          end.dy > start.dy ? height - strokeWidth : strokeWidth,
        ),
        color: color,
        strokeWidth: strokeWidth,
        strokeCap: strokeCap,
      ),
    );
  }

  // Draw an arc
  static Widget drawArc({
    required double radius,
    required double startAngle,
    required double sweepAngle,
    Color color = Colors.blue,
    Color borderColor = Colors.black,
    double borderWidth = 1.0,
    bool filled = true,
  }) {
    return CustomPaint(
      size: Size(radius * 2, radius * 2),
      painter: _ArcPainter(
        radius: radius,
        startAngle: startAngle,
        sweepAngle: sweepAngle,
        color: color,
        borderColor: borderColor,
        borderWidth: borderWidth,
        filled: filled,
      ),
    );
  }

  // Draw a polygon
  static Widget drawPolygon({
    required List<Offset> points,
    Color color = Colors.blue,
    Color borderColor = Colors.black,
    double borderWidth = 1.0,
    bool filled = true,
  }) {
    // Calculate the bounding box
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    for (final point in points) {
      minX = point.dx < minX ? point.dx : minX;
      minY = point.dy < minY ? point.dy : minY;
      maxX = point.dx > maxX ? point.dx : maxX;
      maxY = point.dy > maxY ? point.dy : maxY;
    }
    
    final width = maxX - minX + borderWidth * 2;
    final height = maxY - minY + borderWidth * 2;
    
    // Adjust points relative to the bounding box
    final adjustedPoints = points.map((point) => 
      Offset(point.dx - minX + borderWidth, point.dy - minY + borderWidth)
    ).toList();
    
    return CustomPaint(
      size: Size(width, height),
      painter: _PolygonPainter(
        points: adjustedPoints,
        color: color,
        borderColor: borderColor,
        borderWidth: borderWidth,
        filled: filled,
      ),
    );
  }
}

// Painters

class _CirclePainter extends CustomPainter {
  final double radius;
  final Color color;
  final Color borderColor;
  final double borderWidth;
  final bool filled;

  _CirclePainter({
    required this.radius,
    required this.color,
    required this.borderColor,
    required this.borderWidth,
    required this.filled,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    if (filled) {
      final fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, radius, fillPaint);
    }
    
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawCircle(center, radius, borderPaint);
  }

  @override
  bool shouldRepaint(_CirclePainter oldDelegate) =>
      oldDelegate.radius != radius ||
      oldDelegate.color != color ||
      oldDelegate.borderColor != borderColor ||
      oldDelegate.borderWidth != borderWidth ||
      oldDelegate.filled != filled;
}

class _RectanglePainter extends CustomPainter {
  final double width;
  final double height;
  final Color color;
  final Color borderColor;
  final double borderWidth;
  final bool filled;
  final double cornerRadius;

  _RectanglePainter({
    required this.width,
    required this.height,
    required this.color,
    required this.borderColor,
    required this.borderWidth,
    required this.filled,
    required this.cornerRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(
      borderWidth / 2,
      borderWidth / 2,
      width - borderWidth,
      height - borderWidth,
    );
    
    final rrect = RRect.fromRectAndRadius(
      rect,
      Radius.circular(cornerRadius),
    );
    
    if (filled) {
      final fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawRRect(rrect, fillPaint);
    }
    
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawRRect(rrect, borderPaint);
  }

  @override
  bool shouldRepaint(_RectanglePainter oldDelegate) =>
      oldDelegate.width != width ||
      oldDelegate.height != height ||
      oldDelegate.color != color ||
      oldDelegate.borderColor != borderColor ||
      oldDelegate.borderWidth != borderWidth ||
      oldDelegate.filled != filled ||
      oldDelegate.cornerRadius != cornerRadius;
}

class _TrianglePainter extends CustomPainter {
  final double width;
  final double height;
  final Color color;
  final Color borderColor;
  final double borderWidth;
  final bool filled;

  _TrianglePainter({
    required this.width,
    required this.height,
    required this.color,
    required this.borderColor,
    required this.borderWidth,
    required this.filled,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final path = Path()
      ..moveTo(width / 2, borderWidth)
      ..lineTo(width - borderWidth, height - borderWidth)
      ..lineTo(borderWidth, height - borderWidth)
      ..close();
    
    if (filled) {
      final fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawPath(path, fillPaint);
    }
    
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(_TrianglePainter oldDelegate) =>
      oldDelegate.width != width ||
      oldDelegate.height != height ||
      oldDelegate.color != color ||
      oldDelegate.borderColor != borderColor ||
      oldDelegate.borderWidth != borderWidth ||
      oldDelegate.filled != filled;
}

class _LinePainter extends CustomPainter {
  final Offset start;
  final Offset end;
  final Color color;
  final double strokeWidth;
  final StrokeCap strokeCap;

  _LinePainter({
    required this.start,
    required this.end,
    required this.color,
    required this.strokeWidth,
    required this.strokeCap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = strokeCap
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(start, end, paint);
  }

  @override
  bool shouldRepaint(_LinePainter oldDelegate) =>
      oldDelegate.start != start ||
      oldDelegate.end != end ||
      oldDelegate.color != color ||
      oldDelegate.strokeWidth != strokeWidth ||
      oldDelegate.strokeCap != strokeCap;
}

class _ArcPainter extends CustomPainter {
  final double radius;
  final double startAngle;
  final double sweepAngle;
  final Color color;
  final Color borderColor;
  final double borderWidth;
  final bool filled;

  _ArcPainter({
    required this.radius,
    required this.startAngle,
    required this.sweepAngle,
    required this.color,
    required this.borderColor,
    required this.borderWidth,
    required this.filled,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final rect = Rect.fromCircle(center: center, radius: radius);
    
    if (filled) {
      final fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawArc(rect, startAngle, sweepAngle, true, fillPaint);
    }
    
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawArc(rect, startAngle, sweepAngle, false, borderPaint);
  }

  @override
  bool shouldRepaint(_ArcPainter oldDelegate) =>
      oldDelegate.radius != radius ||
      oldDelegate.startAngle != startAngle ||
      oldDelegate.sweepAngle != sweepAngle ||
      oldDelegate.color != color ||
      oldDelegate.borderColor != borderColor ||
      oldDelegate.borderWidth != borderWidth ||
      oldDelegate.filled != filled;
}

class _PolygonPainter extends CustomPainter {
  final List<Offset> points;
  final Color color;
  final Color borderColor;
  final double borderWidth;
  final bool filled;

  _PolygonPainter({
    required this.points,
    required this.color,
    required this.borderColor,
    required this.borderWidth,
    required this.filled,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (points.length < 3) return; // Need at least 3 points for a polygon
    
    final path = Path()
      ..moveTo(points.first.dx, points.first.dy);
    
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    
    path.close();
    
    if (filled) {
      final fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawPath(path, fillPaint);
    }
    
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(_PolygonPainter oldDelegate) =>
      oldDelegate.points != points ||
      oldDelegate.color != color ||
      oldDelegate.borderColor != borderColor ||
      oldDelegate.borderWidth != borderWidth ||
      oldDelegate.filled != filled;
}
