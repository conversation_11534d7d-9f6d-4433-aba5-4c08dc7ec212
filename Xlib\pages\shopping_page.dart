import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/services.dart';

class ShoppingItem {
  String name;
  double price;
  int quantity;
  String? imagePath;

  ShoppingItem({required this.name, required this.price, required this.quantity, this.imagePath});

  Map<String, dynamic> toJson() => {
        'name': name,
        'price': price,
        'quantity': quantity,
        'imagePath': imagePath,
      };

  factory ShoppingItem.fromJson(Map<String, dynamic> json) => ShoppingItem(
        name: json['name'],
        price: json['price'],
        quantity: json['quantity'] ?? 1, // Default to 1 if not present
        imagePath: json['imagePath'],
      );

  @override
  String toString() {
    return '$name - Quantity: $quantity - ${(price * quantity).toStringAsFixed(2)}';
  }
}

class ShoppingApp extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const ShoppingApp({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<ShoppingApp> createState() => _ShoppingAppState();
}

class _ShoppingAppState extends State<ShoppingApp> {
  List<ShoppingItem> _shoppingItems = [];
  final TextEditingController _itemController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController(text: '1');
  double _budget = 0.0; // You can implement budget functionality if needed
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadShoppingItems();
  }

  Future<void> _loadShoppingItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedItems = prefs.getStringList('shopping_items');
    if (encodedItems != null) {
      setState(() {
        _shoppingItems = encodedItems.map((item) => ShoppingItem.fromJson(jsonDecode(item))).toList();
      });
    }
  }

  Future<void> _saveShoppingItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedItems = _shoppingItems.map((item) => jsonEncode(item.toJson())).toList();
    await prefs.setStringList('shopping_items', encodedItems);
  }

  Future<void> _pickImage(ShoppingItem item) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      final index = _shoppingItems.indexOf(item);
      if (index != -1) {
        setState(() {
          _shoppingItems[index] = ShoppingItem(
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            imagePath: image.path,
          );
        });
        _saveShoppingItems();
      }
    }
  }

  void _addShoppingItem(String name, double price, int quantity, String? imagePath) {
    setState(() {
      _shoppingItems.add(ShoppingItem(name: name, price: price, quantity: quantity, imagePath: imagePath));
      _itemController.clear();
      _priceController.clear();
      _quantityController.text = '1';
      _saveShoppingItems();
    });
  }

  void _deleteShoppingItem(int index) {
    setState(() {
      _shoppingItems.removeAt(index);
      _saveShoppingItems();
    });
  }

  void _updateShoppingItem(int index, String newName, double newPrice, int newQuantity, String? newImagePath) {
    setState(() {
      _shoppingItems[index] = ShoppingItem(name: newName, price: newPrice, quantity: newQuantity, imagePath: newImagePath);
      _saveShoppingItems();
    });
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final ShoppingItem item = _shoppingItems.removeAt(oldIndex);
      _shoppingItems.insert(newIndex, item);
      _saveShoppingItems();
    });
  }

  double _calculateTotal() {
    return _shoppingItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  }

  void _showImageDialog(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              if (imagePath.isNotEmpty)
                InteractiveViewer( // Allows zoom and pan
                  child: Image.file(
                    File(imagePath),
                    fit: BoxFit.contain,
                  ),
                )
              else
                const Icon(Icons.image, size: 100, color: Colors.grey),
              Positioned(
                top: 16,
                right: 16,
                child: IconButton(
                  icon: const Icon(Icons.close, size: 30, color: Colors.white),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _copyTotalToClipboard() async {
    final total = _calculateTotal();
    await Clipboard.setData(ClipboardData(text: total.toStringAsFixed(2)));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Total copied to clipboard')),
    );
  }

  Future<void> _clearShoppingList() async {
    bool? confirmClear = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: widget.isDarkMode ? const Color(0xFF202020) : Theme.of(context).colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Clear Shopping List', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          content: const Text('Are you sure you want to clear the entire shopping list?', style: TextStyle(color: Colors.grey)),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: Text('Clear', style: TextStyle(color: Colors.red.shade400)),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirmClear == true) {
      setState(() {
        _shoppingItems.clear();
        _saveShoppingItems();
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Shopping list cleared')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Shopping List',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(isDarkMode ? 0.1 : 0.2),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _itemController,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Enter item',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 80,
                      child: TextField(
                        controller: _quantityController,
                        keyboardType: TextInputType.number,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Qty',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 100,
                      child: TextField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Price',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                        onSubmitted: (_) => _addItemAction(context),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.add_shopping_cart, color: theme.colorScheme.onSurface),
                      onPressed: () => _addItemAction(context),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ReorderableListView(
              onReorder: _onReorder,
              children: <Widget>[
                for (int index = 0; index < _shoppingItems.length; index++)
                  Dismissible(
                    key: Key(_shoppingItems[index].name),
                    background: Container(color: Colors.red),
                    onDismissed: (direction) {
                      _deleteShoppingItem(index);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${_shoppingItems[index].name} deleted')),
                      );
                    },
                    child: Container(
                      key: ValueKey(_shoppingItems[index].name),
                      color: isDarkMode ? const Color(0xFF202020) : Colors.white,
                      child: Column(
                        children: [
                          ListTile(
                            onTap: _shoppingItems[index].imagePath != null
                                ? () => _showImageDialog(context, _shoppingItems[index].imagePath!)
                                : null,
                            leading: GestureDetector(
                              onTap: () => _pickImage(_shoppingItems[index]),
                              child: _shoppingItems[index].imagePath != null
                                  ? SizedBox(
                                      width: 40,
                                      height: 40,
                                      child: Image.file(
                                        File(_shoppingItems[index].imagePath!),
                                        fit: BoxFit.cover,
                                      ),
                                    )
                                  : Icon(Icons.camera_alt, color: theme.colorScheme.onSurface),
                            ),
                            title: Text(
                              _shoppingItems[index].name,
                              style: TextStyle(color: theme.colorScheme.onSurface, fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Price: ${_shoppingItems[index].price.toStringAsFixed(2)}',
                                  style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                                ),
                                Text(
                                  'Quantity: ${_shoppingItems[index].quantity}',
                                  style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                                ),
                                Text(
                                  'Total: ${(_shoppingItems[index].price * _shoppingItems[index].quantity).toStringAsFixed(2)}',
                                  style: TextStyle(color: theme.hintColor, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: Icon(Icons.edit, color: theme.colorScheme.onSurface),
                                  onPressed: () => _showEditShoppingItemDialog(context, index, _shoppingItems[index]),
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete, color: theme.colorScheme.onSurface),
                                  onPressed: () => _deleteShoppingItem(index),
                                ),
                              ],
                            ),
                          ),
                          if (index < _shoppingItems.length - 1)
                            Divider(
                              height: 0.5,
                              thickness: 0.5,
                              color: isDarkMode ? Colors.grey.shade600 : const Color(0xFFC0C0C0),
                            ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Total:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
                  Text(
                    _calculateTotal().toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.copy, color: theme.colorScheme.onSurface),
                        onPressed: _copyTotalToClipboard,
                      ),
                      IconButton(
                        icon: Icon(Icons.cleaning_services, color: theme.colorScheme.onSurface),
                        onPressed: _clearShoppingList,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addItemAction(BuildContext context) {
    final itemName = _itemController.text.trim();
    final itemPrice = double.tryParse(_priceController.text.trim()) ?? 0.0;
    final itemQuantity = int.tryParse(_quantityController.text.trim()) ?? 0;
    if (itemName.isNotEmpty && itemPrice > 0 && itemQuantity > 0) {
      _addShoppingItem(itemName, itemPrice, itemQuantity, null);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter item, valid price, and quantity.')),
      );
    }
  }

  void _showEditShoppingItemDialog(BuildContext context, int index, ShoppingItem oldItem) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final nameController = TextEditingController(text: oldItem.name);
    final priceController = TextEditingController(text: oldItem.price.toString());
    final quantityController = TextEditingController(text: oldItem.quantity.toString());

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            String? _imagePath = oldItem.imagePath;
            return AlertDialog(
              backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              title: Text('Edit Shopping Item', style: TextStyle(color: theme.colorScheme.onSurface)),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        final XFile? image = await _picker.pickImage(source: ImageSource.camera);
                        if (image != null) {
                          setState(() {
                            _imagePath = image.path;
                          });
                        }
                      },
                      child: _imagePath != null
                          ? Image.file(File(_imagePath!), width: 80, height: 80)
                          : Icon(Icons.camera_alt, size: 60, color: theme.colorScheme.onSurface),
                    ),
                    TextField(
                      controller: nameController,
                      autofocus: true,
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Item Name',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.grey.shade600 : Colors.grey)),
                          focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.blueAccent : Colors.blue))),
                    ),
                    TextField(
                      controller: quantityController,
                      keyboardType: TextInputType.number,
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Quantity',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.grey.shade600 : Colors.grey)),
                          focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.blueAccent : Colors.blue))),
                      onChanged: (value) {
                        setState(() {}); // Trigger rebuild for instant update
                      },
                    ),
                    TextField(
                      controller: priceController,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      decoration: InputDecoration(
                          labelText: 'Price',
                          labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                          enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.grey.shade600 : Colors.grey)),
                          focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.blueAccent : Colors.blue))),
                      onChanged: (value) {
                        setState(() {}); // Trigger rebuild for instant update
                      },
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text('Save', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    final newName = nameController.text.trim();
                    final newPrice = double.tryParse(priceController.text.trim()) ?? 0.0;
                    final newQuantity = int.tryParse(quantityController.text.trim()) ?? 0;
                    if (newName.isNotEmpty && newPrice > 0 && newQuantity > 0) {
                      _updateShoppingItem(index, newName, newPrice, newQuantity, _imagePath);
                      Navigator.of(context).pop();
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Please enter item, valid price, and quantity.')),
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}