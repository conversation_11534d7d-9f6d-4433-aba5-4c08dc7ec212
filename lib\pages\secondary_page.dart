import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart'; // You might need to create a SecondaryDetailPage if needed, or reuse TertiaryDetailPage if structure is similar
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:async';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/cache_manager.dart';

class SecondaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SecondaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SecondaryPage> createState() => _SecondaryPageState();
}

class _SecondaryPageState extends State<SecondaryPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('secondary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _schools = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredSchools = [];
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _loadPreloadedSchools();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchQuery = _searchController.text;
        _filterSchools();
      });
    });
  }

  void _filterSchools() {
    if (_searchQuery.isEmpty) {
      _filteredSchools = List<Map<String, dynamic>>.from(_schools);
    } else {
      _filteredSchools = _schools.where((school) {
        final fullName = (school['fullname'] as String? ?? '').toLowerCase();
        final city = (school['city'] as String? ?? '').toLowerCase();
        final state = (school['state'] as String? ?? '').toLowerCase();
        final searchQueryLower = _searchQuery.toLowerCase();
        return fullName.contains(searchQueryLower) ||
            city.contains(searchQueryLower) ||
            state.contains(searchQueryLower);
      }).toList();
    }
  }


  void _loadPreloadedSchools() {
    if (MyApp.preloadedSecondarySchools != null) {
      setState(() {
        _schools = List<Map<String, dynamic>>.from(MyApp.preloadedSecondarySchools!);
        _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = MyApp.preloadedSecondarySchools!.length == _pageSize;
        _filterSchools();
      });
      for (var school in _schools) {
        if (school['image_url'] == null || school['image_url'] == 'assets/placeholder_image.png') {
          _fetchImageUrl(school);
        }
      }
    } else {
      _loadSchoolsFromSupabase();
    }
  }

  Future<void> _loadSchoolsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    setState(() {
      _isLoading = true;
    });

    // Cache key for secondary schools
    final cacheKey = 'secondary_schools_page_${_page}';

    try {
      List<Map<String, dynamic>> response;
      bool isFromCache = false;

      // Try to get data from cache first
      if (initialLoad) {
        final cachedData = await CacheManager.getCachedData(cacheKey);
        if (cachedData != null) {
          response = cachedData;
          isFromCache = true;
          print('Using cached secondary schools data for page $_page');
        } else {
          // Fetch from Supabase if no cache
          final startIndex = _page * _pageSize;
          final endIndex = startIndex + _pageSize - 1;

          response = await Supabase.instance.client
              .from('secondaryschools')
              .select('*')
              .order('fullname', ascending: true)
              .range(startIndex, endIndex);

          // Cache the response
          await CacheManager.cacheData(cacheKey, response);
        }
      } else {
        // For pagination, always try network first, fallback to cache
        try {
          final startIndex = _page * _pageSize;
          final endIndex = startIndex + _pageSize - 1;

          response = await Supabase.instance.client
              .from('secondaryschools')
              .select('*')
              .order('fullname', ascending: true)
              .range(startIndex, endIndex);

          // Cache the response
          await CacheManager.cacheData(cacheKey, response);
        } catch (e) {
          // If network fails, try cache
          final cachedData = await CacheManager.getCachedData(cacheKey);
          if (cachedData != null) {
            response = cachedData;
            isFromCache = true;
            print('Using cached secondary schools data for page $_page (network failed)');
          } else {
            throw e; // Re-throw if no cache available
          }
        }
      }

      // Update image URLs if not from cache (images should be cached already)
      final updatedSchools = isFromCache ? response : await _updateSchoolImageUrls(response);

      setState(() {
        if (initialLoad) {
          _schools = updatedSchools;
        } else {
          _schools.addAll(updatedSchools);
        }
        _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
        _filterSchools();
      });

      // Update preloaded schools in MyApp for global access
      if (initialLoad || MyApp.preloadedSecondarySchools == null) {
        MyApp.preloadedSecondarySchools = List<Map<String, dynamic>>.from(_schools);
      } else if (!initialLoad && MyApp.preloadedSecondarySchools != null) {
        MyApp.preloadedSecondarySchools!.addAll(updatedSchools);
        MyApp.preloadedSecondarySchools!.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      }

      // If we're online but used cached data, refresh in background
      if (isFromCache && initialLoad) {
        _refreshDataInBackground();
      }
    } catch (error) {
      if (!_isDisposed) {
        // Check error message to see if it's a connectivity issue or table missing
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          errorMsg = "Offline. Using cached data if available.";

          // Try to load from cache as fallback
          final cachedData = await CacheManager.getCachedData(cacheKey, maxAgeMinutes: 43200); // Allow 30-day old cache when offline
          if (cachedData != null) {
            setState(() {
              if (initialLoad) {
                _schools = cachedData;
              } else {
                _schools.addAll(cachedData);
              }
              _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
              _isLoading = false;
              _hasMore = cachedData.length == _pageSize;
              _filterSchools();
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Offline. Using cached data."), backgroundColor: Colors.orange),
            );
            return;
          }
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
        } else {
          errorMsg = "Error fetching secondary schools: $error";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  // Refresh data in background without blocking UI
  Future<void> _refreshDataInBackground() async {
    try {
      final startIndex = _page * _pageSize;
      final endIndex = startIndex + _pageSize - 1;

      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('secondaryschools')
          .select('*')
          .order('fullname', ascending: true)
          .range(startIndex, endIndex);

      // Cache the fresh response
      await CacheManager.cacheData('secondary_schools_page_${_page}', response);

      // Only update UI if component is still mounted
      if (!_isDisposed) {
        final updatedSchools = await _updateSchoolImageUrls(response);

        setState(() {
          _schools = updatedSchools;
          _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          _filterSchools();
        });

        // Update preloaded schools
        MyApp.preloadedSecondarySchools = List<Map<String, dynamic>>.from(_schools);
      }
    } catch (e) {
      print('Background refresh failed: $e');
    }
  }


  Future<List<Map<String, dynamic>>> _updateSchoolImageUrls(List<Map<String, dynamic>> schools) async {
    List<Future<void>> futures = [];
    for (final school in schools) {
      if (school['image_url'] == null || school['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(school));
      }
    }
    await Future.wait(futures);
    return schools;
  }


  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('secondaryschools')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'secondaryschools',
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newSchoolId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newSchoolResponse = await Supabase.instance.client
              .from('secondaryschools')
              .select('*')
              .eq('id', newSchoolId);
          if (newSchoolResponse.isNotEmpty) {
            final newSchool = newSchoolResponse.first;
            final updatedSchool = await _updateSchoolImageUrls([newSchool]);
            setState(() {
              _schools = [..._schools, updatedSchool.first];
              _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
              _filterSchools();
            });
            MyApp.preloadedSecondarySchools = [...MyApp.preloadedSecondarySchools ?? [], updatedSchool.first];
            MyApp.preloadedSecondarySchools?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedSchoolId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedSchoolResponse = await Supabase.instance.client
              .from('secondaryschools')
              .select('*')
              .eq('id', updatedSchoolId);
          if (updatedSchoolResponse.isNotEmpty) {
            final updatedSchool = updatedSchoolResponse.first;
            setState(() {
              _schools = _schools.map((school) {
                return school['id'] == updatedSchool['id'] ? updatedSchool : school;
              }).toList();
              _schools.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
              _filterSchools();
            });
            MyApp.preloadedSecondarySchools = MyApp.preloadedSecondarySchools?.map((school) {
              return school['id'] == updatedSchool['id'] ? updatedSchool : school;
            }).toList();
            MyApp.preloadedSecondarySchools?.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedSchoolId = payload.oldRecord['id'];
          setState(() {
            _schools.removeWhere((school) => school['id'] == deletedSchoolId);
            _filterSchools();
          });
          MyApp.preloadedSecondarySchools?.removeWhere((school) => school['id'] == deletedSchoolId);
        }
      },
    )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed && _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreSchools();
    }
  }

  Future<void> _loadMoreSchools() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadSchoolsFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> school) {
    if (_isDisposed) return;

    // Determine the current theme mode dynamically.
    final currentIsDarkMode = Theme.of(context).brightness == Brightness.dark;

    _checkTodayEventsAvailability(school).then((hasTodayEvents) {
      if (!_isDisposed) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TertiaryDetailPage( // You can create SecondaryDetailPage if needed, or reuse TertiaryDetailPage if it fits
              college: school, // Renamed to college for reusability, but it's a school object
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              hasTodayEventsPreloaded: hasTodayEvents,
            ),
          ),
        );
      }
    });
  }

  Future<bool> _checkTodayEventsAvailability(Map<String, dynamic> school) async {
    final now = DateTime.now();
    final todayDay = DateFormat('dd').format(now);
    final todayMonth = DateFormat('MM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${school['fullname']
        .toString()
        .toLowerCase()
        .replaceAll(' ', '')}_events'; // Assuming same event table naming convention

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('id')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .limit(1);

      if (response is List) {
        return response.isNotEmpty;
      } else {
        return false;
      }

    } catch (error) {
      // Check if error is due to offline connectivity or missing table.
      String errorMsg;
      final errorStr = error.toString().toLowerCase();
      if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
        errorMsg = "Offline. Please check your internet connection.";
      } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
        errorMsg = "Almost all data for this institution hasn't been added yet.";
      } else {
        errorMsg = "Error checking events: $error";
      }
      // Optionally show a snackbar (only once) if not disposed.
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
      }
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    // Dynamically determine the current theme mode.
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: _isSearching
            ? IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                  });
                },
              )
            : IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () => Navigator.pop(context),
              ),
        title: _isSearching
            ? Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.background,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: TextField(
				  controller: _searchController,
				  autofocus: true,
				  style: TextStyle(color: theme.colorScheme.onBackground),
				  decoration: InputDecoration(
					hintText: 'Search secondary schools...',
					hintStyle: TextStyle(
					  color: theme.colorScheme.onBackground.withOpacity(0.6),
					),
					border: InputBorder.none,
					enabledBorder: InputBorder.none, // Add this line
					focusedBorder: InputBorder.none, // Add this line
				  ),
				  cursorColor: theme.colorScheme.onBackground,
				),
              )
            : Text(
                'Secondary',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => _loadSchoolsFromSupabase(initialLoad: true),
        child: (_searchQuery.isNotEmpty ? _filteredSchools : _schools)
                    .isEmpty &&
                _isLoading
            ? const Center(child: CircularProgressIndicator())
            : (_searchQuery.isNotEmpty ? _filteredSchools : _schools)
                        .isEmpty &&
                    !_isLoading
                ? LayoutBuilder(
                    builder: (BuildContext context, BoxConstraints constraints) {
                      return SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: constraints.maxHeight,
                          child: const Center(
                            child: Text('No secondary schools available.'),
                          ),
                        ),
                      );
                    },
                  )
                : ListView.builder(
                    key: _listKey,
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    itemCount: (_searchQuery.isNotEmpty ? _filteredSchools : _schools)
                            .length +
                        (_hasMore && _searchQuery.isEmpty ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index < (_searchQuery.isNotEmpty ? _filteredSchools : _schools).length) {
                        final school = (_searchQuery.isNotEmpty ? _filteredSchools : _schools)[index];
                        return VisibilityDetector(
                          key: Key('school_${school['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 &&
                                (school['image_url'] == null ||
                                    school['image_url'] ==
                                        'assets/placeholder_image.png')) {
                              _fetchImageUrl(school);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: ClipOval(
                                child: SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: CachedNetworkImage(
                                    imageUrl: school['image_url'] ??
                                        'assets/placeholder_image.png',
                                    errorWidget: (context, url, error) =>
                                        Image.asset('assets/placeholder_image.png'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              title: Text(
                                school['fullname'] ?? 'Unnamed School',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '${school['city'] ?? ''}${school['state'] != null && school['city'] != null ? ', ' : ''}${school['state'] ?? ''}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, school),
                            ),
                          ),
                        );
                      } else if (_hasMore && _searchQuery.isEmpty) {
                        return const Center(
                            child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator()));
                      } else {
                        return Container();
                      }
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }


  Future<void> _fetchImageUrl(Map<String, dynamic> school) async {
    if (school['image_url'] != null && school['image_url'] != 'assets/placeholder_image.png') {
      return;
    }

    final fullname = school['fullname'] as String? ?? '';
    final schoolId = school['id']?.toString() ?? '';
    final cacheKey = 'secondary_school_image_${schoolId}_${fullname.replaceAll(' ', '_')}';

    // Try to get image URL from cache first
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedImageUrl = prefs.getString(cacheKey);

      if (cachedImageUrl != null && cachedImageUrl.isNotEmpty) {
        if (mounted) {
          setState(() {
            school['image_url'] = cachedImageUrl;
          });
        }
        return;
      }
    } catch (e) {
      print('Error retrieving cached image URL: $e');
    }

    // If not in cache, fetch from Supabase
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';

    try {
      final file = await Supabase.instance.client
          .storage
          .from('secondaryschools')
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client
            .storage
            .from('secondaryschools')
            .getPublicUrl(imageNameWebp);

        // Cache the image URL
        if (imageUrl.isNotEmpty) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(cacheKey, imageUrl);
        }
      }
    } catch (e) {
      print('Error fetching image from Supabase: $e');
    }

    if (mounted) {
      setState(() {
        school['image_url'] =
            imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      });
    }
  }
}