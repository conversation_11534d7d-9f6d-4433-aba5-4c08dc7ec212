import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class BudgetTrackerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BudgetTrackerPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<BudgetTrackerPage> createState() => _BudgetTrackerPageState();
}

class _BudgetTrackerPageState extends State<BudgetTrackerPage> {
  final TextEditingController _budgetAmountController = TextEditingController();
  final TextEditingController _expenseDescriptionController = TextEditingController();
  final TextEditingController _expenseAmountController = TextEditingController();
  String? _selectedBudgetCateogry;
  String? _selectedExpenseCategory;
  DateTime? _selectedDate;

  List<BudgetItem> _budgets = [];
  List<ExpenseItem> _expenses = [];
  List<String> _categories = [];

  DateTime? _selectedPeriodStartDate;
  DateTime? _selectedPeriodEndDate;
  DateTime? _exportStartDate;
  DateTime? _exportEndDate;

  @override
  void initState() {
    super.initState();
    _loadData();
    _selectedDate = DateTime.now();
  }

  Future<void> _loadData() async {
    final prefs = await SharedPreferences.getInstance();
    final budgetData = prefs.getStringList('budgets');
    final expenseData = prefs.getStringList('expenses');
    final categoryData = prefs.getStringList('categories');

    if (categoryData != null) {
      setState(() {
        _categories = List<String>.from(jsonDecode(categoryData.join('')));
      });
    } else {
      setState(() {
        _categories.addAll(['Food', 'Transportation', 'Entertainment', 'Utilities', 'Other']);
        _saveCategories();
      });
    }

    if (budgetData != null) {
      setState(() {
        _budgets = budgetData.map((item) => BudgetItem.fromJson(jsonDecode(item))).toList();
      });
    }
    if (expenseData != null) {
      setState(() {
        _expenses = expenseData.map((item) => ExpenseItem.fromJson(jsonDecode(item))).toList();
      });
    }
  }

  Future<void> _saveBudgets() async {
    final prefs = await SharedPreferences.getInstance();
    final budgetList = _budgets.map((item) => jsonEncode(item.toJson())).toList();
    await prefs.setStringList('budgets', budgetList);
  }

  Future<void> _saveExpenses() async {
    final prefs = await SharedPreferences.getInstance();
    final expenseList = _expenses.map((item) => jsonEncode(item.toJson())).toList();
    await prefs.setStringList('expenses', expenseList);
  }

  Future<void> _saveCategories() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('categories', [jsonEncode(_categories)]);
  }

  void _addBudget() {
    final amount = double.tryParse(_budgetAmountController.text.trim());
    if (_selectedBudgetCateogry != null && amount != null && amount > 0) {
      setState(() {
        _budgets.add(BudgetItem(category: _selectedBudgetCateogry!, amount: amount));
        _saveBudgets();
        _budgetAmountController.clear();
        _selectedBudgetCateogry = null;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a category and enter a valid budget amount.')),
      );
    }
  }

  void _addExpense() {
    final description = _expenseDescriptionController.text.trim();
    final amount = double.tryParse(_expenseAmountController.text.trim());
    if (_selectedExpenseCategory != null && description.isNotEmpty && amount != null && amount > 0 && _selectedDate != null) {
      setState(() {
        _expenses.add(ExpenseItem(
          category: _selectedExpenseCategory!,
          description: description,
          amount: amount,
          date: _selectedDate!,
        ));
        _saveExpenses();
        _expenseDescriptionController.clear();
        _expenseAmountController.clear();
        _selectedExpenseCategory = null;
        _selectedDate = DateTime.now();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a category, enter description and amount, and select a date.')),
      );
    }
  }

  Map<String, double> _calculateCategorySpending([DateTime? startDate, DateTime? endDate]) {
    Map<String, double> spending = {};
    for (var expense in _expenses) {
      if ((startDate == null || expense.date.isAtSameMomentAs(startDate) || expense.date.isAfter(startDate)) &&
          (endDate == null || expense.date.isAtSameMomentAs(endDate) || expense.date.isBefore(endDate.add(const Duration(days: 1))))) {
        spending[expense.category] = (spending[expense.category] ?? 0) + expense.amount;
      }
    }
    return spending;
  }

  double _getTotalBudget() {
    return _budgets.fold(0, (sum, item) => sum + item.amount);
  }

  double _getTotalSpending([DateTime? startDate, DateTime? endDate]) {
    return _expenses.where((expense) =>
        (startDate == null || expense.date.isAtSameMomentAs(startDate) || expense.date.isAfter(startDate)) &&
        (endDate == null || expense.date.isAtSameMomentAs(endDate) || expense.date.isBefore(endDate.add(const Duration(days: 1)))))
        .fold(0, (sum, item) => sum + item.amount);
  }

  void _editBudget(BudgetItem budget) {
    final TextEditingController editController = TextEditingController(text: budget.amount.toString());
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Budget Amount'),
        content: TextField(
          controller: editController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(labelText: 'New Amount'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newAmount = double.tryParse(editController.text.trim());
              if (newAmount != null && newAmount > 0) {
                setState(() {
                  budget.amount = newAmount;
                  _saveBudgets();
                });
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a valid amount.')),
                );
              }
            },
            child: Text('Update', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
  }

  Future<void> _exportReport(DateTime startDate, DateTime endDate) async {
    final pdf = pw.Document();
    final formattedStartDate = DateFormat('yyyy-MM-dd').format(startDate);
    final formattedEndDate = DateFormat('yyyy-MM-dd').format(endDate);

    final filteredExpenses = _expenses.where((expense) =>
        (expense.date.isAtSameMomentAs(startDate) || expense.date.isAfter(startDate)) &&
        expense.date.isBefore(endDate.add(const Duration(days: 1)))).toList();
    final periodSpending = _getTotalSpending(startDate, endDate);
    final totalBudget = _getTotalBudget();
    final balance = totalBudget - periodSpending;

    pdf.addPage(pw.Page(
      build: (pw.Context context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Header(level: 0, text: 'Budget Report'),
          pw.Text('Period: $formattedStartDate - $formattedEndDate'),
          pw.SizedBox(height: 10),
          pw.Text('Budget: \$${totalBudget.toStringAsFixed(2)}'),
          pw.Text('Total Spending: \$${periodSpending.toStringAsFixed(2)}'),
          pw.Text('Balance: \$${balance.toStringAsFixed(2)}'),
          pw.SizedBox(height: 20),
          pw.Header(level: 1, text: 'Expenses'),
          if (filteredExpenses.isEmpty)
            pw.Text('No expenses for this period.')
          else
            pw.ListView.builder(
              itemCount: filteredExpenses.length,
              itemBuilder: (context, index) {
                final expense = filteredExpenses[index];
                return pw.Text(
                    '- ${DateFormat('yyyy-MM-dd').format(expense.date)}: ${expense.description} (${expense.category}) - \$${expense.amount.toStringAsFixed(2)}');
              },
            ),
        ],
      ),
    ));

    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/budget_report_${formattedStartDate}_${formattedEndDate}.pdf');
    await file.writeAsBytes(await pdf.save());
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Budget report saved to ${file.path}')),
    );
  }

  List<ExpenseItem> _getExpensesForPeriod(DateTime? startDate, DateTime? endDate) {
    return _expenses.where((expense) {
      bool afterOrOnStartDate = startDate == null || !expense.date.isBefore(startDate);
      bool beforeOrOnEndDate = endDate == null || !expense.date.isAfter(endDate);
      return afterOrOnStartDate && beforeOrOnEndDate;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final categorySpending = _calculateCategorySpending();
    final totalBudget = _getTotalBudget();
    final totalSpending = _getTotalSpending();
    final remainingBudget = totalBudget - totalSpending;

    final periodSpending = _calculateCategorySpending(_selectedPeriodStartDate, _selectedPeriodEndDate);
    final totalPeriodSpending = _getTotalSpending(_selectedPeriodStartDate, _selectedPeriodEndDate);
    final periodExpenses = _getExpensesForPeriod(_selectedPeriodStartDate, _selectedPeriodEndDate);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget & Expense Tracker'),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Budget Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Add/Edit Budget', style: Theme.of(context).textTheme.titleMedium),
                    DropdownButtonFormField<String>(
                      value: _selectedBudgetCateogry,
                      onChanged: (newValue) {
                        setState(() {
                          _selectedBudgetCateogry = newValue;
                        });
                      },
                      items: _categories.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Category'),
                    ),
                    TextField(
                      controller: _budgetAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(labelText: 'Budgeted Amount'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _addBudget,
                      child: Text('Add Budget', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                    ),
                    const SizedBox(height: 10),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _budgets.length,
                      itemBuilder: (context, index) {
                        final budget = _budgets[index];
                        return ListTile(
                          title: Text(budget.category),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text('\$${budget.amount.toStringAsFixed(2)}'),
                              IconButton(
                                icon: Icon(Icons.edit, color: widget.isDarkMode ? Colors.white : Colors.black,),
                                onPressed: () => _editBudget(budget),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Expense Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Add Expense', style: Theme.of(context).textTheme.titleMedium),
                    DropdownButtonFormField<String>(
                      value: _selectedExpenseCategory,
                      onChanged: (newValue) {
                        setState(() {
                          _selectedExpenseCategory = newValue;
                        });
                      },
                      items: _categories.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Category'),
                    ),
                    TextField(
                      controller: _expenseDescriptionController,
                      decoration: const InputDecoration(labelText: 'Description'),
                    ),
                    TextField(
                      controller: _expenseAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(labelText: 'Amount'),
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      onTap: () async {
                        final DateTime? pickedDate = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (pickedDate != null) {
                          setState(() {
                            _selectedDate = pickedDate;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Date',
                          hintText: 'Select Date',
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Text(
                              _selectedDate == null
                                  ? ''
                                  : DateFormat('yyyy-MM-dd').format(_selectedDate!),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: widget.isDarkMode ? Colors.white : Colors.black,
                              ),
                            ),
                            const Icon(Icons.calendar_today),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _addExpense,
                      child: Text('Add Expense', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Periodic Spending View
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('View Spending Periodically', style: Theme.of(context).textTheme.titleMedium),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            final DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: _selectedPeriodStartDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                _selectedPeriodStartDate = pickedDate;
                                _selectedPeriodEndDate = pickedDate;
                              });
                            }
                          },
                          child: Text('Select Date', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            final DateTimeRange? pickedRange = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedRange != null) {
                              setState(() {
                                _selectedPeriodStartDate = pickedRange.start;
                                _selectedPeriodEndDate = pickedRange.end;
                              });
                            }
                          },
                          child: Text('Select Range', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                      ],
                    ),
                    if (_selectedPeriodStartDate != null && _selectedPeriodEndDate != null)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                            'Spending from ${DateFormat('yyyy-MM-dd').format(_selectedPeriodStartDate!)} to ${DateFormat('yyyy-MM-dd').format(_selectedPeriodEndDate!)}: \$${totalPeriodSpending.toStringAsFixed(2)}'),
                      ),
                    if (periodExpenses.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Expenses in this period:'),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: periodExpenses.length,
                            itemBuilder: (context, index) {
                              final expense = periodExpenses[index];
                              return ListTile(
                                title: Text('${expense.description} (${expense.category})'),
                                trailing: Text('\$${expense.amount.toStringAsFixed(2)}'),
                              );
                            },
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Summary Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Summary', style: Theme.of(context).textTheme.titleMedium),
                    ListTile(
                      title: const Text('Total Budget'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(totalBudget)),
                    ),
                    ListTile(
                      title: const Text('Total Spending'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(totalSpending)),
                    ),
                    ListTile(
                      title: const Text('Remaining Budget'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(remainingBudget)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Export Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Export Report', style: Theme.of(context).textTheme.titleMedium),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            final DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: _exportStartDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                _exportStartDate = pickedDate;
                                _exportEndDate = pickedDate;
                              });
                            }
                          },
                          child: Text('Start Date', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            final DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: _exportEndDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                _exportEndDate = pickedDate;
                              });
                            }
                          },
                          child: Text('End Date', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: (_exportStartDate != null && _exportEndDate != null && _exportStartDate!.isBefore(_exportEndDate!))
                          ? () => _exportReport(_exportStartDate!, _exportEndDate!)
                          : null,
                      child: Text('Export as PDF', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                    ),
                    if (_exportStartDate != null && _exportEndDate != null && !_exportStartDate!.isBefore(_exportEndDate!))
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Please select a valid date range.',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Charts Section
            if (categorySpending.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text('Spending by Category', style: Theme.of(context).textTheme.titleMedium),
                      SizedBox(
                        height: 200,
                        child: PieChart(
                          PieChartData(
                            sections: categorySpending.entries.map((entry) {
                              return PieChartSectionData(
                                value: entry.value,
                                title: '${entry.key}\n${entry.value.toStringAsFixed(2)}',
                                titleStyle: const TextStyle(fontSize: 10, color: Colors.white),
                                color: _getColorForCategory(entry.key),
                              );
                            }).toList(),
                            borderData: FlBorderData(show: false),
                            sectionsSpace: 2,
                            centerSpaceRadius: 40,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Color _getColorForCategory(String category) {
    final availableColors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.amber,
      Colors.purple,
      Colors.teal,
      Colors.orange,
      Colors.brown,
      Colors.cyan,
      Colors.indigo,
    ];
    return availableColors[_categories.indexOf(category) % availableColors.length];
  }
}

class BudgetItem {
  final String category;
  double amount;

  BudgetItem({required this.category, required this.amount});

  Map<String, dynamic> toJson() => {
        'category': category,
        'amount': amount,
      };

  factory BudgetItem.fromJson(Map<String, dynamic> json) {
    return BudgetItem(
      category: json['category'],
      amount: json['amount'],
    );
  }
}

class ExpenseItem {
  final String category;
  final String description;
  final double amount;
  final DateTime date;

  ExpenseItem({required this.category, required this.description, required this.amount, required this.date});

  Map<String, dynamic> toJson() => {
        'category': category,
        'description': description,
        'amount': amount,
        'date': date.toIso8601String(),
      };

  factory ExpenseItem.fromJson(Map<String, dynamic> json) {
    return ExpenseItem(
      category: json['category'],
      description: json['description'],
      amount: json['amount'],
      date: DateTime.parse(json['date']),
    );
  }
}