import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'accessibility_detail_page.dart';
import 'login_page.dart';

class AccessibilityPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedAccessibilityFeatures;
  final bool isFromDetailPage;

  const AccessibilityPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedAccessibilityFeatures,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _AccessibilityPageState createState() => _AccessibilityPageState();
}

class _AccessibilityPageState extends State<AccessibilityPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('accessibility_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _accessibilityFeatures = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AccessibilityPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // if collegeNameForTable changes, reload
    if (oldWidget.collegeNameForTable != widget.collegeNameForTable) {
      _resetAndLoad();
    }
  }

  void _resetAndLoad() {
    _page = 0;
    _hasMore = true;
    _accessibilityFeatures.clear();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    if (widget.preloadedAccessibilityFeatures != null &&
        widget.preloadedAccessibilityFeatures!.isNotEmpty) {
      setState(() {
        _accessibilityFeatures = List<Map<String, dynamic>>.from(widget.preloadedAccessibilityFeatures!);
        _accessibilityFeatures.forEach((item) => item['_isImageLoading'] = false);
        _accessibilityFeatures.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAccessibilityFeatures!.length == _pageSize;
      });
      // still fetch next pages
      _loadFeaturesFromDatabase(initialLoad: false);
    } else {
      await _loadFeaturesFromDatabase(initialLoad: true);
    }
  }

  Future<void> _loadFeaturesFromDatabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) return;

    setState(() => _isLoading = true);
    final table = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accessibility';

    try {
      final resp = await Supabase.instance.client
          .from(table)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final newItems = await _updateImageUrls(List<Map<String, dynamic>>.from(resp));
      setState(() {
        if (initialLoad) {
          _accessibilityFeatures = newItems;
        } else {
          _accessibilityFeatures.addAll(newItems);
        }
        _accessibilityFeatures.forEach((e) => e['_isImageLoading'] = false);
        _accessibilityFeatures.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = resp.length == _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      if (!_isDisposed) {
        String msg;
        final err = e.toString().toLowerCase();
        if (err.contains('socketexception') || err.contains('offline')) {
          msg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (err.contains('relation') && err.contains('does not exist')) {
          msg = "No accessibility data has been added yet for this institution.";
          _showErrorSnackbar(msg);
        } else {
          msg = "Error fetching accessibility: $e";
          _showErrorSnackbar(msg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateImageUrls(List<Map<String, dynamic>> items) async {
    var futures = <Future>[];
    final bucket = widget.collegeNameForTable.toLowerCase().replaceAll(' ', '');
    for (var item in items) {
      item['_isImageLoading'] = false;
      if (item['image_url'] == null || item['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(item, bucket));
      }
    }
    await Future.wait(futures);
    return items;
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> item, String bucketName) async {
    if (item['_isImageLoading'] == true) return;
    if (item['image_url'] != null && item['image_url'] != 'assets/placeholder_image.png') return;

    setState(() => item['_isImageLoading'] = true);

    final fullname = item['fullname'] as String? ?? '';
    final fileName = '$fullname.webp';
    String url = '';

    try {
      final fileData = await Supabase.instance.client
          .storage
          .from('$bucketName/accessibility')
          .download(fileName);
      if (fileData.isNotEmpty) {
        url = Supabase.instance.client
            .storage
            .from('$bucketName/accessibility')
            .getPublicUrl(fileName);
      }
    } catch (_) {
      // ignore
    }

    if (mounted) {
      setState(() {
        item['image_url'] = url.isNotEmpty ? url : 'assets/placeholder_image.png';
        item['_isImageLoading'] = false;
      });
    } else {
      item['_isImageLoading'] = false;
    }
  }

  void _setupRealtime() {
    final table = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accessibility';
    _realtimeChannel = Supabase.instance.client
        .channel('accessibility')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: table,
          callback: (_) => _loadFeaturesFromDatabase(initialLoad: true),
        )
        .subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _page++;
        _loadFeaturesFromDatabase(initialLoad: false);
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text("Offline. Please check your internet connection."), backgroundColor: Colors.redAccent),
    );
  }

  void _showErrorSnackbar(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(msg), backgroundColor: Colors.redAccent),
    );
  }

  void _navigateToDetail(Map<String, dynamic> item) {
    if (_isDisposed) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AccessibilityDetailPage(
          accessibilityFeature: item,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          collegeNameForBucket: widget.collegeNameForTable,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Accessibility Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _accessibilityFeatures.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadFeaturesFromDatabase(initialLoad: true),
              child: _accessibilityFeatures.isEmpty
                  ? LayoutBuilder(
                      builder: (ctx, bc) => SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: bc.maxHeight,
                          child: Center(
                            child: Text(
                              'No accessibility features found.',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          ),
                        ),
                      ),
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _accessibilityFeatures.length + (_hasMore ? 1 : 0),
                      itemBuilder: (ctx, idx) {
                        if (idx < _accessibilityFeatures.length) {
                          final item = _accessibilityFeatures[idx];
                          return VisibilityDetector(
                            key: Key('accessibility_${item['id']}'),
                            onVisibilityChanged: (info) {
                              if (info.visibleFraction > 0.1 &&
                                  (item['image_url'] == null ||
                                      item['image_url'] == 'assets/placeholder_image.png') &&
                                  !item['_isImageLoading']) {
                                _fetchImageUrl(item, widget.collegeNameForTable.toLowerCase().replaceAll(' ', ''));
                              }
                            },
                            child: _buildCard(item, theme, isDark),
                          );
                        } else {
                          return const Center(
                              child: Padding(
                            padding: EdgeInsets.all(16),
                            child: CircularProgressIndicator(),
                          ));
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).popUntil((r) => r.isFirst),
                ),
                IconButton(
                  icon: Icon(
                    isDark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => LoginPage(isDarkMode: isDark, toggleTheme: widget.toggleTheme),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCard(Map<String, dynamic> item, ThemeData theme, bool isDarkMode) {
    final fullname = item['fullname'] ?? 'Unknown';
    final building = item['building'] ?? '';
    final room = item['room'] ?? '';
    final hours = item['hours'] ?? '';

    String location = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      location = '$building, Room $room';
    } else if (building.isNotEmpty) {
      location = building;
    } else if (room.isNotEmpty) {
      location = 'Room $room';
    }

    final imgUrl = item['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(item),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imgUrl.isNotEmpty && imgUrl != 'assets/placeholder_image.png'
                  ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: imgUrl,
                        width: 48,
                        height: 48,
                        fit: BoxFit.cover,
                        placeholder: (_, __) => CircleAvatar(
                          radius: 24,
                          backgroundColor: isDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(Icons.accessible, color: isDarkMode ? Colors.white : Colors.black),
                        ),
                        errorWidget: (_, __, ___) => CircleAvatar(
                          radius: 24,
                          backgroundColor: isDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(Icons.accessible, color: isDarkMode ? Colors.white : Colors.black),
                        ),
                      ),
                    )
                  : CircleAvatar(
                      radius: 24,
                      backgroundColor: isDarkMode
                          ? Colors.white.withOpacity(0.1)
                          : Colors.black.withOpacity(0.1),
                      child: Icon(Icons.accessible, color: isDarkMode ? Colors.white : Colors.black),
                    ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(location, style: TextStyle(color: theme.colorScheme.onSurfaceVariant)),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(hours, style: TextStyle(color: theme.colorScheme.onSurfaceVariant)),
                      ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: theme.colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }
}
