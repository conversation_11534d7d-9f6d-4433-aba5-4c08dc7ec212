import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'faq_detail_page.dart';
import 'login_page.dart';

class FAQsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedFAQs;
  final bool isFromDetailPage;

  const FAQsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedFAQs,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _FAQsPageState createState() => _FAQsPageState();
}

class _FAQsPageState extends State<FAQsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('faqs_list');

  List<Map<String, dynamic>> _faqs = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _page = 0;
  final int _pageSize = 20;
  bool _isDisposed = false;

  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedFAQs != null && widget.preloadedFAQs!.isNotEmpty) {
      setState(() {
        _faqs = List<Map<String, dynamic>>.from(widget.preloadedFAQs!);
        _hasMore = widget.preloadedFAQs!.length == _pageSize;
      });
      _loadMoreFAQs(initialLoad: false);
    } else {
      _loadMoreFAQs(initialLoad: true);
    }
  }

  void _setupRealtime() {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_faq';
    _realtimeChannel = Supabase.instance.client
        .channel('faqs_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: table,
      callback: (payload) {
        // refresh entire list on any change
        if (!_isDisposed) _refreshFAQs();
      },
    ).subscribe();
  }

  Future<void> _refreshFAQs() async {
    setState(() {
      _page = 0;
      _hasMore = true;
      _faqs.clear();
    });
    await _loadMoreFAQs(initialLoad: true);
  }

  Future<void> _loadMoreFAQs({bool initialLoad = false}) async {
    if (_isLoading) return;
    if (!initialLoad && !_hasMore) return;

    setState(() => _isLoading = true);

    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_faq';
    try {
      final response = await Supabase.instance.client
          .from(table)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      final fetched = List<Map<String, dynamic>>.from(response);
      setState(() {
        if (initialLoad) {
          _faqs = fetched;
        } else {
          _faqs.addAll(fetched);
        }
        _hasMore = fetched.length == _pageSize;
        if (!initialLoad) _page++;
      });
    } catch (e) {
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading FAQs: $e')),
        );
      }
    } finally {
      if (!_isDisposed) setState(() => _isLoading = false);
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreFAQs();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon:
              Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Frequently Asked Questions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('faqs_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 &&
              _faqs.isEmpty &&
              !_isLoading) {
            _loadMoreFAQs(initialLoad: true);
          }
        },
        child: RefreshIndicator(
          onRefresh: _refreshFAQs,
          child: _faqs.isEmpty && !_isLoading
              ? ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: [
                    SizedBox(height: 200),
                    Center(
                      child: Text(
                        'No FAQs available.',
                        style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                  ],
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  itemCount: _faqs.length + (_hasMore ? 1 : 0),
                  itemBuilder: (ctx, idx) {
                    if (idx < _faqs.length) {
                      final faq = _faqs[idx];
                      final title = faq['fullname'] as String? ?? 'Untitled';
                      final topic = faq['topic'] as String? ?? '';
                      final summary = faq['about'] as String? ?? '';
                      return Card(
                        color: theme.colorScheme.surface,
                        surfaceTintColor: Colors.transparent,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => FAQDetailPage(
                                  faq: faq,
                                  isDarkMode: widget.isDarkMode,
                                  toggleTheme: widget.toggleTheme,
                                ),
                              ),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment:
                                  CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                  children: [
                                    CircleAvatar(
                                      radius: 24,
                                      backgroundColor: widget
                                              .isDarkMode
                                          ? Colors.white
                                              .withOpacity(0.1)
                                          : Colors.black
                                              .withOpacity(0.1),
                                      child: Icon(
                                        Icons.question_answer,
                                        color: widget.isDarkMode
                                            ? Colors.white
                                            : Colors.black,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment
                                                .start,
                                        children: [
                                          Text(
                                            title,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight:
                                                  FontWeight.bold,
                                              color: theme
                                                  .colorScheme
                                                  .onSurface,
                                            ),
                                          ),
                                          if (topic.isNotEmpty)
                                            Padding(
                                              padding:
                                                  const EdgeInsets
                                                          .only(
                                                      top: 4),
                                              child: Text(
                                                'Topic: $topic',
                                                style: TextStyle(
                                                  color: theme
                                                      .colorScheme
                                                      .onSurfaceVariant,
                                                  fontStyle:
                                                      FontStyle
                                                          .italic,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: 16,
                                      color: theme.colorScheme
                                          .onSurfaceVariant,
                                    ),
                                  ],
                                ),
                                if (summary.isNotEmpty)
                                  Padding(
                                    padding:
                                        const EdgeInsets.only(
                                            top: 12, left: 64),
                                    child: Text(
                                      summary,
                                      style: TextStyle(
                                          color: theme
                                              .colorScheme
                                              .onSurfaceVariant),
                                      maxLines: 2,
                                      overflow:
                                          TextOverflow.ellipsis,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }

                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                ),
        ),
      ),
    );
  }
}
