// At the top of the file:
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf_render/pdf_render.dart' as pdf_render;
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter/services.dart';
import '../widgets/content_helpers.dart';
import '../widgets/pdf_helpers.dart';
import '../widgets/content_segment.dart';
import 'package:path/path.dart' as path;
import 'dart:io' as io;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
// Geometry features removed due to null safety issues
import '../utils/audio_recorder.dart';
import '../widgets/recording_dialog.dart';
import '../widgets/podcast_player.dart';
import '../utils/podcast_generator.dart';
import '../utils/docx_extractor.dart';
import '../utils/pptx_extractor.dart';
import '../utils/audio_extractor.dart';
import '../utils/video_extractor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdf/pdf.dart' as pdf_package;
import 'package:flutter_math_fork/flutter_math.dart';
import '../widgets/latex_helpers.dart' as latex_helpers;
import '../widgets/latex_image_renderer.dart';
import '../utils/text_recognition_helper.dart';
import '../utils/url_text_extractor.dart';
import '../utils/youtube_transcript_extractor.dart';
import '../widgets/visual_mindmap.dart';
import '../widgets/experiment_simulation.dart';
import '../pages/resume_tailoring_page.dart';
import '../pages/interview_prep_page.dart';

import 'package:markdown/markdown.dart' as md;

// LaTeX syntax and builder classes
class LatexInlineSyntax extends md.InlineSyntax {
  LatexInlineSyntax() : super(r'\\\(.*?\\\)|\$[^\$]*?\$');

  @override
  bool onMatch(md.InlineParser parser, Match match) {
    parser.addNode(md.Element('latex', [md.Text(match.group(0)!)]));
    return true;
  }
}

class LatexBlockSyntax extends md.InlineSyntax {
  LatexBlockSyntax() : super(r'\\\[.*?\\\]|\$\$.*?\$\$');

  @override
  bool onMatch(md.InlineParser parser, Match match) {
    parser.addNode(md.Element('latex_block', [md.Text(match.group(0)!)]));
    return true;
  }
}

class LatexInlineBuilder extends MarkdownElementBuilder {
  final bool isDarkMode;
  final double fontSize;

  LatexInlineBuilder(this.isDarkMode, this.fontSize);

  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    final String latex = element.textContent;
    return _buildLatexWidget(latex, isDarkMode, fontSize, false);
  }
}

class LatexBlockBuilder extends MarkdownElementBuilder {
  final bool isDarkMode;
  final double fontSize;

  LatexBlockBuilder(this.isDarkMode, this.fontSize);

  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    final String latex = element.textContent;
    return _buildLatexWidget(latex, isDarkMode, fontSize, true);
  }
}

// Helper method to build LaTeX widget
Widget _buildLatexWidget(String latex, bool isDarkMode, double fontSize, bool isBlock) {
  final Color textColor = isDarkMode ? Colors.white : Colors.black;
  final Color errorColor = isDarkMode ? Colors.red.shade300 : Colors.red.shade800;

  try {
    // Extract LaTeX content from delimiters
    String texString = latex.trim();
    bool isDisplayMode = isBlock;

    if (texString.startsWith(r'$$') && texString.endsWith(r'$$')) {
      texString = texString.substring(2, texString.length - 2).trim();
      isDisplayMode = true;
    } else if (texString.startsWith(r'$') && texString.endsWith(r'$')) {
      texString = texString.substring(1, texString.length - 1).trim();
    } else if (texString.startsWith(r'\[') && texString.endsWith(r'\]')) {
      texString = texString.substring(2, texString.length - 2).trim();
      isDisplayMode = true;
    } else if (texString.startsWith(r'\(') && texString.endsWith(r'\)')) {
      texString = texString.substring(2, texString.length - 2).trim();
    }

    // Use flutter_math_fork to render LaTeX
    return Container(
      padding: EdgeInsets.symmetric(vertical: isDisplayMode ? 8.0 : 4.0),
      width: double.infinity,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Math.tex(
          texString,
          textStyle: TextStyle(fontSize: fontSize, color: textColor),
          mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
          onErrorFallback: (error) {
            print('LaTeX parsing error: $error for input: $texString');
            // Try to fix common LaTeX errors
            final fixedTeX = _fixCommonLatexErrors(texString);
            if (fixedTeX != texString) {
              try {
                return Math.tex(
                  fixedTeX,
                  textStyle: TextStyle(fontSize: fontSize, color: textColor),
                  mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
                );
              } catch (e) {
                // If fixing didn't work, fall back to showing the original LaTeX
                print('Fixed LaTeX still has error: $e');
              }
            }
            // Fallback to showing the original LaTeX with error styling
            return Text(
              latex,
              style: TextStyle(color: errorColor, fontSize: fontSize, fontFamily: 'monospace'),
            );
          },
        ),
      ),
    );
  } catch (e) {
    print('LaTeX rendering error: $e for input: $latex');
    return Container(
      width: double.infinity,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Text(
          latex,  // Show the original LaTeX code instead of error message
          style: TextStyle(color: errorColor, fontSize: fontSize, fontFamily: 'monospace'),
        ),
      ),
    );
  }
}

// Helper method to fix common LaTeX errors
String _fixCommonLatexErrors(String texString) {
  // Replace common LaTeX errors
  String fixed = texString;

  // Fix missing backslashes before common LaTeX commands
  final commonCommands = ['alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
                         'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'rho', 'sigma',
                         'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega', 'sum', 'prod', 'int',
                         'frac', 'sqrt', 'times', 'div', 'pm', 'leq', 'geq', 'neq', 'approx'];

  for (final cmd in commonCommands) {
    // Only add backslash if it's not already there
    fixed = fixed.replaceAllMapped(RegExp('(?<!\\\\)$cmd'), (match) => '\\$cmd');
  }

  // Fix unbalanced braces
  int openBraces = 0;
  int closeBraces = 0;
  for (int i = 0; i < fixed.length; i++) {
    if (fixed[i] == '{') openBraces++;
    if (fixed[i] == '}') closeBraces++;
  }

  // Add missing closing braces
  if (openBraces > closeBraces) {
    fixed += '}' * (openBraces - closeBraces);
  }

  // Fix missing closing brackets in \frac{}{} commands
  fixed = fixed.replaceAllMapped(
    RegExp(r'\\frac\{([^{}]*)\}\{([^{}]*)(?!\})'),
    (match) => '\\frac{${match.group(1)}}{${match.group(2)}}'
  );

  return fixed;
}

class SuperscriptSyntax extends md.InlineSyntax {
  SuperscriptSyntax() : super(r'\^\{?([^}]+)\}?');

  @override
  bool onMatch(md.InlineParser parser, Match match) {
    parser.addNode(md.Element.text('sup', match.group(1)!));
    return true;
  }
}

class SubscriptSyntax extends md.InlineSyntax {
  SubscriptSyntax() : super(r'_\{?([^}]+)\}?');

  @override
  bool onMatch(md.InlineParser parser, Match match) {
    parser.addNode(md.Element.text('sub', match.group(1)!));
    return true;
  }
}

class SupBuilder extends MarkdownElementBuilder {
  final double fontSize;

  SupBuilder(this.fontSize);

  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    return Transform.translate(
      offset: const Offset(0, -5),
      child: Text(
        element.textContent,
        style: preferredStyle?.copyWith(
          fontSize: fontSize * 0.7,
        ),
      ),
    );
  }
}

class SubBuilder extends MarkdownElementBuilder {
  final double fontSize;

  SubBuilder(this.fontSize);

  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    return Transform.translate(
      offset: const Offset(0, 3),
      child: Text(
        element.textContent,
        style: preferredStyle?.copyWith(
          fontSize: fontSize * 0.7,
        ),
      ),
    );
  }
}


class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  // Add text recognition and input controllers here in the STATE class
  final TextEditingController _textInputController = TextEditingController();
  final textRecognizer = TextRecognizer();

  // Combined Markdown and LaTeX renderer widget
// Update the buildLatexContent method
// Update the MarkdownStyleSheet in the buildLatexContent method
static Widget buildLatexContent(String content, bool isDarkMode, double fontSize, BuildContext context) {
  // Pre-process content to handle raw LaTeX and formatting tags
  content = _preprocessContent(content);

  // Check for geometry commands in the content
  final geometryRegex = RegExp(r'```geometry:.*?```', dotAll: true);
  final hasGeometryCommands = geometryRegex.hasMatch(content);

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      MarkdownBody(
        data: content,
        builders: {
          'latex': LatexInlineBuilder(isDarkMode, fontSize),
          'latex_block': LatexBlockBuilder(isDarkMode, fontSize),
          'sup': SupBuilder(fontSize),
          'sub': SubBuilder(fontSize),
        },
        inlineSyntaxes: [
          LatexInlineSyntax(),
          LatexBlockSyntax(),
          SuperscriptSyntax(),
          SubscriptSyntax(),
        ],
        styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
          p: TextStyle(fontSize: fontSize, color: isDarkMode ? Colors.white : Colors.black, height: 1.8),
          h1: TextStyle(fontSize: fontSize * 1.5, color: isDarkMode ? Colors.white : Colors.black, fontWeight: FontWeight.bold),
          h2: TextStyle(fontSize: fontSize * 1.3, color: isDarkMode ? Colors.white : Colors.black, fontWeight: FontWeight.bold),
          h3: TextStyle(fontSize: fontSize * 1.1, color: isDarkMode ? Colors.white : Colors.black, fontWeight: FontWeight.bold),
          code: TextStyle(fontSize: fontSize * 0.9, color: isDarkMode ? Colors.white : Colors.black, fontFamily: 'monospace'),
          codeblockDecoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
      // Geometry features removed due to null safety issues
    ],
  );
}

// Pre-process content to handle raw LaTeX and formatting tags
static String _preprocessContent(String content) {
  // Replace raw <sub> and <sup> tags with markdown syntax
  content = content.replaceAllMapped(
    RegExp(r'<sub>(.*?)</sub>'),
    (match) => '_${match.group(1)}',
  );

  content = content.replaceAllMapped(
    RegExp(r'<sup>(.*?)</sup>'),
    (match) => '^${match.group(1)}',
  );

  // Ensure LaTeX delimiters have proper spacing
  content = content.replaceAll(r'\(', ' \\( ');
  content = content.replaceAll(r'\)', ' \\) ');
  content = content.replaceAll(r'\[', ' \\[ ');
  content = content.replaceAll(r'\]', ' \\] ');

  // Ensure $$ LaTeX blocks have proper spacing
  content = content.replaceAll(r'$$', ' \$\$ ');

  // Process geometry commands to make them visible in markdown but not rendered as code blocks
  content = content.replaceAllMapped(
    RegExp(r'```geometry:(.*?)```', dotAll: true),
    (match) => '\n\n```geometry:${match.group(1)}```\n\n',
  );

  return content;
}






// Add these helper functions in your _DashboardPageState class
String _convertToSuperscript(String text) {
  final Map<String, String> superscriptMap = {
    '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
    '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
    '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
    'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ', 'd': 'ᵈ', 'e': 'ᵉ',
    'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ', 'i': 'ᶦ', 'j': 'ʲ',
    'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'n': 'ⁿ', 'o': 'ᵒ',
    'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ',
    'v': 'ᵛ', 'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ'
  };
  return text.split('').map((c) => superscriptMap[c] ?? c).join();
}

String _convertToSubscript(String text) {
  final Map<String, String> subscriptMap = {
    '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
    '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
    '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
    'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ', 'i': 'ᵢ', 'k': 'ₖ',
    'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ', 'p': 'ₚ',
    'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ', 'v': 'ᵥ',
    'x': 'ₓ'
  };
  return text.split('').map((c) => subscriptMap[c] ?? c).join();
}






  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  bool _isResponseComplete = true; // Whether the response is complete or cutoff
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<FileWithPageRange> _pickedFiles = [];
  String _processType = 'summary';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  List<ExamQuestion> _examQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';
  List<String> _lessonSteps = [];
  int _currentLessonStepIndex = 0;
  bool _lessonPlaying = false;
  double _lessonSpeed = 1.0;
  double _lessonFontSize = 14.0;
  bool _isNarrationMuted = false;

  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  get isPlaying => ttsState == TtsState.playing;
  get isStopped => ttsState == TtsState.stopped;
  get isPaused => ttsState == TtsState.paused;
  get isContinued => ttsState == TtsState.continued;
  String _ttsLanguage = 'en-US';
  String? _readingGradeLevel;
  String? _difficultyLevel;
  String? _outputLanguage = 'English';

  String _displayText = '';
  int _currentCharIndex = 0;
  Timer? _textAnimationTimer;
  bool _isTextAnimationActive = false;

  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;



  int? _quizTimeLimitMinutes; // Quiz time limit in minutes
  Timer? _quizTimer; // Timer for quiz
  Duration _timeRemaining = Duration.zero; // Time remaining for quiz

  pw.Font? notoSansRegular;
  pw.Font? notoSansBold;
  pw.Font? notoSansItalic;
  pw.Font? notoSansBoldItalic;
  pw.Font? notoSansSymbols;
  pw.Font? stixTwoMathRegular;
  pw.Font? bravura;
  pw.Font? jetBrainsMonoRegular;
  pw.Font? isocpRegular;
  pw.Font? symbola;

  // Scroll controller for FAB visibility
  final ScrollController _scrollController = ScrollController();
  bool _isFabVisible = true;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
    _loadFonts();

    // Add scroll listener for FAB visibility
    _scrollController.addListener(() {
      if (_scrollController.position.pixels > _scrollController.position.minScrollExtent) {
        if (_isFabVisible) {
          setState(() {
            _isFabVisible = false;
          });
        }
      } else {
        if (!_isFabVisible) {
          setState(() {
            _isFabVisible = true;
          });
        }
      }
    });
  }

  Future<void> _loadFonts() async {
    // Load Noto Sans variants
    notoSansRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Regular.ttf'));
    notoSansBold = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Bold.ttf'));
    notoSansItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Italic.ttf'));
    notoSansBoldItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf'));
    notoSansSymbols = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSansSymbols-Regular.ttf'));

    // Load STIX Two Math
    stixTwoMathRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/STIXTwoMath-Regular.ttf'));

    // Load special fonts
    bravura = pw.Font.ttf(await rootBundle.load('assets/fonts/Bravura.ttf'));
    jetBrainsMonoRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/JetBrainsMono-Regular.ttf'));
    isocpRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/ISOCP-Regular.ttf')); // Matches pubspec name
    symbola = pw.Font.ttf(await rootBundle.load('assets/fonts/Symbola.ttf'));
  }


  @override
  void didUpdateWidget(covariant DashboardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  // Audio recorder instance
  final AudioRecorder _audioRecorder = AudioRecorder();
  bool _isRecording = false;
  String _recordingDuration = '0:00';

  // Podcast generator
  final PodcastGenerator _podcastGenerator = PodcastGenerator();
  bool _isGeneratingPodcast = false;
  double _podcastGenerationProgress = 0.0;
  Uint8List? _generatedPodcastAudio;

  @override
  void dispose() {
    _scrollController.dispose(); // Dispose the scroll controller
    _textAnimationTimer?.cancel();
    _stopTts();
    _cancelQuizTimer();
    textRecognizer.close(); // Close text recognizer here
    _audioRecorder.dispose(); // Dispose audio recorder
    _podcastGenerator.dispose(); // Dispose podcast generator
    super.dispose();
  }

  // Show recording dialog
  Future<void> _showRecordingDialog() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RecordingDialog(
        isDarkMode: widget.isDarkMode,
        onRecordingStateChanged: (isRecording) {
          setState(() => _isRecording = isRecording);
        },
        onDurationChanged: (duration) {
          setState(() => _recordingDuration = duration);
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
        onSave: () async {
          final recordedFile = await _audioRecorder.stopRecording();
          Navigator.of(context).pop();

          if (recordedFile != null) {
            setState(() {
              _pickedFiles.add(FileWithPageRange(file: recordedFile));
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Recording saved: ${recordedFile.name}')),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to save recording')),
            );
          }
        },
      ),
    );
  }

  // Generate podcast audio from text
  Future<void> _generatePodcastAudio() async {
    if (_isGeneratingPodcast || _geminiOutput == null || _geminiOutput!.isEmpty) {
      return;
    }

    setState(() {
      _isGeneratingPodcast = true;
      _podcastGenerationProgress = 0.0;
      _generatedPodcastAudio = null;
    });

    try {
      // Extract the script content (remove title and description sections if needed)
      String scriptContent = _geminiOutput!;

      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text('Generating Podcast Audio'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LinearProgressIndicator(value: _podcastGenerationProgress),
              const SizedBox(height: 16),
              Text('${(_podcastGenerationProgress * 100).toStringAsFixed(0)}%'),
            ],
          ),
        ),
      );

      // Generate the podcast audio
      final audioBytes = await _podcastGenerator.generatePodcastFromText(
        scriptContent,
        title: _extractPodcastTitle(),
        onProgress: (progress) {
          setState(() {
            _podcastGenerationProgress = progress;
          });
        },
      );

      // Close the progress dialog
      Navigator.of(context).pop();

      if (audioBytes != null && audioBytes.isNotEmpty) {
        // Create a platform file from the audio bytes
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final sanitizedTitle = _extractPodcastTitle().replaceAll(RegExp(r'[^\w\s]+'), '_');
        final fileName = '${sanitizedTitle}_$timestamp.mp3';

        final platformFile = PlatformFile(
          name: fileName,
          size: audioBytes.length,
          bytes: audioBytes,
        );

        setState(() {
          _generatedPodcastAudio = audioBytes;
          _pickedFiles.add(FileWithPageRange(file: platformFile));
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Podcast audio generated successfully!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to generate podcast audio')),
        );
      }
    } catch (e) {
      // Close the progress dialog if it's open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating podcast audio: $e')),
      );
    } finally {
      setState(() {
        _isGeneratingPodcast = false;
      });
    }
  }

  // Extract podcast title from Gemini output
  String _extractPodcastTitle() {
    String podcastTitle = 'Generated Podcast';

    if (_geminiOutput != null && _geminiOutput!.isNotEmpty) {
      final lines = _geminiOutput!.split('\n');

      // Look for title (usually the first line or after '# ' or 'Title: ')
      for (final line in lines) {
        if (line.startsWith('# ')) {
          podcastTitle = line.substring(2).trim();
          break;
        } else if (line.toLowerCase().startsWith('title:')) {
          podcastTitle = line.substring(6).trim();
          break;
        } else if (line.isNotEmpty && podcastTitle == 'Generated Podcast') {
          // If no explicit title found, use the first non-empty line
          podcastTitle = line.trim();
          break;
        }
      }
    }

    return podcastTitle;
  }



// Camera method to capture an image and add it to the list of uploaded files
Future<void> _openCamera() async {
  try {
    final status = await Permission.camera.request();
    if (status.isDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera permission required')),
      );
      return;
    }

    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.camera,
      imageQuality: 100, // Use highest quality
      preferredCameraDevice: CameraDevice.rear, // Use rear camera for document scanning
    );
    if (pickedFile == null) return;

    // Read the file as bytes
    final bytes = await pickedFile.readAsBytes();

    // Add the captured image to the picked files
    final platformFile = PlatformFile(
      name: 'captured_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      size: bytes.length,
      bytes: bytes,
    );

    setState(() {
      _pickedFiles.add(FileWithPageRange(file: platformFile));
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Image captured and added to learning materials')),
    );
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Camera error: $e')),
    );
  }
}

// Problem solving method removed as images are now processed like other uploaded files




  List<InlineSpan> parseText(String text, TextStyle defaultStyle) {
    // Check if the text contains any LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    if (latexRegExp.hasMatch(text)) {
      // If LaTeX is found, use the buildLatexContent method to render it
      return [WidgetSpan(
        child: _DashboardPageState.buildLatexContent(
          text,
          defaultStyle.color == Colors.white, // Determine if dark mode based on text color
          defaultStyle.fontSize ?? 16.0,
          context,
        ),
      )];
    }

    // If no LaTeX, continue with the original parsing
    List<InlineSpan> spans = [];
    StringBuffer buffer = StringBuffer();
    int i = 0;

    while (i < text.length) {
      if (text.startsWith('<sup>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sup>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, -5), // Move superscript up
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sup>');
          i += 5;
        }
      } else if (text.startsWith('<sub>', i)) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf('</sub>', i + 5);
        if (end != -1) {
          String content = text.substring(i + 5, end);
          spans.add(WidgetSpan(
            child: Transform.translate(
              offset: const Offset(0, 3), // Move subscript down
              child: Text(
                content,
                style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
              ),
            ),
          ));
          i = end + 6;
        } else {
          buffer.write('<sub>');
          i += 5;
        }
      } else if (text[i] == '^') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
          buffer.clear();
        }
        int end = text.indexOf(' ', i + 1);
        if (end == -1) end = text.length;
        String exponent = text.substring(i + 1, end);
        spans.add(WidgetSpan(
          child: Transform.translate(
            offset: const Offset(0, -5), // Move exponent up
            child: Text(
              exponent,
              style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
            ),
          ),
        ));
        i = end;
      } else {
        buffer.write(text[i]);
        i++;
      }
    }

    if (buffer.isNotEmpty) {
      spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
    }

    return spans;
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'mp4', 'txt', 'doc', 'docx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'mp3'],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<PlatformFile> validFiles = result.files
            .where((file) =>
            (file.bytes != null && file.bytes!.isNotEmpty) ||
                file.path != null)
            .toList();

        if (validFiles.isEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'The selected files could not be processed - no content or path available'),
              ),
            );
          }
          return;
        }

        if (mounted) {
          setState(() {
            _pickedFiles
                .addAll(validFiles.map((file) => FileWithPageRange(file: file)));
            _isUploading = true;
            _uploadProgress = 0.0;
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Files ready: ${_pickedFiles.map((fileRange) => fileRange.file.name).join(", ")}'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  void _deleteFile(FileWithPageRange fileToDeleteRange) {
    setState(() {
      _pickedFiles.remove(fileToDeleteRange);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${fileToDeleteRange.file.name} removed')),
    );
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes,
      {int? startPage, int? endPage}) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final pageCount = document.pages.count;
      final extractor = sf_pdf.PdfTextExtractor(document);

      StringBuffer resultText = StringBuffer();

      int start = startPage != null ? startPage - 1 : 0;
      int end = endPage != null ? endPage - 1 : pageCount - 1;

      start = start.clamp(0, pageCount - 1);
      end = end.clamp(0, pageCount - 1);

      for (int i = start; i <= end; i++) {
        try {
          final pageText =
              extractor.extractText(startPageIndex: i, endPageIndex: i);
          if (pageText.isNotEmpty) {
            resultText.writeln(pageText);
            resultText.writeln();
          }
        } catch (e) {
          print('Error extracting text from page $i: $e');
        }
      }

      document.dispose();
      return resultText.toString();
    } catch (e) {
      print('PDF extraction failed: $e');
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<String?> _extractTextFromAudio(PlatformFile audioFile) async {
    if (!_speechAvailable) {
      _initSpeech();
      if (!_speechAvailable) {
        throw Exception('Speech recognition not available');
      }
    }

    if (audioFile.bytes != null) {
      final tempDir = await getTemporaryDirectory();
      final tempPath = path.join(tempDir.path, audioFile.name);
      final file = File(tempPath);
      await file.writeAsBytes(audioFile.bytes!);

      bool available = await _speech.initialize();
      if (!available) {
        throw Exception('Speech service not initialized');
      }

      String textResult = '';
// In _extractTextFromAudio function:
      try {
        await _speech.listen(
          onResult: (result) {
            textResult = result.recognizedWords;
          },
          listenMode: stt.ListenMode.dictation,
          pauseFor: const Duration(seconds: 3),
          localeId: 'en_US',
          partialResults: false,
          cancelOnError: true,
          // Remove the onAudioBuffer parameter
        );
      } catch (e) {
        print('Speech recognition error: $e');
        throw Exception('Speech recognition error: $e');
      } finally {
        _speech.stop();
        file.delete();
      }
      return textResult;
    } else {
      throw Exception('Audio file bytes are null');
    }
  }


Future<void> _processInput() async {
  if (_textInputController.text.isEmpty && _pickedFiles.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Please select files or enter text')),
    );
    return;
  }

  // Handle chat process type immediately
  if (_processType == 'chat') {
    if (mounted) {
      setState(() {
        _isProcessing = false;
        _processingProgress = 1.0;
      });
    }
    _startChatSession();
    return;
  }

  // Check if input is a URL (article or YouTube video)
  final inputText = _textInputController.text.trim();
  if (inputText.isNotEmpty) {
    // Check if it's a URL
    if (UrlTextExtractor.isUrl(inputText)) {
      // Show immediate feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Detected URL. Extracting content...')),
      );

      setState(() {
        _isProcessing = true;
        _processingProgress = 0.1;
      });

      try {
        String extractedContent = '';

        // Check if it's a YouTube URL
        if (YoutubeTranscriptExtractor.isYoutubeUrl(inputText)) {
          // Extract YouTube transcript
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Extracting YouTube transcript... This may take a moment.')),
          );

          setState(() {
            _processingProgress = 0.3;
          });

          try {
            extractedContent = await YoutubeTranscriptExtractor.extractTranscript(inputText);

            // Check if we got actual transcript content or just metadata/error message
            if (extractedContent.contains('Transcript could not be extracted') ||
                extractedContent.contains('[Note: Full transcript could not be extracted')) {
              // Show a warning but continue with what we have
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Could not extract full transcript. Using available video information instead.'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 5),
                ),
              );
            } else {
              // Success case
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('YouTube transcript extracted successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            }

            extractedContent = "YouTube Video Content:\n\n$extractedContent";
          } catch (e) {
            // Handle transcript extraction errors
            print('Error extracting YouTube transcript: $e');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error extracting transcript: ${e.toString().split('\n').first}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );

            // Still provide some information about the video
            extractedContent = "YouTube Video: $inputText\n\nCould not extract transcript: ${e.toString().split('\n').first}\n\nPlease try a different video or manually summarize the content.";
          }
        } else {
          // Extract article content
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Extracting article content...')),
          );

          setState(() {
            _processingProgress = 0.3;
          });

          extractedContent = await UrlTextExtractor.extractTextFromUrl(inputText);
          extractedContent = "Article Content:\n\n$extractedContent";
        }

        // Update the text input with the extracted content
        setState(() {
          _textInputController.text = extractedContent;
          _processingProgress = 0.6;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Content extracted successfully!')),
        );
      } catch (e) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error extracting content: $e')),
        );
        setState(() {
          _isProcessing = false;
        });
        return;
      }
    }
  }

  // Immediate UI update for instant feedback
  setState(() {
    _isProcessing = true;
    _processingProgress = 0.0;
    _geminiOutput = null;
    _flashcards = [];
    _quizQuestions = [];
    _examQuestions = [];
    _lessonSteps = [];
    _isTextAnimationActive = false;
    _cancelQuizTimer();
    _timeRemaining = Duration.zero;
  });

  // Show immediate visual feedback
  final progressController = StreamController<double>();
  progressController.stream.listen((progress) {
    if (mounted) setState(() => _processingProgress = progress);
  });

  // Start progress animation immediately
  for (int i = 0; i <= 10; i++) {
    await Future.delayed(const Duration(milliseconds: 50));
    progressController.add(i / 100);
  }

  try {

    await Future.microtask(() async {
      String content = '';
      if (_textInputController.text.isNotEmpty) {
        content = _textInputController.text;
        _fileContent = content;
        for (int i = 0; i <= 10; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          progressController.add(i / 10 * 0.6);
        }
      } else if (_pickedFiles.isNotEmpty) {
        content = await _extractContentFromFiles();
        _fileContent = content;
        for (int i = 0; i <= 10; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          progressController.add(i / 10 * 0.6);
        }
      }

      for (int i = 0; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 30));
        progressController.add(0.6 + (i / 10 * 0.3));
      }

      final prompt = _buildPrompt(content);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            _handleResponse(response.text!);
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    });
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Processing failed: $e')),
      );
    }
  } finally {
    await progressController.close();
    if (mounted) {
      setState(() {
        _isProcessing = false;
        if (_processingProgress < 1.0) _processingProgress = 1.0;
      });
    }
  }
}



Future<String> _extractContentFromFiles() async {
  String combinedFileContent = '';
  final totalFiles = _pickedFiles.length;

  for (int fileIndex = 0; fileIndex < totalFiles; fileIndex++) {
    final fileRange = _pickedFiles[fileIndex];
    final pickedFile = fileRange.file;
    String fileContent = '';
    final fileName = pickedFile.name.toLowerCase();
    Uint8List? fileBytes = pickedFile.bytes;
    // Only access path if not on web platform
    String? filePath = kIsWeb ? null : pickedFile.path;
    bool contentExtracted = false;

    print("Processing file: ${pickedFile.name}, Has Bytes: ${fileBytes != null}, Has Path: ${filePath != null}, Is Web: $kIsWeb");

    // --- Ensure we have bytes if possible (Mobile/Desktop) - Only if bytes are null initially ---
    if (fileBytes == null && filePath != null && !kIsWeb) {
      try {
        print("Reading bytes from path (non-web): $filePath");
        fileBytes = await io.File(filePath).readAsBytes();
        print("Bytes read from path successfully: ${fileBytes?.length ?? 0} bytes");
      } catch (e) {
        print("Error reading file from path $filePath (non-web): $e");
        // Don't set error content yet, let subsequent logic handle it
      }
    }

    // --- PDF Handling ---
    if (fileName.endsWith('.pdf')) {
      String extractedText = '';
      bool attemptOcr = false;
      Uint8List? pdfDataToUse = fileBytes; // Prefer bytes first

      // If bytes are still null and we are NOT on web, try the path again (redundant but safe)
      if (pdfDataToUse == null && filePath != null && !kIsWeb) {
           try {
               pdfDataToUse = await io.File(filePath).readAsBytes();
               print("Re-read bytes from path for PDF (non-web): ${pdfDataToUse?.length ?? 0} bytes");
           } catch (e) {
                print("Error re-reading PDF from path $filePath (non-web): $e");
                pdfDataToUse = null;
           }
      }

      // Check if we have data to process the PDF
      if (pdfDataToUse == null) {
          print("Error: No data (bytes or readable non-web path) available for PDF: ${pickedFile.name}");
          fileContent = "[Error: PDF data unavailable or not readable]";
      } else {
          // Attempt direct text extraction first using the available data
          try {
              print("Attempting direct text extraction for PDF: ${pickedFile.name} using ${pdfDataToUse.length} bytes.");
              extractedText = await _extractTextFromPdf(
                      pdfDataToUse,
                      startPage: fileRange.startPage,
                      endPage: fileRange.endPage) ?? '';

              if (extractedText.trim().length < 100) {
                 print("PDF ${pickedFile.name}: Minimal text extracted directly (${extractedText.trim().length} chars), will attempt OCR.");
                 attemptOcr = true;
              } else {
                 print("PDF ${pickedFile.name}: Direct text extraction successful.");
                 fileContent = extractedText;
                 contentExtracted = true;
              }
          } catch (e) {
              print("Direct PDF text extraction failed for ${pickedFile.name}, attempting OCR. Error: $e");
              attemptOcr = true;
              extractedText = ''; // Reset text if direct failed
          }

          // Attempt OCR if needed, using the same data source
          if (attemptOcr) {
              try {
                 print("Processing PDF ${pickedFile.name} with OCR using ${pdfDataToUse.length} bytes...");
                 // Pass BYTES to OCR function. Path is only relevant inside if NOT on web and bytes failed initially.
                 extractedText = await _extractTextFromImagePdf(
                    pdfDataToUse, // ALWAYS pass the bytes we have confirmed are available
                    kIsWeb ? null : filePath, // Only pass path if not on web
                    startPage: fileRange.startPage,
                    endPage: fileRange.endPage);
                 print("OCR processing finished for ${pickedFile.name}. Text length: ${extractedText.length}");
                 fileContent = extractedText;
                 // Consider OCR successful even if text is short
                 contentExtracted = true;
              } catch (e) {
                 print("OCR PDF extraction failed for ${pickedFile.name}: $e");
                 if (fileContent.isEmpty) { // Only set error if no previous content
                   fileContent = '[Error during OCR PDF processing: $e]';
                 }
              }
          }
      }
    }
    // --- PowerPoint Handling ---
    else if (fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
        if (fileBytes != null) {
          try {
            print("Processing PowerPoint ${pickedFile.name} with PptxExtractor...");
            if (fileName.endsWith('.pptx')) {
              fileContent = await PptxExtractor.extractText(fileBytes);
            } else { // .ppt
              fileContent = PptxExtractor.extractTextFromPpt(fileBytes);
            }
            contentExtracted = true;
            print("PowerPoint extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          } catch (e) {
            print("PowerPoint extraction failed for ${pickedFile.name}: $e");
            fileContent = '[Error extracting PowerPoint content: $e]';
          }
        } else {
          print("Cannot process PowerPoint ${pickedFile.name} without bytes.");
          fileContent = '[Cannot process PowerPoint without file data]';
        }
    }
    // --- Image Handling (JPG, PNG, etc.) ---
    else if (fileName.endsWith('.jpg') ||
             fileName.endsWith('.jpeg') ||
             fileName.endsWith('.png') ||
             fileName.endsWith('.bmp') ||
             fileName.endsWith('.gif'))
    {
       print("Processing Image ${pickedFile.name} with OCR...");
       // Use the enhanced text recognition helper for better OCR and handwriting detection
       try {
           // Create an instance of the TextRecognitionHelper
           final textRecognitionHelper = TextRecognitionHelper();

           // Process the image for text recognition
           fileContent = await textRecognitionHelper.processImageForText(fileBytes, filePath);
           contentExtracted = true;
           print("Enhanced OCR successful for ${pickedFile.name}. Text length: ${fileContent.length}");
       } catch (e) {
           print("Enhanced OCR failed for ${pickedFile.name}: $e");
           fileContent = '[Error during enhanced OCR processing: $e]';
       } // Our TextRecognitionHelper handles temp file cleanup internally
    }
    // --- TXT Handling ---
    else if (fileName.endsWith('.txt')) {
      try {
          if (fileBytes != null) {
            fileContent = utf8.decode(fileBytes, allowMalformed: true);
            contentExtracted = true;
          } else if (filePath != null && !kIsWeb) {
            fileContent = await io.File(filePath).readAsString();
            contentExtracted = true;
          } else {
             print("TXT file content not available for ${pickedFile.name}");
             fileContent = '[TXT file content not available]';
          }
      } catch (e) {
          print("Error reading TXT file ${pickedFile.name}: $e");
          fileContent = '[Error reading text file: $e]';
      }
    }
    // --- DOC/DOCX Handling ---
    else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
       if (fileBytes != null) {
         try {
           print("Processing Word document ${pickedFile.name} with DocxExtractor...");
           if (fileName.endsWith('.docx')) {
             fileContent = await DocxExtractor.extractText(fileBytes);
           } else { // .doc
             fileContent = DocxExtractor.extractTextFromDoc(fileBytes);
           }
           contentExtracted = true;
           print("Word document extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
         } catch (e) {
           print("Word document extraction failed for ${pickedFile.name}: $e");
           // Fallback to basic extraction if the extractor fails
           try {
             // Try UTF-8 first
             fileContent = utf8.decode(fileBytes, allowMalformed: true);
             // Basic check if decoding likely failed (lots of replacement chars)
             if (fileContent.contains('\uFFFD') && fileContent.length < 100 && fileContent.replaceAll('\uFFFD', '').trim().isEmpty) {
                throw Exception('Likely invalid UTF-8, trying latin1');
             }
             contentExtracted = true;
             print("Fallback: Decoded DOC/DOCX ${pickedFile.name} as UTF-8");
           } catch (_) {
             try {
               // Fallback to Latin-1
               fileContent = latin1.decode(fileBytes);
               contentExtracted = true;
               print("Fallback: Decoded DOC/DOCX ${pickedFile.name} as Latin-1");
             } catch (e2) {
               print("Error decoding DOC/DOCX file ${pickedFile.name} with all methods: $e2");
               fileContent = '[Could not extract content from Word document]';
             }
           }
         }
       } else {
           print("Cannot process DOC/DOCX ${pickedFile.name} without bytes.");
           fileContent = '[Cannot process Word document without file data]';
       }
    }
    // --- MP3 Handling ---
    else if (fileName.endsWith('.mp3')) {
      // Check for bytes first, as path might not be usable on web for audio either
      if (fileBytes != null) {
          try {
              print("Processing MP3 ${pickedFile.name} with AudioExtractor...");
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) { // Check if it's not an error message
                 contentExtracted = true;
                 print("MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 print("MP3 extraction returned an error message: $fileContent");
                 // Fallback to the old method if the new extractor fails
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     bytes: fileBytes);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) { // Check if it's not an error message
                    contentExtracted = true;
                    print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
          } catch (e) {
              print("Audio extraction failed for ${pickedFile.name} (using bytes): $e");
              // Fallback to the old method if the new extractor fails
              try {
                final tempAudioFile = PlatformFile(
                    name: pickedFile.name,
                    size: fileBytes.length,
                    bytes: fileBytes);
                fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                if (!fileContent.startsWith('[')) { // Check if it's not an error message
                   contentExtracted = true;
                   print("Fallback MP3 extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                } else {
                   fileContent = '[Error extracting audio content: $e]';
                }
              } catch (e2) {
                print("Both audio extraction methods failed for ${pickedFile.name}: $e2");
                fileContent = '[Error extracting audio content: $e2]';
              }
          }
      } else if (filePath != null && !kIsWeb) {
         // Fallback for non-web if bytes weren't available
         print("Attempting MP3 extraction from path (non-web): $filePath");
         try {
              // Read bytes from file path
              final fileBytes = await io.File(filePath).readAsBytes();
              fileContent = await AudioExtractor.extractText(fileBytes, pickedFile.name);
              if (!fileContent.startsWith('[')) {
                 contentExtracted = true;
                 print("MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
              } else {
                 // Fallback to old method
                 final tempAudioFile = PlatformFile(
                     name: pickedFile.name,
                     size: fileBytes.length,
                     path: filePath);
                 fileContent = await _extractTextFromAudio(tempAudioFile) ?? '[Audio extraction returned null]';
                 if (!fileContent.startsWith('[')) {
                    contentExtracted = true;
                    print("Fallback MP3 extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
                 }
              }
         } catch (e) {
             print("Audio extraction failed for ${pickedFile.name} (using path): $e");
             fileContent = '[Error extracting audio content from path: $e]';
         }
      } else {
          print("MP3 file ${pickedFile.name} has no bytes or non-web path.");
          fileContent = '[MP3 data unavailable]';
      }
    }
     // --- MP4 Video Handling ---
    else if (fileName.endsWith('.mp4') || fileName.endsWith('.mpeg') || fileName.endsWith('.mov') || fileName.endsWith('.avi')) {
      if (fileBytes != null) {
        try {
          print("Processing video ${pickedFile.name} with VideoExtractor...");
          fileContent = await VideoExtractor.extractTextFromVideo(fileBytes, pickedFile.name);
          if (!fileContent.startsWith('[')) { // Check if it's not an error message
            contentExtracted = true;
            print("Video extraction successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          }
        } catch (e) {
          print("Video extraction failed for ${pickedFile.name}: $e");
          fileContent = '[Error extracting video content: $e]';
        }
      } else if (filePath != null && !kIsWeb) {
        // Fallback for non-web if bytes weren't available
        print("Attempting video extraction from path (non-web): $filePath");
        try {
          // Read bytes from file path
          final videoBytes = await io.File(filePath).readAsBytes();
          fileContent = await VideoExtractor.extractTextFromVideo(videoBytes, pickedFile.name);
          if (!fileContent.startsWith('[')) {
            contentExtracted = true;
            print("Video extraction from path successful for ${pickedFile.name}. Text length: ${fileContent.length}");
          }
        } catch (e) {
          print("Video extraction failed for ${pickedFile.name} (using path): $e");
          fileContent = '[Error extracting video content from path: $e]';
        }
      } else {
        print("Video file ${pickedFile.name} has no bytes or non-web path.");
        fileContent = '[Video data unavailable]';
      }
    }
    // --- Unsupported File Type ---
    else {
       if (fileContent.isEmpty) { // Only set if no prior error message exists
         print("Unsupported file type: ${pickedFile.name}");
         fileContent = '[Unsupported file type: ${fileName.split('.').last}]';
       }
    }

    // --- Append to Combined Content ---
    // Include file name header regardless of success to indicate which file was processed
    combinedFileContent += "## ${pickedFile.name}\n\n";
    if (contentExtracted && fileContent.trim().isNotEmpty) {
      combinedFileContent += "$fileContent\n\n";
    } else if (fileContent.isNotEmpty) { // Include error messages/placeholders
      combinedFileContent += "$fileContent\n\n";
    } else { // Fallback if fileContent remained empty for some reason
      combinedFileContent += "[No content extracted or extraction failed without specific error]\n\n";
    }

    print("Finished processing file: ${pickedFile.name}. Content Extracted: $contentExtracted. Appended Length: ${fileContent.length}");

  } // End of file loop

  print("Finished extracting content from all files.");
  return combinedFileContent;
}




// Helper function to convert raw pixels (RGBA) to PNG bytes
Future<Uint8List?> _encodePixelsToPng(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888, // Assuming pdf_render provides RGBA
    (ui.Image img) {
      completer.complete(img);
    },
    // Add onError callback if desired
  );

  try {
    final ui.Image decodedImage = await completer.future;
    final ByteData? byteData = await decodedImage.toByteData(format: ui.ImageByteFormat.png);
    decodedImage.dispose(); // Dispose the ui.Image
    return byteData?.buffer.asUint8List();
  } catch (e) {
    print("Error encoding pixels to PNG: $e");
    return null;
  }
}


Future<String> _extractTextFromImagePdf(
    Uint8List? pdfBytes,
    String? pdfPath,
    {int? startPage, int? endPage}) async {

  StringBuffer combinedText = StringBuffer();
  pdf_render.PdfDocument? doc;
  final currentTextRecognizer = TextRecognizer();

  io.Directory? tempDir; // Mobile only
  String? tempDirPath; // Mobile only

  if (!kIsWeb) {
      try {
          tempDir = await getTemporaryDirectory();
          tempDirPath = tempDir.path;
          print("Using temp directory for PDF OCR (non-web): $tempDirPath");
      } catch (e) {
          print("Error getting temp directory for PDF OCR (non-web): $e");
          await currentTextRecognizer.close();
          return "[Error: Cannot access temp storage for PDF OCR.]";
      }
  } else {
      print("Running PDF OCR on Web - no temp directory needed.");
  }

  try {
    // --- Open the PDF document ---
    if (pdfBytes != null) {
      print("Opening PDF from bytes for OCR.");
      doc = await pdf_render.PdfDocument.openData(pdfBytes);
    } else if (pdfPath != null) {
      if (!kIsWeb) {
         print("Opening PDF from path for OCR (non-web): $pdfPath");
         doc = await pdf_render.PdfDocument.openFile(pdfPath);
      } else {
          print("Error: Cannot open PDF from path on Web for OCR. Bytes required.");
           await currentTextRecognizer.close();
          return "[Error: Cannot open PDF from path on Web.]";
      }
    } else {
      throw Exception("No PDF bytes or path provided for OCR.");
    }

    if (doc == null) {
      throw Exception("Failed to open PDF document for OCR.");
    }

    // --- Determine Page Range ---
    final pageCount = doc.pageCount;
    int start = (startPage != null && startPage > 0) ? startPage - 1 : 0;
    int end = (endPage != null && endPage <= pageCount) ? endPage - 1 : pageCount - 1;
    start = start.clamp(0, pageCount - 1);
    end = end.clamp(start, pageCount - 1);

    print("Processing PDF pages ${start + 1} to ${end + 1} with OCR.");

    // --- Process each page ---
    for (int i = start; i <= end; i++) {
      pdf_render.PdfPage? page;
      io.File? tempImageFile; // Mobile only

      try {
        print("Processing page ${i + 1}...");
        page = await doc.getPage(i + 1);

        // Render page to raw pixels
final pdf_render.PdfPageImage? pageImage = await page.render(
  width: (page.width * 2).toInt(), // Higher resolution
  height: (page.height * 2).toInt(),
  // Format parameter removed if not required by your version
);

        // **** CORRECTED CHECK: Use .pixels ****
        if (pageImage == null || pageImage.pixels == null) {
          print('Warning: Skipping page ${i + 1} due to rendering failure or null pixels.');
          continue;
        }

        // **** CORRECTED ACCESS: Use .pixels ****
        final Uint8List rawPixels = pageImage.pixels;
        final int imageWidth = pageImage.width;
        final int imageHeight = pageImage.height;

        InputImage? inputImage;

        // --- Create InputImage (Platform Specific) ---
if (kIsWeb) {
  // Define the conversion function inline
  Uint8List convertRgbaToBgra(Uint8List rgba) {
    Uint8List bgra = Uint8List(rgba.length);
    for (int i = 0; i < rgba.length; i += 4) {
      bgra[i] = rgba[i + 2];  // R -> B
      bgra[i + 1] = rgba[i + 1]; // G stays
      bgra[i + 2] = rgba[i];  // B -> R
      bgra[i + 3] = rgba[i + 3]; // Alpha stays
    }
    return bgra;
  }

  // Convert RGBA to BGRA for web compatibility
  final convertedPixels = convertRgbaToBgra(rawPixels);
  inputImage = InputImage.fromBytes(
    bytes: convertedPixels,
    metadata: InputImageMetadata(
      size: Size(imageWidth.toDouble(), imageHeight.toDouble()),
      rotation: InputImageRotation.rotation0deg,
      format: InputImageFormat.bgra8888,
      bytesPerRow: imageWidth * 4
    )
  );
} else {
            // Mobile: Encode pixels to PNG, save PNG to temp file, use fromFilePath
            if (tempDirPath == null) {
                 print("Error: Temp directory path is null on mobile for PDF page ${i + 1}. Skipping OCR.");
                 combinedText.writeln("--- Page ${i + 1} (Error: No Temp Dir) ---");
                 inputImage = null;
            } else {
                print("Encoding page ${i+1} pixels to PNG (Mobile)...");
                final Uint8List? pngBytes = await _encodePixelsToPng(rawPixels, imageWidth, imageHeight);

                if (pngBytes != null) {
                    final tempFileName = 'pdf_page_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.png';
                    final tempFilePath = path.join(tempDirPath, tempFileName);
                    tempImageFile = io.File(tempFilePath);
                    try {
                        await tempImageFile.writeAsBytes(pngBytes);
                        print("Creating InputImage from path for PDF page ${i + 1} (Mobile): $tempFilePath");
                        inputImage = InputImage.fromFilePath(tempFilePath);
                    } catch (e) {
                         print("Error writing/reading temp PNG file on mobile (Page ${i+1}): $e");
                         combinedText.writeln("--- Page ${i + 1} (Temp PNG File Error: $e) ---");
                         inputImage = null;
                         if (await tempImageFile.exists()) {
                             try { await tempImageFile.delete(); } catch (_) {}
                         }
                    }
                } else {
                     print("Error encoding page ${i + 1} pixels to PNG. Skipping OCR.");
                     combinedText.writeln("--- Page ${i + 1} (Error: PNG Encoding Failed) ---");
                     inputImage = null;
                }
            }
        }

// Add conversion helper
Uint8List _convertRgbaToBgra(Uint8List rgba) {
  Uint8List bgra = Uint8List(rgba.length);
  for (int i = 0; i < rgba.length; i += 4) {
    bgra[i] = rgba[i + 2];  // R -> B
    bgra[i + 1] = rgba[i + 1]; // G stays
    bgra[i + 2] = rgba[i];  // B -> R
    bgra[i + 3] = rgba[i + 3]; // Alpha stays
  }
  return bgra;
}


        // --- Perform OCR ---
        if (inputImage != null) {
            try {
                print("Performing OCR on page ${i + 1}...");
                final RecognizedText recognizedText = await currentTextRecognizer.processImage(inputImage);
                combinedText.writeln("--- Page ${i + 1} ---");
                combinedText.writeln(recognizedText.text);
                combinedText.writeln();
                print("OCR successful for page ${i + 1}. Text length: ${recognizedText.text.length}");
            } catch (ocrError) {
                 print("Error during OCR processing for page ${i + 1}: $ocrError");
                 combinedText.writeln("--- Page ${i + 1} (OCR Execution Error: $ocrError) ---");
            }
        } else {
             print("Skipping OCR for page ${i + 1} as InputImage creation failed.");
             if (!combinedText.toString().contains("Page ${i + 1} (Error")) {
                 combinedText.writeln("--- Page ${i + 1} (Skipped OCR due to preparation error) ---");
             }
        }

      } catch (pageProcessingError) {
        print('Error processing page ${i + 1}: $pageProcessingError');
        combinedText.writeln("--- Page ${i + 1} (Error: $pageProcessingError) ---");
      } finally {
         // --- Clean up resources for the current page ---
         // *** REMOVED page.close() as it doesn't exist ***

         // Delete mobile temp file if it was created
         if (tempImageFile != null && await tempImageFile.exists()) {
            try {
               await tempImageFile.delete();
               print("Deleted temp file for page ${i + 1}.");
            } catch (deleteError) {
               print("Error deleting temp pdf page file for page ${i + 1}: $deleteError");
            }
         }
      }
    } // End of page loop

  } catch (e) {
    print('Fatal error during PDF OCR process: $e');
    combinedText.writeln("\n[Extraction failed due to error: $e]");
  } finally {
    // --- Final Cleanup ---
    if (doc != null) {
        try {
           doc.dispose(); // Dispose the main PDF document
           print("PDF document disposed.");
        } catch (disposeError) {
           print("Error disposing PDF document: $disposeError");
        }
    }
    try {
       await currentTextRecognizer.close(); // Close the TextRecognizer
       print("TextRecognizer closed.");
    } catch (closeError) {
       print("Error closing TextRecognizer: $closeError");
    }
  }

  return combinedText.toString();
}

// Helper function
Future<ui.Image> _createImageFromPixels(Uint8List pixels, int width, int height) async {
  final completer = Completer<ui.Image>();
  ui.decodeImageFromPixels(
    pixels,
    width,
    height,
    ui.PixelFormat.rgba8888,
    (ui.Image img) {
      completer.complete(img);
    },
  );
  return completer.future;
}





// 6. Optional Helper to create InputImage from bytes via temp file
Future<InputImage?> _createInputImageFromBytes(Uint8List imageBytes, String tempDirPath, String tempFileName) async {
  File? tempImageFile;
  try {
      final tempFilePath = path.join(tempDirPath, tempFileName);
      tempImageFile = File(tempFilePath);
      await tempImageFile.writeAsBytes(imageBytes);
      return InputImage.fromFilePath(tempFilePath);
      // Note: The caller is responsible for deleting the temp file after ML Kit processing
  } catch (e) {
      print("Error creating temp file for InputImage: $e");
      // Clean up if file creation failed partially
       try {
            if (tempImageFile != null && await tempImageFile.exists()) {
               await tempImageFile.delete();
            }
       } catch (deleteError) {
           print("Error deleting temp image file during error handling: $deleteError");
       }
      return null;
  }
}




  String _buildPrompt(String content) {
 String gradeLevelText = '';
    if (_readingGradeLevel != null && _readingGradeLevel!.isNotEmpty) {
      // Ensure "a Grade 5" vs "an Grade 8" is handled correctly if needed, though "a X reading level" is general enough.
      gradeLevelText = ' Tailor the content to a ${_readingGradeLevel!.toLowerCase()} reading level.';
    }
    String difficultyLevelText = '';
    if (_difficultyLevel != null && _difficultyLevel!.isNotEmpty) {
      difficultyLevelText = ' Difficulty level: ${_difficultyLevel!.toLowerCase()}.';
    }
    String languageText = '';
    // This language instruction needs to be added to all prompts below where applicable.
    if (_outputLanguage != null && _outputLanguage != 'English') {
      languageText = ' Write the output in ${_outputLanguage!} language. All responses must be in ${_outputLanguage!} language only.';
    }

    final prompts = {
       'notes': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES atleast 50% the length of the original document(s) that teach the subject matter directly:$gradeLevelText$languageText
        - Clear headings
        - no bullet points infront of number like 1. or (1)
        - Bullet points
        - Key terms in **bold**
        - Examples in *italic*
        - no lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
        - no lines like *Example:* but Example:
        - Use dash (-) for bullet points in markdown
        - do not mention the source name
        - in pdf export, the header text of every section of the document should be bold, make topic titles bold, headings bold"
        - in pdf export properly italicize and make text bold in the right places, dont use * or surround text with ** or lead any text with #. clean output
        - include tables whenever necessary
        - For tables, use markdown pipe syntax with headers
        - draw images in appropriate places in each output
        - Use # prefixes for all section headers
        - Never use unicode symbols or special characters
        - First create a study guide based on the material(s) then Cover EVERY concept in the given file or files exhaustively without omission in atleast 5 pages with unique content on each page and without repeating anything thats been covered before. leave no stone unturned and the notes should be the condensed but comprehensive and exhaustive version of the material, no omissions
        - Include ALL foundational elements needed for complete understanding
        - Never use "the document states" or similar meta-references - present as primary knowledge
        - **Crucially, for each significant section or concept explained, generate a relevant illustrative image using your native image generation capabilities.** Ensure the generated images visually represent the topic discussed in that section. Place images logically within the notes flow.
            *   **Format ALL mathematical equations, formulas, variables, and symbols using LaTeX syntax.**
        - Where appropriate, include geometric shapes using the geometry syntax. For example:
          ```geometry:circle(radius=50, color=#3498db)```
          ```geometry:rectangle(width=200, height=100, cornerRadius=10)```
          ```geometry:triangle(width=100, height=100, color=#FF5733)```
          ```geometry:line(start=Offset(0,0), end=Offset(100,100))```
          ```geometry:polygon(points=[0:0|50:0|25:50])```
        Content: $content''', // Already had languageText
      'chat': '''Use this document as your ONLY knowledge source:
        $content
        Respond to ALL subsequent questions using ONLY this information but you can infer from the document to give a response to a question.$languageText''', // Already had languageText
       'interactive_lesson': '''Generate an interactive lesson from the content provided, suitable for a smartboard presentation for ${gradeLevelText.isNotEmpty ? gradeLevelText : 'a general audience'}.$languageText
        Create a step-by-step lesson that flows like an experienced teacher teaching someone with no prior knowledge of the subject. The lesson should have a natural narrative flow or story-like progression that builds concepts incrementally.
        Structure the lesson in sequential steps, where each step builds on the previous one. Each step should be a concise point or explanation that feels like a teacher speaking directly to a student.
        Instead of static images, include interactive visualization code snippets using D3.js or Flutter syntax where appropriate. Use these placeholders:
        - [d3_visualization: CONCEPT] for D3.js visualizations
        - [flutter_visualization: CONCEPT] for Flutter-based visualizations
        - [simulation: CONCEPT] for interactive simulations
        Ensure the lesson is easy to parse into individual steps. Each step should be separated by two newlines. Focus on clarity and conciseness for each step, suitable for display on a digital whiteboard.
        Don't include the words "Step" and number when writing content. For example, write "Introduction to Malaria" instead of "Step 1. Introduction to Malaria".
        Example output format (step by step, each separated by two newlines):
        Introduction to Supply and Demand
        What is Demand? Demand is how much of a product consumers are willing to buy at different prices. Think of it like your desire to buy ice cream - when it's cheaper, you might buy more!
        [d3_visualization: demand_curve]
        What is Supply? Supply is how much of a product producers are willing to sell at different prices. Imagine you're selling lemonade - if people are willing to pay more, you'd make more lemonade to sell!
        [flutter_visualization: supply_curve]
        Market Equilibrium occurs when supply and demand meet. This is the "just right" price where buyers are happy to buy and sellers are happy to sell.
        [simulation: market_equilibrium]
        Content: $content''', // Already had languageText
      'cheatsheet': '''Generate a concise yet comprehensive cheatsheet for the content.$gradeLevelText$languageText
        Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.
        Use dash (-) for bullet points in markdown
        Where appropriate, include geometric shapes using the geometry syntax. For example:
          ```geometry:circle(radius=50, color=#3498db)```
          ```geometry:rectangle(width=200, height=100, cornerRadius=10)```
          ```geometry:triangle(width=100, height=100, color=#FF5733)```
          ```geometry:line(start=Offset(0,0), end=Offset(100,100))```
          ```geometry:polygon(points=[0:0|50:0|25:50])```
        Content: $content''', // Added languageText
      'flashcards': '''Generate at least 50 comprehensive and no repeats when we change reading grade level flashcards (Q: question / A: answer) or generate a comprehensive set of flashcards if the content is less but can be comprehensively covered:$gradeLevelText$languageText
        Q: [Question]
        A: [Answer]
        - Each card must contain:
          Front:
          ||Core Concept||: Concise question
          ||Type||: [Definition/Application/Analysis/Connection]
          Full Explanation: (1 sentence)
          Requirements:
          1. Cover EVERY concept from source material
          2. 15-50 cards per major topic
          3. Progressive difficulty within topics
          4. Cross-link cards through connection points
          5. before the full explanation in the back dont put any text like :0 A:
        Content: $content''', // Added languageText
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 questions comprehensive and no repeats when we change difficulty level .$difficultyLevelText$languageText Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz. the questions should be different on every difficulty level so there is no repetition of questions
        Example Format:
        1. What is the capital of France?
        A) London
        B) Paris
        C) Berlin
        D) Rome
        Answer: B
        2. What is the chemical symbol for water?
        A) H2O
        B) CO2
        C) NaCl
        D) O2
        Answer: A
        Now generate a quiz based on the following content, using the EXACT format above:
        Content: $content''', // Added languageText
      'transcript': '''Create comprehensive and no repeats when we change reading grade level transcript:$gradeLevelText$languageText
        - Speaker labels
        - Timestamps
        - Paragraph breaks
        Content: $content''', // Added languageText
       'summary': '''Generate a summary/brief of the following content.$languageText
        Instruction: Dynamically adapt the summary based on the content type.
        If the content is a research paper or academic paper, provide a detailed summary with these sections:$gradeLevelText
        - Background
        - Research Question
        - Study Method
        - Study Limitations
        - Global Alignment (if applicable)
        - Findings
        - include quantitative information where necessary
        - Policy Recommendations
        - Stakeholder Implications (for donors, government, public, decision-makers, private sector, students, academics)
        Otherwise, if the content is not a research paper, provide a concise general summary of the main points.$gradeLevelText
        Content: $content''', // Added languageText to the main instruction and the fallback
      'exam': '''Generate a comprehensive practice exam with at least 50 NEW questions (different from a quiz, more detailed, paper-based exam style) covering all aspects of the content.$difficultyLevelText$languageText
        Use this EXACT format for each question and answer:
        [Question Number]. [Question Text]
        A) [Option 1]
        B) [Option 2]
        C) [Option 3]
        D) [Option 4]
        Answer: [Full Correct Answer Text - not just the letter, explain the answer in detail]
        Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.
        Content: $content''', // Added languageText
      'minutes': '''Generate comprehensive meeting minutes based on the content provided.$gradeLevelText$languageText
        Include:
        - Meeting Title
        - Date and Time
        - Attendees
        - Agenda Items
        - Discussions
        - Action Items
        - Decisions
        Content: $content''', // Added languageText
       'lesson_plan': '''Generate a detailed lesson plan based on the content.$languageText Include:
        - Learning objectives$gradeLevelText
        - Required materials
        - Step-by-step teaching instructions
        - Classroom activities
        - Assessment methods
        Where appropriate, include geometric shapes using the geometry syntax to create visual aids. For example:
          ```geometry:circle(radius=50, color=#3498db)```
          ```geometry:rectangle(width=200, height=100, cornerRadius=10)```
          ```geometry:triangle(width=100, height=100, color=#FF5733)```
          ```geometry:line(start=Offset(0,0), end=Offset(100,100))```
          ```geometry:polygon(points=[0:0|50:0|25:50])```
        Content: $content''', // Added languageText
       'worksheet': '''Generate a worksheet with practice questions and exercises based on the content.$gradeLevelText$languageText
        Content: $content''', // Added languageText
       'homework_guide': '''Generate a homework guide that includes practice problems and solutions based on the content.$gradeLevelText$languageText
        Content: $content''', // Added languageText
       'project_ideas': '''Generate project ideas based on the content, suitable for students.$gradeLevelText$languageText
        Content: $content''', // Added languageText
      'exam_free': '''Generate a comprehensive free-response exam with essay questions based on the content.$difficultyLevelText$languageText
        Use this EXACT format for each question and answer:
        [Question Number]. [Question Text]
        Sample Response: [Detailed sample response that would receive full credit]
        Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.
        Content: $content''', // Added languageText
      'exam_case': '''Generate a comprehensive case-based exam with scenario questions based on the content.$difficultyLevelText$languageText
        Use this EXACT format for each question and answer:
        [Question Number]. [Case scenario and question]
        Solution: [Detailed solution or answer]
        Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.
        Content: $content''', // Added languageText
       'grammar': '''Check the grammar and suggest improvements for the following text. Provide corrections and explanations.$gradeLevelText$languageText
        Content: $content''', // Added languageText
       'paper_grader': '''Provide detailed writing feedback on this paper.$languageText Include:
        - Grammar corrections
        - Structural improvements
        - Argument strength analysis
              - Style suggestions
        - Citation feedback
        Content: $content''', // Added languageText
       'case_studies': '''Generate relevant case studies or real-life applications based on the content.$gradeLevelText$languageText
        Content: $content''', // Added languageText
       'experiment': '''Generate an experiment or lab activity based on the content.$gradeLevelText$languageText
        Content: $content''', // Added languageText
       'mindmap': '''Create a comprehensive visual mindmap based on the content.$gradeLevelText$languageText
        Structure the mindmap with a central concept and branching subtopics. Include:
        - Main central topic
        - Primary branches (key concepts)
        - Secondary branches (supporting details)
        - Tertiary branches (examples, applications)

        Format the mindmap as a JSON structure that can be visualized. Use this exact format:
        ```json
        {
          "central_topic": "Main Topic Name",
          "branches": [
            {
              "name": "Primary Branch 1",
              "color": "#4287f5",
              "sub_branches": [
                {
                  "name": "Secondary Branch 1.1",
                  "sub_branches": [
                    {"name": "Tertiary Branch 1.1.1"},
                    {"name": "Tertiary Branch 1.1.2"}
                  ]
                },
                {"name": "Secondary Branch 1.2"}
              ]
            },
            {
              "name": "Primary Branch 2",
              "color": "#f54242",
              "sub_branches": []
            }
          ]
        }
        ```

        Use different colors for different primary branches to enhance visual learning. Make sure the JSON is properly formatted and valid.
        Content: $content''',
       'scheme_of_work': '''Generate a detailed scheme of work/curriculum plan based on the content.$gradeLevelText$languageText
        Include:
        - Course/subject title
        - Duration (term/semester/year)
        - Weekly breakdown with:
          * Week number
          * Topic/theme
          * Learning objectives
          * Key activities
          * Resources needed
          * Assessment methods
        - Cross-curricular links where appropriate
        - Differentiation strategies

        Format as a well-structured document with clear headings and sections.
        Where appropriate, include geometric shapes using the geometry syntax to create visual aids. For example:
          ```geometry:circle(radius=50, color=#3498db)```
          ```geometry:rectangle(width=200, height=100, cornerRadius=10)```
          ```geometry:line(start=Offset(0,0), end=Offset(100,100))```

        Content: $content''',
       'podcast': '''Create a complete podcast episode script based on the content.$gradeLevelText$languageText
        Include:
        - Podcast title (catchy and descriptive)
        - Brief description/summary (1-2 sentences)
        - Introduction (welcoming listeners, introducing the topic)
        - Main content divided into clear segments
        - Transitions between segments
        - Conclusion with key takeaways
        - Call to action for listeners

        Write in a conversational, engaging tone suitable for audio. Use natural language, contractions, and a friendly voice.
        Include occasional questions to engage listeners. Avoid complex visuals or references that don't work in audio format.

        Format as a well-structured podcast script with clear sections. The script should be ready to be read aloud.

        Content: $content'''
    };


    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    // Check if the response is complete or cutoff
    _isResponseComplete = !response.endsWith('...') &&
                         !response.endsWith('…') &&
                         !response.endsWith('to be continued') &&
                         !response.contains("I'll continue in the next response") &&
                         !response.contains('(continued)') &&
                         !response.contains('To be continued');

    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        _startQuizTimer(); // Start timer when quiz is generated and displayed
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        _parseExam(response);
        break;
      case 'chat':
        _startChatSession();
        break;
      case 'interactive_lesson':
        _parseInteractiveLesson(response);
        break;
      default:
        break;
    }
  }

  // Continue generating content when the response is cutoff
  Future<void> _continueGenerating() async {
    if (_geminiOutput == null || _isResponseComplete) return;

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
    });

    final progressController = StreamController<double>();
    try {
      progressController.stream.listen((progress) {
        if (mounted) setState(() => _processingProgress = progress);
      });

      // Simulate progress for better UX
      for (int i = 0; i <= 20; i++) {
        progressController.add(i / 100);
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Create a prompt asking to continue from where it left off
      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = ' Continue in ${_outputLanguage!} language only.';
      }
      final prompt = 'Please continue from where you left off. Here is the previous response:\n\n${_geminiOutput}${languageText}';
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        if (mounted) {
          setState(() {
            // Append the new response to the existing one
            _geminiOutput = '${_geminiOutput!}\n\n${response.text!}';
            // Check if the response is now complete
            _isResponseComplete = !response.text!.endsWith('...') &&
                               !response.text!.endsWith('…') &&
                               !response.text!.contains("I'll continue in the next response") &&
                               !response.text!.contains('(continued)') &&
                               !response.text!.contains('To be continued');
            progressController.add(0.95);
          });
        }
      }

      for (int i = 0; i <= 5; i++) {
        await Future.delayed(const Duration(milliseconds: 50));
        progressController.add(0.95 + (i / 5 * 0.05));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to continue generating: $e')),
        );
      }
    } finally {
      await progressController.close();
      if (mounted) {
        setState(() {
          _isProcessing = false;
          if (_processingProgress < 1.0) _processingProgress = 1.0;
        });
      }
    }
  }

  void _parseInteractiveLesson(String response) {
    final steps = response.split('\n\n').where((s) => s.trim().isNotEmpty).toList();
    setState(() {
      _lessonSteps = steps;
      _currentLessonStepIndex = 0;
      _lessonPlaying = true; // Start playing automatically
      _displayText = '';
      _currentCharIndex = 0;
    });

    if (_lessonSteps.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _startLessonStepDisplay();
        }
      });
    }
  }

  void _startChatSession() {
    try {
      _chatSession = _geminiModel.startChat();

      // Combine file content and pasted text if available
      String contextContent = _fileContent;
      String pastedText = _textInputController.text.trim();

      if (pastedText.isNotEmpty) {
        contextContent = contextContent.isEmpty ?
            pastedText :
            "$contextContent\n\n$pastedText";
      }

      String languageText = '';
      if (_outputLanguage != null && _outputLanguage != 'English') {
        languageText = "Respond only in ${_outputLanguage!} language. ";
      }

      final systemMessage = "Document content:\n$contextContent\n\n"
          "You are an assistant for this content. "
          "Base all responses strictly on this information. "
          "If asked about something not in the document be sure to infer and give an answer but be sure to, say "
          "'That information is not in the provided document'. "
          "$languageText";

      _chatSession.sendMessage(Content.text(systemMessage));

      if (mounted) {
        setState(() {
          _chatMessages = [
            ChatMessage(
                "AI: I've analyzed the document and am ready to answer questions",
                false),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
        );
      }
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final blocks = response.split(RegExp(r'\n\s*(?=Q:|Question|\d+\.)'));

    final qaRegex = RegExp(
      r'^(?:Q:|Question|\d+\.?)\s*(.*?)\s*(?:A:|Answer:?|\n)(.*)',
      caseSensitive: false,
      dotAll: true,
    );

    for (final block in blocks) {
      final match = qaRegex.firstMatch(block);
      if (match != null) {
        String question = match.group(1)?.trim() ?? '';
        String answer = match.group(2)?.trim() ?? '';

        // Clean question: Remove leading colons and spaces
        question = question
            .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]|^[:\s]+|[:\s]+$'), '')
            .trim();

        // Clean answer: Remove "::0" and other patterns
        answer = answer
            .replaceAll(RegExp(r'(Definition|Full Explanation|Application|Analysis|Connection|::.*?::|\|\|.*?\|\||[-*#`]|^A:?\s*|::0\s*)'), '')
            .trim();

        // Final trimming: Remove any remaining leading or trailing colons
        question = question.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();
        answer = answer.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();

        // Remove any leading colon and space from the answer
        answer = answer.replaceFirst(RegExp(r'^:\s*'), '');

        if (question.isNotEmpty && answer.isNotEmpty) {
          flashcards.add(Flashcard(
            question: question,
            answer: answer,
          ));
        }
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];

    // Updated regex patterns to support multiple languages
    // Match question numbers and text in any language
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer|Answers|Correct|Respuesta|Réponse|Odpowiedź|Jawaban|答案|回答|Antwort|Risposta|Antwoord|Svar|Cevap|Vastaus|Odpověď|Válasz|Απάντηση|Отговор|Відповідь|Ответ|정답|Câu trả lời|Svars|Atsakymas|Vastus|Odgovor|Menjawab|Jibu|Impendulo|Yankho|Jibu):\s*([A-D]?)',
        dotAll: true);

    // Match options A-D in any language
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');

    // Match answer label in multiple languages
    final answerRegex = RegExp(
      r'(?:Answer|Answers|Correct|Respuesta|Réponse|Odpowiedź|Jawaban|答案|回答|Antwort|Risposta|Antwoord|Svar|Cevap|Vastaus|Odpověď|Válasz|Απάντηση|Отговор|Відповідь|Ответ|정답|Câu trả lời|Svars|Atsakymas|Vastus|Odgovor|Menjawab|Jibu|Impendulo|Yankho|Jibu):\s*([A-D]?)',
      caseSensitive: false
    );

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response');

    for (final match in matches) {
      print('\n--- Quiz Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Letter Group: ${match.group(3)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      String correctLetter = match.group(3)?.toUpperCase() ?? '';
      if (correctLetter.isEmpty) {
        final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
        correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
      }
      print('Correct Letter: $correctLetter');

      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
        if (correctAnswerIndex == -1) {
          print(
              'Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');
          correctAnswerIndex = null;
        }
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
        _timeRemaining = _quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0
            ? Duration(minutes: _quizTimeLimitMinutes!)
            : Duration.zero; // Reset timer on new quiz
      });
    }
  }

  void _parseExam(String response) {
    final examQuestions = <ExamQuestion>[];

    // Different regex patterns for different exam types
    RegExp questionRegex;
    RegExp optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');

    if (_processType == 'exam_free') {
      // For free response exams, capture the question and sample response
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?:(?=\n\s*\d+\.)|(?=Sample Response|Answer|Solution)|$)(?:[\s\S]*?(?:Sample Response|Answer|Solution):\s*([\s\S]*?))?(?:(?=\n\s*\d+\.)|$)', dotAll: true);
    } else if (_processType == 'exam_case') {
      // For case-based exams, capture the question and solution
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([\s\S]+?)(?:(?=\n\s*\d+\.)|(?=Solution|Answer)|$)(?:[\s\S]*?(?:Solution|Answer):\s*([\s\S]*?))?(?:(?=\n\s*\d+\.)|$)', dotAll: true);
    } else {
      // For regular exams with multiple choice
      questionRegex = RegExp(r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)(?:Answer|Correct Answer):\s+([^\n]+)', dotAll: true);
    }

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Exam Response:\n$response');
    print('Exam Type: $_processType');
    print('Number of matches: ${matches.length}');

    for (final match in matches) {
      print('\n--- Exam Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Text Group: ${match.group(3)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      String correctAnswerText = match.group(3)?.trim() ?? '';
      // Remove any leading colon and space from the answer
      correctAnswerText = correctAnswerText.replaceFirst(RegExp(r'^:\s*'), '');
      print('Correct Answer Text: $correctAnswerText');

      // Make sure we have a valid answer text
      if (correctAnswerText.isEmpty && (_processType == 'exam_free' || _processType == 'exam_case')) {
        print('Warning: Empty answer text for question: $questionText');
        // For free response and case-based exams, provide a default answer if none is found
        examQuestions.add(ExamQuestion(
          question: questionText,
          options: options,
          correctAnswer: 'No sample answer provided.',
        ));
      } else {
        examQuestions.add(ExamQuestion(
          question: questionText,
          options: options,
          correctAnswer: correctAnswerText,
        ));
      }
    }

    if (mounted) {
      setState(() {
        _examQuestions = examQuestions;
      });
    }
  }

  void _startQuizTimer() {
    _cancelQuizTimer();
    if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0) {
      _timeRemaining = Duration(minutes: _quizTimeLimitMinutes!);
      _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 0) {
          setState(() => _timeRemaining -= const Duration(seconds: 1));
        } else {
          _cancelQuizTimer();
          _submitQuiz();
        }
      });
    }
  }

  void _cancelQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = null;
  }

  String get _formattedTimeRemaining {
    final minutes = _timeRemaining.inMinutes.remainder(60);
    final seconds = _timeRemaining.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    return _quizQuestions.isEmpty
        ? Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          _isProcessing
              ? 'Generating quiz questions...'
              : 'No quiz questions generated. Try processing the file first.',
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
      ),
    )
        : Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
                if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0)
                  Text(
                    'Time: ${_formattedTimeRemaining}',
                    style: GoogleFonts.notoSans(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.secondary),
                  ),
                Text(
                  'Score: $_quizScore',
                  style: GoogleFonts.notoSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.dividerColor),
              ),
              child: _DashboardPageState.buildLatexContent(
                _quizQuestions[_currentQuestionIndex].question,
                widget.isDarkMode,
                _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                context,
              ),
            ),
            const SizedBox(height: 20),
            ..._buildQuizOptions(theme, generalTextColor),
            const SizedBox(height: 24),
            _buildQuizNavigationButtons(theme, generalTextColor),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildQuizOptions(ThemeData theme, Color generalTextColor) {
    return List.generate(
        _quizQuestions[_currentQuestionIndex].options.length, (index) {
      if (_quizQuestions[_currentQuestionIndex].options[index].isEmpty)
        return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Material(
          color: _userAnswers[_currentQuestionIndex] == index
              ? theme.colorScheme.primary.withOpacity(0.2)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: () {
              setState(() => _userAnswers[_currentQuestionIndex] = index);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                    style: GoogleFonts.notoSans(
                        fontWeight: FontWeight.bold, color: generalTextColor),
                  ),
                  Expanded(
                    child: _DashboardPageState.buildLatexContent(
                      _quizQuestions[_currentQuestionIndex].options[index],
                      widget.isDarkMode,
                      _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                      context,
                    ),
                  ),
                  Radio<int>(
                    value: index,
                    groupValue: _userAnswers[_currentQuestionIndex],
                    onChanged: (value) {
                      setState(() => _userAnswers[_currentQuestionIndex] = value);
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildQuizNavigationButtons(ThemeData theme, Color generalTextColor) {
    return ElevatedButton(
      onPressed: _userAnswers[_currentQuestionIndex] != null
          ? () {
        if (_userAnswers[_currentQuestionIndex] ==
            _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
          setState(() => _quizScore++);
        }
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          setState(() => _currentQuestionIndex++);
        } else {
          _submitQuiz();
        }
      }
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 16),
        disabledBackgroundColor: Colors.grey,
      ),
      child: Text(
        _currentQuestionIndex < _quizQuestions.length - 1
            ? 'Next Question'
            : 'Finish Quiz',
        style: GoogleFonts.notoSans(
            color: _userAnswers[_currentQuestionIndex] != null
                ? widget.isDarkMode
                ? Colors.black
                : Colors.white
                : Colors.grey[400],
            fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildExamView(ThemeData theme, Color generalTextColor) {
    // If no exam questions are available, show a placeholder
    if (_examQuestions.isEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            _isProcessing
                ? 'Generating exam questions...'
                : 'No exam questions generated. Process file as Exam.',
            style: GoogleFonts.notoSans(color: generalTextColor),
          ),
        ),
      );
    }

    // For all exam types, show the exam content
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _processType == 'exam_free'
                    ? 'Free Response Exam'
                    : _processType == 'exam_case'
                      ? 'Case-Based Exam'
                      : 'Multiple Choice Exam',
                  style: GoogleFonts.notoSans(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: generalTextColor
                  ),
                  textAlign: TextAlign.center
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _exportToPdf(),
                      tooltip: 'Download as PDF',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._examQuestions.asMap().entries.map((entry) {
              int index = entry.key;
              ExamQuestion question = entry.value;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question text
                  _DashboardPageState.buildLatexContent(
                    '${index + 1}. ${question.question}',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    context,
                  ),
                  const SizedBox(height: 8),
                  // Options (if any)
                  if (question.options.isNotEmpty)
                    ...List.generate(question.options.length, (optionIndex) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: _DashboardPageState.buildLatexContent(
                          '${String.fromCharCode('A'.codeUnitAt(0) + optionIndex)}) ${question.options[optionIndex]}',
                          widget.isDarkMode,
                          _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                          context,
                        ),
                      );
                    }),
                  const SizedBox(height: 8),
                  // Answer section
                  if (question.correctAnswer.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: theme.dividerColor),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _processType == 'exam_free' ? 'Sample Response:' :
                            _processType == 'exam_case' ? 'Solution:' : 'Answer:',
                            style: GoogleFonts.notoSans(
                              fontWeight: FontWeight.bold,
                              color: generalTextColor
                            )
                          ),
                          _DashboardPageState.buildLatexContent(
                            question.correctAnswer,
                            widget.isDarkMode,
                            _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                            context,
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 20),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _submitQuiz() {
    final incorrectQuestions = _quizQuestions.where((q) {
      final userAnswer = _userAnswers[_quizQuestions.indexOf(q)];
      return userAnswer != q.correctAnswerIndex;
    }).toList();

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        incorrectQuestions: incorrectQuestions,
        textColor: generalTextColor,
        onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
        userAnswers: _userAnswers,
      ),
    );
  }


  Future<void> _generateWeaknessNotes(List<QuizQuestion> incorrectQuestions) async {
    final incorrectContent = incorrectQuestions.map((q) => q.question).join('\n');
    final prompt = '''Generate comprehensive notes focused on the topics covered in these quiz questions that the user answered incorrectly. Use the following source material to create the notes. Focus specifically on areas where the user demonstrated weakness in the quiz.

Incorrect Questions:
$incorrectContent

Source Material:
$_fileContent''';

    final response = await _geminiModel.generateContent([Content.text(prompt)]);
    if (response.text != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Improvement Notes', style: GoogleFonts.notoSans(color: generalTextColor)),
          content: SingleChildScrollView(
            child: MarkdownBody(
              data: response.text!,
              styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                p: GoogleFonts.notoSans(color: generalTextColor, fontSize: 16),
                h1: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 24),
                h2: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 20),
                h3: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 18),
                h4: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 16),
                h5: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 14),
                h6: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 12),
                code: GoogleFonts.sourceCodePro(backgroundColor: Theme.of(context).cardColor, color: generalTextColor),
                blockquote: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
                strong: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
                em: GoogleFonts.notoSans(fontStyle: FontStyle.italic, color: generalTextColor),
                listBullet: GoogleFonts.notoSans(color: generalTextColor),
                // Remove listNumber parameter
                checkbox: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Close', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to generate improvement notes.')),
      );
    }
  }


  Future<List<pw.Widget>> _buildPdfContent(String markdownText, String contentType) async {
    if (contentType == 'exam' || contentType == 'exam_free' || contentType == 'exam_case') {
      return await _buildPdfExamContent(markdownText);
    }
    else if (contentType == 'solution') {
      return _buildSolutionPdfContent(markdownText);
    }
    else {
      return _buildPdfNotesContent(markdownText, contentType); // Pass contentType here
    }
  }


List<pw.Widget> _buildSolutionPdfContent(String solutionText) {
  List<pw.Widget> widgets = [];

  // Add header
  widgets.add(pw.Header(
    level: 1,
    child: pw.Text('Problem Solution',
        style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold)),
  ));

  // Use the PDF helpers to convert content
  widgets.addAll(convertLatexToPdfWidgets(
    removePreliminaryText(solutionText),  // Remove preliminary text
    notoSansRegular!,
    notoSansBold!,
    notoSansItalic!,
    [
      stixTwoMathRegular!,
      notoSansSymbols!,
      bravura!,
      jetBrainsMonoRegular!,
      isocpRegular!,
      symbola!
    ]
  ));

  return widgets;
}



// New implementation of _buildPdfNotesContent that uses the PDF helpers
List<pw.Widget> _buildPdfNotesContent(String markdownText, String contentType) {
  List<pw.Widget> widgets = [];

  // Add header only for non-employment materials
  if (contentType != 'resume_tailoring' && contentType != 'cover_letter' && contentType != 'interview_prep') {
    widgets.add(pw.Header(
      level: 1,
      child: pw.Text(
        'Refactr AI',
        style: pw.TextStyle(
          fontSize: 24,
          fontWeight: pw.FontWeight.bold,
          font: notoSansBold,
          fontFallback: [
            stixTwoMathRegular!,
            notoSansSymbols!,
            bravura!,
            jetBrainsMonoRegular!,
            isocpRegular!,
            symbola!
          ],
        ),
      ),
    ));
  }

  // Split the markdown text into lines
  List<String> lines = markdownText.split('\n');
  bool inTable = false;
  List<List<String>> tableData = [];

  // Process each line
  for (var line in lines) {
    line = line.trim();

    // Geometry features removed due to null safety issues
    final geometryRegex = RegExp(r'```geometry:(\w+)\((.*?)\)```');
    final geometryMatch = geometryRegex.firstMatch(line);

    if (geometryMatch != null) {
      // If we were parsing a table, finish table widget creation
      if (inTable) {
        widgets.add(_buildPdfTable(tableData));
        tableData = [];
        inTable = false;
      }

      // Add a note about removed geometry features
      widgets.add(
        pw.Container(
          padding: const pw.EdgeInsets.all(8),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: pdf_package.PdfColors.grey),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
          ),
          child: pw.Text(
            '[Geometry visualization removed]',
            style: pw.TextStyle(font: notoSansItalic),
          ),
        )
      );
      widgets.add(pw.SizedBox(height: 8));
      continue; // Skip to next line after handling geometry
    }

    // Check for image markdown syntax: ![alt text](image_url)
    final imageRegex = RegExp(r'!\[(.*?)\]\((.*?)\)');
    final imageMatch = imageRegex.firstMatch(line);

    if (imageMatch != null) {
      // If we were parsing a table, finish table widget creation
      if (inTable) {
        widgets.add(_buildPdfTable(tableData));
        tableData = [];
        inTable = false;
      }

      // Extract image description
      final imageAlt = imageMatch.group(1) ?? 'Image';

      // Add image placeholder with description
      widgets.add(_buildImagePlaceholder(imageAlt));
      widgets.add(pw.SizedBox(height: 8));
      continue; // Skip to next line after handling image
    }

    if (line.startsWith('|')) {
      // Table detection and processing.
      if (!inTable) {
        inTable = true;
      }
      // Split row into cells and trim each cell.
      List<String> cells = line.split('|').map((c) => c.trim()).toList();
      // Remove empty first/last elements that may result from a leading/trailing '|'
      if (cells.isNotEmpty && cells.first.isEmpty) cells.removeAt(0);
      if (cells.isNotEmpty && cells.last.isEmpty) cells.removeLast();

      // Check for separator row, e.g., |---|---|
      bool isSeparator = cells.every((cell) => cell.replaceAll('-', '').isEmpty);
      if (!isSeparator) {
        tableData.add(cells);
      }
    } else {
      // If we were parsing a table but encountered a non-table line, finish table widget creation.
      if (inTable) {
        widgets.add(_buildPdfTable(tableData));
        tableData = [];
        inTable = false;
      }
      // Handle headings and other markdown content.
      if (line.startsWith('# ')) {
        widgets.add(pw.Text(
          line.substring(2).trim(),
          style: pw.TextStyle(
            fontSize: 20,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ],
          ),
        ));
      } else if (line.startsWith('## ')) {
        widgets.add(pw.Text(
          line.substring(3).trim(),
          style: pw.TextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ],
          ),
        ));
      } else if (line.startsWith('### ')) {
        widgets.add(pw.Text(
          line.substring(4).trim(),
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ],
          ),
        ));
      } else if (line.startsWith('- ')) {
        widgets.add(pw.Bullet(
          text: line.substring(2).trim(),
          style: pw.TextStyle(
            fontSize: 14,
            font: notoSansRegular,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ],
          ),
        ));
      } else if (line.isNotEmpty) {
        // Check for diagram/image descriptions in text
        if (line.toLowerCase().contains('diagram:') ||
            line.toLowerCase().contains('figure:') ||
            line.toLowerCase().contains('image:')) {
          widgets.add(_buildImagePlaceholder(line));
        } else {
          widgets.add(pw.Wrap(
            direction: pw.Axis.horizontal,
            children: _parseLineToWidgets(line),
          ));
        }
      }
      // Add spacing after non-table content
      widgets.add(pw.SizedBox(height: 8));
    }
  }

  // Add any remaining table data (if the markdown ends with a table)
  if (inTable && tableData.isNotEmpty) {
    widgets.add(_buildPdfTable(tableData));
  }

  return widgets;
}

// Helper method to create image placeholders in PDF
pw.Widget _buildImagePlaceholder(String description) {
  return pw.Container(
    margin: const pw.EdgeInsets.symmetric(vertical: 10),
    padding: const pw.EdgeInsets.all(10),
    decoration: pw.BoxDecoration(
      border: pw.Border.all(color: pdf_package.PdfColors.black, width: 1),
    ),
    height: 150, // Fixed height for the placeholder
    child: pw.Column(
      mainAxisAlignment: pw.MainAxisAlignment.center,
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Text(
          'IMAGE PLACEHOLDER',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            font: notoSansBold,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          description,
          textAlign: pw.TextAlign.center,
          style: pw.TextStyle(
            fontSize: 12,
            font: notoSansRegular,
            fontFallback: [
              stixTwoMathRegular!,
              notoSansSymbols!,
              bravura!,
              jetBrainsMonoRegular!,
              isocpRegular!,
              symbola!
            ],
          ),
        ),
      ],
    ),
  );
}

// Geometry features removed due to null safety issues


pw.Widget _buildPdfTable(List<List<String>> tableData) {
  if (tableData.isEmpty) return pw.SizedBox();

  List<pw.TableRow> rows = [];

  // Create header row
  rows.add(pw.TableRow(
    children: tableData[0].map((cell) => pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Wrap(
        children: _parseLineToWidgets(cell),
      ),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: pdf_package.PdfColors.black, width: 0.5),
      ),
    )).toList(),
  ));

  // Create data rows
  for (int i = 1; i < tableData.length; i++) {
    rows.add(pw.TableRow(
      children: tableData[i].map((cell) => pw.Container(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Wrap(
          children: _parseLineToWidgets(cell),
        ),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: pdf_package.PdfColors.black, width: 0.5),
        ),
      )).toList(),
    ));
  }

  return pw.Table(
    border: pw.TableBorder.symmetric(
      inside: const pw.BorderSide(color: pdf_package.PdfColors.black, width: 0.5),
      outside: const pw.BorderSide(color: pdf_package.PdfColors.black, width: 0.5),
    ),
    columnWidths: {for (var i = 0; i < tableData[0].length; i++) i: const pw.FlexColumnWidth(1)},
    children: rows,
  );
}

  Future<List<pw.Widget>> _buildPdfExamContent(String markdownText) async {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];

    // Add Refactr AI header only for non-employment materials
    if (_processType != 'resume_tailoring' && _processType != 'cover_letter' && _processType != 'interview_prep') {
      widgets.add(pw.Header(
        level: 1,
        child: pw.Text('Refactr AI',
            style: pw.TextStyle(
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
                font: notoSansBold)),
      ));
    }

    // Add exam title based on process type
    String examTitle = '';
    if (_processType == 'exam_free') {
      examTitle = 'Free Response Exam';
    } else if (_processType == 'exam_case') {
      examTitle = 'Case-Based Exam';
    } else {
      examTitle = 'Multiple Choice Exam';
    }

    widgets.add(pw.Header(
      level: 2,
      child: pw.Text(examTitle,
          style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              font: notoSansBold)),
    ));

    widgets.add(pw.SizedBox(height: 16));

    for (var line in lines) {
      if (line.trim().isEmpty) continue;

      // Geometry features removed due to null safety issues
      final geometryRegex = RegExp(r'```geometry:(\w+)\((.*?)\)```');
      final geometryMatch = geometryRegex.firstMatch(line);

      if (geometryMatch != null) {
        // Add a note about removed geometry features
        widgets.add(
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: pdf_package.PdfColors.grey),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Text(
              '[Geometry visualization removed]',
              style: pw.TextStyle(font: notoSansItalic),
            ),
          )
        );
        widgets.add(pw.SizedBox(height: 8));
        continue; // Skip to next line after handling geometry
      }

      // Check for image markdown syntax: ![alt text](image_url)
      final imageRegex = RegExp(r'!\[(.*?)\]\((.*?)\)');
      final imageMatch = imageRegex.firstMatch(line);

      if (imageMatch != null) {
        // Extract image description
        final imageAlt = imageMatch.group(1) ?? 'Image';

        // Add image placeholder with description
        widgets.add(_buildImagePlaceholder(imageAlt));
        widgets.add(pw.SizedBox(height: 8));
        continue; // Skip to next line after handling image
      }

      // Check for LaTeX expressions
      final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
      if (latexRegExp.hasMatch(line)) {
        // Process line with LaTeX
        widgets.add(await _buildPdfLatexLine(line));
      } else if (line.toLowerCase().contains('diagram:') ||
                line.toLowerCase().contains('figure:') ||
                line.toLowerCase().contains('image:')) {
        // Handle image descriptions
        widgets.add(_buildImagePlaceholder(line));
      } else {
        // Pre-process markdown formatting
        line = line.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => '<b>${match.group(1)}</b>');
        line = line.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => '<i>${match.group(1)}</i>');
        line = line.replaceAllMapped(RegExp(r'__(.*?)__'), (match) => '<b>${match.group(1)}</b>');
        line = line.replaceAllMapped(RegExp(r'_(.*?)_'), (match) => '<i>${match.group(1)}</i>');

        // Regular text line with proper font fallbacks
        widgets.add(_buildRichTextFromMarkdown(line));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  // Helper method to handle LaTeX in PDF export
  Future<pw.Widget> _buildPdfLatexLine(String line) async {
    // Pre-process markdown formatting
    line = line.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => '<b>${match.group(1)}</b>');
    line = line.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => '<i>${match.group(1)}</i>');
    line = line.replaceAllMapped(RegExp(r'__(.*?)__'), (match) => '<b>${match.group(1)}</b>');
    line = line.replaceAllMapped(RegExp(r'_(.*?)_'), (match) => '<i>${match.group(1)}</i>');

    // Process subscripts and superscripts
    line = line.replaceAllMapped(RegExp(r'<sub>(.*?)</sub>'), (match) => '_${match.group(1)}');
    line = line.replaceAllMapped(RegExp(r'<sup>(.*?)</sup>'), (match) => '^${match.group(1)}');

    // Check for LaTeX expressions
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    final matches = latexRegExp.allMatches(line);

    if (matches.isEmpty) {
      // No LaTeX expressions found, return regular text with rich text formatting
      return _buildRichTextFromMarkdown(line);
    }

    // Process line with LaTeX expressions
    List<pw.Widget> lineWidgets = [];
    int lastEnd = 0;

    for (final match in matches) {
      // Add text before LaTeX expression
      if (match.start > lastEnd) {
        lineWidgets.add(_buildRichTextFromMarkdown(line.substring(lastEnd, match.start)));
      }

      // Process LaTeX expression as text with proper fonts
      final latexExpression = match.group(0)!;
      try {
        // Extract LaTeX content from delimiters
        String texString = latexExpression.trim();
        bool isDisplayMode = false;

        if (texString.startsWith(r'$$') && texString.endsWith(r'$$')) {
          texString = texString.substring(2, texString.length - 2).trim();
          isDisplayMode = true;
        } else if (texString.startsWith(r'$') && texString.endsWith(r'$')) {
          texString = texString.substring(1, texString.length - 1).trim();
        } else if (texString.startsWith(r'\[') && texString.endsWith(r'\]')) {
          texString = texString.substring(2, texString.length - 2).trim();
          isDisplayMode = true;
        } else if (texString.startsWith(r'\(') && texString.endsWith(r'\)')) {
          texString = texString.substring(2, texString.length - 2).trim();
        }

        // Use Noto Sans with fallback fonts instead of LaTeX images
        if (isDisplayMode) {
          // For display mode, add as a centered block with a bit more spacing
          lineWidgets.add(
            pw.Center(
              child: pw.Container(
                margin: const pw.EdgeInsets.symmetric(vertical: 8),
                child: pw.Text(
                  texString,
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: notoSansRegular!,
                    fontFallback: [
                      stixTwoMathRegular!,
                      notoSansSymbols!,
                      bravura!,
                      jetBrainsMonoRegular!,
                      isocpRegular!,
                      symbola!
                    ],
                  ),
                ),
              ),
            ),
          );
        } else {
          // For inline mode, add in the flow
          lineWidgets.add(
            pw.Text(
              texString,
              style: pw.TextStyle(
                fontSize: 12,
                font: notoSansRegular!,
                fontFallback: [
                  stixTwoMathRegular!,
                  notoSansSymbols!,
                  bravura!,
                  jetBrainsMonoRegular!,
                  isocpRegular!,
                  symbola!
                ],
              ),
            ),
          );
        }
      } catch (e) {
        print('Error processing LaTeX in PDF: $e for expression: $latexExpression');
        // Fallback to plain text if processing fails
        lineWidgets.add(
          pw.Text(
            latexExpression,
            style: pw.TextStyle(
              fontSize: 12,
              font: notoSansRegular!,
              fontFallback: [
                stixTwoMathRegular!,
                notoSansSymbols!,
                bravura!,
                jetBrainsMonoRegular!,
                isocpRegular!,
                symbola!
              ],
            ),
          ),
        );
      }

      lastEnd = match.end;
    }

    // Add remaining text after last LaTeX expression
    if (lastEnd < line.length) {
      lineWidgets.add(_buildRichTextFromMarkdown(line.substring(lastEnd)));
    }

    // Return a row with all widgets
    return pw.Row(
      children: lineWidgets,
      crossAxisAlignment: pw.CrossAxisAlignment.center,
    );
  }

  // Helper method to build rich text from markdown
  pw.Widget _buildRichTextFromMarkdown(String text) {
    // Pre-process markdown formatting
    text = text.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => '<b>${match.group(1)}</b>');
    text = text.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => '<i>${match.group(1)}</i>');
    text = text.replaceAllMapped(RegExp(r'__(.*?)__'), (match) => '<b>${match.group(1)}</b>');
    text = text.replaceAllMapped(RegExp(r'_(.*?)_'), (match) => '<i>${match.group(1)}</i>');

    // Process bold, italic, and code tags
    List<pw.TextSpan> spans = [];
    final formattingRegex = RegExp(r'<b>(.*?)</b>|<i>(.*?)</i>|<code>(.*?)</code>|([^<>]+)');

    for (final match in formattingRegex.allMatches(text)) {
      if (match.group(1) != null) {
        // Bold text
        spans.add(pw.TextSpan(
          text: match.group(1)!,
          style: pw.TextStyle(
            fontSize: 12,
            font: notoSansBold!,
            fontWeight: pw.FontWeight.bold,
            fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
          ),
        ));
      } else if (match.group(2) != null) {
        // Italic text
        spans.add(pw.TextSpan(
          text: match.group(2)!,
          style: pw.TextStyle(
            fontSize: 12,
            font: notoSansItalic!,
            fontStyle: pw.FontStyle.italic,
            fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
          ),
        ));
      } else if (match.group(3) != null) {
        // Code text
        spans.add(pw.TextSpan(
          text: match.group(3)!,
          style: pw.TextStyle(
            fontSize: 11,
            font: jetBrainsMonoRegular!,
            fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, isocpRegular!, symbola!],
          ),
        ));
      } else if (match.group(4) != null) {
        // Regular text
        spans.add(pw.TextSpan(
          text: match.group(4)!,
          style: pw.TextStyle(
            fontSize: 12,
            font: notoSansRegular!,
            fontFallback: [stixTwoMathRegular!, notoSansSymbols!, bravura!, jetBrainsMonoRegular!, isocpRegular!, symbola!],
          ),
        ));
      }
    }

    return pw.RichText(text: pw.TextSpan(children: spans));
  }




// Update the _parseLineToWidgets function for PDF generation
List<pw.Widget> _parseLineToWidgets(String line) {
  line = line.replaceAllMapped(RegExp(r"'(.+?)'"), (match) => match.group(1)!);
  List<pw.Widget> widgets = [];
  final pattern = RegExp(
    r'(\^{?([^}]+)}?|_\{?([^}]+)}?|\$.*?\$|\\[()\[\]].*?\\[()\[\]]|\\begin\{.*?\}.*?\\end\{.*?\})',
    dotAll: true,
  );

  int lastPos = 0;
  for (final match in pattern.allMatches(line)) {
    // Add preceding text
    if (match.start > lastPos) {
      widgets.add(pw.Text(
        line.substring(lastPos, match.start),
        style: pw.TextStyle(font: notoSansRegular),
      ));
    }

    // Handle matches
    final fullMatch = match.group(0)!;
    if (fullMatch.startsWith('^')) {
      final content = match.group(2)!;
      widgets.add(pw.Text(
        _convertToSuperscript(content),
        style: pw.TextStyle(
          font: notoSansRegular,
          fontSize: 10,
        ),
      ));
    } else if (fullMatch.startsWith('_')) {
      final content = match.group(3)!;
      widgets.add(pw.Text(
        _convertToSubscript(content),
        style: pw.TextStyle(
          font: notoSansRegular,
          fontSize: 10,
        ),
      ));
    } else {
      // Handle LaTeX with proper font
      widgets.add(pw.Text(
        fullMatch,
        style: pw.TextStyle(
          font: stixTwoMathRegular,
          fontSize: 14,
        ),
      ));
    }

    lastPos = match.end;
  }

  // Add remaining text
  if (lastPos < line.length) {
    widgets.add(pw.Text(
      line.substring(lastPos),
      style: pw.TextStyle(font: notoSansRegular),
    ));
  }

  return widgets;
}

// Update the MarkdownStyleSheet for styling
MarkdownStyleSheet _getMarkdownStyleSheet(ThemeData theme, Color textColor) {
  return MarkdownStyleSheet(
    p: GoogleFonts.notoSans(color: textColor, fontSize: 16),
    h1: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 24),
    h2: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 20),
    h3: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.bold, fontSize: 18),
    strong: GoogleFonts.notoSans(
        color: textColor, fontWeight: FontWeight.w700),
    em: GoogleFonts.notoSans(color: textColor, fontStyle: FontStyle.italic),
    code: GoogleFonts.sourceCodePro(
        fontSize: 14, backgroundColor: theme.cardColor, color: textColor),
    textScaleFactor: 1.0,
  );
}


  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    try {
      final pdf = pw.Document();

      // Get the content widgets asynchronously
      final contentWidgets = await _buildPdfContent(_geminiOutput!, _processType);

      // Create a PDF with multiple pages and page numbers
      pdf.addPage(
        pw.MultiPage(
          pageFormat: pdf_package.PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          footer: (context) {
            return pw.Container(
              alignment: pw.Alignment.centerRight,
              margin: const pw.EdgeInsets.only(top: 10),
              child: pw.Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: pw.TextStyle(fontSize: 10),
              ),
            );
          },
          build: (context) => contentWidgets,
          maxPages: 100, // Allow up to 100 pages
        ),
      );

      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        final blob = html.Blob([pdfBytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
        if (selectedDirectory != null) {
          final fileName = '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
          final filePath = path.join(selectedDirectory, fileName);

          final file = io.File(filePath);
          await file.writeAsBytes(pdfBytes);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to $filePath')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No directory selected')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('PDF export failed: ${e.toString()}')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages
            .add(ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
        // Update lesson playing state when TTS completes
        if (_lessonPlaying && _currentLessonStepIndex >= _lessonSteps.length - 1) {
          _lessonPlaying = false;
        }
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty && !_isNarrationMuted) {
      await flutterTts.setVolume(volume);
      await flutterTts.setSpeechRate(rate);
      await flutterTts.setPitch(pitch);
      await flutterTts.setLanguage(_ttsLanguage);

      String cleanedText = text
          .replaceAll(RegExp(r'<sup>(.*?)</sup>'), r'$1') // Keep superscript content
          .replaceAll(RegExp(r'<sub>(.*?)</sub>'), r'$1') // Keep subscript content
          .replaceAll(RegExp(r'(\w+)\^(\w+)'), r'$1 to the power of $2') // Handle exponents
          .replaceAll(RegExp(r'[*~`#-]'), '')
          .replaceAll(RegExp(r'[\n\r]'), ' ')
          .trim();

      if (ttsState == TtsState.playing) {
        var result = await flutterTts.pause();
        if (result == 1) setState(() => ttsState = TtsState.paused);
      } else {
        var result = await flutterTts.speak(cleanedText);
        if (result == 1) setState(() => ttsState = TtsState.playing);
      }
    }
  }

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }

  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: () => isPlaying
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Colors.grey,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.3),
                trackHeight: 4.0,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
                overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: rate,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: "Speed",
                activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                inactiveColor: Colors.grey,
                onChanged: (double value) {
                  setState(() => rate = value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Legwork Automator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.mic, color: theme.colorScheme.onSurface),
            onPressed: _showRecordingDialog,
            tooltip: 'Record Audio',
          ),
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
            tooltip: 'Toggle Theme',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              controller: _scrollController, // Add scroll controller
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, generalTextColor,
                      buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, generalTextColor,
                      buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, generalTextColor),
                ],
              ),
            ),
      // Add floating action button for resume tailoring
      floatingActionButton: AnimatedOpacity(
        opacity: _isFabVisible ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: FloatingActionButton(
          backgroundColor: buttonBackground,
          foregroundColor: buttonTextColor,
          onPressed: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResumeTailoringPage(
                isDarkMode: widget.isDarkMode,
                toggleTheme: widget.toggleTheme
              )
            )
          ),
          tooltip: 'Resume Tailoring',
          shape: const CircleBorder(),
          child: const Icon(Icons.work),
        ),
      ),
    );
  }

Widget _buildFileSection(ThemeData theme, Color generalTextColor,
    Color buttonTextColor, Color buttonBg) {

// Add this helper function to determine the file icon
Icon _getFileIcon(String fileName) {
  final extension = fileName.split('.').last.toLowerCase();
  final iconColor = generalTextColor.withOpacity(0.6);
  final iconSize = 18.0;
  switch (extension) {
    case 'pdf':
      return Icon(Icons.picture_as_pdf, color: iconColor, size: iconSize);
    case 'txt':
      return Icon(Icons.description, color: iconColor, size: iconSize);
    case 'doc':
    case 'docx':
      return Icon(Icons.article, color: iconColor, size: iconSize);
    case 'ppt':
    case 'pptx':
      return Icon(Icons.slideshow, color: iconColor, size: iconSize);
    case 'jpg':
    case 'jpeg':
    case 'png':
      return Icon(Icons.image, color: iconColor, size: iconSize);
    case 'mp3':
      return Icon(Icons.audiotrack, color: iconColor, size: iconSize);
    case 'mp4':
    case 'mpeg':
    case 'mov':
    case 'avi':
      return Icon(Icons.videocam, color: iconColor, size: iconSize);
    default:
      return Icon(Icons.insert_drive_file, color: iconColor, size: iconSize);
  }
}

  return Column(
    children: [
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Files',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: Icon(Icons.cloud_upload, color: buttonTextColor),
                      label: Text('Select Learning Material(s)',
                          style: GoogleFonts.notoSans(color: buttonTextColor)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBg,
                        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                      ),
                      onPressed: _isUploading ? null : _pickFile,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _isUploading ? null : _openCamera,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: buttonBg,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Icon(Icons.camera_alt, color: buttonTextColor),
                  ),
                ],
              ),
              if (_pickedFiles.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Selected Files:',
                          style: GoogleFonts.notoSans(
                              color: generalTextColor.withOpacity(0.8))),
..._pickedFiles.map((fileRange) => Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _getFileIcon(fileRange.file.name),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  fileRange.file.name,
                  style: GoogleFonts.notoSans(
                      color: generalTextColor.withOpacity(0.6),
                      fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (fileRange.file.name.toLowerCase().endsWith('.pdf'))
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Start Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.startPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 80,
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'End Page',
                      hintStyle: GoogleFonts.notoSans(fontSize: 12),
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                    style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                    onChanged: (value) => fileRange.endPage = value.isEmpty ? null : int.tryParse(value),
                  ),
                ),
              ],
            ),
        ],
      ),
    ),
    IconButton(
      icon: Icon(Icons.delete, color: Colors.red),
      onPressed: () => _deleteFile(fileRange),
    ),
  ],
)).toList(),
                    ],
                  ),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: LinearProgressIndicator(value: _uploadProgress),
                ),
              if (_isUploading)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                    style: GoogleFonts.notoSans(color: generalTextColor),
                  ),
                ),
            ],
          ),
        ),
      ),
      const SizedBox(height: 16),
      Text(
        'Or',
        style: GoogleFonts.notoSans(
          color: generalTextColor,
          fontSize: 16,
        ),
      ),
      const SizedBox(height: 16),
      Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enter Text',
                style: GoogleFonts.notoSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: generalTextColor,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _textInputController,
                maxLines: 2, // Reduced to half the original height
                decoration: InputDecoration(
                  hintText: 'Paste or type your text here...',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12), // Further reduced padding
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ],
          ),
        ),
      ),
    ],
  );
}

  // Check if there are any MP3 files in the picked files
  bool get _hasMP3 {
    return _pickedFiles.any((fileRange) =>
      fileRange.file.name.toLowerCase().endsWith('.mp3') ||
      fileRange.file.name.toLowerCase().contains('audio') ||
      fileRange.file.name.toLowerCase().contains('recording')
    );
  }

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor,
      Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: [
                const DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                const DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                const DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                const DropdownMenuItem(value: 'mindmap', child: Text('Create Mindmap')),
                const DropdownMenuItem(value: 'scheme_of_work', child: Text('Create Scheme of Work')),
                const DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                const DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'podcast', child: Text('Create Podcast')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'minutes', child: Text('Create Meeting Minutes')),
                const DropdownMenuItem(value: 'chat', child: Text('AI Tutor / Chat with Content')),
                const DropdownMenuItem(value: 'lesson_plan', child: Text('Create Lesson Plan')),
                const DropdownMenuItem(value: 'worksheet', child: Text('Worksheet')),
                const DropdownMenuItem(value: 'homework_guide', child: Text('Homework Guide')),
                const DropdownMenuItem(value: 'project_ideas', child: Text('Project Ideas')),
                const DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
                const DropdownMenuItem(value: 'exam_free', child: Text('Exam (Free Response)')),
                const DropdownMenuItem(value: 'exam_case', child: Text('Exam (Case Question)')),
                // Grammar checker removed as it's part of paper grader
                const DropdownMenuItem(value: 'paper_grader', child: Text('Paper Grader')),
                const DropdownMenuItem(value: 'case_studies', child: Text('Case Studies')),
                const DropdownMenuItem(value: 'experiment', child: Text('Experiment/Lab')),
                const DropdownMenuItem(value: 'interactive_lesson', child: Text('Interactive Lesson')),
                // Interview prep removed as it's only available in the briefcase section
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _processType = value;
                    _geminiOutput = null;
                    _flashcards = [];
                    _quizQuestions = [];
                    _examQuestions = [];
                    _chatMessages = [];
                    _readingGradeLevel = null;
                    _difficultyLevel = null;
                    _quizTimeLimitMinutes = null;
                    _cancelQuizTimer();
                    _timeRemaining = Duration.zero;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            // Language dropdown
            DropdownButtonFormField<String>(
              value: _outputLanguage,
              decoration: InputDecoration(
                labelText: 'Output Language',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: const [
                DropdownMenuItem(value: 'English', child: Text('English')),
                DropdownMenuItem(value: 'Chichewa', child: Text('Chichewa')),
                DropdownMenuItem(value: 'Chitumbuka', child: Text('Chitumbuka')),
                DropdownMenuItem(value: 'Swahili', child: Text('Swahili')),
                DropdownMenuItem(value: 'Shona', child: Text('Shona')),
                DropdownMenuItem(value: 'Zulu', child: Text('Zulu')),
              ],
              onChanged: (value) {
                setState(() {
                  _outputLanguage = value;
                });
              },
            ),
            const SizedBox(height: 16),
            if (_processType != 'quiz' && _processType != 'exam' && _processType != 'exam_free' && _processType != 'exam_case')
              DropdownButtonFormField<String>(
                value: _readingGradeLevel,
                decoration: InputDecoration(
                  labelText: 'Reading Grade Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Grade 5', child: Text('Grade 5')),
                  DropdownMenuItem(value: 'Grade 8', child: Text('Grade 8')),
                  DropdownMenuItem(value: 'Grade 10', child: Text('Grade 10')),
                  DropdownMenuItem(value: 'Grade 12', child: Text('Grade 12')),
                  DropdownMenuItem(value: 'College', child: Text('College')),
                  DropdownMenuItem(
                      value: 'Professional', child: Text('Professional')),
                ],
                onChanged: (value) {
                  setState(() {
                    _readingGradeLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz' || _processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case')
              DropdownButtonFormField<String>(
                value: _difficultyLevel,
                decoration: InputDecoration(
                  labelText: 'Difficulty Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                  DropdownMenuItem(value: 'Normal', child: Text('Normal')),
                  DropdownMenuItem(
                      value: 'Intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'Hard', child: Text('Hard')),
                  DropdownMenuItem(
                      value: 'Very Hard', child: Text('Very Hard')),
                ],
                onChanged: (value) {
                  setState(() {
                    _difficultyLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Quiz Time Limit (minutes)',
                          labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        ),
                        onChanged: (value) => setState(() => _quizTimeLimitMinutes = int.tryParse(value)),
                      ),
                    ),
                  ],
                ),
              ),

            SizedBox(height: 16), // Added consistent spacing here

            // Geometry features removed due to null safety issues

            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.memory, color: buttonTextColor),
              label: Text(_isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
				onPressed: _isProcessing ? null : _processInput,
            ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    String titleText = 'Generated Content';
    bool showDownloadButton = true;
    switch (_processType) {
      case 'notes':
        titleText = 'Notes';
        break;
      case 'cheatsheet':
        titleText = 'Cheatsheet';
        break;
      case 'flashcards':
        titleText = 'Flashcards';
        showDownloadButton = false;
        break;
      case 'quiz':
        titleText = 'Quiz';
        showDownloadButton = false;
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        titleText = 'Exam';
        break;
      case 'transcript':
        titleText = 'Transcript';
        showDownloadButton = false;
        break;
      case 'chat':
        titleText = 'Chat';
        showDownloadButton = false;
        break;
      case 'summary':
        titleText = 'Summary';
        break;
      case 'minutes':
        titleText = 'Meeting Minutes';
        break;
      case 'interactive_lesson':
        titleText = 'Lesson';
        showDownloadButton = false;
        break;
      case 'lesson_plan':
        titleText = 'Lesson Plan';
        break;
      case 'worksheet':
        titleText = 'Worksheet';
        break;
      case 'homework_guide':
        titleText = 'Homework Guide';
        break;
      case 'project_ideas':
        titleText = 'Project Ideas';
        break;
      case 'grammar':
        titleText = 'Grammar Check';
        break;
      case 'paper_grader':
        titleText = 'Paper Feedback';
        break;
      case 'case_studies':
        titleText = 'Case Studies';
        break;
      case 'experiment':
        titleText = 'Experiment/Lab';
        break;
      case 'mindmap':
        titleText = 'Mindmap';
        break;
      case 'scheme_of_work':
        titleText = 'Scheme of Work';
        break;
      case 'podcast':
        titleText = 'Podcast';
        break;
      case 'interview_prep':
        titleText = 'Interview Preparation';
        break;
    }

if (_processType == 'flashcards') {
    return _buildFlashcardsView(theme, generalTextColor);
  } else if (_processType == 'quiz') {
    return _buildQuizView(theme, generalTextColor);
  } else if (_processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case') {
    return _buildExamView(theme, generalTextColor);
  } else if (_processType == 'chat') {
    return _buildChatView(theme, generalTextColor);
  } else if (_processType == 'interactive_lesson') {
    return _buildInteractiveLessonView(theme, generalTextColor);
  } else if (_processType == 'podcast') {
    return _buildPodcastView(theme, generalTextColor);
  } else if (_processType == 'mindmap') {
    // Use the existing method at line 4879 instead of the removed duplicate
    return _buildVisualMindmapView(theme, generalTextColor);
  } else if (_processType == 'interview_prep') {
    // Navigate to interview prep page
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => InterviewPrepPage(
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    });
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Interview Preparation',
              style: GoogleFonts.notoSans(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: generalTextColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Redirecting to Interview Preparation page...',
              style: GoogleFonts.notoSans(
                color: generalTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  } else if (_processType == 'experiment') {
    return _buildExperimentView(theme, generalTextColor);
  }

  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(titleText,
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor)),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Font size controls
                  IconButton(
                    icon: Icon(Icons.text_decrease, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize > 12) _lessonFontSize -= 2;
                      });
                    },
                    tooltip: 'Decrease font size',
                  ),
                  IconButton(
                    icon: Icon(Icons.text_increase, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize < 24) _lessonFontSize += 2;
                      });
                    },
                    tooltip: 'Increase font size',
                  ),
                  if (showDownloadButton)
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _exportToPdf(),
                      tooltip: 'Download as PDF',
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _geminiOutput ?? 'No content generated',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    context,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      icon: Icon(Icons.copy, color: generalTextColor),
                      tooltip: 'Copy to Clipboard',
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: _geminiOutput ?? ''));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Content copied to clipboard')),
                        );
                      },
                    ),
                    if (_isResponseComplete == false)
                      IconButton(
                        icon: Icon(Icons.play_arrow, color: theme.colorScheme.primary),
                        tooltip: 'Continue generating',
                        onPressed: () {
                          // Continue generating content
                          _continueGenerating();
                        },
                      )
                    else
                      IconButton(
                        icon: Icon(Icons.check_circle, color: Colors.green),
                        tooltip: 'Response Complete',
                        onPressed: null,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}





  Widget _buildInteractiveLessonView(ThemeData theme, Color generalTextColor) {
    TextStyle defaultStyle = GoogleFonts.notoSans(
      fontSize: _lessonFontSize,
      color: Colors.white,
      height: 1.5,
    );

  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Page ${_currentLessonStepIndex + 1}/${_lessonSteps.length}',
                style: GoogleFonts.notoSans(
                  color: generalTextColor,
                  fontSize: 16,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Add font controls here
                  IconButton(
                    icon: Icon(Icons.text_decrease, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize > 12) _lessonFontSize -= 2;
                      });
                    },
                    tooltip: 'Decrease font size',
                  ),
                  IconButton(
                    icon: Icon(Icons.text_increase, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize < 24) _lessonFontSize += 2;
                      });
                    },
                    tooltip: 'Increase font size',
                  ),
                  IconButton(
                    icon: Icon(
                      _isNarrationMuted ? Icons.volume_off : Icons.volume_up,
                      color: generalTextColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _isNarrationMuted = !_isNarrationMuted;
                        if (_isNarrationMuted) _pauseTts();
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
            SizedBox(
              height: 300,
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDarkMode ? Colors.black : Colors.grey[200], // Adjust based on theme
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _displayText,
                    widget.isDarkMode, // Use the current theme mode
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    context,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 80,
              child: _buildLessonControls(theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLessonControls(ThemeData theme) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(Icons.skip_previous, color: generalTextColor),
              onPressed: _goToPreviousStep,
              tooltip: 'Previous step',
            ),
            IconButton(
              icon: Icon(
                  _lessonPlaying ? Icons.pause : Icons.play_arrow,
                  color: generalTextColor),
              onPressed: _toggleLessonPlay,
              tooltip: _lessonPlaying ? 'Pause' : 'Play',
            ),
            IconButton(
              icon: Icon(Icons.skip_next, color: generalTextColor),
              onPressed: _goToNextStep,
              tooltip: 'Next step',
            ),

          ],
        ),
      ],
    );
  }

  void _goToPreviousStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex > 0) {
        _currentLessonStepIndex--;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _goToNextStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _stopLessonStepDisplay() {
    _stopTts();
    _stopTextAnimation();
  }

  void _stopTextAnimation() {
    _textAnimationTimer?.cancel();
    _isTextAnimationActive = false;
    _displayText = '';
    _currentCharIndex = 0;
  }

  // Geometry features removed due to null safety issues

  void _goToNextStepForTts() {
    if (_lessonSteps.isEmpty) return;

    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
        _displayText = '';
        _currentCharIndex = 0;
        _startLessonStepDisplay();
      } else {
        _lessonPlaying = false;
      }
    });
  }

  void _restartLesson() {
    setState(() {
      _currentLessonStepIndex = 0;
      _stopLessonStepDisplay();
      _startLessonStepDisplay();
    });
  }

  void _toggleLessonPlay() {
    setState(() {
      _lessonPlaying = !_lessonPlaying;
      if (_lessonPlaying) {
        _startLessonStepDisplay();
      } else {
        _pauseLessonStepDisplay();
      }
    });
  }

  void _pauseLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _pause();
    setState(() {
      _isTextAnimationActive = false;
    });
  }

// Update _startLessonStepDisplay
void _startLessonStepDisplay() async {
  _textAnimationTimer?.cancel();
  _currentCharIndex = 0;
  _displayText = '';

  final fullText = _lessonSteps[_currentLessonStepIndex]
      .replaceAll(RegExp(r'\[.*?\]'), '')
      .trim();

  // Start TTS and animation simultaneously
  final ttsStart = _speak(fullText);
  final animationStart = _startTextAnimation(fullText);

  await Future.wait([ttsStart, animationStart]);
}

Future<void> _startTextAnimation(String fullText) async {
  _isTextAnimationActive = true;
  for (_currentCharIndex = 0;
      _currentCharIndex < fullText.length;
      _currentCharIndex++) {
    if (!_isTextAnimationActive) break;
    setState(() => _displayText += fullText[_currentCharIndex]);
    await Future.delayed(Duration(milliseconds: (50 / _lessonSpeed).round()));
  }
  _isTextAnimationActive = false;
}

  void _startTtsForCurrentStep() {
    if (_lessonSteps.isNotEmpty &&
        _currentLessonStepIndex < _lessonSteps.length &&
        _lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);
    }
  }

  void _stopTts() {
    flutterTts.stop();
    setState(() => _lessonPlaying = false);
  }

  void _pauseTts() {
    flutterTts.pause();
    setState(() => _lessonPlaying = false);
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: generalTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildVisualMindmapView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Visual Mindmap',
                  style: GoogleFonts.notoSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: generalTextColor,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _exportToPdf(),
                      tooltip: 'Download as PDF',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: _geminiOutput == null
                  ? Text(
                      'No mindmap generated. Process content first.',
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Visual mindmap
                        Container(
                          height: 500, // Fixed height for the mindmap
                          decoration: BoxDecoration(
                            color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: VisualMindmap(
                            jsonData: _geminiOutput!,
                            isDarkMode: widget.isDarkMode,
                            fontSize: _lessonFontSize,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Raw JSON data (hidden by default)
                        ExpansionTile(
                          title: Text(
                            'View Raw Data',
                            style: GoogleFonts.notoSans(
                              color: generalTextColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: widget.isDarkMode ? Colors.black : Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Text(
                                  _geminiOutput!,
                                  style: GoogleFonts.jetBrainsMono(
                                    color: generalTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperimentView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Experiment/Lab',
                  style: GoogleFonts.notoSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: generalTextColor,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Font size controls
                    IconButton(
                      icon: Icon(Icons.text_decrease, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize > 12) _lessonFontSize -= 2;
                        });
                      },
                      tooltip: 'Decrease font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.text_increase, color: generalTextColor),
                      onPressed: () {
                        setState(() {
                          if (_lessonFontSize < 24) _lessonFontSize += 2;
                        });
                      },
                      tooltip: 'Increase font size',
                    ),
                    IconButton(
                      icon: Icon(Icons.download, color: generalTextColor),
                      onPressed: () => _exportToPdf(),
                      tooltip: 'Download as PDF',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: _geminiOutput == null
                  ? Text(
                      'No experiment generated. Process content first.',
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Experiment content
                        SingleChildScrollView(
                          child: _DashboardPageState.buildLatexContent(
                            _geminiOutput ?? 'No content generated',
                            widget.isDarkMode,
                            _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                            context,
                          ),
                        ),
                        const SizedBox(height: 24),
                        const Divider(thickness: 2),
                        const SizedBox(height: 16),
                        // Interactive simulation
                        Container(
                          decoration: BoxDecoration(
                            color: widget.isDarkMode ? Colors.grey[850] : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.primary.withOpacity(0.5),
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: ExperimentSimulation(
                            experimentData: _geminiOutput!,
                            isDarkMode: widget.isDarkMode,
                            fontSize: _lessonFontSize,
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // Duplicate _buildMindmapView method removed

  Widget _buildPodcastView(ThemeData theme, Color generalTextColor) {
    // Extract podcast title and description from the Gemini output
    String podcastTitle = 'Podcast';
    String podcastDescription = 'Audio content';

    // Try to extract title and description from the Gemini output
    if (_geminiOutput != null && _geminiOutput!.isNotEmpty) {
      final lines = _geminiOutput!.split('\n');

      // Look for title (usually the first line or after '# ' or 'Title: ')
      for (final line in lines) {
        if (line.startsWith('# ')) {
          podcastTitle = line.substring(2).trim();
          break;
        } else if (line.toLowerCase().startsWith('title:')) {
          podcastTitle = line.substring(6).trim();
          break;
        } else if (line.isNotEmpty && podcastTitle == 'Podcast') {
          // If no explicit title found, use the first non-empty line
          podcastTitle = line.trim();
          break;
        }
      }

      // Look for description (usually after 'Description: ' or the second paragraph)
      bool foundTitle = false;
      for (final line in lines) {
        if (line.toLowerCase().contains('description:')) {
          podcastDescription = line.split('description:')[1].trim();
          break;
        } else if (line.toLowerCase().contains('summary:')) {
          podcastDescription = line.split('summary:')[1].trim();
          break;
        } else if (foundTitle && line.isNotEmpty && podcastDescription == 'Audio content') {
          // If no explicit description found, use the first non-empty line after the title
          podcastDescription = line.trim();
          break;
        }

        // Mark when we've found the title to start looking for description
        if (line.contains(podcastTitle)) {
          foundTitle = true;
        }
      }
    }

    // Check if we have a generated podcast or an uploaded MP3 file
    bool hasGeneratedPodcast = _generatedPodcastAudio != null && _generatedPodcastAudio!.isNotEmpty;
    bool hasUploadedMP3 = _pickedFiles.any((f) => f.file.name.toLowerCase().endsWith('.mp3') && f.file.bytes != null);

    // If we don't have any audio yet, show a message and generate button
    if (!hasGeneratedPodcast && !hasUploadedMP3) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Podcast',
                    style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.download, color: generalTextColor),
                    onPressed: () => _exportToPdf(),
                    tooltip: 'Download script as PDF',
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Script content
              Container(
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: _DashboardPageState.buildLatexContent(
                    _geminiOutput ?? 'No podcast script generated',
                    widget.isDarkMode,
                    _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                    context,
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Generate podcast button
              Center(
                child: ElevatedButton.icon(
                  icon: Icon(Icons.music_note, color: widget.isDarkMode ? Colors.black : Colors.white),
                  label: Text(
                    'Generate Podcast Audio',
                    style: GoogleFonts.notoSans(color: widget.isDarkMode ? Colors.black : Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  ),
                  onPressed: _isGeneratingPodcast ? null : _generatePodcastAudio,
                ),
              ),

              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: Icon(Icons.copy, color: generalTextColor),
                    tooltip: 'Copy Script to Clipboard',
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: _geminiOutput ?? ''));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Script copied to clipboard')),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // Get the audio bytes - either from generated podcast or from uploaded MP3
    Uint8List audioBytes;
    if (hasGeneratedPodcast) {
      audioBytes = _generatedPodcastAudio!;
    } else {
      // Find the MP3 file
      final mp3File = _pickedFiles.firstWhere(
        (f) => f.file.name.toLowerCase().endsWith('.mp3') && f.file.bytes != null,
      );
      audioBytes = mp3File.file.bytes!;
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Podcast Player',
                  style: GoogleFonts.notoSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: generalTextColor,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.download, color: generalTextColor),
                  onPressed: () => _exportToPdf(),
                  tooltip: 'Download transcript as PDF',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Podcast player
            PodcastPlayer(
              audioBytes: audioBytes,
              title: podcastTitle,
              description: podcastDescription,
              isDarkMode: widget.isDarkMode,
            ),

            const SizedBox(height: 24),

            // Transcript/Script
            Text(
              hasGeneratedPodcast ? 'Script' : 'Transcript',
              style: GoogleFonts.notoSans(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: generalTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: _DashboardPageState.buildLatexContent(
                  _geminiOutput ?? 'No content available',
                  widget.isDarkMode,
                  _lessonFontSize > 0 ? _lessonFontSize : 16.0,
                  context,
                ),
              ),
            ),

            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // If we have a generated podcast, show a regenerate button
                if (hasGeneratedPodcast)
                  ElevatedButton.icon(
                    icon: Icon(Icons.refresh, color: widget.isDarkMode ? Colors.black : Colors.white),
                    label: Text(
                      'Regenerate Audio',
                      style: GoogleFonts.notoSans(color: widget.isDarkMode ? Colors.black : Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onPressed: _isGeneratingPodcast ? null : _generatePodcastAudio,
                  ),

                IconButton(
                  icon: Icon(Icons.copy, color: generalTextColor),
                  tooltip: 'Copy to Clipboard',
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: _geminiOutput ?? ''));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Content copied to clipboard')),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class FileWithPageRange {
  PlatformFile file;
  int? startPage;
  int? endPage;

  FileWithPageRange({required this.file, this.startPage, this.endPage});
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class ExamQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;

  ExamQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return FlipCard(
      front: Card(
        color: theme.cardColor,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: _DashboardPageState.buildLatexContent(
              flashcard.question,
              Theme.of(context).brightness == Brightness.dark,
              20.0,
              context,
            ),
          ),
        ),
      ),
      back: Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: _DashboardPageState.buildLatexContent(
              flashcard.answer.replaceAll(RegExp(r'^::\w*\s*'), '').replaceAll(RegExp(r'^:\s*'), ''), // Clean answer text and remove leading colon
              Theme.of(context).brightness == Brightness.dark,
              16.0,
              context,
            ),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: _DashboardPageState.buildLatexContent(
          message.text,
          isDarkMode,
          16.0, // Default font size for chat
          context,
        ),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final VoidCallback onGenerateNotes;
  final List<QuizQuestion> incorrectQuestions;
  final List<QuizQuestion> questions;
  final Color textColor;
  final Map<int, int?> userAnswers;

  const ExamResultsDialog({
    Key? key,
    required this.questions,
    required this.textColor,
    required this.incorrectQuestions,
    required this.onGenerateNotes,
    required this.userAnswers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int correctCount = 0;
    for (int i = 0; i < questions.length; i++) {
      if (userAnswers[i] == questions[i].correctAnswerIndex) {
        correctCount++;
      }
    }
    double totalQuestions = questions.length.toDouble();

    return AlertDialog(
      title: Text('Practice Exam Answer Key', style: GoogleFonts.notoSans(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: ${(correctCount / totalQuestions * 100).toStringAsFixed(1)}%',
                style: TextStyle(color: _getScoreColor(correctCount / totalQuestions), fontSize: 24)),
            const SizedBox(height: 20),
            Text('Answer Key:',
                style: GoogleFonts.notoSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...questions.asMap().entries.map((entry) {
              int index = entry.key;
              QuizQuestion question = entry.value;
              String correctAnswer = question.correctAnswerIndex != null && question.correctAnswerIndex! < question.options.length
                  ? question.options[question.correctAnswerIndex!]
                  : 'Unknown';
              String answerLetter = question.correctAnswerIndex != null ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!) : '?';
              bool isCorrect = userAnswers[index] == question.correctAnswerIndex;
              String userAnswerText = userAnswers[index] != null && userAnswers[index]! < question.options.length
                  ? question.options[userAnswers[index]!] : 'Not Answered';
              String userAnswerLetter = userAnswers[index] != null ? String.fromCharCode('A'.codeUnitAt(0) + userAnswers[index]!) : ' ';


              return ListTile(
                title: Text('${index + 1}. ${question.question}',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Correct answer: $answerLetter) $correctAnswer',
                        style: GoogleFonts.notoSans(color: Colors.green)),
                    Text('Your answer: $userAnswerLetter) $userAnswerText',
                        style: GoogleFonts.notoSans(color: isCorrect ? Colors.green : Colors.red)),
                    const Divider(),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: onGenerateNotes,
          child: Text('Generate Improvement Notes', style: GoogleFonts.notoSans(color: Colors.blue)),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }
}

Color _getScoreColor(double percentage) {
  if (percentage >= 0.9) return Colors.green;
  if (percentage >= 0.7) return Colors.orange;
  return Colors.red;
}
