import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'login_page.dart';
import 'rental_detail_page.dart';
import 'tertiary_rentals_page.dart';

class EquipmentRentalsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EquipmentRentalsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EquipmentRentalsPage> createState() => _EquipmentRentalsPageState();
}

class _EquipmentRentalsPageState extends State<EquipmentRentalsPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Rental> _equipmentRentals = [];
  List<Rental> _filteredRentals = [];
  TextEditingController _searchController = TextEditingController();
  String _selectedDepartment = 'All';
  List<String> _departments = ['All'];

  @override
  void initState() {
    super.initState();
    _fetchEquipmentRentals();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchEquipmentRentals() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rentals';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .eq('equipmentrental', true)
          .order('fullname', ascending: true);

      final List<Rental> rentals = List<Map<String, dynamic>>.from(response)
          .map((json) => Rental.fromJson(json))
          .toList();
      
      // Extract unique departments for filters
      final Set<String> departments = {'All'};
      for (var rental in rentals) {
        if (rental.department.isNotEmpty) {
          departments.add(rental.department);
        }
      }

      setState(() {
        _equipmentRentals = rentals;
        _filteredRentals = List.from(rentals);
        _departments = departments.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading equipment rentals: $e';
      });
      print('Error fetching equipment rentals: $e');
    }
  }

  void _filterRentals() {
    final String searchQuery = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredRentals = _equipmentRentals.where((rental) {
        // Filter by department
        if (_selectedDepartment != 'All' && rental.department != _selectedDepartment) {
          return false;
        }
        
        // Filter by search query
        if (searchQuery.isNotEmpty) {
          return rental.fullname.toLowerCase().contains(searchQuery) ||
                 rental.about.toLowerCase().contains(searchQuery) ||
                 rental.department.toLowerCase().contains(searchQuery) ||
                 rental.dimensions.toLowerCase().contains(searchQuery) ||
                 rental.payment.toLowerCase().contains(searchQuery) ||
                 rental.pricing.toLowerCase().contains(searchQuery);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Equipment Rentals',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchEquipmentRentals,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search equipment...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _filterRentals();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    _filterRentals();
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Department filter
                if (_departments.length > 1) ...[
                  Text(
                    'Department:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _departments.map((department) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(department),
                            selected: _selectedDepartment == department,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedDepartment = department;
                                  _filterRentals();
                                });
                              }
                            },
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: Colors.white,
                            labelStyle: TextStyle(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                            side: BorderSide(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Equipment rentals list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchEquipmentRentals,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredRentals.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.business_outlined,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _equipmentRentals.isEmpty
                                      ? 'No equipment rentals available'
                                      : 'No equipment matches your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredRentals.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            itemBuilder: (context, index) {
                              final rental = _filteredRentals[index];
                              return Card(
                                margin: const EdgeInsets.only(bottom: 12.0),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.all(16.0),
                                  leading: CircleAvatar(
                                    backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                    child: Icon(
                                      Icons.devices,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  title: Text(
                                    rental.fullname,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 8),
                                      if (rental.dimensions.isNotEmpty && rental.dimensions != 'Not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.straighten, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Specifications: ${rental.dimensions}'),
                                            ],
                                          ),
                                        ),
                                      if (rental.pricing.isNotEmpty && rental.pricing != 'Not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.attach_money, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Pricing: ${rental.pricing}'),
                                            ],
                                          ),
                                        ),
                                      if (rental.department.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.business, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Department: ${rental.department}'),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                  trailing: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16,
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => RentalDetailPage(
                                          rental: rental,
                                          institutionName: widget.institutionName,
                                          isDarkMode: currentIsDarkMode,
                                          toggleTheme: widget.toggleTheme,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
