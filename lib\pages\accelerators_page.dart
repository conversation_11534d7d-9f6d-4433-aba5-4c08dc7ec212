// accelerators_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'accelerator_detail_page.dart';

class AcceleratorsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedAccelerators;
  final bool isFromDetailPage;

  const AcceleratorsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedAccelerators,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<AcceleratorsPage> createState() => _AcceleratorsPageState();
}

class _AcceleratorsPageState extends State<AcceleratorsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('accelerators_list');
  List<Map<String, dynamic>> _accelerators = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AcceleratorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AcceleratorsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AcceleratorsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AcceleratorsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AcceleratorsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAccelerators != null && widget.preloadedAccelerators!.isNotEmpty) {
      print("Preloaded accelerators found, using them.");
      setState(() {
        _accelerators = List<Map<String, dynamic>>.from(widget.preloadedAccelerators!);
        _accelerators.forEach((accelerator) {
          accelerator['_isImageLoading'] = false;
        });
        _accelerators.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAccelerators!.length == _pageSize;
      });
      _loadAcceleratorsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded accelerators or empty list, loading from database.");
      _loadAcceleratorsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadAcceleratorsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadAcceleratorsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final acceleratorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accelerators';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(acceleratorsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedAccelerators =
          await _updateAcceleratorImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _accelerators = updatedAccelerators;
        } else {
          _accelerators.addAll(updatedAccelerators);
        }
        _accelerators.forEach((accelerator) {
          accelerator['_isImageLoading'] = false;
        });
        _accelerators.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the accelerators
      _cacheAccelerators(_accelerators);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching accelerators: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateAcceleratorImageUrls(
      List<Map<String, dynamic>> accelerators) async {
    List<Future<void>> futures = [];
    for (final accelerator in accelerators) {
      if (accelerator['image_url'] == null ||
          accelerator['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(accelerator));
      }
    }
    await Future.wait(futures);
    return accelerators;
  }

  Future<void> _cacheAccelerators(List<Map<String, dynamic>> accelerators) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String acceleratorsJson = jsonEncode(accelerators);
      await prefs.setString(
          'accelerators_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          acceleratorsJson);
    } catch (e) {
      print('Error caching accelerators: $e');
    }
  }
  
  void _setupRealtime() {
    final acceleratorsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_accelerators';
    _realtimeChannel = Supabase.instance.client
        .channel('accelerators')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: acceleratorsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAcceleratorId = payload.newRecord['id'];
          final newAcceleratorResponse = await Supabase.instance.client
              .from(acceleratorsTableName)
              .select('*')
              .eq('id', newAcceleratorId)
              .single();
          if (mounted) {
            Map<String, dynamic> newAccelerator = Map.from(newAcceleratorResponse);
            final updatedAccelerator = await _updateAcceleratorImageUrls([newAccelerator]);
            setState(() {
              _accelerators = [..._accelerators, updatedAccelerator.first];
              updatedAccelerator.first['_isImageLoading'] = false;
              _accelerators.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAcceleratorId = payload.newRecord['id'];
          final updatedAcceleratorResponse = await Supabase.instance.client
              .from(acceleratorsTableName)
              .select('*')
              .eq('id', updatedAcceleratorId)
              .single();
          if (mounted) {
            final updatedAccelerator = Map<String, dynamic>.from(updatedAcceleratorResponse);
            setState(() {
              _accelerators = _accelerators.map((accelerator) {
                return accelerator['id'] == updatedAccelerator['id'] ? updatedAccelerator : accelerator;
              }).toList();
              _accelerators.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAcceleratorId = payload.oldRecord['id'];
          setState(() {
            _accelerators.removeWhere((accelerator) => accelerator['id'] == deletedAcceleratorId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreAccelerators();
    }
  }

  Future<void> _loadMoreAccelerators() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadAcceleratorsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> accelerator) async {
    if (accelerator['_isImageLoading'] == true) {
      print('Image loading already in progress for ${accelerator['fullname']}, skipping.');
      return;
    }
    if (accelerator['image_url'] != null &&
        accelerator['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${accelerator['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      accelerator['_isImageLoading'] = true;
    });

    final fullname = accelerator['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeAcceleratorBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/accelerators';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeAcceleratorBucket');
    print('Image URL before fetch: ${accelerator['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeAcceleratorBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeAcceleratorBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        accelerator['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        accelerator['_isImageLoading'] = false;
        print('Setting image_url for ${accelerator['fullname']} to: ${accelerator['image_url']}');
      });
    } else {
      accelerator['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> accelerator) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AcceleratorDetailPage(
            accelerator: accelerator,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("AcceleratorsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Accelerators',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _accelerators.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadAcceleratorsFromSupabase(initialLoad: true),
              child: _accelerators.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No accelerators available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _accelerators.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _accelerators.length) {
                          final accelerator = _accelerators[index];
                          final phone = accelerator['phone'] as String? ?? '';
                          final email = accelerator['email'] as String? ?? '';
                          final whatsapp = accelerator['whatsapp'] as String? ?? '';
                          final building = accelerator['building'] as String? ?? '';
                          final room = accelerator['room'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('accelerator_${accelerator['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (accelerator['image_url'] == null ||
                                      accelerator['image_url'] == 'assets/placeholder_image.png') &&
                                  !accelerator['_isImageLoading']) {
                                _fetchImageUrl(accelerator);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: accelerator['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      accelerator['fullname'] ?? 'Unnamed Accelerator',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        if (building.isNotEmpty || room.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.only(bottom: 4),
                                            child: Text(
                                              [
                                                if (building.isNotEmpty) building,
                                                if (room.isNotEmpty) 'Room $room',
                                              ].join(', '),
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: theme.colorScheme.secondary,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        Text(
                                          accelerator['about'] ?? '',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: theme.colorScheme.secondary,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                      ],
                                    ),
                                    onTap: () => _navigateToDetail(context, accelerator),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                          if (whatsapp.isNotEmpty)
                                            IconButton(
                                              icon: FaIcon(
                                                FontAwesomeIcons.whatsapp,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchWhatsapp(whatsapp),
                                              tooltip: 'WhatsApp',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
