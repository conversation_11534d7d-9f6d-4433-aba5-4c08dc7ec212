import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:math';

class AreaMeasurePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AreaMeasurePage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<AreaMeasurePage> createState() => _AreaMeasurePageState();
}

class _AreaMeasurePageState extends State<AreaMeasurePage> {
  final MapController mapController = MapController();
  List<LatLng> _points = [];
  double _calculatedArea = 0.0;
  double _calculatedDistance = 0.0;
  LatLng? _currentLocation;
  bool _isCapturing = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Location services are disabled.')));
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Location permissions are denied')));
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Location permissions are permanently denied, we cannot request permissions.')));
      return;
    }

    try {
      Position position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
        mapController.move(_currentLocation!, 16.0);
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error getting location: ${e.toString()}')));
    }
  }

  void _handleTap(TapPosition tapPosition, LatLng latlng) {
    if (_isCapturing) {
      setState(() {
        _points.add(latlng);
        _calculateArea();
        _calculateDistance();
      });
    }
  }

  void _calculateArea() {
    _calculatedArea = 0.0;
    if (_points.length < 3) return;

    double area = 0.0;
    int n = _points.length;
    for (int i = 0; i < n; i++) {
      final p1 = _points[i];
      final p2 = _points[(i + 1) % n];
      area += (p1.latitude * p2.longitude - p2.latitude * p1.longitude);
    }
    _calculatedArea = area.abs() / 2;

    // Approximate conversion to square meters (very rough)
    const earthRadius = 6371000; // Earth's radius in meters
    double latAverage = _points.map((p) => p.latitude).reduce((a, b) => a + b) / _points.length;
    double meterPerDegree = pi * earthRadius / 180; // More accurate meters per degree
    _calculatedArea *= meterPerDegree * meterPerDegree * pow(cos(latAverage * pi / 180), 2);
  }

  void _calculateDistance() {
    _calculatedDistance = 0.0;
    if (_points.length < 2) return;

    final Distance distance = Distance();
    for (int i = 0; i < _points.length - 1; i++) {
      _calculatedDistance += distance(
        _points[i],
        _points[i + 1],
      );
    }
  }

  void _startCapture() {
    setState(() {
      _isCapturing = true;
      _points.clear();
      _calculatedArea = 0.0;
      _calculatedDistance = 0.0;
    });
  }

  void _stopCapture() {
    setState(() {
      _isCapturing = false;
      _calculateArea();
      _calculateDistance();
    });
  }

  void _clearPoints() {
    setState(() {
      _isCapturing = false;
      _points.clear();
      _calculatedArea = 0.0;
      _calculatedDistance = 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Area Measure',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode, color: theme.colorScheme.onSurface),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Stack(
          children: [
            Card(
              color: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              child: FlutterMap(
                mapController: mapController,
                options: MapOptions(
                  initialCenter: _currentLocation ?? const LatLng(0, 0),
                  initialZoom: 13.0,
                  onTap: _isCapturing ? _handleTap : null,
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.example.app',
                  ),
                  if (_points.isNotEmpty)
                    PolygonLayer(
                      polygons: [
                        Polygon(
                          points: _points,
                          color: Colors.green.withOpacity(0.5),
                          borderColor: Colors.green,
                          borderStrokeWidth: 3,
                        ),
                      ],
                    ),
                  MarkerLayer(
                    markers: _points
                        .map((point) => Marker(
                              point: point,
                              width: 20,
                              height: 20,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 1),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('Number of Points: ${_points.length}', style: TextStyle(color: theme.colorScheme.onSurface)),
                      if (_points.length > 2)
                        Text('Estimated Area: ${_calculatedArea.toStringAsFixed(2)} sq meters', style: TextStyle(color: theme.colorScheme.onSurface)),
                      if (_points.length > 1)
                        Text('Estimated Distance: ${_calculatedDistance.toStringAsFixed(2)} meters', style: TextStyle(color: theme.colorScheme.onSurface)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (!_isCapturing)
            FloatingActionButton(
              onPressed: _startCapture,
              tooltip: 'Start Capture',
              heroTag: 'start',
              backgroundColor: widget.isDarkMode ? Colors.black : theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.onSurface,
              shape: const CircleBorder(),
              child: const Icon(Icons.play_arrow),
            ),
          if (_isCapturing)
            FloatingActionButton(
              onPressed: _stopCapture,
              tooltip: 'Stop Capture',
              heroTag: 'stop',
              backgroundColor: widget.isDarkMode ? Colors.black : theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.onSurface,
              shape: const CircleBorder(),
              child: const Icon(Icons.stop),
            ),
          const SizedBox(height: 16),
          FloatingActionButton(
            onPressed: _clearPoints,
            tooltip: 'Clear Points',
            heroTag: 'clear',
            backgroundColor: widget.isDarkMode ? Colors.black : theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.onSurface,
            shape: const CircleBorder(),
            child: const Icon(Icons.clear),
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}