// thesis_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class ThesisDetailPage extends StatefulWidget {
  final Map<String, dynamic> thesis;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ThesisDetailPage({
    Key? key,
    required this.thesis,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ThesisDetailPage> createState() => _ThesisDetailPageState();
}

class _ThesisDetailPageState extends State<ThesisDetailPage> {
  late RealtimeChannel _thesisRealtimeChannel; // Realtime channel for thesis updates

  @override
  void initState() {
    super.initState();
    _setupThesisRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _thesisRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupThesisRealtimeListener() {
    final thesesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
    _thesisRealtimeChannel = Supabase.instance.client
        .channel('thesis_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: thesesTableName,
          callback: (payload) {
            // Check if this change is for the current thesis
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.thesis['id']) {
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedThesisData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the thesis is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedThesisData() async {
    try {
      final thesesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
      final response = await Supabase.instance.client
          .from(thesesTableName)
          .select('*')
          .eq('id', widget.thesis['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedThesis = Map.from(response);
        // Update the widget.thesis with the new data
        setState(() {
          widget.thesis.clear(); // Clear old data
          widget.thesis.addAll(updatedThesis); // Add updated data
          print("Thesis data updated in detail page for ${widget.thesis['fullname']}");
          _updateThesesCache(updatedThesis); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated thesis data: $e');
    }
  }

  Future<void> _updateThesesCache(Map<String, dynamic> updatedThesis) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'theses_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedTheses = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific thesis in the cache
        bool found = false;
        for (var thesis in cachedTheses) {
          if (thesis['id'] == updatedThesis['id']) {
            updatedCache.add(updatedThesis);
            found = true;
          } else {
            updatedCache.add(Map<String, dynamic>.from(thesis));
          }
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
      }
    } catch (e) {
      print('Error updating theses cache: $e');
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = widget.thesis['fullname'] as String? ?? 'Thesis';
    final String year = widget.thesis['year']?.toString() ?? 'N/A';
    final String author = widget.thesis['author'] as String? ?? '';
    final String advisor = widget.thesis['advisor'] as String? ?? '';
    final String advisor2 = widget.thesis['advisor2'] as String? ?? '';
    final String department = widget.thesis['department'] as String? ?? '';
    final String career = widget.thesis['career'] as String? ?? '';
    final String about = widget.thesis['about'] as String? ?? '';
    final String link = widget.thesis['link'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Thesis Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Year: $year',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Author:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        author,
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Advisor(s):',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (advisor.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            advisor,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (advisor2.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            advisor2,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        'Department: $department',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (career.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Career: $career',
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            if (about.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Abstract',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (link.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Link',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.primary,
                                  decoration: TextDecoration.underline,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _copyToClipboard(link),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _launchURL(link),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
