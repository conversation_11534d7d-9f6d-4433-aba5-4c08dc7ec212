import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'tertiary_rentals_page.dart';

class RentalCalendarEntry {
  final int id;
  final String asset;
  final String tagoridentifier;
  final String department;
  final String renter;
  final DateTime checkoutDate;
  final DateTime returnDate;

  RentalCalendarEntry({
    required this.id,
    required this.asset,
    required this.tagoridentifier,
    required this.department,
    required this.renter,
    required this.checkoutDate,
    required this.returnDate,
  });

  factory RentalCalendarEntry.fromJson(Map<String, dynamic> json) {
    return RentalCalendarEntry(
      id: json['id'] ?? 0,
      asset: json['asset'] ?? '',
      tagoridentifier: json['tagoridentifier'] ?? '',
      department: json['department'] ?? '',
      renter: json['renter'] ?? '',
      checkoutDate: DateTime(
        json['checkoutyear'] ?? DateTime.now().year,
        json['checkoutmonth'] ?? DateTime.now().month,
        json['checkoutday'] ?? DateTime.now().day,
      ),
      returnDate: DateTime(
        json['returnyear'] ?? DateTime.now().year,
        json['returnmonth'] ?? DateTime.now().month,
        json['returnday'] ?? DateTime.now().day,
      ),
    );
  }

  bool isRentedOn(DateTime date) {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    final normalizedCheckout = DateTime(checkoutDate.year, checkoutDate.month, checkoutDate.day);
    final normalizedReturn = DateTime(returnDate.year, returnDate.month, returnDate.day);

    return normalizedDate.compareTo(normalizedCheckout) >= 0 &&
           normalizedDate.compareTo(normalizedReturn) <= 0;
  }
}

class RentalDetailPage extends StatefulWidget {
  final Rental rental;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const RentalDetailPage({
    Key? key,
    required this.rental,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<RentalDetailPage> createState() => _RentalDetailPageState();
}

class _RentalDetailPageState extends State<RentalDetailPage> {
  bool _isLoadingCalendar = true;
  String _calendarErrorMessage = '';
  List<RentalCalendarEntry> _calendarEntries = [];
  Map<DateTime, List<RentalCalendarEntry>> _events = {};
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  List<RentalCalendarEntry> _selectedEvents = [];

  @override
  void initState() {
    super.initState();
    _fetchRentalCalendar();
  }

  Future<void> _fetchRentalCalendar() async {
    setState(() {
      _isLoadingCalendar = true;
      _calendarErrorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_rentailequipmentcalendar';

      // For facility rentals, filter by department
      // For equipment rentals, filter by asset name or tag/identifier
      final query = Supabase.instance.client
          .from(tableName)
          .select('*');

      if (widget.rental.facilityrental) {
        // For facilities, show all rentals in the same department
        if (widget.rental.department.isNotEmpty) {
          query.eq('department', widget.rental.department);
        }
      } else {
        // For equipment, show rentals for this specific item
        query.or('asset.eq.${widget.rental.fullname},tagoridentifier.eq.${widget.rental.fullname}');
      }

      final response = await query.order('checkoutyear', ascending: true);

      final List<RentalCalendarEntry> entries = List<Map<String, dynamic>>.from(response)
          .map((json) => RentalCalendarEntry.fromJson(json))
          .toList();

      // Convert entries to events map for the calendar
      final Map<DateTime, List<RentalCalendarEntry>> events = {};

      for (var entry in entries) {
        // Add entry to each day between checkout and return
        DateTime currentDate = entry.checkoutDate;
        while (currentDate.isBefore(entry.returnDate) ||
               currentDate.isAtSameMomentAs(entry.returnDate)) {
          final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
          if (events[key] == null) {
            events[key] = [];
          }
          events[key]!.add(entry);

          // Move to next day
          currentDate = currentDate.add(const Duration(days: 1));
        }
      }

      setState(() {
        _calendarEntries = entries;
        _events = events;
        _selectedEvents = _getEventsForDay(_selectedDay);
        _isLoadingCalendar = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCalendar = false;
        _calendarErrorMessage = 'Error loading rental calendar: $e';
      });
      print('Error fetching rental calendar: $e');
    }
  }

  List<RentalCalendarEntry> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Determine if this is a facility or equipment rental
    final bool isFacility = widget.rental.facilityrental;
    final String rentalType = isFacility ? 'Facility' : 'Equipment';
    final IconData typeIcon = isFacility ? Icons.home_work : Icons.devices;

    // Check if location coordinates are available
    final bool hasLocation = widget.rental.latitude != null &&
                            widget.rental.longitude != null &&
                            widget.rental.latitude != 0.0 &&
                            widget.rental.longitude != 0.0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.rental.fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Rental header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: theme.colorScheme.primary.withOpacity(0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(typeIcon, size: 32, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          widget.rental.fullname,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$rentalType Rental',
                    style: TextStyle(
                      fontSize: 16,
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  if (widget.rental.department.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        'Department: ${widget.rental.department}',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Rental details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widget.rental.dimensions.isNotEmpty && widget.rental.dimensions != 'Not specified')
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(Icons.straighten,
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          isFacility ? 'Dimensions:' : 'Specifications:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                        Text(
                                          widget.rental.dimensions,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (isFacility && widget.rental.capacity > 0)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(Icons.people,
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Capacity:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                        Text(
                                          '${widget.rental.capacity} people',
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (widget.rental.pricing.isNotEmpty && widget.rental.pricing != 'Not specified')
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(Icons.attach_money,
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Pricing:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                        Text(
                                          widget.rental.pricing,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (widget.rental.payment.isNotEmpty && widget.rental.payment != 'Not specified')
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(Icons.payment,
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Payment Information:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                        Text(
                                          widget.rental.payment,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Contact information
            if (widget.rental.phone.isNotEmpty || widget.rental.whatsapp.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            if (widget.rental.phone.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.phone,
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  widget.rental.phone,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('tel:${widget.rental.phone}'),
                              ),
                            if (widget.rental.whatsapp.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.message,
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  'WhatsApp: ${widget.rental.whatsapp}',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('https://wa.me/${widget.rental.whatsapp}'),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // About/Description
            if (widget.rental.about.isNotEmpty && widget.rental.about != 'No description available')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          widget.rental.about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Availability Calendar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Availability Calendar',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.refresh,
                          color: theme.colorScheme.primary,
                        ),
                        onPressed: _fetchRentalCalendar,
                        tooltip: 'Refresh calendar',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _isLoadingCalendar
                      ? const Center(child: CircularProgressIndicator())
                      : _calendarErrorMessage.isNotEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.error_outline, size: 32, color: Colors.red),
                                  const SizedBox(height: 8),
                                  Text(
                                    _calendarErrorMessage,
                                    style: TextStyle(color: theme.colorScheme.error),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: _fetchRentalCalendar,
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            )
                          : Column(
                              children: [
                                Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: TableCalendar(
                                      firstDay: DateTime.utc(2020, 1, 1),
                                      lastDay: DateTime.utc(2030, 12, 31),
                                      focusedDay: _focusedDay,
                                      calendarFormat: _calendarFormat,
                                      eventLoader: _getEventsForDay,
                                      selectedDayPredicate: (day) {
                                        return isSameDay(_selectedDay, day);
                                      },
                                      onDaySelected: _onDaySelected,
                                      onFormatChanged: (format) {
                                        setState(() {
                                          _calendarFormat = format;
                                        });
                                      },
                                      onPageChanged: (focusedDay) {
                                        _focusedDay = focusedDay;
                                      },
                                      calendarStyle: CalendarStyle(
                                        markersMaxCount: 3,
                                        markerDecoration: BoxDecoration(
                                          color: theme.colorScheme.primary,
                                          shape: BoxShape.circle,
                                        ),
                                        todayDecoration: BoxDecoration(
                                          color: theme.colorScheme.primary.withOpacity(0.5),
                                          shape: BoxShape.circle,
                                        ),
                                        selectedDecoration: BoxDecoration(
                                          color: theme.colorScheme.primary,
                                          shape: BoxShape.circle,
                                        ),
                                        // Mark days with rentals
                                        markerSize: 6.0,
                                        markersAnchor: 1.0,
                                      ),
                                      headerStyle: HeaderStyle(
                                        formatButtonVisible: true,
                                        titleCentered: true,
                                        formatButtonShowsNext: false,
                                        formatButtonDecoration: BoxDecoration(
                                          color: theme.colorScheme.surface,
                                          borderRadius: BorderRadius.circular(16.0),
                                        ),
                                        formatButtonTextStyle: TextStyle(
                                          color: theme.colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                // Selected day events
                                if (_selectedEvents.isNotEmpty) ...[
                                  const SizedBox(height: 16),
                                  Text(
                                    'Rentals on ${DateFormat('MMMM d, yyyy').format(_selectedDay)}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  ...List.generate(_selectedEvents.length, (index) {
                                    final event = _selectedEvents[index];
                                    return Card(
                                      margin: const EdgeInsets.only(bottom: 8.0),
                                      child: ListTile(
                                        title: Text(
                                          event.asset,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: theme.colorScheme.onSurface,
                                          ),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            if (event.tagoridentifier.isNotEmpty)
                                              Text('ID: ${event.tagoridentifier}'),
                                            Text('Renter: ${event.renter}'),
                                            Text(
                                              'Period: ${DateFormat('MMM d').format(event.checkoutDate)} - ${DateFormat('MMM d, yyyy').format(event.returnDate)}',
                                            ),
                                          ],
                                        ),
                                        isThreeLine: true,
                                      ),
                                    );
                                  }),
                                ] else ...[
                                  const SizedBox(height: 16),
                                  Center(
                                    child: Text(
                                      'No rentals on ${DateFormat('MMMM d, yyyy').format(_selectedDay)}',
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                ],
              ),
            ),

            // Map (for facilities with location)
            if (isFacility && hasLocation)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: FlutterMap(
                          options: MapOptions(
                            initialCenter: LatLng(widget.rental.latitude!, widget.rental.longitude!),
                            initialZoom: 15.0,
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.harmonizr.app',
                            ),
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: LatLng(widget.rental.latitude!, widget.rental.longitude!),
                                  width: 40,
                                  height: 40,
                                  child: Icon(
                                    Icons.location_on,
                                    color: theme.colorScheme.primary,
                                    size: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
