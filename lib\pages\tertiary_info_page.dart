import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../main.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryInfoPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String schoolName;
  final Map<String, dynamic> collegeData;
  final bool isFromDetailPage;

  const TertiaryInfoPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.schoolName,
    required this.collegeData,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _TertiaryInfoPageState createState() => _TertiaryInfoPageState();
}

class _TertiaryInfoPageState extends _TertiaryInfoPageStateBase { // Changed to inherit from _TertiaryInfoPageStateBase
  _TertiaryInfoPageState();
}

class _TertiaryInfoPageStateBase extends State<TertiaryInfoPage> { // Created a base class to hold state and methods
  Map<String, String?> _cachedData = {};
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late bool showGridItem;

  @override
  void initState() {
    super.initState();
    showGridItem = !widget.isFromDetailPage;
    _loadCachedData();
    _loadImage();
    print('College Data in TertiaryInfoPage: ${widget.collegeData}');
  }

  Future<void> _loadImage() async {
    setState(() {
      _isLoadingImage = true;
    });
    String imageUrl = await _getCollegeImageUrl(widget.collegeData['fullname']);
    setState(() {
      _imageUrl = imageUrl;
      _isLoadingImage = false;
    });
  }

  Future<void> _loadCachedData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _cachedData = {
        'about': prefs.getString('about_${widget.collegeData['id']}'),
        'vision': prefs.getString('vision_${widget.collegeData['id']}'),
        'mission': prefs.getString('mission_${widget.collegeData['id']}'),
        'address': prefs.getString('address_${widget.collegeData['id']}'),
        'daysnhours': prefs.getString('daysnhours_${widget.collegeData['id']}'),
        'postaladdress': prefs.getString('postaladdress_${widget.collegeData['id']}'),
        'corevalues': prefs.getString('corevalues_${widget.collegeData['id']}'),
        'motto': prefs.getString('motto_${widget.collegeData['id']}'),
        'goals': prefs.getString('goals_${widget.collegeData['id']}'),
        'mandate': prefs.getString('mandate_${widget.collegeData['id']}'),
        'founded': prefs.getString('founded_${widget.collegeData['id']}'),
        'accreditation': prefs.getString('accreditation_${widget.collegeData['id']}'),
        'freewifi': prefs.getString('freewifi_${widget.collegeData['id']}'),
        'objectives': prefs.getString('objectives_${widget.collegeData['id']}'),
        'aims': prefs.getString('aims_${widget.collegeData['id']}'),
        'pledge': prefs.getString('pledge_${widget.collegeData['id']}'),
        'statementoffaith': prefs.getString('statementoffaith_${widget.collegeData['id']}'),
        'religiousaffiliation': prefs.getString('religiousaffiliation_${widget.collegeData['id']}'),
        'whychooseus': prefs.getString('whychooseus_${widget.collegeData['id']}'),
        'institutiontype': prefs.getString('institutiontype_${widget.collegeData['id']}'),
        'campussetting': prefs.getString('campussetting_${widget.collegeData['id']}'),
        'highestqualificationoffered': prefs.getString('highestqualificationoffered_${widget.collegeData['id']}'),
        'studentpopulation': prefs.getString('studentpopulation_${widget.collegeData['id']}'),
        'academicyearcalendar': prefs.getString('academicyearcalendar_${widget.collegeData['id']}'),
        'website': prefs.getString('website_${widget.collegeData['id']}'),
      };
    });
  }

  String? _getPreloadedValue(String key) {
    if (_cachedData.containsKey(key) && _cachedData[key] != null) {
      return _cachedData[key];
    }
    switch (key) {
      case 'about':
        return widget.collegeData['about']?.toString();
      case 'address':
        return widget.collegeData['address']?.toString();
      case 'daysnhours':
        return widget.collegeData['daysnhours']?.toString();
      case 'postaladdress':
        return widget.collegeData['postaladdress']?.toString();
      case 'mission':
        return widget.collegeData['mission']?.toString();
      case 'vision':
        return widget.collegeData['vision']?.toString();
      case 'corevalues':
        return widget.collegeData['corevalues']?.toString();
      case 'motto':
        return widget.collegeData['motto']?.toString();
      case 'goals':
        return widget.collegeData['goals']?.toString();
      case 'mandate':
        return widget.collegeData['mandate']?.toString();
      case 'founded':
        return widget.collegeData['founded']?.toString();
      case 'accreditation':
        return widget.collegeData['accreditation']?.toString();
      case 'freewifi':
        return widget.collegeData['freewifi']?.toString();
      case 'objectives':
        return widget.collegeData['objectives']?.toString();
      case 'aims':
        return widget.collegeData['aims']?.toString();
      case 'pledge':
        return widget.collegeData['pledge']?.toString();
      case 'statementoffaith':
        return widget.collegeData['statementoffaith']?.toString();
      case 'religiousaffiliation':
        return widget.collegeData['religiousaffiliation']?.toString();
      case 'whychooseus':
        return widget.collegeData['whychooseus']?.toString();
      case 'institutiontype':
        return widget.collegeData['institutiontype']?.toString();
      case 'campussetting':
        return widget.collegeData['campussetting']?.toString();
      case 'highestqualificationoffered':
        return widget.collegeData['highestqualificationoffered']?.toString();
      case 'studentpopulation':
        return widget.collegeData['studentpopulation']?.toString();
      case 'academicyearcalendar':
        return widget.collegeData['academicyearcalendar']?.toString();
      case 'website':
        return widget.collegeData['website']?.toString();
      default:
        return null;
    }
  }

  Future<void> _launchWebsiteUrl(String urlString) async {
    if (urlString.isNotEmpty) {
      final Uri uri = Uri.parse(urlString);
      if (await canLaunchUrl(uri)) {
        launchUrl(uri);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch website.')),
        );
      }
    }
  }


  Future<String> _getCollegeImageUrl(String? fullname) async {
    if (fullname == null || fullname.isEmpty) {
      return 'assets/placeholder_image.png';
    }
    if (widget.collegeData.containsKey('image_url') &&
        widget.collegeData['image_url'] != null) {
      return widget.collegeData['image_url'];
    }
    return 'assets/placeholder_image.png';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final String? collegeFullname = widget.collegeData['fullname'];
    bool isFromDetailPage = widget.isFromDetailPage;

    final Map<String, IconData> fieldsToShow = {
      'about': Icons.info_outline,
      'address': Icons.location_on,
      'daysnhours': Icons.calendar_today,
      'postaladdress': Icons.mail_outline,
      'mission': Icons.flag_outlined,
      'vision': Icons.remove_red_eye_outlined,
      'corevalues': Icons.verified_user_outlined,
      'motto': Icons.format_quote,
      'goals': Icons.check_circle_outline,
      'mandate': Icons.assignment_outlined,
      'founded': Icons.history,
      'accreditation': Icons.verified,
      'freewifi': Icons.wifi,
      'objectives': Icons.track_changes_outlined,
      'aims': Icons.flag,
      'pledge': Icons.thumb_up_alt_outlined,
      'statementoffaith': Icons.menu_book_outlined,
      'religiousaffiliation': Icons.account_balance,
      'whychooseus': Icons.lightbulb_outline,
      'institutiontype': Icons.school,
      'campussetting': Icons.map,
      'highestqualificationoffered': Icons.grade,
      'studentpopulation': Icons.people_alt,
      'academicyearcalendar': Icons.event,
      'website': Icons.language,
    };

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.collegeData['shortname'] ?? widget.schoolName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Center(
                    child: SizedBox(
                      width: double.infinity,
                      height: 250,
                      child: CachedNetworkImage(
                        imageUrl: _imageUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) {
                          final screenWidth = MediaQuery.of(context).size.width;
                          return Center(
                            child: screenWidth >= 600
                                ? SizedBox(
                                    width: 200,
                                    height: 200,
                                    child: Image.asset(
                                      'assets/placeholder_image.png',
                                      fit: BoxFit.contain,
                                    ),
                                  )
                                : Image.asset(
                                    'assets/placeholder_image.png',
                                    fit: BoxFit.contain,
                                  ),
                          );
                        },
                        errorWidget: (context, url, error) {
                          final screenWidth = MediaQuery.of(context).size.width;
                          return Center(
                            child: screenWidth >= 600
                                ? SizedBox(
                                    width: 200,
                                    height: 200,
                                    child: Image.asset(
                                      'assets/placeholder_image.png',
                                      fit: BoxFit.contain,
                                    ),
                                  )
                                : Image.asset(
                                    'assets/placeholder_image.png',
                                    fit: BoxFit.contain,
                                  ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: fieldsToShow.entries.map<Widget>((entry) {
                      final key = entry.key;
                      final value = _getPreloadedValue(key);
                      final icon = entry.value;

                      if (value == null || value.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      return _buildDetailRow(theme, icon, key, value);
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String titleKey, String value) {
    String title = _capitalizeWords(titleKey);
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                if (titleKey == 'website' && value.startsWith('http'))
                  InkWell(
                    onTap: () => _launchWebsiteUrl(value), // Call _launchWebsiteUrl here
                    child: Text(
                      value,
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  )
                else
                  Text(
                    value,
                    style: TextStyle(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  String _capitalizeWords(String text) {
    return text.replaceAllMapped(RegExp(r'\b\w+\b'), (match) {
      final word = match.group(0)!;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    });
  }
}