// articles_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'article_detail_page.dart';

class ArticlesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedArticles;
  final bool isFromDetailPage;

  const ArticlesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedArticles,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ArticlesPage> createState() => _ArticlesPageState();
}

class _ArticlesPageState extends State<ArticlesPage> {
  List<Map<String, dynamic>> _articles = [];
  List<Map<String, dynamic>> _filteredArticles = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _articlesChannel;

  @override
  void initState() {
    super.initState();
    _loadArticles();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _articlesChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterArticles();
    });
  }

  void _filterArticles() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredArticles = List.from(_articles);
      return;
    }

    _filteredArticles = _articles.where((article) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          article['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (article['author']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (article['author2']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (article['author3']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (article['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Faculty/Staff') {
          matchesFilter = article['facultyorstaffarticle'] == true;
        } else if (_selectedFilter == 'Student') {
          matchesFilter = article['studentarticle'] == true;
        } else if (_selectedFilter == 'Year') {
          matchesFilter = article['year'].toString() == _selectedFilter;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadArticles() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedArticles.isNotEmpty) {
        setState(() {
          _articles = widget.preloadedArticles;
          _filteredArticles = widget.preloadedArticles;
          _isLoading = false;
        });
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _articles = cachedData;
          _filteredArticles = cachedData;
          _isLoading = false;
        });
      }

      // Then fetch from Supabase
      final articlesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_articles';
      final response = await Supabase.instance.client
          .from(articlesTableName)
          .select('*')
          .order('year', ascending: false);

      final articles = List<Map<String, dynamic>>.from(response);
      
      // Cache the data
      await _saveToCache(articles);
      
      setState(() {
        _articles = articles;
        _filteredArticles = articles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading articles: $e';
      });
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'articles_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading articles from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'articles_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving articles to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final articlesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_articles';
    _articlesChannel = Supabase.instance.client
        .channel('articles_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: articlesTableName,
          callback: (payload) {
            _loadArticles();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> years = {};
    
    for (final article in _articles) {
      if (article['year'] != null) {
        years.add(article['year'].toString());
      }
    }
    
    final List<String> filters = ['All', 'Faculty/Staff', 'Student'];
    filters.addAll(years);
    
    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Articles',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('articles-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _articles.isEmpty && !_isLoading) {
            _loadArticles();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search articles...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterArticles();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadArticles,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredArticles.isEmpty
                          ? const Center(
                              child: Text('No articles found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredArticles.length,
                              itemBuilder: (context, index) {
                                final article = _filteredArticles[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      article['fullname'] ?? 'Untitled Article',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Year: ${article['year'] ?? 'N/A'}'),
                                        Text('Author: ${_formatAuthors(article)}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ArticleDetailPage(
                                            article: article,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatAuthors(Map<String, dynamic> article) {
    final List<String> authors = [];
    
    if (article['author'] != null && article['author'].toString().isNotEmpty) {
      authors.add(article['author'].toString());
    }
    
    if (article['author2'] != null && article['author2'].toString().isNotEmpty) {
      authors.add(article['author2'].toString());
    }
    
    if (article['author3'] != null && article['author3'].toString().isNotEmpty) {
      authors.add(article['author3'].toString());
    }
    
    if (authors.isEmpty) {
      return 'N/A';
    }
    
    return authors.join(', ');
  }
}
