// alumni_departments_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'alumni_department_detail_page.dart';

class AlumniDepartmentsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AlumniDepartmentsPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AlumniDepartmentsPageState createState() => _AlumniDepartmentsPageState();
}

class _AlumniDepartmentsPageState extends State<AlumniDepartmentsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('alumni_departments_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _departments = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSchoolFilter = 'All Schools';
  List<String> _schoolFilterOptions = ['All Schools'];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadDepartmentsFromCache();
    _loadDepartmentsFromSupabase();
  }

  void _setupRealtime() {
    final departmentsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_departments';
    _realtimeChannel = Supabase.instance.client
        .channel('alumni_departments_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: departmentsTableName,
      callback: (payload) async {
        print("Realtime update received for alumni departments: ${payload.eventType}");
        _loadDepartmentsFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadDepartmentsFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_departments_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> departments = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && departments.isNotEmpty) {
          setState(() {
            _departments = departments;
            _updateSchoolFilterOptions(departments);
          });
          print("Loaded ${departments.length} alumni departments from cache");
        }
      }
    } catch (e) {
      print("Error loading alumni departments from cache: $e");
    }
  }

  void _updateSchoolFilterOptions(List<Map<String, dynamic>> departments) {
    Set<String> schools = {'All Schools'};
    for (var department in departments) {
      if (department['school'] != null && department['school'].toString().isNotEmpty) {
        schools.add(department['school'].toString());
      }
    }
    
    setState(() {
      _schoolFilterOptions = schools.toList()..sort();
    });
  }

  Future<void> _loadDepartmentsFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final departmentsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_departments';
      var query = Supabase.instance.client
          .from(departmentsTableName)
          .select('*')
          .eq('alumnidepartment', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('fullname.ilike.%$_searchQuery%,about.ilike.%$_searchQuery%,school.ilike.%$_searchQuery%');
      }
      
      // Apply school filter if not "All Schools"
      if (_selectedSchoolFilter != 'All Schools') {
        query = query.eq('school', _selectedSchoolFilter);
      }
      
      final response = await query.order('fullname', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _departments = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Update school filter options
      _updateSchoolFilterOptions(_departments);

      // Cache the data
      _cacheDepartments(_departments);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading alumni departments: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading alumni departments: $e')),
      );
    }
  }

  Future<void> _cacheDepartments(List<Map<String, dynamic>> departments) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_departments_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(departments));
      print("Cached ${departments.length} alumni departments");
    } catch (e) {
      print("Error caching alumni departments: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadDepartmentsFromSupabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Alumni Departments',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search departments...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // School filter
          if (_schoolFilterOptions.length > 1)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Text(
                    'School:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedSchoolFilter,
                          isExpanded: true,
                          icon: Icon(
                            Icons.arrow_drop_down,
                            color: theme.colorScheme.onSurface,
                          ),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 16,
                          ),
                          dropdownColor: theme.colorScheme.surface,
                          items: _schoolFilterOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null && newValue != _selectedSchoolFilter) {
                              setState(() {
                                _selectedSchoolFilter = newValue;
                              });
                              _loadDepartmentsFromSupabase();
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Departments list
          Expanded(
            child: VisibilityDetector(
              key: const Key('alumni_departments_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _departments.isEmpty && !_isLoading) {
                  _loadDepartmentsFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadDepartmentsFromSupabase,
                child: _isLoading && _departments.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _departments.isEmpty
                        ? Center(
                            child: Text(
                              'No alumni departments found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _departments.length,
                            itemBuilder: (context, index) {
                              return _buildDepartmentCard(
                                _departments[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDepartmentCard(
    Map<String, dynamic> department,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = department['fullname'] ?? 'Unnamed Department';
    final String about = department['about'] ?? '';
    final String school = department['school'] ?? '';
    final String imageUrl = department['image_url'] ?? '';
    final String contactEmail = department['contactemail'] ?? '';
    final String contactPhone = department['contactphone'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AlumniDepartmentDetailPage(
                department: department,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Department image or icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: imageUrl.isEmpty ? theme.colorScheme.surfaceVariant : null,
                ),
                child: imageUrl.isNotEmpty
                    ? ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: imageUrl,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Center(child: CircularProgressIndicator()),
                          errorWidget: (context, url, error) => Icon(
                            Icons.business,
                            size: 30,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.business,
                        size: 30,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
              ),
              const SizedBox(width: 16),
              // Department details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (school.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        school,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                    if (about.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        about.length > 100 ? '${about.substring(0, 100)}...' : about,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    if (contactEmail.isNotEmpty || contactPhone.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      if (contactEmail.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.email,
                              size: 14,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                contactEmail,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      if (contactPhone.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 14,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              contactPhone,
                              style: TextStyle(
                                fontSize: 12,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
