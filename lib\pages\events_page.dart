import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'event_detail_page.dart';

class EventsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const EventsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<dynamic>> _events;
  late List<dynamic> _selectedEvents;
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _allEvents = [];
  String _selectedFilter = 'All';
  List<String> _eventTypes = ['All', 'Academic', 'Graduation', 'Alumni', 'Community'];
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = [];
    _fetchEvents();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchEvents() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_events';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('startday', ascending: true);

      final events = List<Map<String, dynamic>>.from(response);
      
      // Convert events to calendar events
      final Map<DateTime, List<dynamic>> calendarEvents = {};
      
      for (var event in events) {
        // Check if the event has start date
        if (event['startday'] != null && 
            event['startmonth'] != null && 
            event['startyear'] != null) {
          
          final startDate = DateTime(
            event['startyear'],
            event['startmonth'],
            event['startday'],
          );
          
          // For multi-day events, add to each day
          if (event['endday'] != null && 
              event['endmonth'] != null && 
              event['endyear'] != null) {
            
            final endDate = DateTime(
              event['endyear'],
              event['endmonth'],
              event['endday'],
            );
            
            // Add event to each day between start and end
            DateTime currentDate = startDate;
            while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
              final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
              
              // Check if this is a recurring event that only happens on specific days
              bool shouldAdd = true;
              if (event['_mon'] != null || event['_tue'] != null || 
                  event['_wed'] != null || event['_thur'] != null || 
                  event['_fri'] != null || event['_sat'] != null || 
                  event['_sun'] != null) {
                
                shouldAdd = false;
                switch (currentDate.weekday) {
                  case DateTime.monday:
                    shouldAdd = event['_mon'] == true;
                    break;
                  case DateTime.tuesday:
                    shouldAdd = event['_tue'] == true;
                    break;
                  case DateTime.wednesday:
                    shouldAdd = event['_wed'] == true;
                    break;
                  case DateTime.thursday:
                    shouldAdd = event['_thur'] == true;
                    break;
                  case DateTime.friday:
                    shouldAdd = event['_fri'] == true;
                    break;
                  case DateTime.saturday:
                    shouldAdd = event['_sat'] == true;
                    break;
                  case DateTime.sunday:
                    shouldAdd = event['_sun'] == true;
                    break;
                }
              }
              
              if (shouldAdd) {
                if (calendarEvents[key] == null) {
                  calendarEvents[key] = [];
                }
                calendarEvents[key]!.add(event);
              }
              
              // Move to next day
              currentDate = currentDate.add(const Duration(days: 1));
            }
          } else {
            // Single day event
            final key = DateTime(startDate.year, startDate.month, startDate.day);
            if (calendarEvents[key] == null) {
              calendarEvents[key] = [];
            }
            calendarEvents[key]!.add(event);
          }
        }
      }

      setState(() {
        _allEvents = events;
        _events = calendarEvents;
        _selectedEvents = _getEventsForDay(_selectedDay);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading events: $e';
      });
      print('Error fetching events: $e');
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  void _filterEvents(String filter) {
    setState(() {
      _selectedFilter = filter;
      // Re-apply filters and update events
      _updateFilteredEvents();
    });
  }

  void _updateFilteredEvents() {
    final Map<DateTime, List<dynamic>> filteredEvents = {};
    
    for (var event in _allEvents) {
      // Apply event type filter
      if (_selectedFilter != 'All') {
        bool matchesFilter = false;
        
        switch (_selectedFilter) {
          case 'Academic':
            matchesFilter = event['admissionskeydate'] == true || 
                           event['paymentkeydate'] == true ||
                           event['orientationevent'] == true;
            break;
          case 'Graduation':
            matchesFilter = event['graduationevent'] == true || 
                           event['graduationkeydate'] == true;
            break;
          case 'Alumni':
            matchesFilter = event['alumnievent'] == true;
            break;
          case 'Community':
            matchesFilter = event['communityrentalevent'] == true;
            break;
        }
        
        if (!matchesFilter) {
          continue;
        }
      }
      
      // Apply search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        final name = event['fullname']?.toString().toLowerCase() ?? '';
        final location = event['location']?.toString().toLowerCase() ?? '';
        final about = event['about']?.toString().toLowerCase() ?? '';
        final team = event['teamororg']?.toString().toLowerCase() ?? '';
        
        if (!name.contains(searchTerm) && 
            !location.contains(searchTerm) && 
            !about.contains(searchTerm) &&
            !team.contains(searchTerm)) {
          continue;
        }
      }
      
      // Check if the event has start date
      if (event['startday'] != null && 
          event['startmonth'] != null && 
          event['startyear'] != null) {
        
        final startDate = DateTime(
          event['startyear'],
          event['startmonth'],
          event['startday'],
        );
        
        // For multi-day events, add to each day
        if (event['endday'] != null && 
            event['endmonth'] != null && 
            event['endyear'] != null) {
          
          final endDate = DateTime(
            event['endyear'],
            event['endmonth'],
            event['endday'],
          );
          
          // Add event to each day between start and end
          DateTime currentDate = startDate;
          while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
            final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
            
            // Check if this is a recurring event that only happens on specific days
            bool shouldAdd = true;
            if (event['_mon'] != null || event['_tue'] != null || 
                event['_wed'] != null || event['_thur'] != null || 
                event['_fri'] != null || event['_sat'] != null || 
                event['_sun'] != null) {
              
              shouldAdd = false;
              switch (currentDate.weekday) {
                case DateTime.monday:
                  shouldAdd = event['_mon'] == true;
                  break;
                case DateTime.tuesday:
                  shouldAdd = event['_tue'] == true;
                  break;
                case DateTime.wednesday:
                  shouldAdd = event['_wed'] == true;
                  break;
                case DateTime.thursday:
                  shouldAdd = event['_thur'] == true;
                  break;
                case DateTime.friday:
                  shouldAdd = event['_fri'] == true;
                  break;
                case DateTime.saturday:
                  shouldAdd = event['_sat'] == true;
                  break;
                case DateTime.sunday:
                  shouldAdd = event['_sun'] == true;
                  break;
              }
            }
            
            if (shouldAdd) {
              if (filteredEvents[key] == null) {
                filteredEvents[key] = [];
              }
              filteredEvents[key]!.add(event);
            }
            
            // Move to next day
            currentDate = currentDate.add(const Duration(days: 1));
          }
        } else {
          // Single day event
          final key = DateTime(startDate.year, startDate.month, startDate.day);
          if (filteredEvents[key] == null) {
            filteredEvents[key] = [];
          }
          filteredEvents[key]!.add(event);
        }
      }
    }
    
    setState(() {
      _events = filteredEvents;
      _selectedEvents = _getEventsForDay(_selectedDay);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Events',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchEvents,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search events...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _searchController.clear();
                                      _updateFilteredEvents();
                                    });
                                  },
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          _updateFilteredEvents();
                        },
                      ),
                    ),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        children: _eventTypes.map((type) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: FilterChip(
                              label: Text(type),
                              selected: _selectedFilter == type,
                              onSelected: (selected) {
                                if (selected) {
                                  _filterEvents(type);
                                }
                              },
                              backgroundColor: theme.colorScheme.surface,
                              selectedColor: Colors.white,
                              labelStyle: TextStyle(
                                color: _selectedFilter == type
                                    ? Colors.black
                                    : (currentIsDarkMode ? Colors.white : Colors.black),
                              ),
                              side: BorderSide(
                                color: _selectedFilter == type
                                    ? Colors.black
                                    : Colors.grey.shade300,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TableCalendar(
                      firstDay: DateTime.utc(2020, 1, 1),
                      lastDay: DateTime.utc(2030, 12, 31),
                      focusedDay: _focusedDay,
                      calendarFormat: _calendarFormat,
                      eventLoader: _getEventsForDay,
                      selectedDayPredicate: (day) {
                        return isSameDay(_selectedDay, day);
                      },
                      onDaySelected: _onDaySelected,
                      onFormatChanged: (format) {
                        setState(() {
                          _calendarFormat = format;
                        });
                      },
                      onPageChanged: (focusedDay) {
                        _focusedDay = focusedDay;
                      },
                      calendarStyle: CalendarStyle(
                        markersMaxCount: 3,
                        markerDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        todayDecoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        selectedDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      headerStyle: HeaderStyle(
                        formatButtonVisible: true,
                        titleCentered: true,
                        formatButtonShowsNext: false,
                        formatButtonDecoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        formatButtonTextStyle: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _selectedEvents.isEmpty
                          ? Center(
                              child: Text(
                                'No events scheduled for this day',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _selectedEvents.length,
                              itemBuilder: (context, index) {
                                final event = _selectedEvents[index];
                                
                                // Determine event type for icon
                                IconData eventIcon = Icons.event;
                                if (event['graduationevent'] == true || event['graduationkeydate'] == true) {
                                  eventIcon = Icons.school;
                                } else if (event['alumnievent'] == true) {
                                  eventIcon = Icons.people;
                                } else if (event['communityrentalevent'] == true) {
                                  eventIcon = Icons.business;
                                } else if (event['admissionskeydate'] == true || 
                                          event['paymentkeydate'] == true ||
                                          event['orientationevent'] == true) {
                                  eventIcon = Icons.calendar_today;
                                }
                                
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 4.0,
                                  ),
                                  child: ListTile(
                                    leading: Icon(
                                      eventIcon,
                                      color: theme.colorScheme.primary,
                                    ),
                                    title: Text(
                                      event['fullname'] ?? 'Unnamed Event',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        if (event['starttime'] != null && event['endtime'] != null)
                                          Text('Time: ${event['starttime']} - ${event['endtime']}'),
                                        if (event['location'] != null)
                                          Text('Location: ${event['location']}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => EventDetailPage(
                                            eventData: event,
                                            institutionName: widget.institutionName,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
