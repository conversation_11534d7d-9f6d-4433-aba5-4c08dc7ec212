import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'emergency_contacts_page.dart';
import 'safety_procedures_page.dart';

class EmergencyContact {
  final int id;
  final String fullname;
  final String title;
  final String department;
  final String school;
  final String school2;
  final String school3;
  final String building;
  final String room;
  final String officehours;
  final String phone;
  final String ext;
  final String email;
  final String fax;
  final String hours;
  final String about;
  final String services;
  final double? latitude;
  final double? longitude;

  EmergencyContact({
    required this.id,
    required this.fullname,
    required this.title,
    required this.department,
    required this.school,
    required this.school2,
    required this.school3,
    required this.building,
    required this.room,
    required this.officehours,
    required this.phone,
    required this.ext,
    required this.email,
    required this.fax,
    required this.hours,
    required this.about,
    required this.services,
    this.latitude,
    this.longitude,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Contact',
      title: json['title'] ?? '',
      department: json['department'] ?? '',
      school: json['school'] ?? '',
      school2: json['school2'] ?? '',
      school3: json['school3'] ?? '',
      building: json['building'] ?? '',
      room: json['room'] ?? '',
      officehours: json['officehours'] ?? '',
      phone: json['phone'] ?? '',
      ext: json['ext'] ?? '',
      email: json['email'] ?? '',
      fax: json['fax'] ?? '',
      hours: json['hours'] ?? '',
      about: json['about'] ?? 'No description available',
      services: json['services'] ?? '',
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }

  bool hasLocation() {
    return latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0;
  }

  String getFormattedPhone() {
    if (phone.isEmpty) return '';
    return ext.isNotEmpty ? '$phone ext. $ext' : phone;
  }
}

class SafetyProcedure {
  final int id;
  final String fullname;
  final String about;

  SafetyProcedure({
    required this.id,
    required this.fullname,
    required this.about,
  });

  factory SafetyProcedure.fromJson(Map<String, dynamic> json) {
    return SafetyProcedure(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Procedure',
      about: json['about'] ?? 'No description available',
    );
  }
}

class TertiarySafetyPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isFromDetailPage;

  const TertiarySafetyPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiarySafetyPage> createState() => _TertiarySafetyPageState();
}

class _TertiarySafetyPageState extends State<TertiarySafetyPage> {

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Card(
      key: Key('safety_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          switch (title) {
            case 'Emergency Contacts':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EmergencyContactsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
            case 'Safety Procedures':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SafetyProceduresPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Emergency Contacts', 'icon': Icons.contact_emergency},
      {'title': 'Safety Procedures', 'icon': Icons.verified_user},
    ];

    // No need to filter items as both are always relevant
    final filteredGridItems = gridItems;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Safety',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}