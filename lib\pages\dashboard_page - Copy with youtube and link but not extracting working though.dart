import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import 'package:youtube_explode_dart/youtube_explode_dart.dart';

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<FileWithPageRange> _pickedFiles = [];
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  List<ExamQuestion> _examQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';
  List<String> _lessonSteps = [];
  int _currentLessonStepIndex = 0;
  bool _lessonPlaying = false;
  double _lessonSpeed = 1.0;
  double _lessonFontSize = 16.0;
  bool _isNarrationMuted = false;
  final ScrollController _lessonScrollController = ScrollController();
  static const platform = MethodChannel('com.refactrai.studybuddy/media_scanner');

  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  String _ttsLanguage = 'en-US';
  String? _readingGradeLevel;
  String? _difficultyLevel;
  String? _pastedText;

  String _displayText = '';
  int _currentCharIndex = 0;
  Timer? _textAnimationTimer;
  bool _isTextAnimationActive = false;

  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;

  bool _isFullscreen = false;
  bool _showFullscreenButton = true;

  bool _isTimedQuiz = false;
  int? _quizTimeLimitMinutes;
  Timer? _quizTimer;
  Duration _timeRemaining = Duration.zero;
  double _contentFontSize = 16.0;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
  }

  @override
  void didUpdateWidget(covariant DashboardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void dispose() {
    _textAnimationTimer?.cancel();
    _stopTts();
    _cancelQuizTimer();
    super.dispose();
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash-exp',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'txt', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<PlatformFile> validFiles = [];
        List<PlatformFile> invalidFiles = [];

        for (final file in result.files) {
          final fileName = file.name.toLowerCase();
          if (['pdf', 'mp3', 'txt', 'doc', 'docx', 'jpg', 'jpeg', 'png']
              .any((ext) => fileName.endsWith('.$ext'))) {
            if ((file.bytes != null && file.bytes!.isNotEmpty) || file.path != null) {
              validFiles.add(file);
            } else {
              invalidFiles.add(file);
            }
          } else {
            invalidFiles.add(file);
          }
        }

        if (invalidFiles.isNotEmpty) {
          String invalidFileNames = invalidFiles.map((file) => file.name).join(", ");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Unsupported file types: $invalidFileNames. Only PDF, MP3, TXT, DOC, DOCX, JPG, JPEG, PNG supported.')),
          );
          return;
        }

        if (validFiles.isEmpty && invalidFiles.isEmpty && result.files.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Selected files could not be processed - no content or path available.'),
            ),
          );
          return;
        }

        if (mounted && validFiles.isNotEmpty) {
          setState(() {
            _pickedFiles.addAll(validFiles.map((file) => FileWithPageRange(file: file)));
            _isUploading = true;
            _uploadProgress = 0.0;
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Files ready: ${_pickedFiles.map((fileRange) => fileRange.file.name).join(", ")}'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  void _deleteFile(FileWithPageRange fileToDeleteRange) {
    setState(() {
      _pickedFiles.remove(fileToDeleteRange);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${fileToDeleteRange.file.name} removed')),
    );
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes, {int? startPage, int? endPage}) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final pageCount = document.pages.count;
      final extractor = sf_pdf.PdfTextExtractor(document);

      StringBuffer resultText = StringBuffer();

      int start = startPage != null ? startPage - 1 : 0;
      int end = endPage != null ? endPage - 1 : pageCount - 1;

      start = start.clamp(0, pageCount - 1);
      end = end.clamp(0, pageCount - 1);

      for (int i = start; i <= end; i++) {
        try {
          final pageText = extractor.extractText(startPageIndex: i, endPageIndex: i);
          if (pageText.isNotEmpty) {
            resultText.writeln(pageText);
            resultText.writeln();
          }
        } catch (e) {
          print('Error extracting text from page $i: $e');
        }
      }

      document.dispose();
      return resultText.toString();
    } catch (e) {
      print('PDF extraction failed: $e');
      throw Exception('PDF extraction failed: $e');
    }
  }

  // Helper function to extract text from URL
  Future<String?> _extractTextFromUrl(String urlString) async {
    try {
      final Uri url = Uri.parse(urlString);
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final document = html_parser.parse(response.body);
        final text = document.body?.text;
        return text?.trim();
      } else {
        throw Exception('Failed to load URL content: Status code ${response.statusCode}');
      }
    } catch (e) {
      print('Error extracting text from URL: $e');
      return null;
    }
  }

  // Helper function to extract YouTube Transcript
  Future<String?> _extractTranscriptFromYoutubeUrl(String youtubeUrl) async {
    try {
      var yt = YoutubeExplode();
      var videoId = VideoId.parseVideoId(youtubeUrl);
      var trackManifest = await yt.videos.closedCaptions.getManifest(videoId);
      var track = trackManifest.tracks.firstWhere(
        (track) => track.language.code == 'en',
        orElse: () => throw Exception('No English transcript available'),
      );

      var captions = await yt.videos.closedCaptions.get(track);
      StringBuffer transcriptText = StringBuffer();
      for (var caption in captions.captions) {
        transcriptText.writeln('${caption.text}');
      }
      yt.close();
      return transcriptText.toString().trim();
    } catch (e) {
      print('Error extracting YouTube transcript: $e');
      return 'Error extracting transcript from YouTube video.';
    }
  }

  @override
  Future<void> _processFile() async {
    if (_pickedFiles.isEmpty && (_pastedText == null || _pastedText!.isEmpty)) return;

    if (_processType == 'chat') {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _processingProgress = 1.0;
        });
      }
      await _startChatSession();
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
      _geminiOutput = null;
      _flashcards = [];
      _quizQuestions = [];
      _examQuestions = [];
      _lessonSteps = [];
      _isTextAnimationActive = false;
      _cancelQuizTimer();
      _timeRemaining = Duration.zero;
    });

    try {
      List<Content> geminiContent = [];
      String combinedFileContent = '';

      if (_pastedText != null && _pastedText!.isNotEmpty) {
        String pastedText = _pastedText!; // Store in local variable

        // Check if pasted text is a YouTube URL
        if (pastedText.startsWith('https://www.youtube.com') || pastedText.startsWith('https://youtu.be')) {
          String? transcript = await _extractTranscriptFromYoutubeUrl(pastedText);
          if (transcript != null) {
            combinedFileContent += "## YouTube Video Transcript from URL\n\n$transcript\n\n";
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Could not extract transcript from the provided YouTube URL.')),
            );
          }
        }
        // Check if pasted text is a regular URL (article)
        else if (pastedText.startsWith('http://') || pastedText.startsWith('https://')) {
          String? webText = await _extractTextFromUrl(pastedText);
          if (webText != null) {
            combinedFileContent += "## Web Article from URL\n\n$webText\n\n";
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Could not extract text from the provided URL.')),
            );
          }
        }
        // Handle regular pasted text
        else {
          combinedFileContent += "## Pasted Text\n\n$pastedText\n\n";
        }
      }

      for (int fileIndex = 0; fileIndex < _pickedFiles.length; fileIndex++) {
        final FileWithPageRange fileRange = _pickedFiles[fileIndex];
        final PlatformFile pickedFile = fileRange.file;
        String fileContent = '';
        final fileName = pickedFile.name.toLowerCase();

        if (mounted) {
          setState(() => _processingProgress = fileIndex / _pickedFiles.length * 0.5);
        }

        try {
          if (fileName.endsWith('.pdf')) {
            if (pickedFile.bytes != null) {
              fileContent = await _extractTextFromPdf(pickedFile.bytes!,
                      startPage: fileRange.startPage, endPage: fileRange.endPage) ??
                  '';
            } else if (pickedFile.path != null) {
              final bytes = await File(pickedFile.path!).readAsBytes();
              fileContent = await _extractTextFromPdf(bytes,
                      startPage: fileRange.startPage, endPage: fileRange.endPage) ??
                  '';
            }
          } else if (fileName.endsWith('.txt')) {
            if (pickedFile.bytes != null) {
              fileContent = utf8.decode(pickedFile.bytes!, allowMalformed: true);
            }
          } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
            if (pickedFile.bytes != null) {
              try {
                fileContent = utf8.decode(pickedFile.bytes!, allowMalformed: true);
              } catch (e) {
                fileContent = latin1.decode(pickedFile.bytes!);
              }
            }
          } else if (fileName.endsWith('.jpg') ||
              fileName.endsWith('.jpeg') ||
              fileName.endsWith('.png')) {
            if (pickedFile.bytes != null) {
              geminiContent.add(Content('user', [
                DataPart('image/png', pickedFile.bytes!),
                TextPart('Image content of ${pickedFile.name}.'),
              ]));
              fileContent = 'Image content of ${pickedFile.name}.';
            }
          }

          if (fileContent.isNotEmpty) {
            combinedFileContent += "## ${pickedFile.name}\n\n${fileContent}\n\n";
            print('Successfully extracted content from ${pickedFile.name}');
          } else {
            print('No content extracted from ${pickedFile.name}');
          }
        } catch (e) {
          print('Error processing file ${pickedFile.name}: $e');
        }

        setState(() => _processingProgress = (fileIndex + 1) /  _pickedFiles.length * 0.5);
      }


      if (combinedFileContent.isEmpty && geminiContent.isEmpty && (_pastedText == null || _pastedText!.isEmpty)) {
        throw Exception('No content extracted from selected files or pasted text');
      }

      if (mounted) {
        setState(() => _fileContent = combinedFileContent);
      }

      if (mounted) {
        setState(() => _processingProgress = 0.6);
      }

      final prompt = _buildPrompt(combinedFileContent);
      List<Content> finalGeminiContent = [...geminiContent];
      finalGeminiContent.add(Content('user', [TextPart(prompt)]));
      final response = await _geminiModel.generateContent(finalGeminiContent);

      if (response.text != null) {
        _handleResponse(response.text!);
      } else {
        throw Exception('AI response empty');
      }

      for (int i = 60; i <= 100; i++) {
        await Future.delayed(const Duration(milliseconds: 30));
        if (mounted) {
          setState(() => _processingProgress = i / 100);
        }
      }
    } catch (e) {
      print('Processing error: $e');
      List<ConnectivityResult> connectivityResults = await Connectivity().checkConnectivity();
      String errorMessage = 'Processing error: ${e.toString()}';
      if (connectivityResults.contains(ConnectivityResult.none)) {
        errorMessage = 'You are offline. Please check your internet connection.';
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  @override
  Future<void> _startChatSession() async {
    try {
      _chatSession = _geminiModel.startChat();

      String combinedContent = '';

      if (_pastedText != null && _pastedText!.isNotEmpty) {
        String pastedText = _pastedText!; // Store in local variable

        // Check if pasted text is a YouTube URL
        if (pastedText.startsWith('https://www.youtube.com') || pastedText.startsWith('https://youtu.be')) {
          String? transcript = await _extractTranscriptFromYoutubeUrl(pastedText);
          if (transcript != null) {
            combinedContent += "## YouTube Video Transcript from URL\n\n$transcript\n\n";
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Could not extract transcript from the provided YouTube URL for chat context.')),
            );
          }
        }
        // Check if pasted text is a regular URL (article)
        else if (pastedText.startsWith('http://') || pastedText.startsWith('https://')) {
          String? webText = await _extractTextFromUrl(pastedText);
          if (webText != null) {
            combinedContent += "## Web Article from URL\n\n$webText\n\n";
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Could not extract text from the provided URL for chat context.')),
            );
          }
        }
        // Handle regular pasted text
        else {
          combinedContent += "## Pasted Text\n\n$_pastedText\n\n";
        }
      }


      for (final fileRange in _pickedFiles) {
        final pickedFile = fileRange.file;
        String fileContent = '';
        final fileName = pickedFile.name.toLowerCase();

        if (fileName.endsWith('.pdf')) {
          if (pickedFile.bytes != null) {
            fileContent = await _extractTextFromPdf(pickedFile.bytes!,
                    startPage: fileRange.startPage, endPage: fileRange.endPage) ??
                '';
          }
        } else if (fileName.endsWith('.txt')) {
          if (pickedFile.bytes != null) {
            fileContent = utf8.decode(pickedFile.bytes!, allowMalformed: true);
          }
        }

        if (fileContent.isNotEmpty) {
          combinedContent += "## ${pickedFile.name}\n\n$fileContent}\n\n";
        }
      }

      final systemMessage = "Document and Text Content:\n$combinedContent\n\n"
          "You are an assistant for this content. "
          "Base all responses strictly on this information.";

      _chatSession.sendMessage(Content('system', [TextPart(systemMessage)]));

      setState(() {
        _chatMessages = [
          ChatMessage("AI: I've analyzed the content and am ready to assist", false),
        ];
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
      );
    }
  }

  String _buildPrompt(String content) {
    String gradeLevelText = _readingGradeLevel != null && _readingGradeLevel!.isNotEmpty
        ? ' Tailor the content to a ${_readingGradeLevel!.toLowerCase()} reading level.'
        : '';
    String difficultyLevelText = _difficultyLevel != null && _difficultyLevel!.isNotEmpty
        ? ' Difficulty level: ${_difficultyLevel!.toLowerCase()}.'
        : '';

    final prompts = {
      'notes': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES at least 50% the length of the original document(s) that teach the subject matter directly:$gradeLevelText
- Clear headings
- No bullet points in front of numbers like 1. or (1)
- Bullet points
- Key terms in **bold**
- Examples in *italic*
- No lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
- No lines like *Example:* but Example:
- Use dash (-) for bullet points in markdown
- Do not mention the source name
- In PDF export, header text of every section should be bold, make topic titles bold, headings bold
- In PDF export, properly italicize and bold text in the right places, no * or ** or # prefixes
- Include tables when necessary
- Use markdown pipe syntax with headers for tables
- ** response modalities are text and images. Draw and embed images, using markdown `[image: description]`.**
- Use # prefixes for all section headers
- Never use unicode symbols or special characters
- First create a study guide, then cover EVERY concept exhaustively in at least 5 pages with unique content, no omissions
- Include ALL foundational elements needed for complete understanding
- Never use "the document states" or similar meta-references - present as primary knowledge
- Refer to uploaded images/diagrams when relevant, mentioning diagram name or brief description

Analyze the uploaded text and generate detailed, structured notes that summarize every section thoroughly. The notes should be so comprehensive that a student can understand the entire text without needing to read the original document. Ensure the following:

1. Full Coverage
Cover every section, chapter, and subtopic in detail.
Ensure all key ideas, arguments, and concepts are included.
2. Clear & Organized Structure
Use headings, subheadings, bullet points, and numbered lists for readability.
Ensure logical flow and easy navigation through topics.
3. Key Terms & Definitions
Define all important terms and jargon in simple language.
Provide alternative explanations where needed.
4. Examples & Explanations
Include all examples from the text and re-explain them clearly.
Add extra examples if needed to reinforce understanding.
Use real-world applications and analogies to simplify complex ideas.
5. Exercise Questions & Fully Worked-Out Solutions
Extract all exercise questions from the text.
Provide fully worked-out step-by-step solutions with explanations.
Include alternative solving methods where applicable.
Add extra practice questions for mastery.
6. Charts, Diagrams, Tables & Visual Aids
Charts & Graphs: Convert important numerical data into charts for visualization.
Diagrams & Flowcharts: Include labeled diagrams and flowcharts for complex processes.
Tables: Use tables to summarize comparisons, formulas, and key points for clarity.
Mind Maps: Use mind maps for summarizing large topics.
7. Cause and Effect Relationships
Explain comparisons, dependencies, and logical connections between concepts.
Highlight real-world applications of theories.
8. Exam-Focused Content
Highlight important facts, common exam questions, and must-know points for revision.
Provide concise summaries for quick review.

9. Summaries & Key Takeaways
End each section with a concise summary of the most important points.
Include a "Quick Recap" section at the end of each chapter.

10. Reference Section at the End
At the end of the notes, include:
✅ Dictionary of Terms: List and define key words used throughout the notes.
✅ Key Points: A summarized list of the most important facts from the entire text.
✅ Abbreviations & Symbols: Provide explanations for all abbreviations, formulas, and special symbols used in the text.
✅ Tables for Quick Reference: Organize key facts, comparisons, and formulas in table format for quick lookup.

11. The Ultimate Goal
"Ensure that by the end of these notes, a student can study only from them and still perform well in exams—even without reading the original text. The notes should be engaging, structured, and filled with practical insights, ensuring maximum comprehension and retention."

Content: $content''',
      'chat': '''Use this document as your ONLY knowledge source:
$content
Respond to ALL subsequent questions using ONLY this information, but infer from the document if needed. For math questions, provide step-by-step solutions.''',
      'interactive_lesson': '''Generate an interactive lesson from the content, suitable for a smartboard presentation for ${gradeLevelText.isNotEmpty ? gradeLevelText : 'a general audience'}.
Structure the lesson in sequential steps, as if teaching step-by-step on a whiteboard. Each step should be concise.
Incorporate placeholders:
- [chart] for charts/graphs
- [image] for images
- [video] for videos
- [audio] for audio clips
- [pdf] for PDFs
- [docx] for Word docs
- [link: URL] for external URLs
- Draw images when asked
- Reference and describe uploaded images/diagrams when appropriate

Ensure steps are clear and concise for a digital whiteboard.
Example format (each step on a new line):

Introduction to Supply and Demand

What is Demand? - Demand is how much consumers are willing to buy at different prices.
[chart] - Demand Curve showing price vs quantity demanded.

What is Supply? - Supply is how much producers are willing to sell at different prices.
[chart] - Supply Curve showing price vs quantity supplied.

Content: $content''',
      'lesson_plan': '''Generate a detailed lesson plan from the content, suitable for educators. Include:
- Learning Objectives
- Materials Needed
- Step-by-step Lesson Procedure
- Assessment Methods
- Differentiation Strategies
- Extension Activities
- Homework Assignments

Content: $content''',
      'cheatsheet': '''Generate a concise yet comprehensive cheatsheet.$gradeLevelText
Include a clear topic title, key formulas, definitions, and examples in structured markdown.
Use dash (-) for bullet points

Content: $content''',
      'flashcards': '''Generate at least 50 comprehensive, unique flashcards (no repeats across grade levels):$gradeLevelText

Q: [Question]
A: [Answer]

Each card must contain:
Front:
||Core Concept||: Concise question
||Type||: [Definition/Application/Analysis/Connection]

Back:
Full Explanation: (1-2 sentences)

Requirements:
1. Cover EVERY concept from source material
2. 15-20 cards per major topic
3. Progressive difficulty within topics
4. Cross-link cards through connection points

Content: $content''',
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 unique questions (no repeats across difficulty levels).$difficultyLevelText Use this EXACT format:

Example:
1. What is the capital of France?
A) London
B) Paris
C) Rome
D) Florida
Answer: B

Content: $content''',
      'transcript': '''Create a comprehensive transcript (no repeats across grade levels):$gradeLevelText
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',
      'summary': '''Generate a summary/brief of the content.
If a research/academic paper, include:
- Background
- Research Question
- Study Method
- Study Limitations
- Global Alignment (if applicable)
- Findings (quantitative info where needed)
- Policy Recommendations
- Stakeholder Implications
Otherwise, provide a concise general summary.

Content: $content''',
      'exam': '''Generate a comprehensive practice exam with at least 50 NEW questions (different from quiz, paper-based style, no repeats across difficulty).$difficultyLevelText
Use this EXACT format:

[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Full Correct Answer Text - explain in detail]

Content: $content''',
      'minutes': '''Generate comprehensive meeting minutes (no repeats across grade levels):$gradeLevelText
Include:
- Meeting Title
- Date and Time
- Attendees
- Agenda Items
- Discussions
- Action Items
- Decisions

Content: $content'''
    };

    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        _startQuizTimer();
        break;
      case 'exam':
        _parseExam(response);
        break;
      case 'chat':
        _startChatSession();
        break;
      case 'interactive_lesson':
        _parseInteractiveLesson(response);
        break;
      default:
        break;
    }
  }

  void _parseInteractiveLesson(String response) {
    final steps = response.split('\n\n').where((s) => s.trim().isNotEmpty).toList();
    setState(() {
      _lessonSteps = steps;
      _currentLessonStepIndex = 0;
      _lessonPlaying = false;
      _displayText = '';
      _currentCharIndex = 0;
    });

    if (_lessonSteps.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _lessonPlaying) {
          _startLessonStepDisplay();
        }
      });
    }
  }


  void _parseFlashcards(String response) {
    _flashcards = _parseContent(response, (question, answer) => Flashcard(question: question, answer: answer),
        r'^(?:Q:|Question|\d+\.)\s*(.*?)\s*(?:A:|Answer|\n)(.*)');
  }

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer|Answers|Correct):\s*([A-D]?)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');
    final answerRegex = RegExp(r'Answer:\s*([A-D]?)', caseSensitive: false);

    final matches = questionRegex.allMatches(response);

    for (final match in matches) {
      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
      }

      String correctLetter = match.group(3)?.toUpperCase() ?? '';
      if (correctLetter.isEmpty) {
        final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
        correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
      }

      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
        _timeRemaining = _isTimedQuiz && _quizTimeLimitMinutes != null
            ? Duration(minutes: _quizTimeLimitMinutes!)
            : Duration.zero;
      });
    }
  }

  void _parseExam(String response) {
    final examQuestions = <ExamQuestion>[];
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer):\s+([^\n]+)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');

    final matches = questionRegex.allMatches(response);

    for (final match in matches) {
      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];

      for (final optMatch in optionMatches) {
        final optionText = optMatch.group(2)?.trim() ?? '';
        options.add(optionText);
      }

      final correctAnswerText = match.group(3)?.trim() ?? '';

      examQuestions.add(ExamQuestion(
        question: questionText,
        options: options,
        correctAnswer: correctAnswerText,
      ));
    }

    if (mounted) {
      setState(() => _examQuestions = examQuestions);
    }
  }

  List<T> _parseContent<T>(String response, T Function(String, String) creator, String regexPattern) {
    final items = <T>[];
    final blocks = response.split(RegExp(r'\n\s*(?=Q|Question|\d+\.)'));

    final qaRegex = RegExp(
      regexPattern,
      caseSensitive: false,
      dotAll: true,
    );

    for (final block in blocks) {
      final match = qaRegex.firstMatch(block);
      if (match != null) {
        String question = match.group(1)?.trim() ?? '';
        String answer = match.group(2)?.trim() ?? '';

        question = question
            .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]'), '')
            .trim();
        answer = answer
            .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]'), '')
            .trim();

        question = question.replaceAll(RegExp(r'^[:\-. ]+|[:\-. ]+$'), '');
        answer = answer.replaceAll(RegExp(r'^[:\-. ]+|[:\-. ]+$'), '');

        if (question.isNotEmpty && answer.isNotEmpty) {
          items.add(creator(question, answer));
        }
      }
    }
    return items;
  }


  void _startQuizTimer() {
    _cancelQuizTimer();
    if (_isTimedQuiz && _quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0) {
      _timeRemaining = Duration(minutes: _quizTimeLimitMinutes!);
      _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 0) {
          setState(() => _timeRemaining = _timeRemaining - const Duration(seconds: 1));
        } else {
          _cancelQuizTimer();
          _submitQuiz();
        }
      });
    }
  }

  void _cancelQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = null;
  }

  String get _formattedTimeRemaining {
    final minutes = _timeRemaining.inMinutes.remainder(60);
    final seconds = _timeRemaining.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    return _quizQuestions.isEmpty
        ? _buildEmptyCard('Generating quiz questions...', theme, generalTextColor, 'Quiz')
        : Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                        style: GoogleFonts.notoSans(
                            fontSize: 18, fontWeight: FontWeight.bold, color: generalTextColor),
                      ),
                      if (_isTimedQuiz)
                        Text(
                          'Time: ${_formattedTimeRemaining}',
                          style: GoogleFonts.notoSans(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.secondary),
                        ),
                      Text(
                        'Score: $_quizScore',
                        style: GoogleFonts.notoSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: theme.dividerColor),
                    ),
                    child: Text(
                      _quizQuestions[_currentQuestionIndex].question,
                      style: GoogleFonts.notoSans(fontSize: 16, color: generalTextColor),
                    ),
                  ),
                  const SizedBox(height: 20),
                  ..._buildQuizOptions(theme, generalTextColor),
                  const SizedBox(height: 24),
                  _buildQuizNavigationButtons(theme, generalTextColor),
                ],
              ),
            ),
          );
  }

  List<Widget> _buildQuizOptions(ThemeData theme, Color generalTextColor) {
    return List.generate(_quizQuestions[_currentQuestionIndex].options.length, (index) {
      if (_quizQuestions[_currentQuestionIndex].options[index].isEmpty) return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Material(
          color: _userAnswers[_currentQuestionIndex] == index
              ? theme.colorScheme.primary.withOpacity(0.2)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: () {
              setState(() => _userAnswers[_currentQuestionIndex] = index);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
                  ),
                  Expanded(
                    child: Text(
                      _quizQuestions[_currentQuestionIndex].options[index],
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ),
                  Radio<int>(
                    value: index,
                    groupValue: _userAnswers[_currentQuestionIndex],
                    onChanged: (value) {
                      setState(() => _userAnswers[_currentQuestionIndex] = value);
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildQuizNavigationButtons(ThemeData theme, Color generalTextColor) {
    return ElevatedButton(
      onPressed: _userAnswers[_currentQuestionIndex] != null
          ? () {
              if (_userAnswers[_currentQuestionIndex] ==
                  _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
                setState(() => _quizScore++);
              }
              _submitQuiz();
            }
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 16),
        disabledBackgroundColor: Colors.grey,
      ),
      child: Text(
        _currentQuestionIndex < _quizQuestions.length - 1 ? 'Next Question' : 'Finish Quiz',
        style: GoogleFonts.notoSans(
            color: _userAnswers[_currentQuestionIndex] != null
                ? (widget.isDarkMode ? Colors.black : Colors.white)
                : Colors.grey[400],
            fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildExamView(ThemeData theme, Color generalTextColor) {
    return _examQuestions.isEmpty
        ? _buildEmptyCard('Generating exam questions...', theme, generalTextColor, 'Exam')
        : Card(
            color: theme.colorScheme.surface,
            child: _examCardContent(theme, generalTextColor),
          );
  }

  Padding _examCardContent(ThemeData theme, Color generalTextColor) {
    return Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Practice Exam',
                        style: GoogleFonts.notoSans(
                            fontSize: 22 + _contentFontSize - 16,
                            fontWeight: FontWeight.bold,
                            color: generalTextColor),
                        textAlign: TextAlign.center,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(Icons.text_increase, color: generalTextColor),
                            onPressed: () => setState(() => _contentFontSize += 2),
                          ),
                          IconButton(
                            icon: Icon(Icons.text_decrease, color: generalTextColor),
                            onPressed: () => setState(() => _contentFontSize -= 2),
                          ),
                          IconButton(
                            icon: Icon(Icons.translate, color: generalTextColor.withOpacity(0.5)),
                            onPressed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Translate feature coming soon!')),
                              );
                            },
                          ),
                          IconButton(
                            icon: Icon(Icons.download, color: generalTextColor),
                            onPressed: () => _exportToPdf(),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ..._examQuestions.asMap().entries.map((entry) {
                    int index = entry.key;
                    ExamQuestion question = entry.value;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${index + 1}. ${question.question}',
                          style: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
                        ),
                        const SizedBox(height: 8),
                        ...List.generate(question.options.length, (optionIndex) {
                          return Text(
                            '    ${String.fromCharCode('A'.codeUnitAt(0) + optionIndex)}) ${question.options[optionIndex]}',
                            style: GoogleFonts.notoSans(color: generalTextColor),
                          );
                        }),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.cardColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: theme.dividerColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Answer:',
                                style: GoogleFonts.notoSans(
                                    fontWeight: FontWeight.bold, color: generalTextColor),
                              ),
                              Text(
                                question.correctAnswer,
                                style: GoogleFonts.notoSans(color: generalTextColor),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    );
                  }).toList(),
                ],
              ),
            );
  }

  Widget _buildEmptyCard(String message, ThemeData theme, Color generalTextColor, String processName) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          _isProcessing
              ? 'Generating $processName...'
              : message,
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
      ),
    );
  }


  void _submitQuiz() {
    _cancelQuizTimer();
    if (_userAnswers[_currentQuestionIndex] ==
        _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
      setState(() => _quizScore++);
    }

    if (_currentQuestionIndex < _quizQuestions.length - 1) {
      setState(() => _currentQuestionIndex++);
      return;
    }

    final results = <QuizResult>[];
    for (int i = 0; i < _quizQuestions.length; i++) {
      final question = _quizQuestions[i];
      final userAnswerIndex = _userAnswers[i];
      final correctAnswerIndex = question.correctAnswerIndex;
      String userAnswer = userAnswerIndex != null && userAnswerIndex < question.options.length
          ? question.options[userAnswerIndex]
          : 'Not answered';
      String correctAnswer = correctAnswerIndex != null && correctAnswerIndex < question.options.length
          ? question.options[correctAnswerIndex]
          : 'Unknown';
      bool isCorrect = userAnswerIndex == correctAnswerIndex;

      results.add(QuizResult(
        question: question.question,
        userAnswer: userAnswer,
        correctAnswer: correctAnswer,
        isCorrect: isCorrect,
      ));
    }

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        textColor: generalTextColor,
        quizResults: results,
        fileContent: _fileContent,
        onGenerateNotes: () => _generateWeaknessNotes(context, _fileContent, results),
      ),
    );
  }

  List<pw.Widget> _buildPdfContent(String markdownText, String contentType) {
    if (contentType == 'exam') {
      return _buildPdfExamContent(markdownText);
    } else {
      return _buildPdfNotesContent(markdownText);
    }
  }

  List<pw.Widget> _buildPdfNotesContent(String markdownText) {
    String cleanedText = markdownText
        .replaceAll(RegExp(r'^```.*?^```', multiLine: true), '')
        .replaceAll(RegExp(r'^Here is .+?:', multiLine: true), '')
        .replaceAll(RegExp(r'^[#]+ ', multiLine: true), '')
        .trim();

    List<String> lines = cleanedText.split('\n');
    List<pw.Widget> widgets = [];

    widgets.add(pw.Header(level: 1, child: pw.Text('Refactr AI')));

    for (var line in lines) {
      if (line.startsWith('# ')) {
        widgets.add(pw.Text(
          line.substring(2).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
        ));
      } else if (line.startsWith('## ')) {
        widgets.add(pw.Text(
          line.substring(3).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ));
      } else if (line.startsWith('### ')) {
        widgets.add(pw.Text(
          line.substring(4).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ));
      } else if (line.trim().startsWith('- ')) {
        widgets.add(pw.Text(
          line.replaceFirst('- ', '  • '),
          style: pw.TextStyle(fontSize: 14),
        ));
      } else {
        widgets.add(_buildRichTextFromMarkdown(line));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  List<pw.Widget> _buildPdfExamContent(String markdownText) {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];

    widgets.add(pw.Header(level: 1, child: pw.Text('Refactr AI - Practice Exam')));

    for (var line in lines) {
      if (line.trim().isEmpty) continue;
      widgets.add(pw.Text(line, style: pw.TextStyle(fontSize: 12)));
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  pw.Widget _buildRichTextFromMarkdown(String text) {
    List<pw.TextSpan> spans = [];
    final pattern = RegExp(r'(\*\*.*?\*\*|\*.*?\*|~~.*?~~|`.*?`|\[.*?\]\(.*?\)|[^*~`]+)');

    for (final match in pattern.allMatches(text)) {
      final segment = match.group(0)!;
      if (segment.startsWith('**') && segment.endsWith('**')) {
        spans.add(pw.TextSpan(
          text: segment.substring(2, segment.length - 2),
          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        ));
      } else if (segment.startsWith('*') && segment.endsWith('*')) {
        spans.add(pw.TextSpan(
          text: segment.substring(1, segment.length - 1),
          style: pw.TextStyle(fontStyle: pw.FontStyle.italic),
        ));
      } else {
        spans.add(pw.TextSpan(text: segment));
      }
    }

    return pw.RichText(
      text: pw.TextSpan(children: spans, style: pw.TextStyle(fontSize: 14)),
    );
  }

  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    try {
      final pdf = pw.Document();
      pdf.addPage(
        pw.Page(
          margin: const pw.EdgeInsets.all(20),
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: _buildPdfContent(_geminiOutput!, _processType),
          ),
        ),
      );

      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        final blob = html.Blob([pdfBytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
        if (selectedDirectory != null) {
          final fileName = '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
          final filePath = path.join(selectedDirectory, fileName);
          final file = File(filePath);
          await file.writeAsBytes(pdfBytes);

          if (!kIsWeb && Platform.isAndroid) {
            await _scanFileForGalleryAndroid(filePath);
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('PDF saved to $filePath.'),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('PDF export failed: ${e.toString()}')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content('user', [TextPart(message)]));
      if (mounted) {
        setState(() => _chatMessages.add(ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      List<ConnectivityResult> connectivityResults = await Connectivity().checkConnectivity();
      String errorMessage = 'Chat error: ${e.toString()}';
      if (connectivityResults.contains(ConnectivityResult.none)) {
        errorMessage = 'You are offline. Please check your internet connection.';
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty && !_isNarrationMuted) {
      await flutterTts.setVolume(volume);
      await flutterTts.setSpeechRate(rate);
      await flutterTts.setPitch(pitch);
      await flutterTts.setLanguage(_ttsLanguage);

      String cleanedText = text.replaceAll(RegExp(r'[*~`#-]'), '').replaceAll(RegExp(r'[\n\r]'), ' ').trim();

      if (ttsState == TtsState.playing) {
        var result = await flutterTts.pause();
        if (result == 1) setState(() => ttsState = TtsState.paused);
      } else {
        var result = await flutterTts.speak(cleanedText);
        if (result == 1) setState(() => ttsState = TtsState.playing);
      }
    }
  }

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }

  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(
                ttsState == TtsState.playing ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: () => ttsState == TtsState.playing
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Colors.grey,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                trackHeight: 4.0,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
                overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: rate,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: "Speed",
                activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                inactiveColor: Colors.grey,
                onChanged: (double value) {
                  setState(() => rate = value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _scanFileForGalleryAndroid(String filePath) async {
    try {
      await platform.invokeMethod('scanFile', {'filePath': filePath});
      print('Android Media Scanner invoked for: $filePath');
    } on PlatformException catch (e) {
      print("Failed to invoke Android Media Scanner: '${e.message}'. Error code: '${e.code}'");
    }
  }

  bool _hasMp3File() {
    return _pickedFiles.any((fileRange) => fileRange.file.name.toLowerCase().endsWith('.mp3'));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        title: Text('Study Buddy', style: GoogleFonts.notoSans(color: generalTextColor)),
        backgroundColor: theme.appBarTheme.backgroundColor,
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode, color: generalTextColor),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, generalTextColor),
                ],
              ),
            ),
      bottomNavigationBar: _processType == 'notes' && _geminiOutput != null
          ? BottomAppBar(child: _buildTtsControls())
          : null,
    );
  }

  Widget _buildFileSection(ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Column(
      children: [
        Card(
          color: theme.colorScheme.surface,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              icon: Icon(Icons.cloud_upload, color: buttonTextColor),
              label: Text('Select Learning Material(s)',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              ),
              onPressed: _isUploading ? null : _pickFile,
            ),
          ),
        ),
        const SizedBox(height: 10),
        Center(child: Text('OR', style: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.7)))),
        const SizedBox(height: 10),
        Card(
          color: theme.cardColor,
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              controller: TextEditingController(text: _pastedText),
              maxLines: 1,
              decoration: InputDecoration(
                hintText: 'Paste text or write here',
                hintStyle: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
                border: InputBorder.none,
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              onChanged: (text) {
                _pastedText = text;
              },
            ),
          ),
        ),
        if (_pickedFiles.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Selected Files:',
                    style: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.8))),
                ..._pickedFiles.map((fileRange) => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('• ${fileRange.file.name}',
                                  style: GoogleFonts.notoSans(
                                      color: generalTextColor.withOpacity(0.6), fontSize: 14)),
                              if (fileRange.file.name.toLowerCase().endsWith('.pdf'))
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 80,
                                      child: TextField(
                                        keyboardType: TextInputType.number,
                                        decoration: InputDecoration(
                                          hintText: 'Start Page',
                                          hintStyle: GoogleFonts.notoSans(fontSize: 12),
                                          border: const OutlineInputBorder(),
                                          contentPadding:
                                              const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        ),
                                        style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                                        onChanged: (value) =>
                                            fileRange.startPage = value.isEmpty ? null : int.tryParse(value),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 80,
                                      child: TextField(
                                        keyboardType: TextInputType.number,
                                        decoration: InputDecoration(
                                          hintText: 'End Page',
                                          hintStyle: GoogleFonts.notoSans(fontSize: 12),
                                          border: const OutlineInputBorder(),
                                          contentPadding:
                                              const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        ),
                                        style: GoogleFonts.notoSans(fontSize: 12, color: generalTextColor),
                                        onChanged: (value) =>
                                            fileRange.endPage = value.isEmpty ? null : int.tryParse(value),
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _deleteFile(fileRange),
                        ),
                      ],
                    )).toList(),
              ],
            ),
          ),
        if (_isUploading)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: LinearProgressIndicator(value: _uploadProgress),
          ),
        if (_isUploading)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
              style: GoogleFonts.notoSans(color: generalTextColor),
            ),
          ),
      ],
    );
  }


  Widget _buildProcessingControls(
      ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: [
                const DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                const DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                const DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                const DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                if (_hasMp3File())
                  const DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                if (_hasMp3File())
                  const DropdownMenuItem(value: 'minutes', child: Text('Create Meeting Minutes')),
                const DropdownMenuItem(value: 'chat', child: Text('AI Tutor / Chat with Content')),
                const DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                const DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
                const DropdownMenuItem(
                    value: 'interactive_lesson', child: Text('Create Interactive Lesson')),
                const DropdownMenuItem(value: 'lesson_plan', child: Text('Generate Lesson Plan')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _processType = value;
                    _geminiOutput = null;
                    _flashcards = [];
                    _quizQuestions = [];
                    _examQuestions = [];
                    _chatMessages = [];
                    _readingGradeLevel = null;
                    _difficultyLevel = null;
                    _isTimedQuiz = false;
                    _quizTimeLimitMinutes = null;
                    _cancelQuizTimer();
                    _timeRemaining = Duration.zero;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            if (_processType != 'quiz' && _processType != 'exam')
              DropdownButtonFormField<String>(
                value: _readingGradeLevel,
                decoration: InputDecoration(
                  labelText: 'Reading Grade Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Grade 5', child: Text('Grade 5')),
                  DropdownMenuItem(value: 'Grade 8', child: Text('Grade 8')),
                  DropdownMenuItem(value: 'Grade 10', child: Text('Grade 10')),
                  DropdownMenuItem(value: 'Grade 12', child: Text('Grade 12')),
                  DropdownMenuItem(value: 'College', child: Text('College')),
                  DropdownMenuItem(value: 'Professional', child: Text('Professional')),
                ],
                onChanged: (value) {
                  setState(() => _readingGradeLevel = value);
                },
              ),
            if (_processType == 'quiz' || _processType == 'exam')
              DropdownButtonFormField<String>(
                value: _difficultyLevel,
                decoration: InputDecoration(
                  labelText: 'Difficulty Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                  DropdownMenuItem(value: 'Normal', child: Text('Normal')),
                  DropdownMenuItem(value: 'Intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'Hard', child: Text('Hard')),
                  DropdownMenuItem(value: 'Very Hard', child: Text('Very Hard')),
                ],
                onChanged: (value) {
                  setState(() => _difficultyLevel = value);
                },
              ),
            if (_processType == 'quiz')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Quiz Time Limit (minutes, optional)',
                          labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                          border: const OutlineInputBorder(),
                        ),
                        style: GoogleFonts.notoSans(color: generalTextColor),
                        onChanged: (value) {
                          setState(() => _quizTimeLimitMinutes = int.tryParse(value));
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Timed Quiz', style: GoogleFonts.notoSans(color: generalTextColor)),
                        Switch(
                          value: _isTimedQuiz,
                          onChanged: (value) {
                            setState(() => _isTimedQuiz = value);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            if (_processType != 'quiz' && _processType != 'exam' && _processType != 'interactive_lesson')
              const SizedBox(height: 16),
            if (_processType != 'interactive_lesson')
              ElevatedButton.icon(
                icon: _isProcessing ? const SizedBox() : Icon(Icons.memory, color: buttonTextColor),
                label: Text(_isProcessing ? 'Processing...' : 'Process with AI',
                    style: GoogleFonts.notoSans(color: buttonTextColor)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonBg,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  minimumSize: const Size(double.infinity, 50),
                ),
                onPressed: _isProcessing ? null : _processFile,
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    String titleText = 'Generated Content';
    bool showDownloadButton = true;
    switch (_processType) {
      case 'notes':
        titleText = 'Notes';
        break;
      case 'lesson_plan':
        titleText = 'Lesson Plan';
        break;
      case 'cheatsheet':
        titleText = 'Cheatsheet';
        break;
      case 'flashcards':
        titleText = 'Flashcards';
        showDownloadButton = false;
        break;
      case 'quiz':
        titleText = 'Quiz';
        showDownloadButton = false;
        break;
      case 'exam':
        titleText = 'Practice Exam';
        break;
      case 'transcript':
        titleText = 'Transcript';
        showDownloadButton = false;
        break;
      case 'chat':
        titleText = 'Chat';
        showDownloadButton = false;
        break;
      case 'summary':
        titleText = 'Summary';
        break;
      case 'minutes':
        titleText = 'Meeting Minutes';
        showDownloadButton = false;
        break;
      case 'interactive_lesson':
        titleText = 'Interactive Lesson';
        showDownloadButton = false;
        break;
    }

    if (_processType == 'flashcards') {
      return _buildFlashcardsView(theme, generalTextColor);
    } else if (_processType == 'quiz') {
      return _buildQuizView(theme, generalTextColor);
    } else if (_processType == 'exam') {
      return _buildExamView(theme, generalTextColor);
    } else if (_processType == 'chat') {
      return _buildChatView(theme, generalTextColor);
    } else if (_processType == 'interactive_lesson') {
      return _buildInteractiveLessonView(theme, generalTextColor);
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(titleText,
                    style: GoogleFonts.notoSans(
                        fontSize: 18, fontWeight: FontWeight.bold, color: generalTextColor)),
                if (showDownloadButton)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.text_increase, color: generalTextColor),
                        onPressed: () => setState(() => _contentFontSize += 2),
                      ),
                      IconButton(
                        icon: Icon(Icons.text_decrease, color: generalTextColor),
                        onPressed: () => setState(() => _contentFontSize -= 2),
                      ),
                      IconButton(
                        icon: Icon(Icons.translate, color: generalTextColor.withOpacity(0.5)),
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Translate feature coming soon!')),
                          );
                        },
                      ),
                      IconButton(
                        icon: Icon(Icons.download, color: generalTextColor),
                        onPressed: () => _exportToPdf(),
                      ),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGeminiOutputWidget(theme, generalTextColor),
          ],
        ),
      ),
    );
  }

  Widget _buildGeminiOutputWidget(ThemeData theme, Color generalTextColor) {
    if (_geminiOutput == null) {
      return Text('No content generated', style: GoogleFonts.notoSans(color: generalTextColor));
    }

    List<Widget> children = [];
    List<String> segments = _geminiOutput!.split(RegExp(r'(\[image:.*?\])'));

    for (int i = 0; i < segments.length; i++) {
      String segment = segments[i];
      if (segment.startsWith('[image:') && segment.endsWith(']')) {
        String imageName = segment.substring('[image:'.length, segment.length - 1).trim();
        PlatformFile? imageFile =
            _pickedFiles.firstWhereOrNull((fileRange) => fileRange.file.name == imageName)?.file;

        if (imageFile != null && imageFile.bytes != null) {
          children.add(Image.memory(imageFile.bytes!));
        } else {
          children.add(Text('Image not found: $imageName', style: TextStyle(color: Colors.red)));
        }
      } else {
        children.add(MarkdownBody(
          data: segment,
          styleSheet: MarkdownStyleSheet(
            h1: GoogleFonts.notoSans(
                fontSize: 24 + _contentFontSize - 16,
                fontWeight: FontWeight.bold,
                color: generalTextColor),
            p: GoogleFonts.notoSans(fontSize: _contentFontSize, color: generalTextColor),
            code: TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
                backgroundColor: theme.cardColor,
                color: generalTextColor),
          ),
        ));
      }
    }
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: children);
  }

  Widget _buildInteractiveLessonView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Step ${_currentLessonStepIndex + 1}/${_lessonSteps.length}',
                  style: GoogleFonts.notoSans(color: generalTextColor, fontSize: 16),
                ),
                IconButton(
                  icon: Icon(Icons.fullscreen, color: generalTextColor),
                  onPressed: () => setState(() => _isFullscreen = !_isFullscreen),
                ),
              ],
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDarkMode ? Colors.grey[900] : Colors.black,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: Text(
                    _lessonSteps.isNotEmpty ? _lessonSteps[_currentLessonStepIndex] : 'No lesson content available',
                    style: GoogleFonts.notoSans(
                      fontSize: 18,
                      color: Colors.white,
                      height: 1.5,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back, color: generalTextColor),
                  onPressed: _currentLessonStepIndex > 0
                      ? () => setState(() => _currentLessonStepIndex--)
                      : null,
                ),
                IconButton(
                  icon: Icon(Icons.arrow_forward, color: generalTextColor),
                  onPressed: _currentLessonStepIndex < _lessonSteps.length - 1
                      ? () => setState(() => _currentLessonStepIndex++)
                      : null,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLessonControls(ThemeData theme) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(Icons.skip_previous, color: generalTextColor),
              onPressed: _goToPreviousStep,
            ),
            IconButton(
              icon: Icon(_lessonPlaying ? Icons.pause : Icons.play_arrow, color: generalTextColor),
              onPressed: _toggleLessonPlay,
            ),
            IconButton(
              icon: Icon(Icons.skip_next, color: generalTextColor),
              onPressed: _goToNextStep,
            ),
            if (_isFullscreen)
              IconButton(
                icon: Icon(Icons.fullscreen_exit, color: generalTextColor),
                onPressed: () => setState(() {
                  _isFullscreen = false;
                  _showFullscreenButton = true;
                }),
              ),
          ],
        ),
      ],
    );
  }

  void _goToPreviousStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex > 0) {
        _currentLessonStepIndex--;
      }
      _lessonPlaying = false;
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _goToNextStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
      }
      _lessonPlaying = false;
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _stopLessonStepDisplay() {
    _stopTts();
    _stopTextAnimation();
  }

  void _stopTextAnimation() {
    _textAnimationTimer?.cancel();
    _isTextAnimationActive = false;
    _displayText = '';
    _currentCharIndex = 0;
  }

  void _goToNextStepForTts() {
    if (_lessonSteps.isEmpty) return;

    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
        _displayText = '';
        _currentCharIndex = 0;
        _startLessonStepDisplay();
      } else {
        _lessonPlaying = false;
      }
    });
  }

  void _restartLesson() {
    setState(() {
      _currentLessonStepIndex = 0;
      _stopLessonStepDisplay();
      _startLessonStepDisplay();
    });
  }

  void _toggleLessonPlay() {
    setState(() {
      _lessonPlaying = !_lessonPlaying;
      if (_lessonPlaying) {
        _startLessonStepDisplay();
      } else {
        _pauseLessonStepDisplay();
      }
    });
  }

  void _pauseLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _pause();
    setState(() => _isTextAnimationActive = false);
  }

  void _startLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _currentCharIndex = 0;
    _displayText = '';
    _isTextAnimationActive = true;

    final fullText = _lessonSteps[_currentLessonStepIndex].replaceAll(RegExp(r'\[.*?\]'), '').trim();

    if (_lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);

      _textAnimationTimer = Timer.periodic(const Duration(milliseconds: 30), (timer) {
        if (_currentCharIndex < fullText.length) {
          setState(() {
            _displayText += fullText[_currentCharIndex];
            _currentCharIndex++;
          });
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _lessonScrollController.animateTo(
              _lessonScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 100),
              curve: Curves.easeOut,
            );
          });
        } else {
          timer.cancel();
          _isTextAnimationActive = false;
        }
      });
    }
  }

  void _startTtsForCurrentStep() {
    if (_lessonSteps.isNotEmpty && _currentLessonStepIndex < _lessonSteps.length && _lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);
    }
  }

  void _stopTts() {
    flutterTts.stop();
    setState(() => _lessonPlaying = false);
  }

  void _pauseTts() {
    flutterTts.pause();
    setState(() => _lessonPlaying = false);
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return _flashcards.isEmpty
        ? _buildEmptyCard('Generating flashcards...', theme, generalTextColor, 'Flashcards')
        : Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SizedBox(
                    height: 400,
                    child: PageView.builder(
                      itemCount: _flashcards.length,
                      itemBuilder: (context, index) => FlashcardWidget(
                        flashcard: _flashcards[index],
                        textColor: generalTextColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
  }

  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(20)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateWeaknessNotes(
      BuildContext context, String? fileContent, List<QuizResult> quizResults) async {
    if (fileContent == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No file content available to analyze.')),
      );
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Generating review notes...')),
    );
    try {
      String weaknessPrompt = _buildWeaknessPrompt(fileContent, quizResults);
      final response = await _geminiModel.generateContent([Content('user', [TextPart(weaknessPrompt)])]);
      if (response.text != null) {
        await _exportWeaknessNotesToPdf(response.text!);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to generate review notes.')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating review notes: ${e.toString()}')),
      );
    }
  }

  String _buildWeaknessPrompt(String fileContent, List<QuizResult> quizResults) {
    List<String> incorrectQuestions =
        quizResults.where((result) => !result.isCorrect).map((result) => result.question).toList();
    String questionsList = incorrectQuestions.join("\n- ");

    return '''Generate comprehensive notes focused on the topics covered in these quiz questions that the user answered incorrectly. Use the following source material to create the notes. Focus specifically on areas where the user demonstrated weakness in the quiz.

Incorrect Quiz Questions:
- $questionsList

Source Material:
$fileContent
''';
  }

  Future<void> _exportWeaknessNotesToPdf(String notesContent) async {
    final pdf = pw.Document();
    pdf.addPage(
      pw.Page(
        margin: const pw.EdgeInsets.all(20),
        build: (context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: _buildPdfNotesContent(notesContent),
        ),
      ),
    );

    final Uint8List pdfBytes = await pdf.save();
    if (kIsWeb) {
      final blob = html.Blob([pdfBytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'review_notes_${DateTime.now().millisecondsSinceEpoch}.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
        if (!await _requestStoragePermission()) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Storage permission denied')),
          );
          return;
        }
      }

      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        final fileName = 'review_notes_${DateTime.now().millisecondsSinceEpoch}.pdf';
        final filePath = path.join(selectedDirectory, fileName);
        final file = File(filePath);
        await file.writeAsBytes(pdfBytes);

        if (!kIsWeb && Platform.isAndroid) {
          await _scanFileForGalleryAndroid(filePath);
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Review Notes PDF saved to $filePath.'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}

class FileWithPageRange {
  PlatformFile file;
  int? startPage;
  int? endPage;

  FileWithPageRange({required this.file, this.startPage, this.endPage});
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({required this.question, required this.options, required this.correctAnswerIndex});
}

class ExamQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;

  ExamQuestion({required this.question, required this.options, required this.correctAnswer});
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({Key? key, required this.flashcard, required this.textColor}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FlipCard(
      front: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.question,
                style: GoogleFonts.notoSans(fontSize: 17, color: textColor)),
          ),
        ),
      ),
      back: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.answer,
                style: GoogleFonts.notoSans(fontSize: 18, color: textColor)),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({Key? key, required this.message, required this.isDarkMode}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(message.text, style: GoogleFonts.notoSans(color: bubbleTextColor)),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final List<QuizQuestion> questions;
  final Color textColor;
  final List<QuizResult>? quizResults;
  final String? fileContent;
  final VoidCallback onGenerateNotes;

  const ExamResultsDialog({
    Key? key,
    required this.questions,
    required this.textColor,
    this.quizResults,
    this.fileContent,
    required this.onGenerateNotes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        quizResults != null ? 'Quiz Results' : 'Practice Exam Answer Key',
        style: GoogleFonts.notoSans(color: textColor),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              quizResults != null ? 'Quiz Results:' : 'Answer Key:',
              style: GoogleFonts.notoSans(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            if (quizResults != null) ..._buildQuizResultsView(quizResults!),
            if (quizResults == null) ..._buildExamAnswerKeyView(),
            if (quizResults != null && fileContent != null)
              _buildWeaknessAnalysisSection(context, quizResults!, fileContent!),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }

  List<Widget> _buildQuizResultsView(List<QuizResult> results) {
    return results.asMap().entries.map((entry) {
      int index = entry.key;
      QuizResult result = entry.value;
      String correctAnswer = result.correctAnswer;
      String userAnswer = result.userAnswer;
      String answerLetter = questions[index].correctAnswerIndex != null
          ? String.fromCharCode('A'.codeUnitAt(0) + questions[index].correctAnswerIndex!)
          : '?';
      String userAnswerDisplay = userAnswer == 'Not answered' ? 'Not answered' : userAnswer;

      return ListTile(
        title: Text('${index + 1}. ${result.question}',
            style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your answer: $userAnswerDisplay',
                style: GoogleFonts.notoSans(
                    color: userAnswer == correctAnswer ? Colors.green : Colors.red)),
            Text('Correct answer: $answerLetter) $correctAnswer',
                style: GoogleFonts.notoSans(color: textColor)),
          ],
        ),
      );
    }).toList();
  }

  List<Widget> _buildExamAnswerKeyView() {
    return questions.asMap().entries.map((entry) {
      int index = entry.key;
      QuizQuestion question = entry.value;
      String correctAnswer = question.correctAnswerIndex != null
          ? question.options[question.correctAnswerIndex!]
          : 'Unknown';
      String answerLetter = question.correctAnswerIndex != null
          ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!)
          : '?';

      return ListTile(
        title: Text('${index + 1}. ${question.question}',
            style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
        subtitle: Text('Correct answer: $answerLetter) $correctAnswer',
            style: GoogleFonts.notoSans(color: textColor)),
      );
    }).toList();
  }

  Widget _buildWeaknessAnalysisSection(
      BuildContext context, List<QuizResult> quizResults, String fileContent) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Text(
          'Weakness Analysis:',
          style: GoogleFonts.notoSans(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        ElevatedButton(
          onPressed: onGenerateNotes,
          child: Text('Generate Notes for Weak Areas',
              style: GoogleFonts.notoSans(color: Colors.white)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
          ),
        ),
      ],
    );
  }
}