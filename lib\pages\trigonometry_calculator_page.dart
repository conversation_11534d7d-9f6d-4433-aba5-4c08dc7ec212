import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

class TrigonometryCalculatorPage extends StatefulWidget {
  const TrigonometryCalculatorPage({Key? key}) : super(key: key);

  @override
  _TrigonometryCalculatorPageState createState() => _TrigonometryCalculatorPageState();
}

class _TrigonometryCalculatorPageState extends State<TrigonometryCalculatorPage> {
  double _angle = 0.0;
  String _angleUnit = 'Degrees';
  String _sinValue = '';
  String _cosValue = '';
  String _tanValue = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Trigonometry Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Angle Value',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _angle = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  DropdownButton<String>(
                    value: _angleUnit,
                    dropdownColor: theme.colorScheme.surface,
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    items: <String>['Degrees', 'Radians'].map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, style: TextStyle(color: theme.colorScheme.onSurface)),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        _angleUnit = newValue!;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateTrig();
                    },
                    child: const Text('Calculate Trig Functions'),
                  ),
                  const SizedBox(height: 20),
                  Text('Sin: $_sinValue', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Cos: $_cosValue', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Tan: $_tanValue', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateTrig() {
    double angleInRadians = _angleUnit == 'Degrees' ? _angle * math.pi / 180 : _angle;

    setState(() {
      _sinValue = math.sin(angleInRadians).toStringAsFixed(4);
      _cosValue = math.cos(angleInRadians).toStringAsFixed(4);
      _tanValue = math.tan(angleInRadians).toStringAsFixed(4);
    });
  }
}