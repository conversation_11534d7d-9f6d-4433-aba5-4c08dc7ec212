import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ExperimentSimulation extends StatefulWidget {
  final String experimentData;
  final bool isDarkMode;
  final double fontSize;

  const ExperimentSimulation({
    Key? key,
    required this.experimentData,
    required this.isDarkMode,
    this.fontSize = 14.0,
  }) : super(key: key);

  @override
  State<ExperimentSimulation> createState() => _ExperimentSimulationState();
}

class _ExperimentSimulationState extends State<ExperimentSimulation> {
  // Default simulation parameters
  double _temperature = 25.0; // Celsius
  double _pressure = 1.0; // atm
  double _concentration = 1.0; // mol/L
  double _time = 0.0; // seconds
  double _volume = 1.0; // L
  double _mass = 10.0; // g
  
  // Simulation results
  double _reactionRate = 0.0;
  double _equilibriumConstant = 0.0;
  double _yield = 0.0;
  double _energy = 0.0;
  
  // Simulation type
  String _simulationType = 'chemical_reaction';
  
  @override
  void initState() {
    super.initState();
    _parseExperimentType();
    _updateSimulation();
  }
  
  void _parseExperimentType() {
    // Try to determine the experiment type from the content
    final content = widget.experimentData.toLowerCase();
    
    if (content.contains('pendulum') || content.contains('oscillation') || 
        content.contains('frequency') || content.contains('wave')) {
      setState(() => _simulationType = 'physics_oscillation');
    } else if (content.contains('circuit') || content.contains('voltage') || 
               content.contains('current') || content.contains('resistance')) {
      setState(() => _simulationType = 'physics_circuit');
    } else if (content.contains('gravity') || content.contains('acceleration') || 
               content.contains('projectile') || content.contains('motion')) {
      setState(() => _simulationType = 'physics_mechanics');
    } else if (content.contains('titration') || content.contains('acid') || 
               content.contains('base') || content.contains('ph')) {
      setState(() => _simulationType = 'chemistry_titration');
    } else if (content.contains('cell') || content.contains('bacteria') || 
               content.contains('culture') || content.contains('growth')) {
      setState(() => _simulationType = 'biology_growth');
    } else {
      setState(() => _simulationType = 'chemical_reaction');
    }
  }
  
  void _updateSimulation() {
    switch (_simulationType) {
      case 'chemical_reaction':
        _updateChemicalReaction();
        break;
      case 'physics_oscillation':
        _updatePhysicsOscillation();
        break;
      case 'physics_circuit':
        _updatePhysicsCircuit();
        break;
      case 'physics_mechanics':
        _updatePhysicsMechanics();
        break;
      case 'chemistry_titration':
        _updateChemistryTitration();
        break;
      case 'biology_growth':
        _updateBiologyGrowth();
        break;
    }
  }
  
  void _updateChemicalReaction() {
    // Arrhenius equation for temperature dependence: k = A * e^(-Ea/RT)
    final activationEnergy = 50.0; // kJ/mol (example value)
    final gasConstant = 8.314 / 1000; // kJ/(mol·K)
    final preExponentialFactor = 1e10; // example value
    
    // Convert temperature to Kelvin
    final temperatureK = _temperature + 273.15;
    
    // Calculate rate constant using Arrhenius equation
    final rateConstant = preExponentialFactor * exp(-activationEnergy / (gasConstant * temperatureK));
    
    // Rate = k * [A] * [B], simplified as k * concentration^2 for a second-order reaction
    _reactionRate = rateConstant * pow(_concentration, 2);
    
    // Calculate equilibrium constant (simplified model)
    _equilibriumConstant = 10 * exp(-activationEnergy / (gasConstant * temperatureK));
    
    // Calculate theoretical yield (simplified)
    _yield = 100 * (1 - exp(-_time * _reactionRate / 100));
    
    // Calculate energy change (simplified)
    _energy = _mass * 4.184 * (_temperature - 25); // Using specific heat of water as approximation
  }
  
  void _updatePhysicsOscillation() {
    // For a pendulum: T = 2π * sqrt(L/g)
    final length = _mass / 10; // Using mass as a proxy for length (m)
    final gravity = 9.8; // m/s²
    
    // Period of oscillation
    final period = 2 * pi * sqrt(length / gravity);
    
    // Frequency
    _reactionRate = 1 / period; // Hz
    
    // Energy of oscillation
    _energy = 0.5 * _mass * gravity * length * (1 - cos(_time * 2 * pi / period));
    
    // Amplitude decay due to damping
    final dampingFactor = 0.1;
    _yield = 100 * exp(-dampingFactor * _time);
    
    // Quality factor
    _equilibriumConstant = pi * _reactionRate / dampingFactor;
  }
  
  void _updatePhysicsCircuit() {
    // Using Ohm's Law: V = IR
    final voltage = _temperature; // Using temperature as voltage (V)
    final resistance = _mass / 10; // Using mass as resistance (Ω)
    
    // Current
    _reactionRate = voltage / resistance; // A
    
    // Power
    _energy = voltage * _reactionRate; // W
    
    // Charge
    _yield = _reactionRate * _time; // C
    
    // Capacitance (using concentration as proxy)
    _equilibriumConstant = _concentration * 10; // μF
  }
  
  void _updatePhysicsMechanics() {
    // Projectile motion
    final initialVelocity = _temperature; // m/s
    final angle = 45 * pi / 180; // 45 degrees in radians
    final gravity = 9.8; // m/s²
    
    // Range
    _reactionRate = pow(initialVelocity, 2) * sin(2 * angle) / gravity; // m
    
    // Maximum height
    _equilibriumConstant = pow(initialVelocity * sin(angle), 2) / (2 * gravity); // m
    
    // Time of flight
    _yield = 2 * initialVelocity * sin(angle) / gravity; // s
    
    // Kinetic energy
    _energy = 0.5 * _mass * pow(initialVelocity, 2); // J
  }
  
  void _updateChemistryTitration() {
    // pH calculation for acid-base titration
    final acidConcentration = _concentration; // mol/L
    final baseVolume = _volume; // L
    final baseConcentration = _temperature / 10; // mol/L (using temperature as proxy)
    
    // Calculate moles
    final acidMoles = acidConcentration * _mass / 100; // mol
    final baseMoles = baseConcentration * baseVolume; // mol
    
    // Calculate pH based on titration progress
    if (baseMoles < acidMoles) {
      // Before equivalence point
      final remainingAcid = acidMoles - baseMoles;
      _reactionRate = -log10(remainingAcid / _volume); // pH
    } else if (baseMoles > acidMoles) {
      // After equivalence point
      final excessBase = baseMoles - acidMoles;
      _reactionRate = 14 + log10(excessBase / _volume); // pH
    } else {
      // At equivalence point
      _reactionRate = 7.0; // pH
    }
    
    // Titration progress
    _yield = min(100 * baseMoles / acidMoles, 100); // percentage
    
    // Buffer capacity
    _equilibriumConstant = 2.303 * acidConcentration; // mol/L
    
    // Heat of neutralization
    _energy = -57 * min(acidMoles, baseMoles); // kJ
  }
  
  void _updateBiologyGrowth() {
    // Bacterial growth model: N = N₀ * e^(k*t)
    final initialPopulation = 1000.0; // cells
    final growthRate = _temperature / 100; // per hour
    
    // Population at time t
    final population = initialPopulation * exp(growthRate * _time);
    _reactionRate = population; // cells
    
    // Doubling time
    _equilibriumConstant = log(2) / growthRate; // hours
    
    // Growth percentage
    _yield = 100 * (population - initialPopulation) / initialPopulation; // percentage
    
    // Metabolic energy
    _energy = 0.1 * population * _concentration; // arbitrary units
  }
  
  double log10(double x) {
    return log(x) / log(10);
  }

  @override
  Widget build(BuildContext context) {
    final textColor = widget.isDarkMode ? Colors.white : Colors.black;
    final backgroundColor = widget.isDarkMode ? Colors.grey[850] : Colors.grey[200];
    final accentColor = widget.isDarkMode ? Colors.tealAccent : Colors.teal;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Title
        Text(
          'Interactive Simulation',
          style: GoogleFonts.notoSans(
            fontSize: widget.fontSize * 1.2,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        
        // Simulation type selector
        DropdownButton<String>(
          value: _simulationType,
          isExpanded: true,
          dropdownColor: backgroundColor,
          style: GoogleFonts.notoSans(color: textColor),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _simulationType = newValue;
                _updateSimulation();
              });
            }
          },
          items: [
            DropdownMenuItem(
              value: 'chemical_reaction',
              child: Text('Chemical Reaction', style: GoogleFonts.notoSans()),
            ),
            DropdownMenuItem(
              value: 'physics_oscillation',
              child: Text('Physics - Oscillation', style: GoogleFonts.notoSans()),
            ),
            DropdownMenuItem(
              value: 'physics_circuit',
              child: Text('Physics - Circuit', style: GoogleFonts.notoSans()),
            ),
            DropdownMenuItem(
              value: 'physics_mechanics',
              child: Text('Physics - Mechanics', style: GoogleFonts.notoSans()),
            ),
            DropdownMenuItem(
              value: 'chemistry_titration',
              child: Text('Chemistry - Titration', style: GoogleFonts.notoSans()),
            ),
            DropdownMenuItem(
              value: 'biology_growth',
              child: Text('Biology - Growth', style: GoogleFonts.notoSans()),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Parameters
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Adjust Parameters:',
                style: GoogleFonts.notoSans(
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
              const SizedBox(height: 12),
              
              // Temperature slider
              Row(
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      'Temperature:',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Slider(
                      value: _temperature,
                      min: 0,
                      max: 100,
                      divisions: 100,
                      activeColor: accentColor,
                      inactiveColor: accentColor.withOpacity(0.3),
                      label: '${_temperature.toStringAsFixed(1)} °C',
                      onChanged: (value) {
                        setState(() {
                          _temperature = value;
                          _updateSimulation();
                        });
                      },
                    ),
                  ),
                  SizedBox(
                    width: 50,
                    child: Text(
                      '${_temperature.toStringAsFixed(1)} °C',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Concentration slider
              Row(
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      'Concentration:',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Slider(
                      value: _concentration,
                      min: 0.1,
                      max: 5.0,
                      divisions: 49,
                      activeColor: accentColor,
                      inactiveColor: accentColor.withOpacity(0.3),
                      label: '${_concentration.toStringAsFixed(1)} mol/L',
                      onChanged: (value) {
                        setState(() {
                          _concentration = value;
                          _updateSimulation();
                        });
                      },
                    ),
                  ),
                  SizedBox(
                    width: 50,
                    child: Text(
                      '${_concentration.toStringAsFixed(1)}',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Time slider
              Row(
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      'Time:',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Slider(
                      value: _time,
                      min: 0,
                      max: 60,
                      divisions: 60,
                      activeColor: accentColor,
                      inactiveColor: accentColor.withOpacity(0.3),
                      label: '${_time.toStringAsFixed(1)} s',
                      onChanged: (value) {
                        setState(() {
                          _time = value;
                          _updateSimulation();
                        });
                      },
                    ),
                  ),
                  SizedBox(
                    width: 50,
                    child: Text(
                      '${_time.toStringAsFixed(1)} s',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Mass slider
              Row(
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      'Mass:',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Slider(
                      value: _mass,
                      min: 1,
                      max: 50,
                      divisions: 49,
                      activeColor: accentColor,
                      inactiveColor: accentColor.withOpacity(0.3),
                      label: '${_mass.toStringAsFixed(1)} g',
                      onChanged: (value) {
                        setState(() {
                          _mass = value;
                          _updateSimulation();
                        });
                      },
                    ),
                  ),
                  SizedBox(
                    width: 50,
                    child: Text(
                      '${_mass.toStringAsFixed(1)} g',
                      style: GoogleFonts.notoSans(
                        fontSize: widget.fontSize * 0.9,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Results
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: accentColor,
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Simulation Results:',
                style: GoogleFonts.notoSans(
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
              const SizedBox(height: 12),
              
              // Results based on simulation type
              _buildResultsForSimulationType(textColor),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildResultsForSimulationType(Color textColor) {
    String result1Label, result2Label, result3Label, result4Label;
    String result1Value, result2Value, result3Value, result4Value;
    
    switch (_simulationType) {
      case 'chemical_reaction':
        result1Label = 'Reaction Rate:';
        result2Label = 'Equilibrium Constant:';
        result3Label = 'Yield:';
        result4Label = 'Energy Change:';
        
        result1Value = '${_reactionRate.toStringAsFixed(6)} mol/(L·s)';
        result2Value = '${_equilibriumConstant.toStringAsFixed(4)}';
        result3Value = '${_yield.toStringAsFixed(2)}%';
        result4Value = '${_energy.toStringAsFixed(2)} kJ';
        break;
        
      case 'physics_oscillation':
        result1Label = 'Frequency:';
        result2Label = 'Quality Factor:';
        result3Label = 'Amplitude:';
        result4Label = 'Energy:';
        
        result1Value = '${_reactionRate.toStringAsFixed(4)} Hz';
        result2Value = '${_equilibriumConstant.toStringAsFixed(2)}';
        result3Value = '${_yield.toStringAsFixed(2)}%';
        result4Value = '${_energy.toStringAsFixed(4)} J';
        break;
        
      case 'physics_circuit':
        result1Label = 'Current:';
        result2Label = 'Capacitance:';
        result3Label = 'Charge:';
        result4Label = 'Power:';
        
        result1Value = '${_reactionRate.toStringAsFixed(4)} A';
        result2Value = '${_equilibriumConstant.toStringAsFixed(2)} μF';
        result3Value = '${_yield.toStringAsFixed(4)} C';
        result4Value = '${_energy.toStringAsFixed(2)} W';
        break;
        
      case 'physics_mechanics':
        result1Label = 'Range:';
        result2Label = 'Max Height:';
        result3Label = 'Flight Time:';
        result4Label = 'Kinetic Energy:';
        
        result1Value = '${_reactionRate.toStringAsFixed(2)} m';
        result2Value = '${_equilibriumConstant.toStringAsFixed(2)} m';
        result3Value = '${_yield.toStringAsFixed(2)} s';
        result4Value = '${_energy.toStringAsFixed(2)} J';
        break;
        
      case 'chemistry_titration':
        result1Label = 'pH:';
        result2Label = 'Buffer Capacity:';
        result3Label = 'Titration Progress:';
        result4Label = 'Heat of Neutralization:';
        
        result1Value = '${_reactionRate.toStringAsFixed(2)}';
        result2Value = '${_equilibriumConstant.toStringAsFixed(4)} mol/L';
        result3Value = '${_yield.toStringAsFixed(2)}%';
        result4Value = '${_energy.toStringAsFixed(2)} kJ';
        break;
        
      case 'biology_growth':
        result1Label = 'Population:';
        result2Label = 'Doubling Time:';
        result3Label = 'Growth:';
        result4Label = 'Metabolic Energy:';
        
        result1Value = '${_reactionRate.toStringAsFixed(0)} cells';
        result2Value = '${_equilibriumConstant.toStringAsFixed(2)} hours';
        result3Value = '${_yield.toStringAsFixed(2)}%';
        result4Value = '${_energy.toStringAsFixed(2)} units';
        break;
        
      default:
        result1Label = 'Result 1:';
        result2Label = 'Result 2:';
        result3Label = 'Result 3:';
        result4Label = 'Result 4:';
        
        result1Value = '${_reactionRate.toStringAsFixed(4)}';
        result2Value = '${_equilibriumConstant.toStringAsFixed(4)}';
        result3Value = '${_yield.toStringAsFixed(2)}%';
        result4Value = '${_energy.toStringAsFixed(4)}';
    }
    
    return Column(
      children: [
        _buildResultRow(result1Label, result1Value, textColor),
        const SizedBox(height: 8),
        _buildResultRow(result2Label, result2Value, textColor),
        const SizedBox(height: 8),
        _buildResultRow(result3Label, result3Value, textColor),
        const SizedBox(height: 8),
        _buildResultRow(result4Label, result4Value, textColor),
      ],
    );
  }
  
  Widget _buildResultRow(String label, String value, Color textColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.notoSans(
            fontSize: widget.fontSize * 0.9,
            color: textColor,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.notoSans(
            fontSize: widget.fontSize * 0.9,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ],
    );
  }
}
