import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'menu_detail_page.dart';
import 'login_page.dart';

class MenuPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String diningLocation;
  final List<Map<String, dynamic>>? preloadedMenus;
  final bool isFromDetailPage;

  const MenuPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.diningLocation,
    this.preloadedMenus,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MenuPageState createState() => _MenuPageState();
}

class _MenuPageState extends State<MenuPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('menu_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _menus = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _selectedDay = 'Today';
  List<String> _daysOfWeek = ['Today', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  String _selectedMeal = 'All';
  List<String> _mealOptions = ['All', 'Breakfast', 'Lunch', 'Dinner'];
  List<String> _diningStations = [];
  String _selectedStation = 'All Stations';

  @override
  void initState() {
    super.initState();
    print("MenuPage initState called for ${widget.diningLocation}");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _setTodayAsDefault();
  }

  void _setTodayAsDefault() {
    final now = DateTime.now();
    int dayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday
    
    // Convert to our format (Monday = 1, Sunday = 7)
    String today;
    switch (dayOfWeek) {
      case 1: today = 'Monday'; break;
      case 2: today = 'Tuesday'; break;
      case 3: today = 'Wednesday'; break;
      case 4: today = 'Thursday'; break;
      case 5: today = 'Friday'; break;
      case 6: today = 'Saturday'; break;
      case 7: today = 'Sunday'; break;
      default: today = 'Monday';
    }
    
    setState(() {
      _selectedDay = today;
    });
  }

  void _loadInitialData() {
    if (widget.preloadedMenus != null &&
        widget.preloadedMenus!.isNotEmpty) {
      setState(() {
        _menus = List.from(widget.preloadedMenus!);
        _isLoading = false;
        _updateDiningStations();
      });
    } else {
      _loadMenusFromDatabase();
    }
  }

  void _updateDiningStations() {
    Set<String> stations = {'All Stations'};
    for (var item in _menus) {
      if (item['diningstation'] != null && (item['diningstation'] as String).isNotEmpty) {
        stations.add(item['diningstation'] as String);
      }
    }
    setState(() {
      _diningStations = stations.toList()..sort();
    });
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('menu_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'menus',
      callback: (payload) async {
        print("Realtime update received for menus: ${payload.eventType}");
        _loadMenusFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadMenusFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _menus = [];
    });

    await _loadMoreMenus();
  }

  Future<void> _loadMoreMenus() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      var query = Supabase.instance.client
          .from('menus')
          .select('*')
          .eq('dininglocation', widget.diningLocation);
      
      // Apply day filter
      if (_selectedDay != 'Today') {
        String dayColumn = '_${_selectedDay.toLowerCase().substring(0, 3)}';
        query = query.not(dayColumn, 'is', null);
      }
      
      // Apply meal filter
      if (_selectedMeal != 'All') {
        query = query.not(_selectedMeal.toLowerCase(), 'is', null);
      }
      
      // Apply station filter
      if (_selectedStation != 'All Stations') {
        query = query.eq('diningstation', _selectedStation);
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _menus.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
        _updateDiningStations();
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading menus: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading menus: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreMenus();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Menu - ${widget.diningLocation}',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Filter options
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Day filter
                Row(
                  children: [
                    Text(
                      'Day: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                          ),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedDay,
                            isExpanded: true,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: theme.colorScheme.onSurface,
                            ),
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontSize: 16,
                            ),
                            dropdownColor: theme.colorScheme.surface,
                            items: _daysOfWeek.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null && newValue != _selectedDay) {
                                setState(() {
                                  _selectedDay = newValue;
                                  _page = 0;
                                  _menus = [];
                                  _hasMore = true;
                                });
                                _loadMoreMenus();
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Meal filter
                Row(
                  children: [
                    Text(
                      'Meal: ',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                          ),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedMeal,
                            isExpanded: true,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: theme.colorScheme.onSurface,
                            ),
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontSize: 16,
                            ),
                            dropdownColor: theme.colorScheme.surface,
                            items: _mealOptions.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null && newValue != _selectedMeal) {
                                setState(() {
                                  _selectedMeal = newValue;
                                  _page = 0;
                                  _menus = [];
                                  _hasMore = true;
                                });
                                _loadMoreMenus();
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Station filter
                if (_diningStations.isNotEmpty)
                  Row(
                    children: [
                      Text(
                        'Station: ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                            ),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: _selectedStation,
                              isExpanded: true,
                              icon: Icon(
                                Icons.arrow_drop_down,
                                color: theme.colorScheme.onSurface,
                              ),
                              style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                fontSize: 16,
                              ),
                              dropdownColor: theme.colorScheme.surface,
                              items: _diningStations.map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null && newValue != _selectedStation) {
                                  setState(() {
                                    _selectedStation = newValue;
                                    _page = 0;
                                    _menus = [];
                                    _hasMore = true;
                                  });
                                  _loadMoreMenus();
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
          
          // Menu list
          Expanded(
            child: VisibilityDetector(
              key: const Key('menu_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _menus.isEmpty && !_isLoading) {
                  _loadMenusFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadMenusFromDatabase,
                child: _menus.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No menu items found for the selected filters',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _menus.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _menus.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildMenuCard(
                            _menus[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildMenuCard(
    Map<String, dynamic> menu,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = menu['fullname'] ?? 'Unknown';
    final String diningStation = menu['diningstation'] ?? '';
    final double price = menu['price'] != null ? double.tryParse(menu['price'].toString()) ?? 0.0 : 0.0;
    
    // Get meal information based on selected day and meal
    String dayColumn = '_${_selectedDay.toLowerCase().substring(0, 3)}';
    if (_selectedDay == 'Today') {
      // Use the current day
      final now = DateTime.now();
      int dayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday
      
      switch (dayOfWeek) {
        case 1: dayColumn = '_mon'; break;
        case 2: dayColumn = '_tue'; break;
        case 3: dayColumn = '_wed'; break;
        case 4: dayColumn = '_thur'; break;
        case 5: dayColumn = '_fri'; break;
        case 6: dayColumn = '_sat'; break;
        case 7: dayColumn = '_sun'; break;
        default: dayColumn = '_mon';
      }
    }
    
    String mealInfo = '';
    if (_selectedMeal != 'All') {
      mealInfo = menu[_selectedMeal.toLowerCase()]?.toString() ?? '';
    } else {
      // Show all available meals
      if (menu['breakfast'] != null && menu['breakfast'].toString().isNotEmpty) {
        mealInfo += 'Breakfast: ${menu['breakfast']}\n';
      }
      if (menu['lunch'] != null && menu['lunch'].toString().isNotEmpty) {
        mealInfo += 'Lunch: ${menu['lunch']}\n';
      }
      if (menu['dinner'] != null && menu['dinner'].toString().isNotEmpty) {
        mealInfo += 'Dinner: ${menu['dinner']}\n';
      }
      
      // Remove trailing newline
      if (mealInfo.isNotEmpty) {
        mealInfo = mealInfo.substring(0, mealInfo.length - 1);
      }
    }
    
    // Check if this item is available on the selected day
    bool isAvailableOnSelectedDay = menu[dayColumn] != null && menu[dayColumn].toString().isNotEmpty;
    
    // If not available on the selected day, don't show it
    if (!isAvailableOnSelectedDay && _selectedDay != 'Today') {
      return const SizedBox.shrink();
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MenuDetailPage(
                menu: menu,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
                selectedDay: _selectedDay,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.restaurant_menu,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (diningStation.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          diningStation,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (price > 0)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Price: \$${price.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    if (mealInfo.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          mealInfo,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
