// helpdesks_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../main.dart';
import 'helpdesks_detail_page.dart';
import 'login_page.dart';

class HelpdesksPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable; // College name to construct table name

  const HelpdesksPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
  }) : super(key: key);

  @override
  State<HelpdesksPage> createState() => _HelpdesksPageState();
}

class _HelpdesksPageState extends State<HelpdesksPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('helpdesks_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _helpdesks = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadHelpdesks(); // Modified to use preloaded data
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadHelpdesks() async {
    // 1. Check if preloaded data exists
    if (MyApp.preloadedHelpdesks.containsKey(widget.collegeNameForTable) && MyApp.preloadedHelpdesks[widget.collegeNameForTable] != null) {
      setState(() {
        _helpdesks = MyApp.preloadedHelpdesks[widget.collegeNameForTable]!;
        _isLoading = false; // No need to show loading if preloaded
      });
      print('Helpdesks loaded from preloaded data for ${widget.collegeNameForTable}.');
      // Still fetch in background to refresh cache and get updates (optional, but good for fresh data)
      _fetchHelpdesksFromSupabase();
    } else {
      // 2. If no preloaded data, fetch from Supabase directly (fallback)
      print('No preloaded helpdesks found for ${widget.collegeNameForTable}. Fetching from Supabase...');
      _fetchHelpdesksFromSupabase();
    }
  }


  Future<void> _fetchHelpdesksFromSupabase() async {
    setState(() {
      _isLoading = true; // Show loading indicator while fetching from Supabase
    });
    final helpdesksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    print("DEBUG: Helpdesks Table Name being queried: $helpdesksTableName");

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(helpdesksTableName)
          .select('*')
          .order('fullname', ascending: true);

      print("DEBUG: Supabase Response (raw): $response");

      if (response == null || response.isEmpty) {
        print("DEBUG: Supabase response is empty or null.");
      }

      final updatedHelpdesks = await _updateHelpdeskImageUrls(response);

      setState(() {
        _helpdesks = updatedHelpdesks;
        _isLoading = false;
        _helpdesks.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
      });
       // Update preloaded data in MyApp for future use
      MyApp.preloadedHelpdesks[widget.collegeNameForTable] = List<Map<String, dynamic>>.from(updatedHelpdesks);
       print('Helpdesks updated in preloaded cache for ${widget.collegeNameForTable} after Supabase fetch.');


    } catch (error) {
      print("DEBUG: Error fetching helpdesks: $error");
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching helpdesks: $error')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }


  Future<List<Map<String, dynamic>>> _updateHelpdeskImageUrls(List<Map<String, dynamic>> helpdesks) async {
    final collegeHelpdeskBucket = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';
    print("DEBUG: Helpdesks Image Bucket: $collegeHelpdeskBucket");
    List<Map<String, dynamic>> updatedHelpdesksWithImages = []; // Create a new list

    for (final helpdesk in helpdesks) {
      final fullname = helpdesk['fullname'] as String? ?? '';
      final imageNamePng = '$fullname.png';
      final imageNameJpg = '$fullname.jpg';
      final imageNameWebp = '$fullname.webp';
      String imageUrl = 'assets/placeholder_image.png'; // Default placeholder

      try {
        await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      } catch (e) {
        try {
          await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        } catch (e) {
          try {
            await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          } catch (e) {
            // Image not found, use placeholder
          }
        }
      }
      var updatedHelpdesk = Map<String, dynamic>.from(helpdesk); // Create a copy
      updatedHelpdesk['image_url'] = imageUrl;
      updatedHelpdesksWithImages.add(updatedHelpdesk); // Add the updated copy to the new list
      print("DEBUG: Image URL for ${helpdesk['fullname']}: $imageUrl");
    }
    return updatedHelpdesksWithImages; // Return the new list with image URLs
  }


  void _setupRealtime() {
    final helpdesksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_helpdesks';
    _realtimeChannel = Supabase.instance.client
        .channel('helpdesks')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: helpdesksTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newHelpdeskId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newHelpdeskResponse = await Supabase.instance.client
              .from(helpdesksTableName)
              .select('*')
              .eq('id', newHelpdeskId);
          if (newHelpdeskResponse.isNotEmpty) {
            final newHelpdesk = newHelpdeskResponse.first;
            final updatedHelpdesk = await _updateHelpdeskImageUrls([newHelpdesk]);
            setState(() {
              _helpdesks = [..._helpdesks, updatedHelpdesk.first];
              _helpdesks.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data as well:
            (_MyAppState.maybeOf(context) ?? context.findAncestorStateOfType<_MyAppState>())?._updatePreloadedHelpdesks(widget.collegeNameForTable, [payload]); // Update preloaded data on real-time insert - Modified line
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedHelpdeskId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedHelpdeskResponse = await Supabase.instance.client
              .from(helpdesksTableName)
              .select('*')
              .eq('id', updatedHelpdeskId);
          if (updatedHelpdeskResponse.isNotEmpty) {
            final updatedHelpdesk = updatedHelpdeskResponse.first;
            setState(() {
              _helpdesks = _helpdesks.map((helpdesk) {
                return helpdesk['id'] == updatedHelpdesk['id'] ? updatedHelpdesk : helpdesk;
              }).toList();
              _helpdesks.sort((a, b) => (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
            // Update preloaded data as well:
             (_MyAppState.maybeOf(context) ?? context.findAncestorStateOfType<_MyAppState>())?._updatePreloadedHelpdesks(widget.collegeNameForTable, [payload]); // Update preloaded data on real-time update - Modified line
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedHelpdeskId = payload.oldRecord['id'];
          setState(() {
            _helpdesks.removeWhere((helpdesk) => helpdesk['id'] == deletedHelpdeskId);
          });
           // Update preloaded data as well:
           (_MyAppState.maybeOf(context) ?? context.findAncestorStateOfType<_MyAppState>())?._updatePreloadedHelpdesks(widget.collegeNameForTable, [payload]); // Update preloaded data on real-time delete - Modified line
        }
      },
    )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed) {
      setState(() {});
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> helpdesk) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HelpdeskDetailPage(
            helpdesk: helpdesk,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Helpdesks',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadHelpdesks,
              child: _helpdesks.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No helpdesks available.'),
                            ),
                          ),
                        );
                      })
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _helpdesks.length,
                      itemBuilder: (context, index) {
                        final helpdesk = _helpdesks[index];
                        return VisibilityDetector(
                          key: Key('helpdesk_${helpdesk['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 && (helpdesk['image_url'] == null || helpdesk['image_url'] == 'assets/placeholder_image.png')) {
                              _fetchImageUrl(helpdesk);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor: theme.colorScheme.secondary.withOpacity(0.1),
                                backgroundImage: CachedNetworkImageProvider(helpdesk['image_url'] ?? 'assets/placeholder_image.png') as ImageProvider<Object>,
                                onBackgroundImageError: (exception, stackTrace) {
                                  print('Error loading image for ${helpdesk['fullname']}: $exception');
                                },
                              ),
                              title: Text(
                                helpdesk['fullname'] ?? 'Unnamed Helpdesk',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  helpdesk['about'] ?? '',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, helpdesk),
                            ),
                          ),
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> helpdesk) async {
    final fullname = helpdesk['fullname'] as String? ?? '';
    final imageNamePng = '$fullname.png';
    final imageNameJpg = '$fullname.jpg';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = 'assets/placeholder_image.png';
    final collegeHelpdeskBucket = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/helpdesks';

    try {
      final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      }
    } catch (e) {
      try {
        final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
        if (file.isNotEmpty) {
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        }
      } catch (e) {
        try {
          final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
          if (file.isNotEmpty) {
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          }
        } catch (e) {
          // Image not found, use placeholder
        }
      }
    }

    if (mounted) {
      setState(() {
        helpdesk['image_url'] = imageUrl;
      });
    }
  }
}