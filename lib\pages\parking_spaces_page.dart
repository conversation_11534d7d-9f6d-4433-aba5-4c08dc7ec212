import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'parking_space_detail_page.dart';
import 'login_page.dart';

class ParkingSpacesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedParkingSpaces;
  final bool isFromDetailPage;

  const ParkingSpacesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedParkingSpaces,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ParkingSpacesPageState createState() => _ParkingSpacesPageState();
}

class _ParkingSpacesPageState extends State<ParkingSpacesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('parking_spaces_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _parkingSpaces = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _selectedFilter = 'All';
  List<String> _filterOptions = ['All', 'Student', 'Faculty/Staff'];

  @override
  void initState() {
    super.initState();
    print("ParkingSpacesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedParkingSpaces != null &&
        widget.preloadedParkingSpaces!.isNotEmpty) {
      setState(() {
        _parkingSpaces = List.from(widget.preloadedParkingSpaces!);
        _isLoading = false;
      });
    } else {
      _loadParkingSpacesFromDatabase();
    }
  }

  void _setupRealtime() {
    final parkingSpacesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_parkingspaces';
    _realtimeChannel = Supabase.instance.client
        .channel('parking_spaces_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: parkingSpacesTableName,
      callback: (payload) async {
        print("Realtime update received for parking spaces: ${payload.eventType}");
        _loadParkingSpacesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadParkingSpacesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _parkingSpaces = [];
    });

    await _loadMoreParkingSpaces();
  }

  Future<void> _loadMoreParkingSpaces() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final parkingSpacesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_parkingspaces';
      var query = Supabase.instance.client
          .from(parkingSpacesTableName)
          .select('*');
      
      if (_selectedFilter == 'Student') {
        query = query.eq('studentparking', true);
      } else if (_selectedFilter == 'Faculty/Staff') {
        query = query.eq('facultyorstaffparking', true);
      }
      
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _parkingSpaces.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading parking spaces: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading parking spaces: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreParkingSpaces();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Parking Spaces/Lots',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Filter dropdown
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Filter: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface,
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.colorScheme.surface,
                        items: _filterOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != _selectedFilter) {
                            setState(() {
                              _selectedFilter = newValue;
                              _page = 0;
                              _parkingSpaces = [];
                              _hasMore = true;
                            });
                            _loadMoreParkingSpaces();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Parking spaces list
          Expanded(
            child: VisibilityDetector(
              key: const Key('parking_spaces_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _parkingSpaces.isEmpty && !_isLoading) {
                  _loadParkingSpacesFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadParkingSpacesFromDatabase,
                child: _parkingSpaces.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No parking spaces found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _parkingSpaces.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _parkingSpaces.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildParkingSpaceCard(
                            _parkingSpaces[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildParkingSpaceCard(
    Map<String, dynamic> parkingSpace,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = parkingSpace['fullname'] ?? 'Unknown';
    final String hours = parkingSpace['hours'] ?? '';
    final String payment = parkingSpace['payment'] ?? '';
    final int capacity = parkingSpace['capacity'] ?? 0;
    final bool isStudentParking = parkingSpace['studentparking'] ?? false;
    final bool isFacultyStaffParking = parkingSpace['facultyorstaffparking'] ?? false;
    
    // Get nearby buildings
    List<String> nearbyBuildings = [];
    for (int i = 1; i <= 7; i++) {
      final String? building = parkingSpace['building$i'];
      if (building != null && building.isNotEmpty) {
        nearbyBuildings.add(building);
      }
    }
    
    // Determine parking type
    String parkingType = '';
    if (isStudentParking && isFacultyStaffParking) {
      parkingType = 'Student & Faculty/Staff';
    } else if (isStudentParking) {
      parkingType = 'Student';
    } else if (isFacultyStaffParking) {
      parkingType = 'Faculty/Staff';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ParkingSpaceDetailPage(
                parkingSpace: parkingSpace,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.local_parking,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (parkingType.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          parkingType,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (capacity > 0)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Capacity: $capacity spaces',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (nearbyBuildings.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Near: ${nearbyBuildings.join(", ")}',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (payment.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Payment: $payment',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
