import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui';
import 'package:path/path.dart' as path;
import 'dart:io' as io;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<PlatformFile> _pickedFiles = [];
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';

  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  get isPlaying => ttsState == TtsState.playing;
  get isStopped => ttsState == TtsState.stopped;
  get isPaused => ttsState == TtsState.paused;
  get isContinued => ttsState == TtsState.continued;
  String _ttsLanguage = 'en-US';

  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
  }

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-1.5-pro',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'txt', 'doc', 'docx'],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        if (mounted) {
          setState(() {
            _pickedFiles = result.files;
            _isUploading = true;
            _uploadProgress = 0.0;
            _geminiOutput = null;
            _processingProgress = 0.0;
            _geminiOutput = null;
            _flashcards = [];
            _quizQuestions = [];
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Files ready: ${_pickedFiles.map((file) => file.name).join(', ')}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final extractor = sf_pdf.PdfTextExtractor(document);
      final text = extractor.extractText();
      document.dispose();
      return text;
    } catch (e) {
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<void> _processFile() async {
    if (_pickedFiles.isEmpty) return;

    if (mounted) {
      setState(() {
        _isProcessing = true;
        _geminiOutput = null;
        _processingProgress = 0.0;
        _geminiOutput = null;
        _flashcards = [];
        _quizQuestions = [];
      });
    }

    try {
      String combinedFileContent = '';

      for (int fileIndex = 0; fileIndex < _pickedFiles.length; fileIndex++) {
        final PlatformFile pickedFile = _pickedFiles[fileIndex];
        String fileContent = '';
        final fileName = pickedFile.name.toLowerCase();

        if (fileName.endsWith('.pdf')) {
          if (pickedFile.bytes != null) {
            fileContent = await _extractTextFromPdf(pickedFile.bytes!) ?? '';
          } else if (pickedFile.path != null) {
            final bytes = await File(pickedFile.path!).readAsBytes();
            fileContent = await _extractTextFromPdf(bytes) ?? '';
          }
        } else if (fileName.endsWith('.txt') ||
            fileName.endsWith('.doc') ||
            fileName.endsWith('.docx')) {
          fileContent = utf8.decode(pickedFile.bytes!);
        }

        if (fileContent.isNotEmpty) {
          combinedFileContent += fileContent + '\n\n';
        }

        setState(() => _processingProgress = (fileIndex + 1) / _pickedFiles.length * 0.8);

      }

      if (combinedFileContent.isEmpty) throw Exception('Content extraction failed from all files');

      if (mounted) {
        setState(() => _fileContent = combinedFileContent);
      }


      final prompt = _buildPrompt(combinedFileContent);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        _handleResponse(response.text!);
      } else {
        throw Exception('AI response empty');
      }

      for (int i = 80; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 50));
          if (mounted) {
            setState(() => _processingProgress = i / 100);
          }
        }


    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Processing error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
        _processingProgress = 0.0;
      }
    }
  }

  String _buildPrompt(String content) {
    final prompts = {
      'notes': '''Generate detailed notes with:
- Clear headings
- Bullet points
- Key terms in **bold**
- Examples in *italic*
- Use dash (-) for bullet points in markdown
- do not mention the source name

Content: $content''',
      'cheatsheet': '''Generate a concise cheatsheet for the content.
Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.
Use dash (-) for bullet points in markdown

Content: $content''',
      'flashcards': '''Generate at least 50 flashcards (Q: question / A: answer) or generate a comprehensive set of flashcards if the content is less but can be comprehensively covered:

Q: [Question]
A: [Answer]

Content: $content''',
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 questions. Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz.

Example Format:
1. What is the capital of France?
A) London
B) Paris
C) Berlin
D) Rome
Answer: B

2. What is the chemical symbol for water?
A) H2O
B) CO2
C) NaCl
D) O2
Answer: A

Now generate a quiz based on the following content, using the EXACT format above:

Content: $content''',
      'transcript': '''Create transcript:
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',
      'chat': '''Use as context:
$content
Provide summary and discussion question''',
      'summary': '''Generate a brief summary of the content. Keep it concise and to the point.

Content: $content''',
      'exam': '''Generate a comprehensive practice exam with at least 50 questions covering all aspects of the content.
Use this EXACT format for each question and answer:
[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]

Answer: [Correct Answer Text]


Do NOT include any markdown formatting, headers, or explanatory text.
Content: $content''',
      'minutes': '''Generate meeting minutes based on the content provided.
Include:
- Meeting Title
- Date and Time
- Attendees
- Agenda Items
- Discussions
- Action Items
- Decisions

Content: $content'''
    };

    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {
    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
      case 'exam':
        _parseQuiz(response);
        break;
      case 'chat':
        _startChatSession(response);
        break;
      default:
        break;
    }
  }

  void _parseFlashcards(String response) {
    final flashcards = <Flashcard>[];
    final lines = response.split('\n');
    String? currentQuestion;

    for (String line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('Q:')) {
        currentQuestion = trimmedLine.substring(2).trim();
      } else if (trimmedLine.startsWith('A:') && currentQuestion != null) {
        flashcards.add(Flashcard(
          question: currentQuestion,
          answer: trimmedLine.substring(2).trim(),
        ));
        currentQuestion = null;
      }
    }

    if (mounted) {
      setState(() => _flashcards = flashcards);
    }
  }

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer|Answers|Correct):\s*([A-D]?)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');
    final answerRegex = RegExp(r'Answer:\s*([A-D]?)', caseSensitive: false); // Corrected regex

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response');

    for (final match in matches) {
      print('\n--- Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Letter Group: ${match.group(3)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      String correctLetter = match.group(3)?.toUpperCase() ?? '';
       if (correctLetter.isEmpty) {
          final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
          correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
       }
      print('Correct Letter: $correctLetter');


      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
        if (correctAnswerIndex == -1) {
          print(
              'Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');
          correctAnswerIndex = null;
        }
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
      });
    }
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    if (_quizQuestions.isEmpty) {
      return Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            _isProcessing
                ? 'Generating quiz questions...'
                : 'No quiz questions generated. Try processing the file first.',
            style: GoogleFonts.notoSans(color: generalTextColor),
          ),
        ),
      );
    }

    final question = _quizQuestions[_currentQuestionIndex];
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                  style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                ),
                Text(
                  'Score: $_quizScore',
                  style: GoogleFonts.notoSans(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.dividerColor),
              ),
              child: Text(question.question,
                  style:
                      GoogleFonts.notoSans(fontSize: 16, color: generalTextColor)),
            ),
            const SizedBox(height: 20),
            ...List.generate(question.options.length, (index) {
              if (question.options[index].isEmpty) return const SizedBox.shrink();

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Material(
                  color: _userAnswers[_currentQuestionIndex] == index
                      ? theme.colorScheme.primary.withOpacity(0.2)
                      : theme.cardColor,
                  borderRadius: BorderRadius.circular(8),
                  child: InkWell(
                    onTap: () {
                      setState(() => _userAnswers[_currentQuestionIndex] = index);
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Text(
                            '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                            style: GoogleFonts.notoSans(
                                fontWeight: FontWeight.bold,
                                color: generalTextColor),
                          ),
                          Expanded(
                            child: Text(
                              question.options[index],
                              style:
                                  GoogleFonts.notoSans(color: generalTextColor),
                            ),
                          ),
                          Radio<int>(
                            value: index,
                            groupValue: _userAnswers[_currentQuestionIndex],
                            onChanged: (value) {
                              setState(
                                  () => _userAnswers[_currentQuestionIndex] = value);
                            },
                            activeColor: theme.colorScheme.primary,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _userAnswers[_currentQuestionIndex] != null
                  ? () {
                      if (_userAnswers[_currentQuestionIndex] ==
                          question.correctAnswerIndex) {
                        setState(() => _quizScore++);
                      }
                      _submitQuiz();
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBackground,
                padding: const EdgeInsets.symmetric(vertical: 16),
                disabledBackgroundColor: Colors.grey,
              ),
              child: Text(
                _currentQuestionIndex < _quizQuestions.length - 1
                    ? 'Next Question'
                    : 'Finish Quiz',
                style: GoogleFonts.notoSans(
                    color: _userAnswers[_currentQuestionIndex] != null
                        ? buttonTextColor
                        : Colors.grey[400],
                    fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _submitQuiz() {
    if (_currentQuestionIndex < _quizQuestions.length - 1) {
      setState(() => _currentQuestionIndex++);
      return;
    }

    final results = <QuizResult>[];

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        textColor: generalTextColor,
      ),
    );
  }

  void _startChatSession(String response) {
    try {
      _chatSession = _geminiModel.startChat();

      _chatSession.sendMessage(
          Content.text("Here's the document content for context:\n$_fileContent"));

      if (mounted) {
        setState(() {
          _chatMessages = [
            ChatMessage(
                "AI Assistant: I've analyzed your document. How can I help you with it?",
                false),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
        );
      }
    }
  }

  List<pw.Widget> _buildPdfContent(String markdownText, String contentType) {
    if (contentType == 'exam') {
      return _buildPdfExamContent(markdownText);
    } else {
      return _buildPdfNotesContent(markdownText);
    }
  }


  List<pw.Widget> _buildPdfNotesContent(String markdownText) {
    String cleanedText = markdownText
        .replaceAll(RegExp(r'^```.*?^```', multiLine: true), '')
        .replaceAll(RegExp(r'^Here is .+?:', multiLine: true), '')
        .replaceAll(RegExp(r'^[#]+ ', multiLine: true), '')
        .trim();

    List<String> lines = cleanedText.split('\n');
    List<pw.Widget> widgets = [];

    for (var line in lines) {
      if (line.startsWith('# ')) {
        widgets.add(pw.Text(
          line.substring(2).replaceFirst(RegExp(r'^[#]+ '), '',),
          style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
        ));
      } else if (line.startsWith('## ')) {
        widgets.add(pw.Text(
          line.substring(3).replaceFirst(RegExp(r'^[#]+ '), '',),
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ));
      }
      else if (line.startsWith('### ')) {
        widgets.add(pw.Text(
          line.substring(4).replaceFirst(RegExp(r'^[#]+ '), '',),
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ));
      }
      else if (line.trim().startsWith('- ')) {
        widgets.add(pw.Text(
          line.replaceFirst('- ', '  • '),
          style: pw.TextStyle(fontSize: 14),
        ));
      }
      else {
        widgets.add(_buildRichTextFromMarkdown(line));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }


  List<pw.Widget> _buildPdfExamContent(String markdownText) {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];
    bool isAnswerSection = false;

    for (var line in lines) {
      if (line.startsWith('Answers:') || line.startsWith('Answer Key:')) {
        isAnswerSection = true;
        widgets.add(pw.SizedBox(height: 20));
        widgets.add(pw.Text('Answer Key', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)));
        widgets.add(pw.SizedBox(height: 10));
        continue;
      }

      if (line.trim().isEmpty) continue;

      pw.TextStyle textStyle = pw.TextStyle(fontSize: 12);
      if (isAnswerSection) {
        textStyle = pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold);
      }

      widgets.add(pw.Text(line, style: textStyle));
    }
    return widgets;
  }


  pw.Widget _buildRichTextFromMarkdown(String text) {
    List<pw.TextSpan> spans = [];
    final pattern =
        RegExp(r'(\*\*.*?\*\*|\*.*?\*|~~.*?~~|`.*?`|\[.*?\]\(.*?\)|[^*~`]+)');

    for (final match in pattern.allMatches(text)) {
      final segment = match.group(0)!;
      if (segment.startsWith('**') && segment.endsWith('**')) {
        spans.add(pw.TextSpan(
          text: segment.substring(2, segment.length - 2),
          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        ));
      } else if (segment.startsWith('*') && segment.endsWith('*')) {
        spans.add(pw.TextSpan(
          text: segment.substring(1, segment.length - 1),
          style: pw.TextStyle(fontStyle: pw.FontStyle.italic),
        ));
      } else {
        spans.add(pw.TextSpan(text: segment));
      }
    }

    return pw.RichText(
      text: pw.TextSpan(
        children: spans,
        style: pw.TextStyle(fontSize: 14),
      ),
    );
  }

  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    if (kIsWeb) {
      final regularData = await rootBundle.load('assets/fonts/NotoSans-Regular.ttf');
      final boldData = await rootBundle.load('assets/fonts/NotoSans-Bold.ttf');
      final italicData = await rootBundle.load('assets/fonts/NotoSans-Italic.ttf');
      final boldItalicData = await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf');

      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: pw.Font.ttf(regularData.buffer.asByteData()),
          bold: pw.Font.ttf(boldData.buffer.asByteData()),
          italic: pw.Font.ttf(italicData.buffer.asByteData()),
          boldItalic: pw.Font.ttf(boldItalicData.buffer.asByteData()),
        ),
      );

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          header: (context) => pw.Container(
            alignment: pw.Alignment.centerLeft,
            margin: const pw.EdgeInsets.only(bottom: 10),
            child: pw.Text('Refactr AI',
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                )),
          ),
          build: (context) => _buildPdfContent(_geminiOutput!, _processType),
        ),
      );

      Uint8List pdfBytes = await pdf.save();
      final blob = html.Blob([pdfBytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      html.document.body!.children.add(anchor);
      anchor.click();
      html.Url.revokeObjectUrl(url);
      html.document.body!.children.remove(anchor);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('PDF downloaded successfully!')),
      );
    } else {
      if (io.Platform.isAndroid || io.Platform.isIOS) {
        if (!await _requestStoragePermission()) {
          return;
        }
      }

      String? outputFile;
      try {
        outputFile = await FilePicker.platform.getDirectoryPath();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open file picker.')),
        );
        return;
      }

      if (outputFile == null) {
        return;
      }

      try {
        final regularData = await rootBundle.load('assets/fonts/NotoSans-Regular.ttf');
        final boldData = await rootBundle.load('assets/fonts/NotoSans-Bold.ttf');
        final italicData = await rootBundle.load('assets/fonts/NotoSans-Italic.ttf');
        final boldItalicData = await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf');

        final pdf = pw.Document(
          theme: pw.ThemeData.withFont(
            base: pw.Font.ttf(regularData.buffer.asByteData()),
            bold: pw.Font.ttf(boldData.buffer.asByteData()),
            italic: pw.Font.ttf(italicData.buffer.asByteData()),
            boldItalic: pw.Font.ttf(boldItalicData.buffer.asByteData()),
          ),
        );

        pdf.addPage(
          pw.MultiPage(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(32),
            header: (context) => pw.Container(
              alignment: pw.Alignment.centerLeft,
              margin: const pw.EdgeInsets.only(bottom: 10),
              child: pw.Text('Refactr AI',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  )),
            ),
            build: (context) => _buildPdfContent(_geminiOutput!, _processType),
          ),
        );

        Uint8List pdfBytes = await pdf.save();
        final fileName = '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
        final filePath = path.join(outputFile, fileName);
        final file = io.File(filePath);
        await file.writeAsBytes(pdfBytes);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF saved to ${file.path}')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error exporting to PDF: ${e.toString()}')),
        );
      }
    }
  }

  Future<bool> _requestStoragePermission() async {
    return true;
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages.add(
            ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setStartHandler(() {
      setState(() {
        ttsState = TtsState.playing;
      });
    });

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

  Future<void> _speak(String text) async {
    if (text.isNotEmpty) {
      await flutterTts.setVolume(volume);
      await flutterTts.setSpeechRate(rate);
      await flutterTts.setPitch(pitch);
      await flutterTts.setLanguage(_ttsLanguage);

      String cleanedText = text
          .replaceAll(RegExp(r'[*~`#-]'), '')
          .replaceAll(RegExp(r'[\n\r]'), ' ')
          .trim();

      if (ttsState == TtsState.playing) {
        var result = await flutterTts.pause();
        if (result == 1) setState(() => ttsState = TtsState.paused);
      } else {
        var result = await flutterTts.speak(cleanedText);
        if (result == 1) setState(() => ttsState = TtsState.playing);
      }
    }
  }

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }


  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(isPlaying ? Icons.pause : Icons.play_arrow, color: generalTextColor),
            onPressed: () => isPlaying
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Colors.grey,
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              trackHeight: 4.0,
              thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
              overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
            ),
            child: Slider(
              value: rate,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: "Speed",
              activeColor: widget.isDarkMode ? Colors.white : Colors.black, // Theme-aware color
              inactiveColor: Colors.grey, // Keep inactive color grey
              onChanged: (double value) {
                setState(() => rate = value);
              },
            ),
          ),
        ],
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'AI Learning Dashboard',
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: generalTextColor),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, generalTextColor, buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, generalTextColor),
                ],
              ),
            ),
      bottomNavigationBar: _processType == 'notes' && _geminiOutput != null ? BottomAppBar(
        child: _buildTtsControls(),
      ) : null,
    );
  }

  Widget _buildFileSection(ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.upload_file, color: buttonTextColor),
              label: Text('Select Learning Material(s)',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              ),
              onPressed: _isUploading ? null : _pickFile,
            ),
            if (_pickedFiles.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Selected Files:',
                        style: GoogleFonts.notoSans(
                            color: generalTextColor.withOpacity(0.8))),
                    ..._pickedFiles.map((file) => Text('• ${file.name}',
                        style: GoogleFonts.notoSans(
                            color: generalTextColor.withOpacity(0.6), fontSize: 14))).toList(),
                  ],
                ),
              ),
            if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _uploadProgress),
              ),
             if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor, Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: const [
                DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                DropdownMenuItem(value: 'chat', child: Text('Chat with Content')),
                DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
                DropdownMenuItem(value: 'minutes', child: Text('Create Meeting Minutes')),
              ],
              onChanged: _isProcessing ? null : (value) => setState(() => _processType = value!),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.auto_awesome, color: buttonTextColor),
              label: Text(
                  _isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: _isProcessing ? null : _processFile,
            ),
             if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
              if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    String titleText = 'Generated Content';
    switch (_processType) {
      case 'notes': titleText = 'Notes'; break;
      case 'cheatsheet': titleText = 'Cheatsheet'; break;
      case 'flashcards': titleText = 'Flashcards'; break;
      case 'quiz': titleText = 'Quiz'; break;
      case 'exam': titleText = 'Practice Exam'; break;
      case 'transcript': titleText = 'Transcript'; break;
      case 'chat': titleText = 'Chat'; break;
      case 'summary': titleText = 'Summary'; break;
      case 'minutes': titleText = 'Meeting Minutes'; break;
    }


    if (_processType == 'flashcards') {
      return _buildFlashcardsView(theme, generalTextColor);
    } else if (_processType == 'quiz' || _processType == 'exam') {
      return _buildQuizView(theme, generalTextColor);
    } else if (_processType == 'chat') {
      return _buildChatView(theme, generalTextColor);
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(titleText,
                    style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor)),
                IconButton(
                  icon: Icon(Icons.download, color: generalTextColor),
                  onPressed: () => _exportToPdf(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: MarkdownBody(
                data: _geminiOutput ?? 'No content generated',
                styleSheet: MarkdownStyleSheet(
                  h1: GoogleFonts.notoSans(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: generalTextColor),
                  p: GoogleFonts.notoSans(fontSize: 16, color: generalTextColor),
                  code: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                      backgroundColor: theme.cardColor,
                      color: generalTextColor),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: generalTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FlipCard(
      front: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.question,
                style: GoogleFonts.notoSans(fontSize: 20, color: textColor)),
          ),
        ),
      ),
      back: Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(flashcard.answer,
                style: GoogleFonts.notoSans(fontSize: 18, color: textColor)),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(message.text,
            style: GoogleFonts.notoSans(color: bubbleTextColor)),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final List<QuizQuestion> questions;
  final Color textColor;

  const ExamResultsDialog({
    required this.questions,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Practice Exam Answer Key', style: GoogleFonts.notoSans(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Answer Key:',
                style: GoogleFonts.notoSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...questions.asMap().entries.map((entry) {
              int index = entry.key;
              QuizQuestion question = entry.value;
              String correctAnswer = question.correctAnswerIndex != null && question.correctAnswerIndex! < question.options.length
                  ? question.options[question.correctAnswerIndex!]
                  : 'Unknown';
              String answerLetter = question.correctAnswerIndex != null ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!) : '?';

              return ListTile(
                title: Text('${index + 1}. ${question.question}',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Correct answer: $answerLetter) $correctAnswer',
                        style: GoogleFonts.notoSans(color: Colors.green)),
                    const Divider(),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }
}


Color _getScoreColor(double percentage) {
  if (percentage >= 0.9) return Colors.green;
  if (percentage >= 0.7) return Colors.orange;
  return Colors.red;
}