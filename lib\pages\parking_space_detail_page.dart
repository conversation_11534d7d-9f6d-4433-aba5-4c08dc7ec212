import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ParkingSpaceDetailPage extends StatefulWidget {
  final Map<String, dynamic> parkingSpace;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ParkingSpaceDetailPage({
    Key? key,
    required this.parkingSpace,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ParkingSpaceDetailPage> createState() => _ParkingSpaceDetailPageState();
}

class _ParkingSpaceDetailPageState extends State<ParkingSpaceDetailPage> {
  late RealtimeChannel _parkingSpaceRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _parkingSpaceRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _parkingSpaceRealtimeChannel = Supabase.instance.client
        .channel('parking_space_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'parkingspaces',
      callback: (payload) async {
        // Manual filtering for the specific parking space
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.parkingSpace['id']) {
          print("Realtime update received for parking space detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshParkingSpace();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshParkingSpace() async {
    try {
      final response = await Supabase.instance.client
          .from('parkingspaces')
          .select('*')
          .eq('id', widget.parkingSpace['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's parkingSpace with the new data
          widget.parkingSpace.clear();
          widget.parkingSpace.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing parking space: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$phoneNumber',
    );
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $whatsappUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.parkingSpace['fullname'] ?? 'Unknown';
    final String hours = widget.parkingSpace['hours'] ?? '';
    final String payment = widget.parkingSpace['payment'] ?? '';
    final int capacity = widget.parkingSpace['capacity'] ?? 0;
    final bool isStudentParking = widget.parkingSpace['studentparking'] ?? false;
    final bool isFacultyStaffParking = widget.parkingSpace['facultyorstaffparking'] ?? false;
    final String about = widget.parkingSpace['about'] ?? '';
    
    // Get nearby buildings
    List<String> nearbyBuildings = [];
    for (int i = 1; i <= 7; i++) {
      final String? building = widget.parkingSpace['building$i'];
      if (building != null && building.isNotEmpty) {
        nearbyBuildings.add(building);
      }
    }
    
    // Determine parking type
    String parkingType = '';
    if (isStudentParking && isFacultyStaffParking) {
      parkingType = 'Student & Faculty/Staff';
    } else if (isStudentParking) {
      parkingType = 'Student';
    } else if (isFacultyStaffParking) {
      parkingType = 'Faculty/Staff';
    }
    
    // Contact information
    final String phone = widget.parkingSpace['phone']?.toString() ?? '';
    final String whatsapp = widget.parkingSpace['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.parkingSpace['latitude'];
    final dynamic longitude = widget.parkingSpace['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Parking space details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.local_parking,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (parkingType.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      parkingType,
                                      style: TextStyle(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Parking information
                      if (capacity > 0)
                        _buildDetailRow(theme, Icons.car_rental, 'Capacity', '$capacity spaces'),
                      if (hours.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                      if (payment.isNotEmpty)
                        _buildDetailRow(theme, Icons.payment, 'Payment Methods', payment),
                      
                      // Nearby buildings
                      if (nearbyBuildings.isNotEmpty)
                        _buildDetailRow(theme, Icons.location_city, 'Nearby Buildings', nearbyBuildings.join(", ")),
                      
                      // Contact information
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                      if (whatsapp.isNotEmpty)
                        _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsapp, canCopy: true),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isWhatsappAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                    if (isWhatsappAvailable)
                      IconButton(
                        icon: FaIcon(
                          FontAwesomeIcons.whatsapp,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchWhatsapp(whatsapp),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
