// offline_library_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart'; // Ensure this import is correct
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'dart:convert'; // Import the dart:convert library
// Import for platformViewRegistry (if targeting web specifically)
// import 'dart:ui_web' as ui_web;

class OfflineLibraryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  const OfflineLibraryPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<OfflineLibraryPage> createState() => _OfflineLibraryPageState();
}

class _OfflineLibraryPageState extends State<OfflineLibraryPage> {
  List<String> _pdfFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPdfFiles();
  }

  Future<void> _loadPdfFiles() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);
      final libraryAssets = manifestMap.keys
          .where((String key) => key.startsWith('assets/library/') && key.endsWith('.pdf'))
          .toList();

      setState(() {
        _pdfFiles = libraryAssets;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading PDF files: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<File?> _loadFileFromAsset(String assetPath) async {
    try {
      final byteData = await rootBundle.load(assetPath);
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${path.basename(assetPath)}');
      await file.create(recursive: true);
      await file.writeAsBytes(byteData.buffer.asUint8List());
      return file;
    } catch (e) {
      print('Error loading asset to file: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text('Offline Library', style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: theme.colorScheme.primary))
          : _pdfFiles.isEmpty
              ? Center(child: Text('No PDF files found in assets/library', style: TextStyle(color: theme.colorScheme.onSurface)))
              : ListView.builder(
                  itemCount: _pdfFiles.length,
                  itemBuilder: (context, index) {
                    final fileName = path.basename(_pdfFiles[index]);
                    return Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      child: ListTile(
                        leading: Icon(Icons.picture_as_pdf, color: Colors.red.shade400),
                        title: Text(
                          fileName,
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                        onTap: () async {
                          print('Tapped on PDF: $fileName');
                          File? pdfFile = await _loadFileFromAsset(_pdfFiles[index]);
                          if (pdfFile != null) {
                            print('PDF file path: ${pdfFile.path}');
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PdfViewPage(
                                  filePath: pdfFile.path,
                                  title: fileName,
                                ),
                              ),
                            );
                            print('Navigation pushed to PdfViewPage');
                          } else {
                            // Handle the case where the file couldn't be loaded
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Failed to load PDF: $fileName')),
                            );
                          }
                        },
                      ),
                    );
                  },
                ),
    );
  }
}

class PdfViewPage extends StatelessWidget { // Changed to StatelessWidget
  final String filePath;
  final String title;
  const PdfViewPage({Key? key, required this.filePath, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    print('Building PdfViewPage with path: $filePath');
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(title, style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: PDFView(
        filePath: filePath,
        enableSwipe: true,
        swipeHorizontal: true,
        autoSpacing: false,
        pageSnap: false,
        pageFling: false,
        onViewCreated: (PDFViewController pdfViewController) {
          print('PDFView created');
        },
        onRender: (_pages) {
          print('PDF rendered with $_pages pages');
        },
        onError: (error) {
          print('Error loading PDF: $error');
        },
      ),
    );
  }
}