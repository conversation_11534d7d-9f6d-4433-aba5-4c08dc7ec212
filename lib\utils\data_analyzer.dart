import 'dart:convert';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'excel_extractor.dart';
import 'csv_extractor.dart';

class DataAnalyzer {
  /// Analyze data from Excel or CSV files
  static Future<Map<String, dynamic>> analyzeData(Uint8List bytes, String fileName) async {
    try {
      List<Map<String, dynamic>> data = [];

      // Extract data based on file type
      if (fileName.toLowerCase().endsWith('.xlsx') || fileName.toLowerCase().endsWith('.xls')) {
        data = await ExcelExtractor.extractData(bytes);
      } else if (fileName.toLowerCase().endsWith('.csv')) {
        data = await CsvExtractor.extractData(bytes);
      } else {
        return {
          'error': 'Unsupported file type for data analysis',
          'summary': 'Only Excel (.xlsx, .xls) and CSV files are supported for data analysis.'
        };
      }

      if (data.isEmpty) {
        return {
          'error': 'No data found',
          'summary': 'The file does not contain any data that can be analyzed.'
        };
      }

      // Perform basic analysis
      final analysisResult = _performAnalysis(data);
      return analysisResult;
    } catch (e) {
      return {
        'error': 'Error analyzing data: $e',
        'summary': 'An error occurred while analyzing the data.'
      };
    }
  }

  /// Perform basic analysis on the data
  static Map<String, dynamic> _performAnalysis(List<Map<String, dynamic>> data) {
    final Map<String, dynamic> result = {
      'summary': '',
      'columns': <String>[],
      'numericColumns': <String>[],
      'categoricalColumns': <String>[],
      'rowCount': data.length,
      'statistics': <String, Map<String, dynamic>>{},
      'chartData': <String, dynamic>{},
    };

    if (data.isEmpty) {
      result['summary'] = 'No data to analyze';
      return result;
    }

    // Get column names
    final columns = data.first.keys.toList();
    result['columns'] = columns;

    // Identify numeric and categorical columns
    final numericColumns = <String>[];
    final categoricalColumns = <String>[];

    for (final column in columns) {
      bool isNumeric = true;
      final Set<dynamic> uniqueValues = {};

      for (final row in data) {
        final value = row[column];
        uniqueValues.add(value);

        if (value is! num && (value is String && num.tryParse(value) == null)) {
          isNumeric = false;
        }
      }

      if (isNumeric) {
        numericColumns.add(column);
      } else {
        categoricalColumns.add(column);
      }

      // Calculate basic statistics for each column
      final stats = <String, dynamic>{
        'uniqueValues': uniqueValues.length,
        'missingValues': data.where((row) => row[column] == null || row[column] == '').length,
      };

      if (isNumeric) {
        final numericValues = data
            .map((row) => row[column] is num ? row[column] : num.tryParse(row[column].toString() ?? ''))
            .where((value) => value != null)
            .cast<num>()
            .toList();

        if (numericValues.isNotEmpty) {
          numericValues.sort();
          final sum = numericValues.reduce((a, b) => a + b);
          final mean = sum / numericValues.length;
          final median = numericValues.length % 2 == 0
              ? (numericValues[numericValues.length ~/ 2 - 1] + numericValues[numericValues.length ~/ 2]) / 2
              : numericValues[numericValues.length ~/ 2];

          // Calculate standard deviation
          final variance = numericValues.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / numericValues.length;
          final stdDev = math.sqrt(variance);

          // Calculate min, max, and range
          final min = numericValues.first;
          final max = numericValues.last;
          final range = max - min;

          stats.addAll({
            'min': min,
            'max': max,
            'range': range,
            'mean': mean,
            'median': median,
            'sum': sum,
            'stdDev': stdDev,
          });
        }
      } else {
        // For categorical columns, calculate frequency distribution
        final Map<dynamic, int> frequencies = {};
        for (final row in data) {
          final value = row[column];
          frequencies[value] = (frequencies[value] ?? 0) + 1;
        }

        // Find the mode (most frequent value)
        dynamic mode;
        int maxFrequency = 0;
        frequencies.forEach((value, frequency) {
          if (frequency > maxFrequency) {
            maxFrequency = frequency;
            mode = value;
          }
        });

        stats.addAll({
          'mode': mode,
          'frequencies': frequencies,
        });
      }

      result['statistics'][column] = stats;
    }

    result['numericColumns'] = numericColumns;
    result['categoricalColumns'] = categoricalColumns;

    // Generate chart data for numeric columns
    if (numericColumns.isNotEmpty) {
      final chartData = <String, List<FlSpot>>{};

      // For the first numeric column, create a line chart
      final firstNumericColumn = numericColumns.first;
      final spots = <FlSpot>[];

      for (int i = 0; i < data.length; i++) {
        final y = data[i][firstNumericColumn] is num
            ? data[i][firstNumericColumn]
            : num.tryParse(data[i][firstNumericColumn].toString() ?? '0') ?? 0;
        spots.add(FlSpot(i.toDouble(), y.toDouble()));
      }

      chartData[firstNumericColumn] = spots;
      result['chartData'] = chartData;
    }

    // Generate a summary
    final buffer = StringBuffer();
    buffer.writeln('Data Analysis Summary:');
    buffer.writeln('- Total rows: ${data.length}');
    buffer.writeln('- Total columns: ${columns.length}');
    buffer.writeln('- Numeric columns: ${numericColumns.length}');
    buffer.writeln('- Categorical columns: ${categoricalColumns.length}');

    if (numericColumns.isNotEmpty) {
      buffer.writeln('\nNumeric Columns:');
      for (final column in numericColumns) {
        final stats = result['statistics'][column];
        buffer.writeln('  $column:');
        buffer.writeln('    - Min: ${stats['min']}');
        buffer.writeln('    - Max: ${stats['max']}');
        buffer.writeln('    - Mean: ${stats['mean']}');
        buffer.writeln('    - Median: ${stats['median']}');
        buffer.writeln('    - Standard Deviation: ${stats['stdDev']}');
      }
    }

    if (categoricalColumns.isNotEmpty) {
      buffer.writeln('\nCategorical Columns:');
      for (final column in categoricalColumns) {
        final stats = result['statistics'][column];
        buffer.writeln('  $column:');
        buffer.writeln('    - Unique values: ${stats['uniqueValues']}');
        buffer.writeln('    - Mode: ${stats['mode']}');
      }
    }

    result['summary'] = buffer.toString();
    return result;
  }

  /// Generate a line chart widget for numeric data
  static Widget generateLineChart(List<FlSpot> spots, String title, {Color color = Colors.blue}) {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(show: true),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: true),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: true),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: true),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: color,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                color: color.withOpacity(0.3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Generate a bar chart widget for categorical data
  static Widget generateBarChart(Map<dynamic, int> frequencies, String title, {Color color = Colors.blue}) {
    final entries = frequencies.entries.toList();

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: frequencies.values.reduce((a, b) => a > b ? a : b).toDouble() * 1.2,
          barTouchData: BarTouchData(enabled: true),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  String text = '';
                  if (index >= 0 && index < entries.length) {
                    text = entries[index].key.toString().substring(0, math.min(5, entries[index].key.toString().length));
                  }
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(text),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: true),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: true),
          barGroups: List.generate(
            entries.length,
            (index) => BarChartGroupData(
              x: index,
              barRods: [
                BarChartRodData(
                  toY: entries[index].value.toDouble(),
                  color: color,
                  width: 22,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


