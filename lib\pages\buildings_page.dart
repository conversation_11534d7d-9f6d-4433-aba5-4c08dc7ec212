import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'building_detail_page.dart';

class BuildingsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedBuildings;
  final bool isFromDetailPage;

  const BuildingsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedBuildings,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<BuildingsPage> createState() => _BuildingsPageState();
}

class _BuildingsPageState extends State<BuildingsPage> {
  List<Map<String, dynamic>> _buildings = [];
  List<Map<String, dynamic>> _filteredBuildings = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _buildingsChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadBuildings();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _buildingsChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterBuildings();
    });
  }

  void _filterBuildings() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredBuildings = List.from(_buildings);
      return;
    }

    _filteredBuildings = _buildings.where((building) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          building['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (building['location']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (building['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Building') {
          matchesFilter = building['building'] == true;
        } else if (_selectedFilter == 'Housing') {
          matchesFilter = building['housing'] == true;
        } else if (_selectedFilter == 'Parking') {
          matchesFilter = building['parking'] == true;
        } else if (_selectedFilter == 'School') {
          matchesFilter = building['school'] == true;
        } else if (_selectedFilter == 'Residence') {
          matchesFilter = building['residence'] == true;
        } else if (_selectedFilter == 'Library') {
          matchesFilter = building['library'] == true;
        } else if (_selectedFilter == 'Lab') {
          matchesFilter = building['lab'] == true;
        } else if (_selectedFilter == 'Studio') {
          matchesFilter = building['studio'] == true;
        } else if (_selectedFilter == 'Gallery') {
          matchesFilter = building['gallery'] == true;
        } else if (_selectedFilter == 'Theater') {
          matchesFilter = building['theater'] == true;
        } else if (_selectedFilter == 'Coworking Space') {
          matchesFilter = building['coworkingspace'] == true;
        } else if (_selectedFilter == 'Study Space') {
          matchesFilter = building['studyspace'] == true;
        } else if (_selectedFilter == 'Fitness Space') {
          matchesFilter = building['fitnessspace'] == true;
        } else if (_selectedFilter == 'Fun Space') {
          matchesFilter = building['funspace'] == true;
        } else if (_selectedFilter == 'Storage Space') {
          matchesFilter = building['storagespace'] == true;
        } else if (_selectedFilter == 'Mailing Space') {
          matchesFilter = building['mailingspace'] == true;
        } else if (_selectedFilter == 'Museum') {
          matchesFilter = building['museum'] == true;
        } else if (_selectedFilter == 'Sacred Space') {
          matchesFilter = building['sacredspace'] == true;
        } else if (_selectedFilter == 'Outdoor Space') {
          matchesFilter = building['outdoorspace'] == true;
        } else if (_selectedFilter == 'Research Station') {
          matchesFilter = building['researchstation'] == true;
        } else if (_selectedFilter == 'Clinic') {
          matchesFilter = building['clinic'] == true;
        } else if (_selectedFilter == 'Laundry Space') {
          matchesFilter = building['laundryspace'] == true;
        } else if (_selectedFilter == 'Campus Dumpsters') {
          matchesFilter = building['campusdumpsters'] == true;
        } else if (_selectedFilter == 'Water Tanks') {
          matchesFilter = building['watertanks'] == true;
        } else if (_selectedFilter == 'Other') {
          matchesFilter = building['other'] == true;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadBuildings() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedBuildings.isNotEmpty) {
        setState(() {
          _buildings = widget.preloadedBuildings;
          _filteredBuildings = widget.preloadedBuildings;
          _isLoading = false;
        });
        print('Using preloaded buildings data for ${widget.collegeNameForTable}');
        // Still fetch in background to refresh cache
        _fetchBuildingsFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _buildings = cachedData;
          _filteredBuildings = cachedData;
          _isLoading = false;
        });
        print('Loaded buildings from cache for ${widget.collegeNameForTable}');
      }

      // Then fetch from Supabase
      await _fetchBuildingsFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading buildings: $e';
      });
      print('Error in _loadBuildings: $e');
    }
  }

  Future<void> _fetchBuildingsFromSupabase() async {
    try {
      final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_building';
      print('Fetching from table: $buildingsTableName');

      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .order('fullname', ascending: true);

      final buildings = List<Map<String, dynamic>>.from(response);
      print('Fetched ${buildings.length} buildings from Supabase');

      // Cache the data
      await _saveToCache(buildings);

      if (mounted) {
        setState(() {
          _buildings = buildings;
          _filteredBuildings = buildings;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching buildings from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_buildings.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading buildings: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'building_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} buildings in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached buildings found');
      }
    } catch (e) {
      print('Error loading buildings from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'building_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} buildings to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved buildings to cache');
    } catch (e) {
      print('Error saving buildings to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_building';
    print('Setting up realtime listener for table: $buildingsTableName');
    _buildingsChannel = Supabase.instance.client
        .channel('buildings_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: buildingsTableName,
          callback: (payload) {
            print('Realtime update received for buildings');
            _fetchBuildingsFromSupabase();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final List<String> filters = ['All'];

    // Add all building types as filters
    if (_buildings.any((building) => building['building'] == true)) filters.add('Building');
    if (_buildings.any((building) => building['housing'] == true)) filters.add('Housing');
    if (_buildings.any((building) => building['parking'] == true)) filters.add('Parking');
    if (_buildings.any((building) => building['school'] == true)) filters.add('School');
    if (_buildings.any((building) => building['residence'] == true)) filters.add('Residence');
    if (_buildings.any((building) => building['library'] == true)) filters.add('Library');
    if (_buildings.any((building) => building['lab'] == true)) filters.add('Lab');
    if (_buildings.any((building) => building['studio'] == true)) filters.add('Studio');
    if (_buildings.any((building) => building['gallery'] == true)) filters.add('Gallery');
    if (_buildings.any((building) => building['theater'] == true)) filters.add('Theater');
    if (_buildings.any((building) => building['coworkingspace'] == true)) filters.add('Coworking Space');
    if (_buildings.any((building) => building['studyspace'] == true)) filters.add('Study Space');
    if (_buildings.any((building) => building['fitnessspace'] == true)) filters.add('Fitness Space');
    if (_buildings.any((building) => building['funspace'] == true)) filters.add('Fun Space');
    if (_buildings.any((building) => building['storagespace'] == true)) filters.add('Storage Space');
    if (_buildings.any((building) => building['mailingspace'] == true)) filters.add('Mailing Space');
    if (_buildings.any((building) => building['museum'] == true)) filters.add('Museum');
    if (_buildings.any((building) => building['sacredspace'] == true)) filters.add('Sacred Space');
    if (_buildings.any((building) => building['outdoorspace'] == true)) filters.add('Outdoor Space');
    if (_buildings.any((building) => building['researchstation'] == true)) filters.add('Research Station');
    if (_buildings.any((building) => building['clinic'] == true)) filters.add('Clinic');
    if (_buildings.any((building) => building['laundryspace'] == true)) filters.add('Laundry Space');
    if (_buildings.any((building) => building['campusdumpsters'] == true)) filters.add('Campus Dumpsters');
    if (_buildings.any((building) => building['watertanks'] == true)) filters.add('Water Tanks');
    if (_buildings.any((building) => building['other'] == true)) filters.add('Other');

    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Buildings',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('buildings-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _buildings.isEmpty && !_isLoading) {
            _loadBuildings();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search buildings...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterBuildings();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadBuildings,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredBuildings.isEmpty
                          ? const Center(
                              child: Text('No buildings found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredBuildings.length,
                              itemBuilder: (context, index) {
                                final building = _filteredBuildings[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      building['fullname'] ?? 'Unnamed Building',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Location: ${building['location'] ?? 'N/A'}'),
                                        Text('Hours: ${building['hours'] ?? 'N/A'}'),
                                        Text('Capacity: ${building['capacity'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => BuildingDetailPage(
                                            building: building,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
