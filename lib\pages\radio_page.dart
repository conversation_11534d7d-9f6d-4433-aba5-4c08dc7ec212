// radio_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'radio_detail_page.dart';

class RadioPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedRadioStations;
  final bool isFromDetailPage;

  const RadioPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedRadioStations,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<RadioPage> createState() => _RadioPageState();
}

class _RadioPageState extends State<RadioPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('radio_list');
  List<Map<String, dynamic>> _radioStations = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("RadioPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant RadioPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("RadioPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("RadioPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("RadioPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedRadioStations != null && widget.preloadedRadioStations!.isNotEmpty) {
      print("Preloaded radio stations found, using them.");
      setState(() {
        _radioStations = List<Map<String, dynamic>>.from(widget.preloadedRadioStations!);
        _radioStations.forEach((station) {
          station['_isImageLoading'] = false;
        });
        _radioStations.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedRadioStations!.length == _pageSize;
      });
      _loadRadioStationsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded radio stations or empty list, loading from database.");
      _loadRadioStationsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadRadioStationsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadRadioStationsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final radioTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_radio';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(radioTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedRadioStations =
          await _updateRadioStationImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _radioStations = updatedRadioStations;
        } else {
          _radioStations.addAll(updatedRadioStations);
        }
        _radioStations.forEach((station) {
          station['_isImageLoading'] = false;
        });
        _radioStations.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the radio stations
      _cacheRadioStations(_radioStations);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching radio stations: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateRadioStationImageUrls(
      List<Map<String, dynamic>> radioStations) async {
    List<Future<void>> futures = [];
    for (final station in radioStations) {
      if (station['image_url'] == null ||
          station['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(station));
      }
    }
    await Future.wait(futures);
    return radioStations;
  }

  Future<void> _cacheRadioStations(List<Map<String, dynamic>> radioStations) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String radioStationsJson = jsonEncode(radioStations);
      await prefs.setString(
          'radio_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          radioStationsJson);
    } catch (e) {
      print('Error caching radio stations: $e');
    }
  }
  
  void _setupRealtime() {
    final radioTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_radio';
    _realtimeChannel = Supabase.instance.client
        .channel('radio')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: radioTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newStationId = payload.newRecord['id'];
          final newStationResponse = await Supabase.instance.client
              .from(radioTableName)
              .select('*')
              .eq('id', newStationId)
              .single();
          if (mounted) {
            Map<String, dynamic> newStation = Map.from(newStationResponse);
            final updatedStation = await _updateRadioStationImageUrls([newStation]);
            setState(() {
              _radioStations = [..._radioStations, updatedStation.first];
              updatedStation.first['_isImageLoading'] = false;
              _radioStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedStationId = payload.newRecord['id'];
          final updatedStationResponse = await Supabase.instance.client
              .from(radioTableName)
              .select('*')
              .eq('id', updatedStationId)
              .single();
          if (mounted) {
            final updatedStation = Map<String, dynamic>.from(updatedStationResponse);
            setState(() {
              _radioStations = _radioStations.map((station) {
                return station['id'] == updatedStation['id'] ? updatedStation : station;
              }).toList();
              _radioStations.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedStationId = payload.oldRecord['id'];
          setState(() {
            _radioStations.removeWhere((station) => station['id'] == deletedStationId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreRadioStations();
    }
  }

  Future<void> _loadMoreRadioStations() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadRadioStationsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> station) async {
    if (station['_isImageLoading'] == true) {
      print('Image loading already in progress for ${station['fullname']}, skipping.');
      return;
    }
    if (station['image_url'] != null &&
        station['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${station['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      station['_isImageLoading'] = true;
    });

    final fullname = station['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeRadioBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/radio';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeRadioBucket');
    print('Image URL before fetch: ${station['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeRadioBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeRadioBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        station['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        station['_isImageLoading'] = false;
        print('Setting image_url for ${station['fullname']} to: ${station['image_url']}');
      });
    } else {
      station['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> station) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RadioDetailPage(
            radioStation: station,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("RadioPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Radio Stations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _radioStations.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadRadioStationsFromSupabase(initialLoad: true),
              child: _radioStations.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No radio stations available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _radioStations.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _radioStations.length) {
                          final station = _radioStations[index];
                          final phone = station['phone'] as String? ?? '';
                          final email = station['email'] as String? ?? '';
                          final whatsapp = station['whatsapp'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('radio_${station['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (station['image_url'] == null ||
                                      station['image_url'] == 'assets/placeholder_image.png') &&
                                  !station['_isImageLoading']) {
                                _fetchImageUrl(station);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 60,
                                        height: 60,
                                        child: CachedNetworkImage(
                                          imageUrl: station['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      station['fullname'] ?? 'Unnamed Radio Station',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    subtitle: Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        station['about'] ?? '',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: theme.colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                    onTap: () => _navigateToDetail(context, station),
                                  ),
                                  if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          if (phone.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.phone,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchDialer(phone),
                                              tooltip: 'Call',
                                            ),
                                          if (email.isNotEmpty)
                                            IconButton(
                                              icon: Icon(
                                                Icons.email,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchEmail(email),
                                              tooltip: 'Email',
                                            ),
                                          if (whatsapp.isNotEmpty)
                                            IconButton(
                                              icon: FaIcon(
                                                FontAwesomeIcons.whatsapp,
                                                color: theme.colorScheme.primary,
                                                size: 20,
                                              ),
                                              onPressed: () => _launchWhatsapp(whatsapp),
                                              tooltip: 'WhatsApp',
                                            ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
