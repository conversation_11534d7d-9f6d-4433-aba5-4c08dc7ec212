import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'academic_jobs_page.dart';
import 'admin_jobs_page.dart';

class Job {
  final int id;
  final String fullname;
  final DateTime openingDate;
  final DateTime closingDate;
  final String email;
  final String link;
  final bool adminjob;
  final bool academicjob;
  final String about;

  Job({
    required this.id,
    required this.fullname,
    required this.openingDate,
    required this.closingDate,
    required this.email,
    required this.link,
    required this.adminjob,
    required this.academicjob,
    required this.about,
  });

  factory Job.fromJson(Map<String, dynamic> json) {
    return Job(
      id: json['id'] ?? 0,
      fullname: json['fullname'] ?? 'Unnamed Job',
      openingDate: DateTime(
        json['openingyear'] ?? DateTime.now().year,
        json['openingmonth'] ?? DateTime.now().month,
        json['openingday'] ?? DateTime.now().day,
      ),
      closingDate: DateTime(
        json['closingyear'] ?? DateTime.now().year,
        json['closingmonth'] ?? DateTime.now().month,
        json['closingday'] ?? DateTime.now().day,
      ),
      email: json['email'] ?? '',
      link: json['link'] ?? '',
      adminjob: json['adminjob'] ?? false,
      academicjob: json['academicjob'] ?? false,
      about: json['about'] ?? 'No description available',
    );
  }

  bool isActive() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final normalizedClosingDate = DateTime(
      closingDate.year,
      closingDate.month,
      closingDate.day
    );
    return normalizedClosingDate.compareTo(today) >= 0;
  }

  String formattedOpeningDate() {
    return DateFormat('MMMM d, yyyy').format(openingDate);
  }

  String formattedClosingDate() {
    return DateFormat('MMMM d, yyyy').format(closingDate);
  }

  String getJobType() {
    if (academicjob && adminjob) {
      return 'Academic & Administrative';
    } else if (academicjob) {
      return 'Academic';
    } else if (adminjob) {
      return 'Administrative';
    } else {
      return 'Other';
    }
  }
}

class TertiaryJobsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isFromDetailPage;

  const TertiaryJobsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeData,
    required this.institutionName,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryJobsPage> createState() => _TertiaryJobsPageState();
}

class _TertiaryJobsPageState extends State<TertiaryJobsPage> {

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDark = theme.brightness == Brightness.dark;
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Card(
      key: Key('jobs_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          switch (title) {
            case 'Academic Jobs':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AcademicJobsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
            case 'Administrative Jobs':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AdminJobsPage(
                    institutionName: widget.institutionName,
                    collegeData: widget.collegeData,
                    isDarkMode: currentIsDarkMode,
                    toggleTheme: widget.toggleTheme,
                  ),
                ),
              );
              break;
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDark ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Academic Jobs', 'icon': Icons.school},
      {'title': 'Administrative Jobs', 'icon': Icons.business_center},
    ];

    // No need to filter items as both are always relevant
    final filteredGridItems = gridItems;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Jobs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: filteredGridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}