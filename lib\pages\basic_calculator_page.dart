// basic_calculator_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:math_expressions/math_expressions.dart';

class BasicCalculatorPage extends StatefulWidget {
  const BasicCalculatorPage({Key? key}) : super(key: key);

  @override
  _BasicCalculatorPageState createState() => _BasicCalculatorPageState();
}

class _BasicCalculatorPageState extends State<BasicCalculatorPage> {
  String _expression = '';
  String _output = '0';

  void _buttonPressed(String buttonText) {
    setState(() {
      if (buttonText == 'AC') {
        _expression = '';
        _output = '0';
      } else if (buttonText == '⌫') {
        if (_expression.isNotEmpty) {
          _expression = _expression.substring(0, _expression.length - 1);
        }
      } else if (buttonText == '=') {
        _evaluateExpression();
        // Finalize the result by setting the expression to the output
        _expression = _output;
      } else if (buttonText == '±') {
        // Toggle sign of current output if it's a valid number
        if (_output != '0' && _output != 'Error') {
          if (_output.startsWith('-')) {
            _output = _output.substring(1);
          } else {
            _output = '-' + _output;
          }
          _expression = _output;
        }
      } else if (['+', '-', 'x', '÷'].contains(buttonText)) {
        // Avoid consecutive operators (allow leading '-' for negative numbers)
        if (_expression.isNotEmpty &&
            !(['+', '-', 'x', '÷']
                .contains(_expression.substring(_expression.length - 1)))) {
          _expression += buttonText;
        } else if (_expression.isEmpty && buttonText == '-') {
          _expression += buttonText;
        }
      } else {
        // For digits and the decimal point
        _expression += buttonText;
      }

      // Update live evaluation for most buttons (except when finalizing or clearing)
      if (buttonText != '=' && buttonText != 'AC' && buttonText != '±') {
        _evaluateExpression();
      }
    });
  }

  void _evaluateExpression() {
    if (_expression.isEmpty) {
      _output = '0';
      return;
    }

    try {
      final Parser p = Parser();
      // Replace custom symbols with valid operators for evaluation
      final Expression exp = p.parse(
          _expression.replaceAll('x', '*').replaceAll('÷', '/'));
      final ContextModel cm = ContextModel();
      final result = exp.evaluate(EvaluationType.REAL, cm);
      _output = (result % 1 == 0 ? result.toInt() : result).toString();
    } catch (e) {
      // Only display "Error" if it's not simply an incomplete expression
      if (!(['+', '-', 'x', '÷']
          .contains(_expression.substring(_expression.length - 1)))) {
        _output = 'Error';
      }
    }
  }

  Widget _buildButton(String buttonText) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    Color buttonColor;
    Color foregroundColor;

    // Use the same background for all non-digit buttons (operators and functions)
    if (['AC', '=', '÷', 'x', '-', '+', '⌫', '±'].contains(buttonText)) {
      buttonColor = isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
      foregroundColor = isDarkMode ? Colors.white : Colors.black;
    } else {
      // For digits and the decimal point
      buttonColor = isDarkMode ? Colors.grey[850]! : Colors.white;
      foregroundColor = isDarkMode ? Colors.white : Colors.black;
    }

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: ElevatedButton(
            onPressed: () => _buttonPressed(buttonText),
            style: ElevatedButton.styleFrom(
              backgroundColor: buttonColor,
              foregroundColor: foregroundColor,
              padding: const EdgeInsets.all(18.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
                side: BorderSide(
                  color: isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
                ),
              ),
            ),
            child: Text(
              buttonText,
              style: TextStyle(
                fontSize: 22.0,
                fontWeight: FontWeight.bold,
                color: foregroundColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Basic Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            // Display area for expression and result
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                alignment: Alignment.bottomRight,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  reverse: true,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _expression,
                        style: TextStyle(
                          fontSize: 24.0,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ),
                      Text(
                        _output,
                        style: TextStyle(
                          fontSize: 40.0,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
            // Button grid area
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  // Row 1: Functions and division operator
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('AC'),
                        _buildButton('⌫'),
                        _buildButton('±'),
                        _buildButton('÷'),
                      ],
                    ),
                  ),
                  // Row 2: 7, 8, 9, multiplication operator
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('7'),
                        _buildButton('8'),
                        _buildButton('9'),
                        _buildButton('x'),
                      ],
                    ),
                  ),
                  // Row 3: 4, 5, 6, subtraction operator
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('4'),
                        _buildButton('5'),
                        _buildButton('6'),
                        _buildButton('-'),
                      ],
                    ),
                  ),
                  // Row 4: 1, 2, 3, addition operator
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('1'),
                        _buildButton('2'),
                        _buildButton('3'),
                        _buildButton('+'),
                      ],
                    ),
                  ),
                  // Row 5: 0 (wide), decimal point, equals operator
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: _buildButton('0'),
                        ),
                        _buildButton('.'),
                        _buildButton('='),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
