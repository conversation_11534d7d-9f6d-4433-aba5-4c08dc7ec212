// startupfund_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class StartupFundDetailPage extends StatefulWidget {
  final Map<String, dynamic> startupFund;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const StartupFundDetailPage({
    Key? key,
    required this.startupFund,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<StartupFundDetailPage> createState() => _StartupFundDetailPageState();
}

class _StartupFundDetailPageState extends State<StartupFundDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _startupFundRealtimeChannel; // Realtime channel for startup fund updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupStartupFundRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _startupFundRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.startupFund['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupStartupFundRealtimeListener() {
    final startupFundsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_startupfunds';
    _startupFundRealtimeChannel = Supabase.instance.client
        .channel('startupfund_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: startupFundsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current startup fund's ID
        if (payload.newRecord['id'] == widget.startupFund['id']) {
          print("Realtime UPDATE event received for THIS startup fund (manual filter applied): ${widget.startupFund['fullname']}");
          _fetchUpdatedStartupFundData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER startup fund, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedStartupFundData() async {
    final startupFundsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_startupfunds';
    try {
      final updatedStartupFundResponse = await Supabase.instance.client
          .from(startupFundsTableName)
          .select('*')
          .eq('id', widget.startupFund['id'])
          .single();

      if (mounted && updatedStartupFundResponse != null) {
        Map<String, dynamic> updatedStartupFund = Map.from(updatedStartupFundResponse);
        // Update the widget.startupFund with the new data
        setState(() {
          widget.startupFund.clear(); // Clear old data
          widget.startupFund.addAll(updatedStartupFund); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Startup fund data updated in detail page for ${widget.startupFund['fullname']}");
          _updateStartupFundsCache(updatedStartupFund); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated startup fund data: $error");
    }
  }

  Future<void> _updateStartupFundsCache(Map<String, dynamic> updatedStartupFund) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'startupfunds_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedStartupFundsJson = prefs.getString(cacheKey);

    if (cachedStartupFundsJson != null) {
      List<Map<String, dynamic>> cachedStartupFunds = (jsonDecode(cachedStartupFundsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the startup fund in the cached list
      for (int i = 0; i < cachedStartupFunds.length; i++) {
        if (cachedStartupFunds[i]['id'] == updatedStartupFund['id']) {
          cachedStartupFunds[i] = updatedStartupFund;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedStartupFunds));
      print("Startup funds cache updated with realtime change for ${updatedStartupFund['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.startupFund['phone'] as String? ?? '';
    final email = widget.startupFund['email'] as String? ?? '';
    final about = widget.startupFund['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.startupFund['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _isLoadingImage
                ? const SizedBox(height: 200, child: Center(child: CircularProgressIndicator()))
                : CachedNetworkImage(
              imageUrl: _imageUrl,
              placeholder: (context, url) => Image.asset(
                'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                fit: BoxFit.contain,
                height: 200,
              ),
              errorWidget: (context, url, error) => Image.asset(
                'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                fit: BoxFit.contain,
                height: 200,
              ),
              fit: BoxFit.cover,
              height: 200,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (about.isNotEmpty)
                        _buildDetailRow(theme, Icons.info_outline, 'About', about),
                      if (email.isNotEmpty)
                        _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
