// utilities_page.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'login_page.dart';
import 'todo_page.dart';
import 'shopping_page.dart';
import 'wallet_page.dart';
import 'notes_page.dart';
import 'budget_tracker_page.dart';
import 'timer_page.dart'; // Now contains TimerScreen
import 'stopwatch_page.dart'; // Now contains StopwatchScreen
import 'whiteboard_page.dart'; // Import Whiteboard Page
import 'package:webview_flutter/webview_flutter.dart'; // Import webview_flutter

class UtilitiesPage extends StatefulWidget { // Renamed ListPage to UtilitiesPage
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const UtilitiesPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key); // Renamed ListPage to UtilitiesPage

  @override
  State<UtilitiesPage> createState() => _UtilitiesPageState(); // Renamed _ListPageState to _UtilitiesPageState
}

class _UtilitiesPageState extends State<UtilitiesPage> { // Renamed _ListPageState to _UtilitiesPageState
  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;
  //WebViewController? _ministryOfEducationController; // WebViewController for preloading - Removed

  // Replace with your actual AdMob Rewarded Ad Unit ID
  final String _adUnitId = 'ca-app-pub-3940256099942544/5224354917'; // Using test ad unit ID from TertiaryDetailPage

  @override
  void initState() {
    super.initState();
    _loadRewardedAd();
    //_preloadMinistryOfEducationWebsite(); // Start preloading website - Removed
  }

  //void _preloadMinistryOfEducationWebsite() { // Removed
  //  _ministryOfEducationController = WebViewController()
  //    ..loadRequest(Uri.parse('https://www.education.gov.mw/')); // Load website URL
  //}

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (AdAd) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              _loadRewardedAd(); // Load a new ad after dismissal
              Navigator.pop(context); // Navigate back after ad dismissed
            },
            onAdFailedToShowFullScreenContent: (AdAd, error) {
              setState(() {
                _rewardedAd = null;
                _isAdLoaded = false;
              });
              print('Failed to show rewarded ad: $error');
              _loadRewardedAd(); // Retry loading the ad
              Navigator.pop(context); // Navigate back even if ad fails to show
            },
          );
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          setState(() {
            _rewardedAd = null;
            _isAdLoaded = false;
          });
        },
      ),
    );
  }

  void _showRewardedAd() { // Removed context and title parameters
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        // Reward the user (optional, but good practice for true rewarded ads)
        print('User earned reward: ${reward.amount} ${reward.type}');
        // Navigation.pop(context) is now handled in ad callbacks
      });
    } else {
      // If the ad is not loaded, navigate directly (or handle as needed)
      print('Rewarded ad not ready, navigating back directly.');
      Navigator.pop(context); // Navigate back directly if ad is not ready
    }
  }


  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool currentIsDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          _navigateToPage(context, title); // Navigate directly on grid item tap
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: currentIsDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return WillPopScope( // Wrap Scaffold with WillPopScope
      onWillPop: () async {
        _showRewardedAd(); // Show ad when back button is pressed
        return false; // Prevent immediate back navigation, it will be handled after ad is dismissed
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd(); // Show ad when back button is pressed via icon
            },
          ),
          title: Text(
            'Utilities',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = 2;
              double aspectRatio = 1.3;

              if (constraints.maxWidth > 1200) {
                crossAxisCount = 6;
                aspectRatio = 1.4;
              } else if (constraints.maxWidth > 900) {
                crossAxisCount = 4;
                aspectRatio = 1.3;
              } else if (constraints.maxWidth > 600) {
                crossAxisCount = 3;
                aspectRatio = 1.2;
              }

              return GridView.count(
                crossAxisCount: crossAxisCount,
                padding: const EdgeInsets.all(16),
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: aspectRatio,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildGridItem(context, 'Wallet', Icons.wallet, theme),
                  _buildGridItem(context, 'Todo List', Icons.list, theme),
                  _buildGridItem(context, 'Shopping List', Icons.shopping_cart_outlined, theme),
                  _buildGridItem(context, 'Notes', Icons.note, theme),
                  // Whiteboard is added here where Google Classroom was
                  _buildGridItem(context, 'Whiteboard', Icons.airplay, theme),
                  _buildGridItem(context, 'Budget & Expense Tracker', Icons.account_balance, theme),
                  _buildGridItem(context, 'Timer', Icons.timer_outlined, theme),
                  _buildGridItem(context, 'Stopwatch', Icons.watch_later_outlined, theme),
                  // Ministry of Education moved to the bottom and renamed, with website icon - Removed
                  //_buildGridItem(context, 'Ministry of Education', Icons.web_rounded, theme),
                  // _buildGridItem(context, 'Barcode & QR Code Scanner', Icons.qr_code_scanner, theme), // Removed
                ],
              );
            },
          ),
        ),
        bottomNavigationBar: Container(
          color: theme.colorScheme.surface,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.home_outlined,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      // Action for Home button
                      Navigator.of(context).popUntil((route) => route.isFirst);
                    },
                  ),
                  const SizedBox(width: 24),
                  IconButton(
                    icon: Icon(
                      currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: widget.toggleTheme,
                  ),
                  const SizedBox(width: 24),
                  IconButton(
                    icon: Icon(
                      Icons.person_outline,
                      color: theme.colorScheme.onSurface,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => LoginPage(
                            isDarkMode: currentIsDarkMode,
                            toggleTheme: widget.toggleTheme,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String title) {
    final currentIsDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (title == 'Wallet') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => WalletPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Todo List') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => TodoApp(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Shopping List') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ShoppingApp(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Notes') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => NotesPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
      // Removed YT Video Downloader navigation
      // Removed File Manager navigation
    } else if (title == 'Budget & Expense Tracker') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => BudgetTrackerPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    } else if (title == 'Timer') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const TimerScreen()), // Navigate to TimerScreen
      );
    } else if (title == 'Stopwatch') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const StopwatchScreen()), // Navigate to StopwatchScreen
      );
    //} else if (title == 'Ministry of Education') { // Renamed Google Classroom Navigation - Removed
    //  Navigator.push(    // - Removed
    //    context,
    //    MaterialPageRoute(
    //      builder: (context) => GoogleClassroomPage( // - Removed
    //        toggleTheme: widget.toggleTheme,
    //        isDarkMode: currentIsDarkMode,
    //        controller: _ministryOfEducationController, // Pass the preloaded controller - Removed
    //      ),
    //    ),
    //  );
    } else if (title == 'Whiteboard') { // Added Whiteboard Navigation
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => WhiteboardPage(toggleTheme: widget.toggleTheme, isDarkMode: currentIsDarkMode)),
      );
    }
  }
}