// statistics_calculator_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math; // Import dart:math as math to avoid naming conflicts
import 'package:collection/collection.dart'; // For mode calculation

class StatisticsCalculatorPage extends StatefulWidget {
  const StatisticsCalculatorPage({Key? key}) : super(key: key);

  @override
  _StatisticsCalculatorPageState createState() => _StatisticsCalculatorPageState();
}

class _StatisticsCalculatorPageState extends State<StatisticsCalculatorPage> {
  List<double> _data = [];
  String _mean = '';
  String _median = '';
  String _mode = '';
  String _stdDev = '';

  final TextEditingController _dataController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Statistics Calculator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    controller: _dataController,
                    keyboardType: TextInputType.text, // Allow comma separated numbers
                    decoration: InputDecoration(
                      labelText: 'Enter Data (comma-separated numbers)',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                      hintText: 'e.g., 1, 2.5, 3, 4.7, 5',
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _calculateStatistics();
                    },
                    child: const Text('Calculate Statistics'),
                  ),
                  const SizedBox(height: 20),
                  Text('Mean: $_mean', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Median: $_median', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Mode: $_mode', style: TextStyle(color: theme.colorScheme.onSurface)),
                  Text('Standard Deviation: $_stdDev', style: TextStyle(color: theme.colorScheme.onSurface)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _calculateStatistics() {
    String dataString = _dataController.text;
    List<String> numberStrings = dataString.split(',');
    List<double> numbers = [];
    for (String numStr in numberStrings) {
      double? num = double.tryParse(numStr.trim());
      if (num != null) {
        numbers.add(num);
      }
    }

    if (numbers.isEmpty) {
      setState(() {
        _mean = 'N/A';
        _median = 'N/A';
        _mode = 'N/A';
        _stdDev = 'N/A';
      });
      return;
    }

    double sum = 0;
    for (double num in numbers) {
      sum += num;
    }
    double mean = sum / numbers.length;

    numbers.sort();
    double median;
    if (numbers.length % 2 == 0) {
      median = (numbers[numbers.length ~/ 2 - 1] + numbers[numbers.length ~/ 2]) / 2;
    } else {
      median = numbers[numbers.length ~/ 2];
    }

    // Using collection package for mode calculation
    Map<double, int> counts = {};
    for (double num in numbers) {
      counts[num] = (counts[num] ?? 0) + 1;
    }
    double modeVal = double.nan;
    int maxCount = 0;
    counts.forEach((key, value) {
      if (value > maxCount) {
        maxCount = value;
        modeVal = key;
      }
    });
    String mode = maxCount > 1 ? modeVal.toString() : 'No Mode';


    double varianceSum = 0;
    for (double num in numbers) {
      varianceSum += math.pow(num - mean, 2); // Using math.pow for squaring
    }
    double variance = varianceSum / numbers.length;
    double stdDev = math.sqrt(variance); // Using math.sqrt - **Corrected Line**

    setState(() {
      _data = numbers;
      _mean = mean.toStringAsFixed(2);
      _median = median.toStringAsFixed(2);
      _mode = mode;
      _stdDev = stdDev.toStringAsFixed(2);
    });
  }
}