import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;

class PodcastGenerator {
  final FlutterTts _flutterTts = FlutterTts();
  bool _isGenerating = false;
  
  PodcastGenerator() {
    _initTts();
  }
  
  Future<void> _initTts() async {
    await _flutterTts.setLanguage('en-US');
    await _flutterTts.setSpeechRate(0.5); // Slower for podcast quality
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
    
    // Set voice - try to get a high-quality voice
    final voices = await _flutterTts.getVoices;
    if (voices != null && voices is List) {
      // Look for premium voices first
      final premiumVoice = voices.firstWhere(
        (voice) => voice is Map && 
                  (voice['name'].toString().toLowerCase().contains('premium') ||
                   voice['name'].toString().toLowerCase().contains('enhanced')),
        orElse: () => null,
      );
      
      if (premiumVoice != null) {
        await _flutterTts.setVoice({"name": premiumVoice['name'], "locale": premiumVoice['locale']});
      }
    }
  }
  
  Future<Uint8List?> generatePodcastFromText(String text, {
    String title = 'Generated Podcast',
    Function(double)? onProgress,
  }) async {
    if (_isGenerating) {
      return null; // Already generating
    }
    
    _isGenerating = true;
    
    try {
      // Split text into manageable chunks (TTS has limits on text length)
      final chunks = _splitTextIntoChunks(text);
      final totalChunks = chunks.length;
      
      // Create a temporary file to store the audio
      final tempDir = await getTemporaryDirectory();
      final outputPath = path.join(tempDir.path, 'generated_podcast_${DateTime.now().millisecondsSinceEpoch}.mp3');
      
      // Set file output
      if (!kIsWeb) {
        await _flutterTts.synthesizeToFile(text, outputPath);
        
        // Wait for synthesis to complete
        final completer = Completer<void>();
        _flutterTts.setCompletionHandler(() {
          completer.complete();
        });
        
        // Report progress
        int processedChunks = 0;
        for (final chunk in chunks) {
          await _flutterTts.speak(chunk);
          processedChunks++;
          
          if (onProgress != null) {
            onProgress(processedChunks / totalChunks);
          }
          
          // Wait for speech to complete
          await _flutterTts.awaitSpeakCompletion(true);
        }
        
        await completer.future;
        
        // Read the generated file
        final file = File(outputPath);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          _isGenerating = false;
          return bytes;
        }
      } else {
        // Web platform doesn't support file output
        // We would need to use a different approach for web
        _isGenerating = false;
        return null;
      }
    } catch (e) {
      print('Error generating podcast: $e');
    }
    
    _isGenerating = false;
    return null;
  }
  
  // Alternative method using a TTS API service
  Future<Uint8List?> generatePodcastFromTextUsingApi(String text, {
    String title = 'Generated Podcast',
    Function(double)? onProgress,
  }) async {
    if (_isGenerating) {
      return null; // Already generating
    }
    
    _isGenerating = true;
    
    try {
      // This is a placeholder for a real TTS API service
      // You would replace this with an actual API call to a service like:
      // - Google Cloud Text-to-Speech
      // - Amazon Polly
      // - Microsoft Azure Speech Service
      
      // For demonstration, we'll simulate an API call
      if (onProgress != null) {
        onProgress(0.2); // Started
        await Future.delayed(Duration(seconds: 1));
        onProgress(0.5); // In progress
        await Future.delayed(Duration(seconds: 1));
        onProgress(0.8); // Almost done
        await Future.delayed(Duration(seconds: 1));
        onProgress(1.0); // Complete
      }
      
      // In a real implementation, you would:
      // 1. Make an API request to a TTS service
      // 2. Receive the audio file
      // 3. Return the bytes
      
      // For now, we'll return a placeholder
      _isGenerating = false;
      return Uint8List(0);
    } catch (e) {
      print('Error generating podcast using API: $e');
    }
    
    _isGenerating = false;
    return null;
  }
  
  List<String> _splitTextIntoChunks(String text, {int maxChunkLength = 1000}) {
    final chunks = <String>[];
    
    // Split by paragraphs first
    final paragraphs = text.split('\n');
    
    String currentChunk = '';
    for (final paragraph in paragraphs) {
      // If adding this paragraph would exceed the max length, add the current chunk and start a new one
      if (currentChunk.length + paragraph.length > maxChunkLength) {
        if (currentChunk.isNotEmpty) {
          chunks.add(currentChunk);
        }
        
        // If the paragraph itself is too long, split it further
        if (paragraph.length > maxChunkLength) {
          // Split by sentences
          final sentences = paragraph.split(RegExp(r'(?<=[.!?])\s+'));
          
          currentChunk = '';
          for (final sentence in sentences) {
            if (currentChunk.length + sentence.length > maxChunkLength) {
              if (currentChunk.isNotEmpty) {
                chunks.add(currentChunk);
              }
              
              // If the sentence itself is too long, split it into words
              if (sentence.length > maxChunkLength) {
                final words = sentence.split(' ');
                
                currentChunk = '';
                for (final word in words) {
                  if (currentChunk.length + word.length + 1 > maxChunkLength) {
                    chunks.add(currentChunk);
                    currentChunk = word + ' ';
                  } else {
                    currentChunk += word + ' ';
                  }
                }
              } else {
                currentChunk = sentence + ' ';
              }
            } else {
              currentChunk += sentence + ' ';
            }
          }
        } else {
          currentChunk = paragraph + '\n';
        }
      } else {
        currentChunk += paragraph + '\n';
      }
    }
    
    // Add the last chunk if it's not empty
    if (currentChunk.isNotEmpty) {
      chunks.add(currentChunk);
    }
    
    return chunks;
  }
  
  void dispose() {
    _flutterTts.stop();
  }
}
