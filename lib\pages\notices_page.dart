import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import 'notice_detail_page.dart';
import 'login_page.dart';

class NoticesPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedNotices;
  final bool isFromDetailPage;

  const NoticesPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedNotices,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _NoticesPageState createState() => _NoticesPageState();
}

class _NoticesPageState extends State<NoticesPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('notices_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _notices = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("NoticesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedNotices != null &&
        widget.preloadedNotices!.isNotEmpty) {
      setState(() {
        _notices = List.from(widget.preloadedNotices!);
        _isLoading = false;
      });
    } else {
      _loadNoticesFromDatabase();
    }
  }

  void _setupRealtime() {
    final noticesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_notices';
    _realtimeChannel = Supabase.instance.client
        .channel('notices_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: noticesTableName,
      callback: (payload) async {
        print("Realtime update received for notices: ${payload.eventType}");
        _loadNoticesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadNoticesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _notices = [];
    });

    await _loadMoreNotices();
  }

  Future<void> _loadMoreNotices() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final noticesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_notices';
      final response = await Supabase.instance.client
          .from(noticesTableName)
          .select('*')
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _notices.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading notices: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading notices: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreNotices();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Noticeboard',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('notices_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _notices.isEmpty && !_isLoading) {
            _loadNoticesFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadNoticesFromDatabase,
          child: _notices.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No notices found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _notices.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _notices.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildNoticeCard(
                      _notices[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildNoticeCard(
    Map<String, dynamic> notice,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = notice['fullname'] ?? 'Unknown';
    final String about = notice['about'] ?? '';
    
    // Format date
    String dateText = '';
    if (notice['day'] != null && notice['month'] != null && notice['year'] != null) {
      final day = notice['day'] as int;
      final month = notice['month'] as int;
      final year = notice['year'] as int;
      
      final date = DateTime(year, month, day);
      final DateFormat formatter = DateFormat('MMMM d, yyyy');
      dateText = formatter.format(date);
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NoticeDetailPage(
                notice: notice,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.announcement,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (dateText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          dateText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
