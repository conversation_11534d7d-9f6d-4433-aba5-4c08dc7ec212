import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class MajorDetailPage extends StatefulWidget {
  final Map<String, dynamic> major;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const MajorDetailPage({
    Key? key,
    required this.major,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<MajorDetailPage> createState() => _MajorDetailPageState();
}

class _MajorDetailPageState extends State<MajorDetailPage> {
  late RealtimeChannel _majorRealtimeChannel;
  List<Map<String, dynamic>> _courses = [];
  bool _isLoadingCourses = false;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
    _loadCoursesForMajor();
  }

  @override
  void dispose() {
    _majorRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _majorRealtimeChannel = Supabase.instance.client
        .channel('major_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'majors',
      callback: (payload) async {
        // Manual filtering for the specific major
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.major['id']) {
          print("Realtime update received for major detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshMajor();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshMajor() async {
    try {
      final response = await Supabase.instance.client
          .from('majors')
          .select('*')
          .eq('id', widget.major['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's major with the new data
          widget.major.clear();
          widget.major.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing major: $e");
    }
  }

  Future<void> _loadCoursesForMajor() async {
    if (_isLoadingCourses) return;

    setState(() {
      _isLoadingCourses = true;
    });

    try {
      final majorName = widget.major['fullname'];
      if (majorName == null) {
        setState(() {
          _isLoadingCourses = false;
        });
        return;
      }

      // Try to find courses that belong to this major
      final response = await Supabase.instance.client
          .from('coursecatalog')
          .select('*')
          .eq('major', majorName)
          .order('modulecode', ascending: true);

      if (mounted) {
        setState(() {
          _courses = List<Map<String, dynamic>>.from(response);
          _isLoadingCourses = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCourses = false;
        });
      }
      print("Error loading courses for major: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.major['fullname'] ?? 'Unknown';
    final String department = widget.major['department'] ?? '';
    final String department2 = widget.major['department2'] ?? '';
    final String department3 = widget.major['department3'] ?? '';
    final String school = widget.major['school'] ?? '';
    final String school2 = widget.major['school2'] ?? '';
    final String school3 = widget.major['school3'] ?? '';
    final String duration = widget.major['duration'] ?? '';
    final String about = widget.major['about'] ?? '';
    final String whoItsFor = widget.major['whoitsfor'] ?? '';
    final String goalsAndObjectives = widget.major['goalsandobjectives'] ?? '';
    final String aims = widget.major['aims'] ?? '';
    final String careerProspects = widget.major['careerprospects'] ?? '';
    final String learningOutcomes = widget.major['learningoutcomes'] ?? '';
    final String learningMethods = widget.major['learningmethods'] ?? '';
    final String assessment = widget.major['assessment'] ?? '';
    final String studentSupport = widget.major['studentsupport'] ?? '';
    final bool isAccredited = widget.major['accredited'] == true;
    
    // Departments information
    List<String> departments = [];
    if (department.isNotEmpty) departments.add(department);
    if (department2.isNotEmpty) departments.add(department2);
    if (department3.isNotEmpty) departments.add(department3);
    
    // Schools information
    List<String> schools = [];
    if (school.isNotEmpty) schools.add(school);
    if (school2.isNotEmpty) schools.add(school2);
    if (school3.isNotEmpty) schools.add(school3);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (isAccredited)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Accredited',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Major details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.school,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Basic information
                      if (departments.isNotEmpty)
                        _buildDetailRow(theme, Icons.account_balance, 'Department(s)', departments.join(', ')),
                      
                      if (schools.isNotEmpty)
                        _buildDetailRow(theme, Icons.school, 'School(s)', schools.join(', ')),
                      
                      if (duration.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Duration', duration),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              // Who it's for section
              if (whoItsFor.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Who It\'s For', whoItsFor),
              ],
              
              // Goals and objectives section
              if (goalsAndObjectives.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Goals and Objectives', goalsAndObjectives),
              ],
              
              // Aims section
              if (aims.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Aims', aims),
              ],
              
              // Career prospects section
              if (careerProspects.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Career Prospects', careerProspects),
              ],
              
              // Learning outcomes section
              if (learningOutcomes.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Learning Outcomes', learningOutcomes),
              ],
              
              // Learning methods section
              if (learningMethods.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Learning Methods', learningMethods),
              ],
              
              // Assessment section
              if (assessment.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Assessment', assessment),
              ],
              
              // Student support section
              if (studentSupport.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSectionCard(theme, 'Student Support', studentSupport),
              ],
              
              // Courses section
              if (_courses.isNotEmpty) ...[
                const SizedBox(height: 24),
                Text(
                  'Courses',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _courses.length,
                  itemBuilder: (context, index) {
                    final course = _courses[index];
                    return Card(
                      color: theme.colorScheme.surface,
                      surfaceTintColor: Colors.transparent,
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: currentIsDarkMode 
                              ? Colors.white.withOpacity(0.1) 
                              : Colors.black.withOpacity(0.1),
                          child: Icon(
                            Icons.book,
                            color: currentIsDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        title: Text(
                          '${course['modulecode'] ?? ''}: ${course['modulename'] ?? 'Unknown'}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        subtitle: course['about'] != null
                            ? Text(
                                course['about'],
                                style: TextStyle(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              )
                            : null,
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onTap: () {
                          // Navigate to course detail page
                        },
                      ),
                    );
                  },
                ),
              ],
              
              // Loading indicator for courses
              if (_isLoadingCourses)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, String value) {
    if (value.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(ThemeData theme, String title, String content) {
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: TextStyle(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
