// athletics_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'athletic_detail_page.dart';

class AthleticsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedAthletics;
  final bool isFromDetailPage;

  const AthleticsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedAthletics,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<AthleticsPage> createState() => _AthleticsPageState();
}

class _AthleticsPageState extends State<AthleticsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('athletics_list');
  List<Map<String, dynamic>> _athletics = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("AthleticsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant AthleticsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("AthleticsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("AthleticsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("AthleticsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedAthletics != null && widget.preloadedAthletics!.isNotEmpty) {
      print("Preloaded athletics found, using them.");
      setState(() {
        _athletics = List<Map<String, dynamic>>.from(widget.preloadedAthletics!);
        _athletics.forEach((athletic) {
          athletic['_isImageLoading'] = false;
        });
        _athletics.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedAthletics!.length == _pageSize;
      });
      _loadAthleticsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded athletics or empty list, loading from database.");
      _loadAthleticsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadAthleticsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadAthleticsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final athleticsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_athletics';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(athleticsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedAthletics =
          await _updateAthleticImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _athletics = updatedAthletics;
        } else {
          _athletics.addAll(updatedAthletics);
        }
        _athletics.forEach((athletic) {
          athletic['_isImageLoading'] = false;
        });
        _athletics.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the athletics
      _cacheAthletics(_athletics);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching athletics: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateAthleticImageUrls(
      List<Map<String, dynamic>> athletics) async {
    List<Future<void>> futures = [];
    for (final athletic in athletics) {
      if (athletic['image_url'] == null ||
          athletic['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(athletic));
      }
    }
    await Future.wait(futures);
    return athletics;
  }

  Future<void> _cacheAthletics(List<Map<String, dynamic>> athletics) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String athleticsJson = jsonEncode(athletics);
      await prefs.setString(
          'athletics_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          athleticsJson);
    } catch (e) {
      print('Error caching athletics: $e');
    }
  }
  
  void _setupRealtime() {
    final athleticsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_athletics';
    _realtimeChannel = Supabase.instance.client
        .channel('athletics')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: athleticsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newAthleticId = payload.newRecord['id'];
          final newAthleticResponse = await Supabase.instance.client
              .from(athleticsTableName)
              .select('*')
              .eq('id', newAthleticId)
              .single();
          if (mounted) {
            Map<String, dynamic> newAthletic = Map.from(newAthleticResponse);
            final updatedAthletic = await _updateAthleticImageUrls([newAthletic]);
            setState(() {
              _athletics = [..._athletics, updatedAthletic.first];
              updatedAthletic.first['_isImageLoading'] = false;
              _athletics.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedAthleticId = payload.newRecord['id'];
          final updatedAthleticResponse = await Supabase.instance.client
              .from(athleticsTableName)
              .select('*')
              .eq('id', updatedAthleticId)
              .single();
          if (mounted) {
            final updatedAthletic = Map<String, dynamic>.from(updatedAthleticResponse);
            setState(() {
              _athletics = _athletics.map((athletic) {
                return athletic['id'] == updatedAthletic['id'] ? updatedAthletic : athletic;
              }).toList();
              _athletics.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedAthleticId = payload.oldRecord['id'];
          setState(() {
            _athletics.removeWhere((athletic) => athletic['id'] == deletedAthleticId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreAthletics();
    }
  }

  Future<void> _loadMoreAthletics() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadAthleticsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> athletic) async {
    if (athletic['_isImageLoading'] == true) {
      print('Image loading already in progress for ${athletic['fullname']}, skipping.');
      return;
    }
    if (athletic['image_url'] != null &&
        athletic['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${athletic['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      athletic['_isImageLoading'] = true;
    });

    final fullname = athletic['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeAthleticBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/athletics';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeAthleticBucket');
    print('Image URL before fetch: ${athletic['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeAthleticBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeAthleticBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        athletic['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        athletic['_isImageLoading'] = false;
        print('Setting image_url for ${athletic['fullname']} to: ${athletic['image_url']}');
      });
    } else {
      athletic['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> athletic) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AthleticDetailPage(
            athletic: athletic,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("AthleticsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Athletics/Sports',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _athletics.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadAthleticsFromSupabase(initialLoad: true),
              child: _athletics.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No athletics/sports available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _athletics.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _athletics.length) {
                          final athletic = _athletics[index];
                          final hasLink = athletic['link'] != null && athletic['link'].toString().isNotEmpty;
                          
                          return VisibilityDetector(
                            key: Key('athletic_${athletic['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (athletic['image_url'] == null ||
                                      athletic['image_url'] == 'assets/placeholder_image.png') &&
                                  !athletic['_isImageLoading']) {
                                _fetchImageUrl(athletic);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: athletic['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        athletic['fullname'] ?? 'Unnamed Athletic/Sport',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                    if (hasLink)
                                      IconButton(
                                        icon: Icon(
                                          Icons.link,
                                          color: theme.colorScheme.primary,
                                          size: 20,
                                        ),
                                        onPressed: () => _launchURL(athletic['link']),
                                        tooltip: 'Visit website',
                                      ),
                                  ],
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    athletic['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, athletic),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
