import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'campus_shuttle_detail_page.dart';
import 'login_page.dart';

class CampusShuttlePage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedCampusShuttles;
  final bool isFromDetailPage;

  const CampusShuttlePage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedCampusShuttles,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _CampusShuttlePageState createState() => _CampusShuttlePageState();
}

class _CampusShuttlePageState extends State<CampusShuttlePage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('campus_shuttle_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _campusShuttles = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("CampusShuttlePage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedCampusShuttles != null &&
        widget.preloadedCampusShuttles!.isNotEmpty) {
      setState(() {
        _campusShuttles = List.from(widget.preloadedCampusShuttles!);
        _isLoading = false;
      });
    } else {
      _loadCampusShuttlesFromDatabase();
    }
  }

  void _setupRealtime() {
    final campusShuttleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
    _realtimeChannel = Supabase.instance.client
        .channel('campus_shuttle_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: campusShuttleTableName,
      callback: (payload) async {
        print("Realtime update received for campus shuttle: ${payload.eventType}");
        _loadCampusShuttlesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadCampusShuttlesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _campusShuttles = [];
    });

    await _loadMoreCampusShuttles();
  }

  Future<void> _loadMoreCampusShuttles() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final campusShuttleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_campusshuttle';
      final response = await Supabase.instance.client
          .from(campusShuttleTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _campusShuttles.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading campus shuttles: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading campus shuttles: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreCampusShuttles();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Campus Bus/Shuttle',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('campus_shuttle_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _campusShuttles.isEmpty && !_isLoading) {
            _loadCampusShuttlesFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadCampusShuttlesFromDatabase,
          child: _campusShuttles.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No campus shuttles found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _campusShuttles.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _campusShuttles.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildCampusShuttleCard(
                      _campusShuttles[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildCampusShuttleCard(
    Map<String, dynamic> shuttle,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = shuttle['fullname'] ?? 'Unknown';
    final String about = shuttle['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CampusShuttleDetailPage(
                campusShuttle: shuttle,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.directions_bus,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
