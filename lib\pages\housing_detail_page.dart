import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class HousingDetailPage extends StatefulWidget {
  final Map<String, dynamic> housing;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HousingDetailPage({
    Key? key,
    required this.housing,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HousingDetailPage> createState() => _HousingDetailPageState();
}

class _HousingDetailPageState extends State<HousingDetailPage> {
  late RealtimeChannel _housingRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _housingRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _housingRealtimeChannel = Supabase.instance.client
        .channel('housing_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'housing',
      callback: (payload) async {
        // Manual filtering for the specific housing
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.housing['id']) {
          print("Realtime update received for housing detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshHousing();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshHousing() async {
    try {
      final response = await Supabase.instance.client
          .from('housing')
          .select('*')
          .eq('id', widget.housing['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's housing with the new data
          widget.housing.clear();
          widget.housing.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing housing: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.housing['fullname'] ?? 'Unknown';
    final String hours = widget.housing['hours'] ?? '';
    final String accessMethod = widget.housing['accessmethod'] ?? '';
    final int capacity = widget.housing['capacity'] ?? 0;
    final String residentOrg = widget.housing['residentorgorclub'] ?? '';
    final String about = widget.housing['about'] ?? '';
    
    // Contact information
    final String phone = widget.housing['phone']?.toString() ?? '';
    final dynamic latitude = widget.housing['latitude'];
    final dynamic longitude = widget.housing['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Housing details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.apartment,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (residentOrg.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      residentOrg,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Housing information
                      if (capacity > 0)
                        _buildDetailRow(theme, Icons.people, 'Capacity', capacity.toString()),
                      if (hours.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                      if (accessMethod.isNotEmpty)
                        _buildDetailRow(theme, Icons.vpn_key, 'Access Method', accessMethod),
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About this Housing:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
