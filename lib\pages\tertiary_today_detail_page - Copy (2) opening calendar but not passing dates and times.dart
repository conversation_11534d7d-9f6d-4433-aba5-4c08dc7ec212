// tertiary_today_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart'; // For date formatting

import 'login_page.dart';

class TertiaryTodayDetailPage extends StatefulWidget {
  final Map<String, dynamic> event;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const TertiaryTodayDetailPage({
    Key? key,
    required this.event,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<TertiaryTodayDetailPage> createState() => _TertiaryTodayDetailPageState();
}

class _TertiaryTodayDetailPageState extends State<TertiaryTodayDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  void _loadImage() {
    setState(() {
      _imageUrl = widget.event['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchNavigation(double latitude, double longitude) async {
    final Uri url = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude');
    if (await launchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch navigation.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  String _formatDateRangeForCalendar() {
    String startDay = widget.event['startday'] ?? '';
    String startMonth = widget.event['startmonth'] ?? '';
    String startYear = widget.event['startyear'] ?? '';
    String endDay = widget.event['endday'] ?? startDay;
    String endMonth = widget.event['endmonth'] ?? startMonth;
    String endYear = widget.event['endyear'] ?? startYear;

    String startTime = widget.event['starttime'] ?? '00:00';
    String endTime = widget.event['endtime'] ?? '23:59';

    DateFormat dateFormat = DateFormat('yyyyMMdd');
    DateFormat timeFormat = DateFormat('HHmmss');

    DateTime startDate = DateTime(int.parse(startYear), int.parse(_monthNameToNumber(startMonth)), int.parse(startDay));
    DateTime endDate = DateTime(int.parse(endYear), int.parse(_monthNameToNumber(endMonth)), int.parse(endDay));

    TimeOfDay? startTimeOfDay = _parseTimeOfDay(startTime);
    TimeOfDay? endTimeOfDay = _parseTimeOfDay(endTime);

    String formattedStartTime = startTimeOfDay != null ? timeFormat.format(DateTime(startDate.year, startDate.month, startDate.day, startTimeOfDay.hour, startTimeOfDay.minute)) : '000000';
    String formattedEndTime = endTimeOfDay != null ? timeFormat.format(DateTime(endDate.year, endDate.month, endDate.day, endTimeOfDay.hour, endTimeOfDay.minute)) : '235959';


    String startDateFormatted = dateFormat.format(startDate);
    String endDateFormatted = dateFormat.format(endDate);

    return '$startDateFormatted${formattedStartTime}00/$endDateFormatted${formattedEndTime}00'; // Google Calendar format
  }

  String _monthNameToNumber(String monthName) {
    const monthMap = {
      'january': '1', 'february': '2', 'march': '3', 'april': '4',
      'may': '5', 'june': '6', 'july': '7', 'august': '8',
      'september': '9', 'october': '10', 'november': '11', 'december': '12',
      'jan': '1', 'feb': '2', 'mar': '3', 'apr': '4',
      'jun': '6', 'jul': '7', 'aug': '8', 'sep': '9', 'oct': '10', 'nov': '11', 'dec': '12',
    };
    final lowerMonthName = monthName.toLowerCase();
    return monthMap[lowerMonthName] ?? monthName; // Return original if not found
  }

  TimeOfDay? _parseTimeOfDay(String timeString) {
    try {
      // Try parsing with AM/PM format first
      DateTime parsedTime12H = DateFormat('h:mm a').parse(timeString.trim());
      return TimeOfDay.fromDateTime(parsedTime12H);
    } catch (e1) {
      try {
        // If AM/PM parsing fails, try 24-hour format
        DateTime parsedTime24H = DateFormat('HH:mm').parse(timeString.trim());
        return TimeOfDay.fromDateTime(parsedTime24H);
      } catch (e2) {
        print("Error parsing time string: $timeString, errors: 12H: $e1, 24H: $e2");
        return null;
      }
    }
  }

  Future<void> _launchCalendar() async {
    String eventTitle = widget.event['fullname'] ?? 'Event';
    String eventDescription = widget.event['about'] ?? '';
    String eventLocation = widget.event['location'] ?? '';
    String dateRange = _formatDateRangeForCalendar();

    String calendarUrl = 'https://calendar.google.com/calendar/render?action=TEMPLATE&text=${Uri.encodeComponent(eventTitle)}&dates=$dateRange&details=${Uri.encodeComponent(eventDescription)}&location=${Uri.encodeComponent(eventLocation)}';

    final Uri url = Uri.parse(calendarUrl);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch calendar.')),
      );
    }
  }

  Future<void> _launchTicketsLink(String ticketsLink) async {
    if (ticketsLink.isNotEmpty) {
      final Uri uri = Uri.parse(ticketsLink);
      if (await canLaunchUrl(uri)) {
        launchUrl(uri);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not launch tickets link.')),
        );
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.event['phone'] as String? ?? '';
    final whatsappNumber = widget.event['whatsapp'] as String? ?? '';
    // Parse latitude and longitude using double.tryParse
    final latitude = double.tryParse(widget.event['latitude']?.toString() ?? '');
    final longitude = double.tryParse(widget.event['longitude']?.toString() ?? '');
    final building = widget.event['building'] as String? ?? '';
    final room = widget.event['room'] as String? ?? '';
    final locationText = (building.isNotEmpty && room.isNotEmpty) ? '$building $room' : (building.isNotEmpty ? building : (room.isNotEmpty ? room : null));
    // Parse capacity using int.tryParse
    final capacity = int.tryParse(widget.event['capacity']?.toString() ?? '') ?? 0;
    final payment = widget.event['payment'] as String? ?? '';
    final email = widget.event['email'] as String? ?? '';
    final department = widget.event['department'] as String? ?? '';
    final ticketslink = widget.event['ticketslink'] as String? ?? '';

    final startTime = widget.event['starttime'] as String? ?? '';
    final endTime = widget.event['endtime'] as String? ?? '';
    final startDay = widget.event['startday'] as String? ?? '';
    final startMonth = widget.event['startmonth'] as String? ?? '';
    final startYear = widget.event['startyear'] as String? ?? '';
    final endDay = widget.event['endday'] as String? ?? '';
    final endMonth = widget.event['endmonth'] as String? ?? '';
    final endYear = widget.event['endyear'] as String? ?? '';

    String formattedTime = '';
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      TimeOfDay? startTimeOfDay = _parseTimeOfDay(startTime);
      TimeOfDay? endTimeOfDay = _parseTimeOfDay(endTime);

      String formattedStartTime = startTimeOfDay != null ? DateFormat('h:mm a').format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, startTimeOfDay.hour, startTimeOfDay.minute)) : startTime;
      String formattedEndTime = endTimeOfDay != null ? DateFormat('h:mm a').format(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, endTimeOfDay.hour, endTimeOfDay.minute)) : endTime;
      formattedTime = '$formattedStartTime - $formattedEndTime';
    }

    String formattedDateRange = '';
    if (startDay.isNotEmpty && startMonth.isNotEmpty && startYear.isNotEmpty) {
      String startDateFormatted = '$startDay/${_monthNameToNumber(startMonth)}/$startYear';
      if (endDay.isNotEmpty && endMonth.isNotEmpty && endYear.isNotEmpty && (startDateFormatted != '$endDay/${_monthNameToNumber(endMonth)}/$endYear')) {
        String endDateFormatted = '$endDay/${_monthNameToNumber(endMonth)}/$endYear';
        formattedDateRange = '$startDateFormatted - $endDateFormatted';
      } else {
        formattedDateRange = startDateFormatted;
      }
    }


    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;
    final bool isTicketsLinkAvailable = ticketslink.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;


    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.event['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.event_available, // Using Icons.event_available as a good alternative
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _launchCalendar,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 200, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.cover,
                      height: 200,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.cover,
                      height: 200,
                    ),
                    fit: BoxFit.cover,
                    height: 200,
                  ),
                  const SizedBox(height: 16),
                  if (widget.event['about'] != null && (widget.event['about'] as String).isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', widget.event['about']),
                  if (formattedDateRange.isNotEmpty)
                    _buildDetailRow(theme, Icons.calendar_month_outlined, 'Date', formattedDateRange),
                  if (formattedTime.isNotEmpty)
                    _buildDetailRow(theme, Icons.access_time_outlined, 'Time', formattedTime),
                  if (locationText != null)
                    _buildDetailRow(theme, Icons.location_city_outlined, 'Location', locationText),
                  if (department.isNotEmpty)
                    _buildDetailRow(theme, Icons.category_outlined, 'Department', department),
                  if (capacity > 0)
                    _buildDetailRow(theme, Icons.people_outline, 'Capacity', '$capacity'),
                  if (payment.isNotEmpty)
                    _buildDetailRow(theme, Icons.payment_outlined, 'Payment', payment),
                  if (isEmailAvailable)
                    _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                  if (isPhoneAvailable)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                  if (isTicketsLinkAvailable) // Conditionally build the button
                    Padding(
                      padding: const EdgeInsets.only(top: 24.0),
                      child: _buildTicketsButton(theme, ticketslink),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isNavigationAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.navigation,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isNavigationAvailable ? () => _launchNavigation(latitude!, longitude!) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isWhatsappAvailable ? 1.0 : 0.5,
                    child: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkRow(ThemeData theme, IconData icon, String title, String url) { // No changes here as it's not used for tickets anymore
    if (url.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: InkWell(
        onTap: () async {
          final Uri uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            launchUrl(uri);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Could not launch link.')),
            );
          }
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: theme.colorScheme.onSurfaceVariant,
              size: 20,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    url,
                    style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTicketsButton(ThemeData theme, String ticketsLink) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.secondary,
        foregroundColor: theme.colorScheme.surface,
        minimumSize: const Size(double.infinity, 48),
      ),
      icon: const Icon(Icons.confirmation_number_outlined),
      label: const Text('Get Tickets'),
      onPressed: () => _launchTicketsLink(ticketsLink),
    );
  }
}