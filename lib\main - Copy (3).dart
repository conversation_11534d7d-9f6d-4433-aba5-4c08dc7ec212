import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform, WidgetState;
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_messaging_service.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart'; // Keep import, but will conditionally use
import 'package:supabase_flutter/supabase_flutter.dart';
//import 'package:flutter_dotenv/flutter_dotenv.dart'; // Import flutter_dotenv
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart'; // Import url_launcher
import 'package:google_mlkit_document_scanner/google_mlkit_document_scanner.dart'; // Import ML Kit Document Scanner

import 'pages/tertiary_page.dart';
import 'pages/secondary_page.dart';
import 'pages/primary_page.dart';
import 'pages/pre_primary_page.dart';
import 'pages/login_page.dart';
import 'pages/utilities_page.dart'; // Correct import path
import 'pages/weblinks_page.dart'; // Correct import path
import 'pages/calculators.dart'; // Import Calculators Page
import 'pages/converters.dart'; // Import Converters Page
import 'package:google_fonts/google_fonts.dart';
import 'pages/dashboard_page.dart';
import 'pages/downloader.dart'; // Use our conditional import
import 'pages/my_time_page.dart'; // Import the new page
import 'package:pdf/widgets.dart' as pw; // Add this line
import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // Import Font Awesome
import 'pages/document_scanner_page.dart'; // Import Document Scanner Page
import 'widgets/latex_image_renderer.dart'; // Import LaTeX image renderer

// Custom scroll behavior that always shows scrollbar on desktop/web
class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.linux ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        thickness: 12.0,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFF8c8c8c)
            : const Color.fromRGBO(158, 158, 158, .6),
        trackColor: Theme.of(context).brightness == Brightness.light
            ? Colors.white
            : null,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}


// Add these global variables at the top of your main file
late final pw.Font roboto;
late final pw.Font robotoBold;


void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize fonts for PDF generation
  final robotoData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
  final robotoBoldData = await rootBundle.load('assets/fonts/Roboto-Bold.ttf');
  roboto = pw.Font.ttf(robotoData);
  robotoBold = pw.Font.ttf(robotoBoldData);

    if (!kIsWeb) {
    // Initialize the downloader only on mobile/desktop.
    // import 'package:flutter_downloader/flutter_downloader.dart'; is conditional in downloader.dart
    // await FlutterDownloader.initialize(); // moved to downloader.dart
  }

  // Initialize Firebase FIRST (before Supabase)
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await FirebaseMessagingService().initialize();

  if (!kIsWeb) {
    await MobileAds.instance.initialize(); // Initialize AdMob only if not web
  }

  // Load environment variables from .env file
  //await dotenv.load(fileName: '.env');

  // Initialize Supabase AFTER Firebase
  await Supabase.initialize(
    url: 'https://qyhhhvqmbahyknltcbqw.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5aGhodnFtYmFoeWtubHRjYnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYxNDkzMzQsImV4cCI6MjA1MTcyNTMzNH0.gEAdk-Efly7wec5AoQrErtL2kHLuEjzMVmCBCKrse7g',
  );

  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  runApp(MyApp(initialIsDarkMode: isDarkMode));
}

class MyApp extends StatefulWidget {
  final bool initialIsDarkMode;
  // Static variable to store the preloaded colleges
  static List<Map<String, dynamic>>? preloadedColleges;
  static List<Map<String, dynamic>>? preloadedSecondarySchools;
  static List<Map<String, dynamic>>? preloadedPrimarySchools;
  static List<Map<String, dynamic>>? preloadedPreSchools;

  const MyApp({Key? key, required this.initialIsDarkMode}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late bool isDarkMode;
  StreamSubscription<List<Map<String, dynamic>>>? _collegesSubscription; // Subscription for real-time updates
  StreamSubscription<List<Map<String, dynamic>>>? _secondarySchoolsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _primarySchoolsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _preSchoolsSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    isDarkMode = widget.initialIsDarkMode;
    _preloadAllDataAndListen();
   }

  // Function to preload all data and listen for changes
  void _preloadAllDataAndListen() async {
     await _preloadColleges();
      await _preloadSecondarySchools();
      await _preloadPrimarySchools();
      await _preloadPreSchools();
      _listenToCollegeChanges();
      _listenToSecondarySchoolChanges();
      _listenToPrimarySchoolChanges();
      _listenToPreSchoolChanges();
   }


  // Function to preload colleges from Supabase
  Future<void> _preloadColleges() async {
    try {
      final response = await Supabase.instance.client
          .from('colleges')
          .select('*'); // Select all fields

      if (response is List) {
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(response);
        print('Colleges preloaded successfully: ${MyApp.preloadedColleges?.length} colleges with all fields.');
      } else {
        print('Error preloading colleges: Response is not a List');
      }
    } catch (e) {
      print('Error preloading colleges: $e');
    }
  }

  void _listenToCollegeChanges() {
    _collegesSubscription = Supabase.instance.client
        .from('colleges')
        .stream(primaryKey: ['id']) // Assuming 'id' is the primary key
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for colleges: ${snapshot.length} changes.');
            _updatePreloadedColleges(snapshot);
          }
        }, onError: (err) {
          print('Realtime colleges stream error: $err');
        });
  }

  void _updatePreloadedColleges(List<Map<String, dynamic>> changes) {
    if (MyApp.preloadedColleges == null) {
      MyApp.preloadedColleges = List<Map<String, dynamic>>.from(changes); // Initialize if null
      return;
    }

    Map<int, Map<String, dynamic>> currentCollegesMap = {};
    for (var college in MyApp.preloadedColleges!) {
      if (college['id'] is int) { // Assuming 'id' is int, adjust if different type
        currentCollegesMap[college['id'] as int] = college;
      } else if (college['id'] is String) {
        try {
          currentCollegesMap[int.parse(college['id'] as String)] = college;
        } catch (e) {
          print('Error parsing college ID to int: ${college['id']}, error: $e');
          continue; // Skip if ID cannot be parsed
        }
      } else {
        print('Unexpected type for college ID: ${college['id'].runtimeType}');
        continue; // Skip if ID is of unexpected type
      }
    }


    for (var change in changes) {
      if (change['id'] == null) {
        print('Warning: Change data missing ID: $change');
        continue; // Skip if change data is missing ID
      }
      int? changeId;
      if (change['id'] is int) {
        changeId = change['id'] as int;
      } else if (change['id'] is String) {
        try {
          changeId = int.parse(change['id'] as String);
        } catch (e) {
          print('Error parsing change ID to int: ${change['id']}, error: $e');
          continue; // Skip if ID cannot be parsed
        }
      } else {
        print('Unexpected type for change ID: ${change['id'].runtimeType}');
        continue; // Skip if ID is of unexpected type
      }

      if (changeId == null) continue; // Ensure changeId is not null after parsing

      final eventType = change['type']; // 'INSERT', 'UPDATE', 'DELETE'
      final changedRecord = change['record']; // The changed record data

      if (eventType == 'INSERT' || eventType == 'UPDATE') {
        if (changedRecord != null) {
           currentCollegesMap[changeId] = Map<String, dynamic>.from(changedRecord); // Ensure it's a Map
        }
      } else if (eventType == 'DELETE') {
        currentCollegesMap.remove(changeId);
      }
    }
    MyApp.preloadedColleges = currentCollegesMap.values.toList();
    print('Preloaded colleges updated. Total colleges: ${MyApp.preloadedColleges?.length}');
  }

  // Preload Secondary Schools
  Future<void> _preloadSecondarySchools() async {
    try {
      final response = await Supabase.instance.client
          .from('secondaryschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedSecondarySchools = List<Map<String, dynamic>>.from(response);
        print('Secondary Schools preloaded successfully: ${MyApp.preloadedSecondarySchools?.length}');
      } else {
        print('Error preloading Secondary Schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Secondary Schools: $e');
    }
  }

  void _listenToSecondarySchoolChanges() {
    _secondarySchoolsSubscription = Supabase.instance.client
        .from('secondaryschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for secondary schools: ${snapshot.length} changes.');
            _updatePreloadedSecondarySchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime secondary schools stream error: $err');
        });
  }

  void _updatePreloadedSecondarySchools(List<Map<String, dynamic>> changes) {
    if (MyApp.preloadedSecondarySchools == null) {
      MyApp.preloadedSecondarySchools = List<Map<String, dynamic>>.from(changes);
      return;
    }

    Map<int, Map<String, dynamic>> currentSchoolsMap = {};
    for (var school in MyApp.preloadedSecondarySchools!) {
      if (school['id'] is int) {
        currentSchoolsMap[school['id'] as int] = school;
      } else if (school['id'] is String) {
        try {
          currentSchoolsMap[int.parse(school['id'] as String)] = school;
        } catch (e) {
          print('Error parsing secondary school ID to int: ${school['id']}, error: $e');
          continue;
        }
      } else {
        print('Unexpected type for secondary school ID: ${school['id'].runtimeType}');
        continue;
      }
    }

    for (var change in changes) {
      int? changeId = _parseIdToInt(change['id'], 'secondary school change');
      if (changeId == null) continue;

      final eventType = change['type'];
      final changedRecord = change['record'];

      if (eventType == 'INSERT' || eventType == 'UPDATE') {
        if (changedRecord != null) {
          currentSchoolsMap[changeId] = Map<String, dynamic>.from(changedRecord);
        }
      } else if (eventType == 'DELETE') {
        currentSchoolsMap.remove(changeId);
      }
    }
    MyApp.preloadedSecondarySchools = currentSchoolsMap.values.toList();
    print('Preloaded secondary schools updated. Total schools: ${MyApp.preloadedSecondarySchools?.length}');
  }


  // Preload Primary Schools
  Future<void> _preloadPrimarySchools() async {
    try {
      final response = await Supabase.instance.client
          .from('primaryschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedPrimarySchools = List<Map<String, dynamic>>.from(response);
        print('Primary Schools preloaded successfully: ${MyApp.preloadedPrimarySchools?.length}');
      } else {
        print('Error preloading Primary Schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Primary Schools: $e');
    }
  }

  void _listenToPrimarySchoolChanges() {
    _primarySchoolsSubscription = Supabase.instance.client
        .from('primaryschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for primary schools: ${snapshot.length} changes.');
            _updatePreloadedPrimarySchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime primary schools stream error: $err');
        });
  }

  void _updatePreloadedPrimarySchools(List<Map<String, dynamic>> changes) {
    if (MyApp.preloadedPrimarySchools == null) {
      MyApp.preloadedPrimarySchools = List<Map<String, dynamic>>.from(changes);
      return;
    }

    Map<int, Map<String, dynamic>> currentSchoolsMap = {};
    for (var school in MyApp.preloadedPrimarySchools!) {
      if (school['id'] is int) {
        currentSchoolsMap[school['id'] as int] = school;
      } else if (school['id'] is String) {
        try {
          currentSchoolsMap[int.parse(school['id'] as String)] = school;
        } catch (e) {
          print('Error parsing primary school ID to int: ${school['id']}, error: $e');
          continue;
        }
      } else {
        print('Unexpected type for primary school ID: ${school['id'].runtimeType}');
        continue;
      }
    }

    for (var change in changes) {
      int? changeId = _parseIdToInt(change['id'], 'primary school change');
      if (changeId == null) continue;

      final eventType = change['type'];
      final changedRecord = change['record'];

      if (eventType == 'INSERT' || eventType == 'UPDATE') {
        if (changedRecord != null) {
          currentSchoolsMap[changeId] = Map<String, dynamic>.from(changedRecord);
        }
      } else if (eventType == 'DELETE') {
        currentSchoolsMap.remove(changeId);
      }
    }
    MyApp.preloadedPrimarySchools = currentSchoolsMap.values.toList();
    print('Preloaded primary schools updated. Total schools: ${MyApp.preloadedPrimarySchools?.length}');
  }

  // Preload Pre-schools
  Future<void> _preloadPreSchools() async {
    try {
      final response = await Supabase.instance.client
          .from('preschools')
          .select('*');

      if (response is List) {
        MyApp.preloadedPreSchools = List<Map<String, dynamic>>.from(response);
        print('Pre-schools preloaded successfully: ${MyApp.preloadedPreSchools?.length}');
      } else {
        print('Error preloading Pre-schools: Response is not a List');
      }
    } catch (e) {
      print('Error preloading Pre-schools: $e');
    }
  }

  void _listenToPreSchoolChanges() {
    _preSchoolsSubscription = Supabase.instance.client
        .from('preschools')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
          if (snapshot.isNotEmpty) {
            print('Realtime update received for pre-schools: ${snapshot.length} changes.');
            _updatePreloadedPreSchools(snapshot);
          }
        }, onError: (err) {
          print('Realtime pre-schools stream error: $err');
        });
  }

  void _updatePreloadedPreSchools(List<Map<String, dynamic>> changes) {
    if (MyApp.preloadedPreSchools == null) {
      MyApp.preloadedPreSchools = List<Map<String, dynamic>>.from(changes);
      return;
    }

    Map<int, Map<String, dynamic>> currentSchoolsMap = {};
    for (var school in MyApp.preloadedPreSchools!) {
      if (school['id'] is int) {
        currentSchoolsMap[school['id'] as int] = school;
      } else if (school['id'] is String) {
        try {
          currentSchoolsMap[int.parse(school['id'] as String)] = school;
        } catch (e) {
          print('Error parsing pre-school ID to int: ${school['id']}, error: $e');
          continue;
        }
      } else {
        print('Unexpected type for pre-school ID: ${school['id'].runtimeType}');
        continue;
      }
    }

    for (var change in changes) {
      int? changeId = _parseIdToInt(change['id'], 'pre-school change');
      if (changeId == null) continue;

      final eventType = change['type'];
      final changedRecord = change['record'];

      if (eventType == 'INSERT' || eventType == 'UPDATE') {
        if (changedRecord != null) {
          currentSchoolsMap[changeId] = Map<String, dynamic>.from(changedRecord);
        }
      } else if (eventType == 'DELETE') {
        currentSchoolsMap.remove(changeId);
      }
    }
    MyApp.preloadedPreSchools = currentSchoolsMap.values.toList();
    print('Preloaded pre-schools updated. Total schools: ${MyApp.preloadedPreSchools?.length}');
  }

  // Helper function to parse ID to int with error handling
  int? _parseIdToInt(dynamic id, String context) {
    if (id == null) {
      print('Warning: Change data missing ID for $context.');
      return null;
    }
    if (id is int) {
      return id;
    } else if (id is String) {
      try {
        return int.parse(id);
      } catch (e) {
        print('Error parsing ID to int for $context: $id, error: $e');
        return null;
      }
    } else {
      print('Unexpected type for ID in $context: ${id.runtimeType}');
      return null;
    }
  }


  void _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
     await prefs.setBool('darkMode', isDark);
   }

  void toggleTheme() {
    setState(() {
      isDarkMode = !isDarkMode;
      _saveThemePreference(isDarkMode);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _collegesSubscription?.cancel(); // Cancel the stream subscription
    _secondarySchoolsSubscription?.cancel();
    _primarySchoolsSubscription?.cancel();
    _preSchoolsSubscription?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey, // Add navigatorKey for LaTeX image rendering
      scrollBehavior: CustomScrollBehavior(),
      title: 'apptelligent',
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      theme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFFEEEEEE),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: Colors.white,
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: ColorScheme.light(
          surface: Colors.white,
          onSurface: Colors.black,
          secondary: Colors.black.withOpacity(0.6),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: Colors.grey,
        ),
        textSelectionTheme: const TextSelectionThemeData( // Cursor and selection color
          cursorColor: Colors.black,
          selectionHandleColor: Colors.black,
        ),
        inputDecorationTheme: const InputDecorationTheme( // TextFormField border
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.black),
          ),
        ),
        radioTheme: RadioThemeData( // Radio button theming
          fillColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.black; // Selected radio button color - Light mode: Black
            }
            return null; // Use default color for other states
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.focused)) {
              return Colors.black.withOpacity(0.12); // Optional: focus overlay
            }
            return null;
          }),
        ),
      ),
      darkTheme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFF090909),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF202020),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: const Color(0xFF202020),
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: const ColorScheme.dark(
          surface: Color(0xFF202020),
          onSurface: Colors.white,
          secondary: Colors.white70,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
        progressIndicatorTheme: const ProgressIndicatorThemeData(
          color: Colors.grey,
        ),
        textSelectionTheme: const TextSelectionThemeData( // Cursor and selection color
          cursorColor: Colors.white,
          selectionHandleColor: Colors.white,
        ),
        inputDecorationTheme: const InputDecorationTheme( // TextFormField border
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.white),
          ),
        ),
        radioTheme: RadioThemeData( // Radio button theming
          fillColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.white; // Selected radio button color - Dark mode: White
            }
            return null; // Use default color for other states
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
            if (states.contains(WidgetState.focused)) {
              return Colors.white.withOpacity(0.12); // Optional: focus overlay
            }
            return null;
          }),
        ),
      ),
      home: HomeScreen(
        isDarkMode: isDarkMode,
        toggleTheme: toggleTheme,
      ),
      // Add routes here to define navigation paths
      routes: {
        '/dashboard': (context) => DashboardPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme),
        '/login': (context) => LoginPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // If you want to explicitly define login route as well
        '/calculators': (context) => CalculatorsPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // Calculators Route
        '/converters': (context) => ConvertersPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // Converters Route
        '/document_scanner': (context) => DocumentScannerPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // Document Scanner Route
      },
    );
  }
}

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HomeScreen({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _adTimer;
  List<String> _adUrls = []; // Store URLs from Supabase
  int _currentAdIndex = 0;
  late bool _isDarkMode;
  VoidCallback? _toggleTheme;
  VideoPlayerController? _adVideoController;
  bool _isVideoInitialized = false;

  BannerAd? _bannerAd; // Will be null on web
  bool _isBannerAdReady = false; // Will be false on web

  bool _isWebPlatform = kIsWeb; // Track if it's web platform

  final GlobalKey _appBarKey = GlobalKey(); // Global key for AppBar

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
    _toggleTheme = widget.toggleTheme;

    // Request Calendar Permissions here at app start:
    _requestCalendarPermissions();
    _requestCameraPermission(); // Request camera permission for document scanner

    _loadAdUrlsFromSupabase(); // Load ads from Supabase
    _adTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_adUrls.isNotEmpty) {
        setState(() {
          _currentAdIndex = (_currentAdIndex + 1) % _adUrls.length;
          _initializeVideoPlayer();
        });
      }
    });
    if (!_isWebPlatform) { // Load ads only if not web
      _loadBannerAd();
    }
  }

  Future<void> _requestCameraPermission() async {
    PermissionStatus cameraStatus = await Permission.camera.status;
    if (!cameraStatus.isGranted) {
      PermissionStatus status = await Permission.camera.request();
      if (status == PermissionStatus.granted) {
        print("Camera permissions granted!");
      } else {
        print("Camera permissions denied.");
        if (status == PermissionStatus.permanentlyDenied) {
          print("Camera permissions permanently denied.");
          openAppSettings();
        }
      }
    } else {
      print("Camera permissions already granted.");
    }
  }


  Future<void> _requestCalendarPermissions() async {
    PermissionStatus calendarStatus = await Permission.calendar.status;

    if (!calendarStatus.isGranted) {
      PermissionStatus status = await Permission.calendar.request();

      if (status == PermissionStatus.granted) {
        // Permissions granted, you can now access the calendar
        print("Calendar permissions granted!");
        // Proceed to use calendar functionality here in your app if needed
      } else {
        // Permissions denied
        print("Calendar permissions denied.");
        // Handle the case where permissions are denied (e.g., show a message to the user)
        if (status == PermissionStatus.permanentlyDenied) {
          // The user has permanently denied permissions, you might want to open app settings
          print("Calendar permissions permanently denied.");
          openAppSettings(); // This opens app settings so the user can manually enable permissions
        }
      }
    } else {
      // Permissions already granted
      print("Calendar permissions already granted.");
      // Proceed to use calendar functionality here in your app if needed
    }
  }


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isWebPlatform) { // Load banner ad only if not web
      _loadBannerAd();
    }
  }


  void _loadAdUrlsFromSupabase() async {
    try {
      final List<FileObject> response = await Supabase.instance.client
          .storage
          .from('ads') // Ensure 'ads' is your bucket name
          .list();

      if (response.isNotEmpty) {
        final urls = response.map((file) {
          return Supabase.instance.client.storage
              .from('ads')
              .getPublicUrl(file.name);
        }).toList();

        setState(() {
          _adUrls = urls;
          _initializeVideoPlayer(); // Initialize video player after loading ads
        });
        print('Successfully loaded ad URLs from Supabase: $urls'); // Add this log
      } else {
        print('No files found in the Supabase ads bucket.'); // Add this log
      }
    } catch (e) {
      print('Error loading ads from Supabase: $e');
      // Handle error, maybe show a default image/video or message
     }
   }


  void _initializeVideoPlayer() {
    if (_adUrls.isNotEmpty && _adUrls[_currentAdIndex].endsWith('.mp4')) {
      if (_adVideoController != null) {
        if (_adVideoController!.dataSource != _adUrls[_currentAdIndex]) {
          _adVideoController!.dispose().then((_) {
            _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
            _adVideoController!.initialize().then((_) {
              _adVideoController!.setVolume(0.0); // Mute the video in the grid
              _adVideoController!.play();
              _adVideoController!.setLooping(true);
              if (mounted) {
                setState(() {
                  _isVideoInitialized = true;
                });
              }
            }).catchError((error) {
              print("Error initializing video player: $error");
            });
          });
        } else if (!_isVideoInitialized) {
          _adVideoController!.initialize().then((_) {
            _adVideoController!.setVolume(0.0); // Mute the video in the grid
            _adVideoController!.play();
            _adVideoController!.setLooping(true);
            if (mounted) {
              setState(() {
                _isVideoInitialized = true;
              });
            }
          }).catchError((error) {
            print("Error initializing video player: $error");
          });
        }
      } else {
        _adVideoController = VideoPlayerController.network(_adUrls[_currentAdIndex]);
        _adVideoController!.initialize().then((_) {
          _adVideoController!.setVolume(0.0); // Mute the video in the grid
          _adVideoController!.play();
          _adVideoController!.setLooping(true);
          if (mounted) {
            setState(() {
              _isVideoInitialized = true;
            });
          }
        }).catchError((error) {
          print("Error initializing video player: $error");
        });
      }
     } else {
       _disposeVideoPlayer();
      }
    }

    void _disposeVideoPlayer() {
      if (_adVideoController != null) {
        _adVideoController!.pause();
        _adVideoController!.dispose();
        _adVideoController = null;
        _isVideoInitialized = false;
      }
    }

    Future<void> _loadBannerAd() async {
      if (_isWebPlatform) return; // Don't load on web

      String adUnitId;
      AdSize adSize = AdSize.fluid; // Adaptive for mobile
      adUnitId = 'ca-app-pub-3940256099942544/9214589741'; // Android test banner ad ID


      _bannerAd = BannerAd(
        adUnitId: adUnitId,
        request: const AdRequest(),
        size: adSize,
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            setState(() {
              _isBannerAdReady = true;
            });
          },
          onAdFailedToLoad: (ad, error) {
            print('Banner Ad failed to load: $error');
            _isBannerAdReady = false;
            ad.dispose();
          },
          onAdOpened: (Ad ad) => print('Banner Ad opened.'),
          onAdClosed: (Ad ad) => print('Banner Ad closed.'),
        ),
      );
      _bannerAd!.load();
    }

    @override
    void dispose() {
      _adTimer.cancel();
      _disposeVideoPlayer();
      if (!_isWebPlatform) {
        _bannerAd?.dispose();
      }
      super.dispose();
    }

    void _navigateToPage(BuildContext context, String title) {
      final Map<String, Widget Function(BuildContext)> pages = {
        'Tertiary': (context) => TertiaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Secondary': (context) => SecondaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Primary & Pre-K': (context) => PrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
        'Work 10X Faster': (context) {
          // Placeholder for login check - replace with your actual logic
          bool isLoggedIn = false; // Assume not logged in for now
          return isLoggedIn
              ? DashboardPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!)
              : LoginPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!);
        },
      };

      if (pages.containsKey(title)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: pages[title]!,
          ),
        );
      }
    }

    void _showFullPageAd(BuildContext context, String adUrl) {
      print('_showFullPageAd called with URL: $adUrl');
      Navigator.of(context).push(
        PageRouteBuilder(
          opaque: false,
          pageBuilder: (BuildContext context, _, __) {
            return FullScreenAdModal(
              adUrl: adUrl,
            );
          },
        ),
      );
    }

   _launchURL(Uri url) async {
     if (await canLaunchUrl(url)) {
       await launchUrl(url);
     } else {
       print('Could not launch $url');
     }
   }

   void _showCalendarMenu(BuildContext context) async {
     final theme = Theme.of(context);
     final RenderBox appBarBox = _appBarKey.currentContext!.findRenderObject() as RenderBox;
     final Offset appBarPosition = appBarBox.localToGlobal(Offset.zero);
     final Size appBarSize = appBarBox.size;

     double menuWidth = 240.0; // Adjust width for icons and text
     double appBarCenter = appBarPosition.dx + appBarSize.width / 2;
     double menuLeft = appBarCenter - menuWidth / 2;
     double menuTop = appBarPosition.dy + appBarSize.height;

     await showMenu(
       context: context,
       position: RelativeRect.fromLTRB(
         menuLeft, // left
         menuTop,  // top
         menuLeft + menuWidth, // right (using left + width for explicit width)
         menuTop + 300, // bottom (adjust as needed, large enough to cover menu)
       ),
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       items: <PopupMenuEntry<String>>[
         PopupMenuItem<String>(
           value: 'header_my_time',
           enabled: false,
           child: Text('My Time', style: TextStyle(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
         ),
         PopupMenuItem<String>(
           value: 'google_calendar',
           child: Row(
             children: <Widget>[
               Icon(Icons.today, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Google Calendar', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri googleCalendarUri = Uri.parse('https://calendar.google.com/calendar/r');
               _launchURL(googleCalendarUri);
             });
           },
         ),
         const PopupMenuDivider(),
         PopupMenuItem<String>(
           value: 'header_my_classes',
           enabled: false,
           child: Text('My Classes', style: TextStyle(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
         ),
         PopupMenuItem<String>(
           value: 'google_classroom',
           child: Row(
             children: <Widget>[
               Icon(Icons.airplay, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Google Classroom', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri googleClassroomUri = Uri.parse('https://classroom.google.com');
               _launchURL(googleClassroomUri);
             });
           },
         ),
         PopupMenuItem<String>(
           value: 'canvas',
           child: Row(
             children: <Widget>[
               Icon(Icons.layers_outlined, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Canvas', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri canvasUri = Uri.parse('https://canvas.instructure.com/');
               _launchURL(canvasUri);
             });
           },
         ),
         PopupMenuItem<String>(
           value: 'blackboard',
           child: Row(
             children: <Widget>[
               Icon(Icons.check_box_outline_blank, color: theme.colorScheme.onSurface),
               const SizedBox(width: 12),
               Text('Blackboard', style: TextStyle(color: theme.colorScheme.onSurface)),
             ],
           ),
           onTap: () {
             WidgetsBinding.instance.addPostFrameCallback((_) {
               final Uri blackboardUri = Uri.parse('https://www.blackboard.com/');
               _launchURL(blackboardUri);
             });
           },
         ),
       ],
     );
   }


    Widget _buildAdBanner(BuildContext context, ThemeData theme) {
      if (_adUrls.isEmpty) { // Removed `|| _isWebPlatform` here
        return const SizedBox(); // Or a placeholder
      }

      final currentAdUrl = _adUrls[_currentAdIndex];
      return Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            print('Tapped on ad URL: $currentAdUrl');
            _showFullPageAd(context, currentAdUrl);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Sponsored',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: currentAdUrl.endsWith('.mp4')
                      ? _isVideoInitialized && _adVideoController != null
                          ? VideoPlayer(_adVideoController!)
                          : Theme(
                              data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                              child: const Center(child: CircularProgressIndicator())
                            )
                      : Image.network(
                          currentAdUrl,
                          fit: BoxFit.cover,
                          loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Theme(
                              data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                              child: Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                            print('Error loading image: $exception');
                            return const Icon(Icons.error_outline);
                          },
                        ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    @override
    Widget build(BuildContext context) {
      final theme = Theme.of(context);

      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          key: _appBarKey,
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          title: Text(
            'Refactr',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          actions: [
            IconButton(
              icon: Icon(
                Icons.bolt,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => UtilitiesPage(
                      isDarkMode: _isDarkMode,
                      toggleTheme: _toggleTheme!,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.calculate,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CalculatorsPage(
                      isDarkMode: _isDarkMode,
                      toggleTheme: _toggleTheme!,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.watch_outlined,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () => _showCalendarMenu(context),
            ),
            IconButton(
              icon: Icon(
                Icons.swap_horiz,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ConvertersPage(
                      isDarkMode: _isDarkMode,
                      toggleTheme: _toggleTheme!,
                    ),
                  ),
                );
              },
            ),
            IconButton(
              icon: Icon(
                Icons.document_scanner_outlined, // New icon for Document Scanner
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DocumentScannerPage( // Navigate to Document Scanner Page
                      isDarkMode: _isDarkMode,
                      toggleTheme: _toggleTheme!,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      int crossAxisCount = 2;
                      if (constraints.maxWidth > 1200) {
                        crossAxisCount = 4;
                      } else if (constraints.maxWidth > 800) {
                        crossAxisCount = 2;
                      }

                      return GridView.count(
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        childAspectRatio: 1.1,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          _buildGridItem(context, 'Work 10X Faster', FontAwesomeIcons.brain, theme), // Work Faster first
                          _buildGridItem(context, 'Tertiary', Icons.auto_awesome, theme),
                          _buildGridItem(context, 'Secondary', Icons.star, theme),
                          _buildGridItem(context, 'Primary & Pre-K', Icons.workspace_premium, theme), // Renamed Primary
                        ],
                      );
                    },
                  ),
                ),
                Padding(
                   padding: const EdgeInsets.symmetric(horizontal: 16.0),
                   child: _buildAdBanner(context, theme),
                 ),
                const SizedBox(height: 16),
                 // Example Chart Placeholder (Replace with your actual chart widget)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SizedBox(
                    height: 200,
                    child: CustomPaint(
                      painter: LineChartPainter(
                        lineColor: theme.brightness == Brightness.dark ? Colors.white : Colors.black, // Themed line color
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // AdMob Banner will be placed here
            Container(
              width: double.infinity,
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.home,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          // Home button action
                        },
                      ),
                      const SizedBox(width: 24),
                      IconButton(
                        icon: Icon(
                          Theme.of(context).brightness == Brightness.dark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: _toggleTheme,
                      ),
                      const SizedBox(width: 24),
                      IconButton(
                        icon: Icon(
                          Icons.person_outline,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LoginPage(
                                isDarkMode: _isDarkMode,
                                toggleTheme: _toggleTheme!,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (!_isWebPlatform && _isBannerAdReady && _bannerAd != null)
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: _bannerAd?.size.height.toDouble() ?? AdSize.banner.height.toDouble(),
                  child: AdWidget(ad: _bannerAd!),
                ),
          ],
        ),
      );
    }


      Widget _buildGridItem(BuildContext context, String title, dynamic icon, ThemeData theme) { // Modified to accept dynamic icon
      final bool isDarkMode = theme.brightness == Brightness.dark;
      return Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToPage(context, title),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon is IconData // Check if icon is IconData for Material Icons
                ? Icon(
                    icon,
                    size: 48,
                    color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                  )
                : FaIcon( // Otherwise assume it's FontAwesome IconData
                    icon as IconData?, // Cast to IconData? for FontAwesome
                    size: 48,
                    color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                  ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  class FullScreenAdModal extends StatefulWidget {
     final String adUrl;

     const FullScreenAdModal({Key? key, required this.adUrl}) : super(key: key);

     @override
     State<FullScreenAdModal> createState() => _FullScreenAdModalState();
  }

  class _FullScreenAdModalState extends State<FullScreenAdModal> {
     VideoPlayerController? _localVideoController;

     @override
     void initState() {
       super.initState();
       print('FullScreenAdModal initState with URL: ${widget.adUrl}');
       if (widget.adUrl.endsWith('.mp4')) {
         _localVideoController = VideoPlayerController.network(widget.adUrl);
         // Initialize the video player and start playing
         _localVideoController!.initialize().then((_) {
           _localVideoController!.setVolume(1.0);
           _localVideoController!.play();
           _localVideoController!.setLooping(true);
           if (mounted) {
             setState(() {});
           }
         }).catchError((error) {
           print("Error initializing video player: $error");
         });
       }
     }

     @override
     void dispose() {
       print('FullScreenAdModal dispose');
       if (_localVideoController != null) {
         _localVideoController!.dispose();
       }
       super.dispose();
     }

     @override
     Widget build(BuildContext context) {
       final theme = Theme.of(context);
       return Scaffold(
         backgroundColor: Colors.black.withOpacity(0.9),
         body: Center(
           child: SingleChildScrollView(
             child: Stack(
               alignment: Alignment.center,
               children: <Widget>[
                 if (widget.adUrl.endsWith('.mp4'))
                   _localVideoController != null && _localVideoController!.value.isInitialized
                       ? GestureDetector(
                           onTap: () {
                             setState(() {
                               if (_localVideoController!.value.isPlaying) {
                                 _localVideoController!.pause();
                               } else {
                                 _localVideoController!.play();
                               }
                             });
                           },
                           child: AspectRatio(
                             aspectRatio: _localVideoController!.value.aspectRatio,
                             child: VideoPlayer(_localVideoController!),
                           ),
                         )
                       : Theme(
                           data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                           child: const Center(child: CircularProgressIndicator())
                         )
                 else
                   Image.network(
                     widget.adUrl,
                     fit: BoxFit.contain,
                     loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                       if (loadingProgress == null) return child;
                       return Theme(
                         data: theme.copyWith(progressIndicatorTheme: const ProgressIndicatorThemeData(color: Colors.grey)),
                         child: Center(
                           child: CircularProgressIndicator(
                             value: loadingProgress.expectedTotalBytes != null
                                 ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                 : null,
                           ),
                         ),
                       );
                     },
                     errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                       print('Error loading full screen image: $exception');
                       return const Icon(Icons.error_outline);
                     },
                   ),
                 Positioned(
                   top: 20,
                   right: 20,
                   child: SafeArea(
                     child: IconButton(
                       icon: const Icon(Icons.close, color: Colors.white),
                       onPressed: () {
                         Navigator.pop(context);
                       },
                     ),
                   ),
                 ),
               ],
             ),
           ),
         ),
       );
     }
  }

  // Example Chart Painter (Replace with your actual chart library widget)
  class LineChartPainter extends CustomPainter {
    final Color lineColor;
    LineChartPainter({required this.lineColor});

    @override
    void paint(Canvas canvas, Size size) {
      final paint = Paint()
        ..color = lineColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      final path = Path();
      path.moveTo(0, size.height / 2);
      path.lineTo(size.width, size.height / 2);

      canvas.drawPath(path, paint);
    }

    @override
    bool shouldRepaint(covariant CustomPainter oldDelegate) {
      return false;
    }
  }