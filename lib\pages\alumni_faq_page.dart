// alumni_faq_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class AlumniFaqPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AlumniFaqPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AlumniFaqPageState createState() => _AlumniFaqPageState();
}

class _AlumniFaqPageState extends State<AlumniFaqPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('alumni_faq_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _faqs = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategoryFilter = 'All Categories';
  List<String> _categoryFilterOptions = ['All Categories'];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadFaqsFromCache();
    _loadFaqsFromSupabase();
  }

  void _setupRealtime() {
    final faqTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_faq';
    _realtimeChannel = Supabase.instance.client
        .channel('alumni_faq_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: faqTableName,
      callback: (payload) async {
        print("Realtime update received for alumni FAQs: ${payload.eventType}");
        _loadFaqsFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadFaqsFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_faq_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> faqs = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && faqs.isNotEmpty) {
          setState(() {
            _faqs = faqs;
            _updateCategoryFilterOptions(faqs);
          });
          print("Loaded ${faqs.length} alumni FAQs from cache");
        }
      }
    } catch (e) {
      print("Error loading alumni FAQs from cache: $e");
    }
  }

  void _updateCategoryFilterOptions(List<Map<String, dynamic>> faqs) {
    Set<String> categories = {'All Categories'};
    for (var faq in faqs) {
      if (faq['category'] != null && faq['category'].toString().isNotEmpty) {
        categories.add(faq['category'].toString());
      }
    }
    
    setState(() {
      _categoryFilterOptions = categories.toList()..sort();
    });
  }

  Future<void> _loadFaqsFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final faqTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_faq';
      var query = Supabase.instance.client
          .from(faqTableName)
          .select('*')
          .eq('alumniquestion', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('question.ilike.%$_searchQuery%,answer.ilike.%$_searchQuery%');
      }
      
      // Apply category filter if not "All Categories"
      if (_selectedCategoryFilter != 'All Categories') {
        query = query.eq('category', _selectedCategoryFilter);
      }
      
      final response = await query.order('id', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _faqs = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Update category filter options
      _updateCategoryFilterOptions(_faqs);

      // Cache the data
      _cacheFaqs(_faqs);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading alumni FAQs: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading alumni FAQs: $e')),
      );
    }
  }

  Future<void> _cacheFaqs(List<Map<String, dynamic>> faqs) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_faq_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(faqs));
      print("Cached ${faqs.length} alumni FAQs");
    } catch (e) {
      print("Error caching alumni FAQs: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadFaqsFromSupabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Alumni FAQs',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search FAQs...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Category filter
          if (_categoryFilterOptions.length > 1)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Text(
                    'Category:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedCategoryFilter,
                          isExpanded: true,
                          icon: Icon(
                            Icons.arrow_drop_down,
                            color: theme.colorScheme.onSurface,
                          ),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 16,
                          ),
                          dropdownColor: theme.colorScheme.surface,
                          items: _categoryFilterOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null && newValue != _selectedCategoryFilter) {
                              setState(() {
                                _selectedCategoryFilter = newValue;
                              });
                              _loadFaqsFromSupabase();
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // FAQs list
          Expanded(
            child: VisibilityDetector(
              key: const Key('alumni_faq_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _faqs.isEmpty && !_isLoading) {
                  _loadFaqsFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadFaqsFromSupabase,
                child: _isLoading && _faqs.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _faqs.isEmpty
                        ? Center(
                            child: Text(
                              'No alumni FAQs found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _faqs.length,
                            itemBuilder: (context, index) {
                              return _buildFaqCard(
                                _faqs[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFaqCard(
    Map<String, dynamic> faq,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String question = faq['question'] ?? 'Unknown Question';
    final String answer = faq['answer'] ?? 'No answer provided.';
    final String category = faq['category'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          title: Text(
            question,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          leading: Icon(
            Icons.question_answer,
            color: theme.colorScheme.primary,
          ),
          subtitle: category.isNotEmpty
              ? Text(
                  category,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                )
              : null,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                answer,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
          backgroundColor: theme.colorScheme.surface,
          collapsedBackgroundColor: theme.colorScheme.surface,
        ),
      ),
    );
  }
}
