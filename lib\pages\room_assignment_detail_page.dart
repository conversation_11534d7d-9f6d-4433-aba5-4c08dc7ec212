import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'room_detail_page.dart';
import 'building_detail_page.dart';

class RoomAssignmentDetailPage extends StatefulWidget {
  final Map<String, dynamic> assignment;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const RoomAssignmentDetailPage({
    Key? key,
    required this.assignment,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<RoomAssignmentDetailPage> createState() => _RoomAssignmentDetailPageState();
}

class _RoomAssignmentDetailPageState extends State<RoomAssignmentDetailPage> {
  late RealtimeChannel _assignmentRealtimeChannel;
  Map<String, dynamic>? _roomData;
  Map<String, dynamic>? _buildingData;
  bool _isLoadingRoom = false;
  bool _isLoadingBuilding = false;

  @override
  void initState() {
    super.initState();
    _setupAssignmentRealtimeListener();
    _loadRoomData();
    _loadBuildingData();
  }

  @override
  void dispose() {
    _assignmentRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupAssignmentRealtimeListener() {
    final roomAssignmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomassignments';
    print('Setting up realtime listener for assignment detail: $roomAssignmentsTableName');
    _assignmentRealtimeChannel = Supabase.instance.client
        .channel('assignment_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomAssignmentsTableName,
          callback: (payload) {
            // Check if this change is for the current assignment
            if (payload.newRecord != null && 
                payload.newRecord!['id'] == widget.assignment['id']) {
              print('Received update for current assignment: ${widget.assignment['fullname']}');
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedAssignmentData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the assignment is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedAssignmentData() async {
    try {
      final roomAssignmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomassignments';
      print('Fetching updated data from table: $roomAssignmentsTableName for assignment ID: ${widget.assignment['id']}');
      
      final response = await Supabase.instance.client
          .from(roomAssignmentsTableName)
          .select('*')
          .eq('id', widget.assignment['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedAssignment = Map.from(response);
        print('Successfully fetched updated assignment data');
        
        // Update the widget.assignment with the new data
        setState(() {
          widget.assignment.clear(); // Clear old data
          widget.assignment.addAll(updatedAssignment); // Add updated data
          print("Assignment data updated in detail page for ${widget.assignment['fullname']}");
          _updateAssignmentsCache(updatedAssignment); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated assignment data: $e');
    }
  }

  Future<void> _updateAssignmentsCache(Map<String, dynamic> updatedAssignment) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomassignments_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Updating cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson != null) {
        final List<dynamic> cachedAssignments = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];
        
        // Update the specific assignment in the cache
        bool found = false;
        for (var assignment in cachedAssignments) {
          if (assignment['id'] == updatedAssignment['id']) {
            updatedCache.add(updatedAssignment);
            found = true;
            print('Updated assignment in cache: ${updatedAssignment['fullname']}');
          } else {
            updatedCache.add(Map<String, dynamic>.from(assignment));
          }
        }
        
        if (!found) {
          print('Assignment not found in cache, adding it');
          updatedCache.add(updatedAssignment);
        }
        
        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
        print('Successfully updated assignments cache');
      } else {
        // If cache doesn't exist, create it with just this assignment
        print('Cache not found, creating new cache with this assignment');
        await prefs.setString(cacheKey, jsonEncode([updatedAssignment]));
      }
    } catch (e) {
      print('Error updating assignments cache: $e');
    }
  }

  Future<void> _loadRoomData() async {
    if (_isLoadingRoom || widget.assignment['room'] == null) return;
    
    setState(() {
      _isLoadingRoom = true;
    });
    
    try {
      final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching room data for: ${widget.assignment['room']}');
      
      final response = await Supabase.instance.client
          .from(roomsTableName)
          .select('*')
          .eq('fullname', widget.assignment['room'])
          .single();
      
      setState(() {
        _roomData = response;
        _isLoadingRoom = false;
      });
      print('Fetched room data for: ${widget.assignment['room']}');
    } catch (e) {
      print('Error fetching room data: $e');
      setState(() {
        _isLoadingRoom = false;
      });
    }
  }

  Future<void> _loadBuildingData() async {
    if (_isLoadingBuilding || widget.assignment['building'] == null) return;
    
    setState(() {
      _isLoadingBuilding = true;
    });
    
    try {
      final buildingsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_buildings';
      print('Fetching building data for: ${widget.assignment['building']}');
      
      final response = await Supabase.instance.client
          .from(buildingsTableName)
          .select('*')
          .eq('fullname', widget.assignment['building'])
          .single();
      
      setState(() {
        _buildingData = response;
        _isLoadingBuilding = false;
      });
      print('Fetched building data for: ${widget.assignment['building']}');
    } catch (e) {
      print('Error fetching building data: $e');
      setState(() {
        _isLoadingBuilding = false;
      });
    }
  }

  void _viewRoom() {
    if (_roomData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RoomDetailPage(
            room: _roomData!,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  void _viewBuilding() {
    if (_buildingData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuildingDetailPage(
            building: _buildingData!,
            collegeNameForTable: widget.collegeNameForTable,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    final String fullname = widget.assignment['fullname'] ?? 'Unnamed Assignment';
    final String building = widget.assignment['building'] ?? '';
    final String room = widget.assignment['room'] ?? '';
    final String gender = widget.assignment['gender'] ?? '';
    
    final bool hasRoom = room.isNotEmpty && _roomData != null;
    final bool hasBuilding = building.isNotEmpty && _buildingData != null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (building.isNotEmpty) ...[
                const Text(
                  'Building:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(building),
                    if (hasBuilding)
                      TextButton(
                        onPressed: _viewBuilding,
                        child: const Text('View Building'),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              if (room.isNotEmpty) ...[
                const Text(
                  'Room:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(room),
                    if (hasRoom)
                      TextButton(
                        onPressed: _viewRoom,
                        child: const Text('View Room'),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              if (gender.isNotEmpty) ...[
                const Text(
                  'Gender:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(gender),
                const SizedBox(height: 16),
              ],
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
