import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb, defaultTargetPlatform, TargetPlatform;
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart' show rootBundle; // Import rootBundle
import 'dart:convert'; // Import the dart:convert library for json decoding
import 'package:shared_preferences/shared_preferences.dart'; // Import shared_preferences

import 'pages/tertiary_page.dart';
import 'pages/secondary_page.dart';
import 'pages/primary_page.dart';
import 'pages/pre_primary_page.dart';
import 'pages/login_page.dart';
import 'pages/whiteboard_page.dart';
import 'pages/wellness_page.dart';
// import 'pages/corporatecpd_page.dart'; // Import CorporateCPDPage - REMOVED
// import 'pages/boardexamprep_page.dart'; // Import BoardExamPrepPage - REMOVED
import 'pages/utilities_page.dart'; // Import ListPage
import 'pages/calculate_page.dart'; // Import CalculatePage

// Custom scroll behavior that always shows scrollbar on desktop/web
class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Only show persistent scrollbar on desktop platforms
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.linux ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows) {
      return RawScrollbar(
        controller: details.controller,
        thumbVisibility: true,
        thickness: 12.0,
        radius: const Radius.circular(6),
        thumbColor: Theme.of(context).brightness == Brightness.light
            ? const Color(0xFF8c8c8c)
            : const Color.fromRGBO(158, 158, 158, .6),
        trackColor: Theme.of(context).brightness == Brightness.light
            ? Colors.white
            : null,
        child: child,
      );
    }
    return super.buildScrollbar(context, child, details);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  runApp(MyApp(initialIsDarkMode: isDarkMode));
}

class MyApp extends StatefulWidget {
  final bool initialIsDarkMode;
  const MyApp({Key? key, required this.initialIsDarkMode}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late bool isDarkMode; // Make isDarkMode non-nullable and initialize in initState

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    isDarkMode = widget.initialIsDarkMode; // Initialize with the value from main
  }

  Future<void> _saveThemePreference(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('darkMode', isDark);
  }

  void toggleTheme() {
    setState(() {
      isDarkMode = !isDarkMode;
      _saveThemePreference(isDarkMode);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      scrollBehavior: CustomScrollBehavior(),
      title: 'Harmonizr360',
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      theme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFFEEEEEE),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: Colors.white,
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: ColorScheme.light(
          surface: Colors.white,
          onSurface: Colors.black,
          secondary: Colors.black.withOpacity(0.6),
        ),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      darkTheme: ThemeData(
        scaffoldBackgroundColor: const Color(0xFF090909),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF202020),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        navigationBarTheme: NavigationBarThemeData(
          backgroundColor: const Color(0xFF202020),
          indicatorColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          height: 60,
        ),
        colorScheme: const ColorScheme.dark(
          surface: Color(0xFF202020),
          onSurface: Colors.white,
          secondary: Colors.white70,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return isDarkMode ? Colors.white : Colors.black;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey.shade300;
            }
            return isDarkMode ? Colors.white.withOpacity(0.3) : Colors.black.withOpacity(0.3);
          }),
        ),
      ),
      home: HomeScreen(
        isDarkMode: isDarkMode,
        toggleTheme: toggleTheme,
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HomeScreen({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _adTimer;
  List<String> _adFiles = []; // Store asset paths as Strings
  int _currentAdIndex = 0;
  late bool _isDarkMode;
  VoidCallback? _toggleTheme;
  VideoPlayerController? _adVideoController;
  bool _isVideoInitialized = false;

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
    _toggleTheme = widget.toggleTheme;
    _loadAdFiles();
    _adTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_adFiles.isNotEmpty) {
        setState(() {
          _currentAdIndex = (_currentAdIndex + 1) % _adFiles.length;
          _initializeVideoPlayer();
        });
      }
    });
  }

  Future<void> _loadAdFiles() async {
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = await DefaultAssetBundle.of(context)
        .loadString('AssetManifest.json')
        .then((value) => json.decode(value)); // Use the imported json

    final adPaths = manifestMap.keys
        .where((String key) => key.startsWith('assets/ads/'))
        .toList();

    setState(() {
      _adFiles = adPaths;
      _initializeVideoPlayer(); // Initialize video player after loading ads
    });
  }

  Future<void> _initializeVideoPlayer() async {
    if (_adFiles.isNotEmpty && _adFiles[_currentAdIndex].endsWith('.mp4')) {
      if (_adVideoController != null) {
        if (_adVideoController!.dataSource != _adFiles[_currentAdIndex]) {
          await _adVideoController!.dispose();
          _adVideoController = VideoPlayerController.asset(_adFiles[_currentAdIndex]);
          try {
            await _adVideoController!.initialize();
            await _adVideoController!.setVolume(0.0); // Mute the video in the grid
            await _adVideoController!.play();
            await _adVideoController!.setLooping(true);
            setState(() {
              _isVideoInitialized = true;
            });
          } catch (e) {
            print("Error initializing video player: $e");
          }
        } else if (!_isVideoInitialized) {
          try {
            await _adVideoController!.initialize();
            await _adVideoController!.setVolume(0.0); // Mute the video in the grid
            await _adVideoController!.play();
            await _adVideoController!.setLooping(true);
            setState(() {
              _isVideoInitialized = true;
            });
          } catch (e) {
            print("Error initializing video player: $e");
          }
        }
      } else {
        _adVideoController = VideoPlayerController.asset(_adFiles[_currentAdIndex]);
        try {
          await _adVideoController!.initialize();
          await _adVideoController!.setVolume(0.0); // Mute the video in the grid
          await _adVideoController!.play();
          await _adVideoController!.setLooping(true);
          setState(() {
            _isVideoInitialized = true;
          });
        } catch (e) {
          print("Error initializing video player: $e");
        }
      }
    } else {
      _disposeVideoPlayer();
    }
  }

  void _disposeVideoPlayer() {
    if (_adVideoController != null) {
      _adVideoController!.pause();
      _adVideoController!.dispose();
      _adVideoController = null;
      _isVideoInitialized = false;
    }
  }

  @override
  void dispose() {
    _adTimer.cancel();
    _disposeVideoPlayer();
    super.dispose();
  }

  void _navigateToPage(BuildContext context, String title) {
    final Map<String, Widget Function(BuildContext)> pages = {
      'Tertiary': (context) => TertiaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Secondary': (context) => SecondaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Primary': (context) => PrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      'Pre-Primary': (context) => PrePrimaryPage(isDarkMode: _isDarkMode, toggleTheme: _toggleTheme!),
      // 'Board Exam Prep': (context) => BoardExamPrepPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // REMOVED
      // 'Corporate CPD': (context) => CorporateCPDPage(isDarkMode: isDarkMode, toggleTheme: toggleTheme), // REMOVED
    };

    if (pages.containsKey(title)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: pages[title]!,
        ),
      );
    }
  }

  void _showFullPageAd(BuildContext context, String adAssetPath) {
    print('_showFullPageAd called with: $adAssetPath');
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (BuildContext context, _, __) {
          return FullScreenAdModal(
            adAssetPath: adAssetPath,
          );
        },
      ),
    );
  }

  Widget _buildAdBanner(BuildContext context, ThemeData theme) {
    if (_adFiles.isEmpty) {
      return const SizedBox(); // Or a placeholder
    }

    final currentAd = _adFiles[_currentAdIndex];
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          print('Tapped on ad: $currentAd');
          _showFullPageAd(context, currentAd);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Sponsored',
                style: TextStyle(fontSize: 12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              AspectRatio(
                aspectRatio: 16 / 9,
                child: currentAd.endsWith('.mp4')
                    ? _isVideoInitialized && _adVideoController != null
                        ? VideoPlayer(_adVideoController!)
                        : const Center(child: CircularProgressIndicator()) // Show indicator while loading
                    : Image.asset(
                        currentAd,
                        fit: BoxFit.cover,
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(
          'Harmonizr 360',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.bolt,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ListPage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(
              Icons.calculate,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CalculatePage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(
              Icons.airplay,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WhiteboardPage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(
              Icons.self_improvement,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WellnessPage(
                    isDarkMode: _isDarkMode,
                    toggleTheme: _toggleTheme!,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    int crossAxisCount = 2;
                    if (constraints.maxWidth > 1200) {
                      crossAxisCount = 3; // Adjust count to accommodate the full-width ad
                    } else if (constraints.maxWidth > 800) {
                      crossAxisCount = 2;
                    }

                    return GridView.count(
                      crossAxisCount: crossAxisCount,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 1.1,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildGridItem(context, 'Tertiary', Icons.auto_awesome, theme),
                        _buildGridItem(context, 'Secondary', Icons.star, theme),
                        _buildGridItem(context, 'Primary', Icons.workspace_premium, theme),
                        _buildGridItem(context, 'Pre-Primary', Icons.lightbulb, theme),
                        // _buildGridItem(context, 'Board Exam Prep', Icons.archive, theme), // REMOVED
                        // _buildGridItem(context, 'Corporate CPD', Icons.next_week, theme), // REMOVED
                      ],
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildAdBanner(context, theme),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    // Action for Home button
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    _isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: _toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: _isDarkMode,
                          toggleTheme: _toggleTheme!,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToPage(context, title),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FullScreenAdModal extends StatefulWidget {
  final String adAssetPath;

  const FullScreenAdModal({Key? key, required this.adAssetPath}) : super(key: key);

  @override
  State<FullScreenAdModal> createState() => _FullScreenAdModalState();
}

class _FullScreenAdModalState extends State<FullScreenAdModal> {
  VideoPlayerController? _localVideoController;
  Future<void>? _initializeVideoPlayerFuture;

  @override
  void initState() {
    super.initState();
    print('FullScreenAdModal initState with: ${widget.adAssetPath}');
    if (widget.adAssetPath.endsWith('.mp4')) {
      _localVideoController = VideoPlayerController.asset(widget.adAssetPath);
      _initializeVideoPlayerFuture = _localVideoController!.initialize().then((_) {
        _localVideoController!.setVolume(1.0); // Unmute the video in the modal
        _localVideoController!.play();
        _localVideoController!.setLooping(true);
      });
    }
  }

  @override
  void dispose() {
    print('FullScreenAdModal dispose');
    if (_localVideoController != null) {
      _localVideoController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.9),
      body: Center( // Center the content vertically and horizontally
        child: SingleChildScrollView( // Make the content scrollable if it's too large
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              if (widget.adAssetPath.endsWith('.mp4'))
                FutureBuilder(
                  future: _initializeVideoPlayerFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            if (_localVideoController!.value.isPlaying) {
                              _localVideoController!.pause();
                            } else {
                              _localVideoController!.play();
                            }
                          });
                        },
                        child: AspectRatio(
                          aspectRatio: _localVideoController!.value.aspectRatio,
                          child: VideoPlayer(_localVideoController!),
                        ),
                      );
                    } else if (snapshot.hasError) {
                      return Center(child: Icon(Icons.error));
                    } else {
                      return Center(child: CircularProgressIndicator());
                    }
                  },
                )
              else
                Image.asset(
                  widget.adAssetPath,
                  fit: BoxFit.contain, // Ensure the image fits within the modal
                ),
              Positioned(
                top: 20,
                right: 20,
                child: SafeArea(
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}