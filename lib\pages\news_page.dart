// news_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'news_detail_page.dart';

class NewsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedNews;
  final bool isFromDetailPage;

  const NewsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedNews,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('news_list');
  List<Map<String, dynamic>> _news = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("NewsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant NewsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("NewsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("NewsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("NewsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedNews != null && widget.preloadedNews!.isNotEmpty) {
      print("Preloaded news found, using them.");
      setState(() {
        _news = List<Map<String, dynamic>>.from(widget.preloadedNews!);
        _news.forEach((newsItem) {
          newsItem['_isImageLoading'] = false;
        });
        _news.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _hasMore = widget.preloadedNews!.length == _pageSize;
      });
      _loadNewsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded news or empty list, loading from database.");
      _loadNewsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadNewsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadNewsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final newsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_news';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(newsTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedNews =
          await _updateNewsImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _news = updatedNews;
        } else {
          _news.addAll(updatedNews);
        }
        _news.forEach((newsItem) {
          newsItem['_isImageLoading'] = false;
        });
        _news.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the news
      _cacheNews(_news);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching news: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateNewsImageUrls(
      List<Map<String, dynamic>> news) async {
    List<Future<void>> futures = [];
    for (final newsItem in news) {
      if (newsItem['image_url'] == null ||
          newsItem['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(newsItem));
      }
    }
    await Future.wait(futures);
    return news;
  }

  Future<void> _cacheNews(List<Map<String, dynamic>> news) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String newsJson = jsonEncode(news);
      await prefs.setString(
          'news_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          newsJson);
    } catch (e) {
      print('Error caching news: $e');
    }
  }
  
  void _setupRealtime() {
    final newsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_news';
    _realtimeChannel = Supabase.instance.client
        .channel('news')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: newsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newNewsId = payload.newRecord['id'];
          final newNewsResponse = await Supabase.instance.client
              .from(newsTableName)
              .select('*')
              .eq('id', newNewsId)
              .single();
          if (mounted) {
            Map<String, dynamic> newNews = Map.from(newNewsResponse);
            final updatedNews = await _updateNewsImageUrls([newNews]);
            setState(() {
              _news = [..._news, updatedNews.first];
              updatedNews.first['_isImageLoading'] = false;
              _news.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedNewsId = payload.newRecord['id'];
          final updatedNewsResponse = await Supabase.instance.client
              .from(newsTableName)
              .select('*')
              .eq('id', updatedNewsId)
              .single();
          if (mounted) {
            final updatedNews = Map<String, dynamic>.from(updatedNewsResponse);
            setState(() {
              _news = _news.map((newsItem) {
                return newsItem['id'] == updatedNews['id'] ? updatedNews : newsItem;
              }).toList();
              _news.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedNewsId = payload.oldRecord['id'];
          setState(() {
            _news.removeWhere((newsItem) => newsItem['id'] == deletedNewsId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreNews();
    }
  }

  Future<void> _loadMoreNews() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadNewsFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> newsItem) async {
    if (newsItem['_isImageLoading'] == true) {
      print('Image loading already in progress for ${newsItem['fullname']}, skipping.');
      return;
    }
    if (newsItem['image_url'] != null &&
        newsItem['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${newsItem['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      newsItem['_isImageLoading'] = true;
    });

    final fullname = newsItem['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeNewsBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/news';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeNewsBucket');
    print('Image URL before fetch: ${newsItem['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeNewsBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeNewsBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        newsItem['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        newsItem['_isImageLoading'] = false;
        print('Setting image_url for ${newsItem['fullname']} to: ${newsItem['image_url']}');
      });
    } else {
      newsItem['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> newsItem) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NewsDetailPage(
            news: newsItem,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> newsItem) {
    final day = newsItem['day'] as int?;
    final month = newsItem['month'] as int?;
    final year = newsItem['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("NewsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'News & Blog Posts',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _news.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadNewsFromSupabase(initialLoad: true),
              child: _news.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No news available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _news.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _news.length) {
                          final newsItem = _news[index];
                          final hasLink = newsItem['link'] != null && newsItem['link'].toString().isNotEmpty;
                          final dateStr = _formatDate(newsItem);
                          final publisher = newsItem['publisher'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('news_${newsItem['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (newsItem['image_url'] == null ||
                                      newsItem['image_url'] == 'assets/placeholder_image.png') &&
                                  !newsItem['_isImageLoading']) {
                                _fetchImageUrl(newsItem);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: SizedBox(
                                    width: 60,
                                    height: 60,
                                    child: CachedNetworkImage(
                                      imageUrl: newsItem['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        newsItem['fullname'] ?? 'Unnamed News',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                    if (hasLink)
                                      IconButton(
                                        icon: Icon(
                                          Icons.link,
                                          color: theme.colorScheme.primary,
                                          size: 20,
                                        ),
                                        onPressed: () => _launchURL(newsItem['link']),
                                        tooltip: 'Visit website',
                                      ),
                                  ],
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (dateStr.isNotEmpty) ...[
                                      const SizedBox(height: 4),
                                      Text(
                                        dateStr,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: theme.colorScheme.secondary,
                                        ),
                                      ),
                                    ],
                                    if (publisher.isNotEmpty) ...[
                                      const SizedBox(height: 4),
                                      Text(
                                        'Published by: $publisher',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: theme.colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ],
                                  ],
                                ),
                                onTap: () => _navigateToDetail(context, newsItem),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
