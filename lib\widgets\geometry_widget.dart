import 'package:flutter/material.dart';
import 'package:superdeclarative_geometry/superdeclarative_geometry.dart';
import '../utils/geometry_utils.dart';

enum ShapeType {
  circle,
  rectangle,
  triangle,
  line,
  arc,
  polygon,
}

class GeometryWidget extends StatelessWidget {
  final ShapeType shapeType;
  final Map<String, dynamic> properties;
  final double width;
  final double height;

  const GeometryWidget({
    Key? key,
    required this.shapeType,
    required this.properties,
    this.width = 100,
    this.height = 100,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (shapeType) {
      case ShapeType.circle:
        return GeometryUtils.drawCircle(
          radius: properties['radius'] ?? 50.0,
          color: properties['color'] ?? Colors.blue,
          borderColor: properties['borderColor'] ?? Colors.black,
          borderWidth: properties['borderWidth'] ?? 1.0,
          filled: properties['filled'] ?? true,
        );
      
      case ShapeType.rectangle:
        return GeometryUtils.drawRectangle(
          width: properties['width'] ?? width,
          height: properties['height'] ?? height,
          color: properties['color'] ?? Colors.blue,
          borderColor: properties['borderColor'] ?? Colors.black,
          borderWidth: properties['borderWidth'] ?? 1.0,
          filled: properties['filled'] ?? true,
          cornerRadius: properties['cornerRadius'] ?? 0.0,
        );
      
      case ShapeType.triangle:
        return GeometryUtils.drawTriangle(
          width: properties['width'] ?? width,
          height: properties['height'] ?? height,
          color: properties['color'] ?? Colors.blue,
          borderColor: properties['borderColor'] ?? Colors.black,
          borderWidth: properties['borderWidth'] ?? 1.0,
          filled: properties['filled'] ?? true,
        );
      
      case ShapeType.line:
        return GeometryUtils.drawLine(
          start: properties['start'] ?? Offset.zero,
          end: properties['end'] ?? Offset(width, height),
          color: properties['color'] ?? Colors.black,
          strokeWidth: properties['strokeWidth'] ?? 2.0,
          strokeCap: properties['strokeCap'] ?? StrokeCap.round,
        );
      
      case ShapeType.arc:
        return GeometryUtils.drawArc(
          radius: properties['radius'] ?? 50.0,
          startAngle: properties['startAngle'] ?? 0.0,
          sweepAngle: properties['sweepAngle'] ?? 1.5,
          color: properties['color'] ?? Colors.blue,
          borderColor: properties['borderColor'] ?? Colors.black,
          borderWidth: properties['borderWidth'] ?? 1.0,
          filled: properties['filled'] ?? true,
        );
      
      case ShapeType.polygon:
        return GeometryUtils.drawPolygon(
          points: properties['points'] ?? [
            Offset(width / 2, 0),
            Offset(width, height),
            Offset(0, height),
          ],
          color: properties['color'] ?? Colors.blue,
          borderColor: properties['borderColor'] ?? Colors.black,
          borderWidth: properties['borderWidth'] ?? 1.0,
          filled: properties['filled'] ?? true,
        );
      
      default:
        return Container(
          width: width,
          height: height,
          color: Colors.grey.withOpacity(0.3),
          child: const Center(
            child: Text('Unknown shape type'),
          ),
        );
    }
  }
}

// A widget that parses geometry markdown syntax and renders shapes
class GeometryMarkdownWidget extends StatelessWidget {
  final String markdownText;
  
  const GeometryMarkdownWidget({
    Key? key,
    required this.markdownText,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Parse the markdown text to extract geometry commands
    final geometryCommands = _parseGeometryCommands(markdownText);
    
    if (geometryCommands.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: geometryCommands.map((command) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: _buildGeometryWidget(command),
        );
      }).toList(),
    );
  }
  
  List<Map<String, dynamic>> _parseGeometryCommands(String text) {
    final List<Map<String, dynamic>> commands = [];
    
    // Regular expression to match geometry commands
    // Format: ```geometry:shape(param1=value1,param2=value2)```
    final regex = RegExp(r'```geometry:(\w+)\((.*?)\)```', multiLine: true);
    
    final matches = regex.allMatches(text);
    
    for (final match in matches) {
      if (match.groupCount >= 2) {
        final shapeType = match.group(1) ?? '';
        final paramsString = match.group(2) ?? '';
        
        final params = _parseParams(paramsString);
        
        commands.add({
          'shapeType': shapeType,
          'params': params,
        });
      }
    }
    
    return commands;
  }
  
  Map<String, dynamic> _parseParams(String paramsString) {
    final Map<String, dynamic> params = {};
    
    final paramPairs = paramsString.split(',');
    
    for (final pair in paramPairs) {
      final keyValue = pair.split('=');
      if (keyValue.length == 2) {
        final key = keyValue[0].trim();
        final value = keyValue[1].trim();
        
        // Parse the value based on its format
        if (value == 'true') {
          params[key] = true;
        } else if (value == 'false') {
          params[key] = false;
        } else if (double.tryParse(value) != null) {
          params[key] = double.parse(value);
        } else if (value.startsWith('Offset(') && value.endsWith(')')) {
          // Parse Offset
          final offsetValues = value
              .substring(7, value.length - 1)
              .split(',')
              .map((v) => double.tryParse(v.trim()) ?? 0.0)
              .toList();
          
          if (offsetValues.length == 2) {
            params[key] = Offset(offsetValues[0], offsetValues[1]);
          }
        } else if (value.startsWith('[') && value.endsWith(']')) {
          // Parse list of Offsets for polygon
          final pointsString = value.substring(1, value.length - 1);
          final pointPairs = pointsString.split('|');
          
          final points = pointPairs.map((pair) {
            final coords = pair.split(':').map((v) => double.tryParse(v.trim()) ?? 0.0).toList();
            if (coords.length == 2) {
              return Offset(coords[0], coords[1]);
            }
            return Offset.zero;
          }).toList();
          
          params[key] = points;
        } else if (value.startsWith('#')) {
          // Parse color
          try {
            final colorValue = int.parse(value.substring(1), radix: 16);
            params[key] = Color(colorValue | 0xFF000000); // Add alpha if needed
          } catch (e) {
            params[key] = Colors.blue; // Default color
          }
        } else {
          params[key] = value;
        }
      }
    }
    
    return params;
  }
  
  Widget _buildGeometryWidget(Map<String, dynamic> command) {
    final shapeTypeStr = command['shapeType'] as String;
    final params = command['params'] as Map<String, dynamic>;
    
    ShapeType? shapeType;
    
    switch (shapeTypeStr.toLowerCase()) {
      case 'circle':
        shapeType = ShapeType.circle;
        break;
      case 'rectangle':
        shapeType = ShapeType.rectangle;
        break;
      case 'triangle':
        shapeType = ShapeType.triangle;
        break;
      case 'line':
        shapeType = ShapeType.line;
        break;
      case 'arc':
        shapeType = ShapeType.arc;
        break;
      case 'polygon':
        shapeType = ShapeType.polygon;
        break;
    }
    
    if (shapeType != null) {
      return GeometryWidget(
        shapeType: shapeType,
        properties: params,
        width: params['width'] ?? 200.0,
        height: params['height'] ?? 200.0,
      );
    }
    
    return Container(
      width: 200,
      height: 100,
      color: Colors.grey.withOpacity(0.3),
      child: Center(
        child: Text('Unknown shape type: $shapeTypeStr'),
      ),
    );
  }
}
