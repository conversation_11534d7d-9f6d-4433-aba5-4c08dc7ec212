import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart'; // Ensure correct import
import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'downloader.dart'; // Use the conditional import instead of flutter_downloader

class WeblinksDetailPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String url;
  final String pageTitle;
  final WebViewController? controller;

  const WeblinksDetailPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.url,
    required this.pageTitle,
    this.controller,
  }) : super(key: key);

  @override
  _WeblinksDetailPageState createState() => _WeblinksDetailPageState();
}

class _WeblinksDetailPageState extends State<WeblinksDetailPage> {
  late WebViewController _controller;
  bool _isLoading = true;
  final List<String> _downloadableExtensions = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx',
    '.zip', '.rar', '.apk', '.mp3', '.mp4',
    '.png', '.jpg', '.jpeg', '.csv'
  ];

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    if (!kIsWeb) {
      // Register the download callback only on mobile/desktop.
      // FlutterDownloader.registerCallback(downloadCallback); // Assuming you still need this, uncomment if necessary and define downloadCallback if you use flutter_downloader in downloader.dart
    }
  }

void _initializeWebView() {
  if (widget.controller != null) {
    _controller = widget.controller!;
    _isLoading = false;
  } else {
    _controller = WebViewController()
      ..loadRequest(Uri.parse(widget.url))
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(_createNavigationDelegate());
      // Removed .setDownloadListener(...)
  }
}


  NavigationDelegate _createNavigationDelegate() {
    return NavigationDelegate(
      onPageStarted: (url) => setState(() => _isLoading = true),
      onPageFinished: (url) => setState(() => _isLoading = false),
      onNavigationRequest: (request) async {
        final url = request.url.toLowerCase();

        if (_isDownloadable(url)) {
          await _handleDownload(request.url);
          return NavigationDecision.prevent;
        }

        if (url.startsWith('http') || url.startsWith('https')) {
          return NavigationDecision.navigate;
        }

        await _launchExternal(request.url);
        return NavigationDecision.prevent;
      },
    );
  }

  bool _isDownloadable(String url) {
    return _downloadableExtensions.any((ext) => url.toLowerCase().endsWith(ext)); // Ensure case-insensitive check
  }

  Future<void> _handleDownload(String url) async {
    if (kIsWeb) {
      // On web, simply open the URL externally (letting the browser handle the download)
      await _launchExternal(url);
      return;
    }
    try {
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        _showSnackBar('Storage permission required for downloads');
        return;
      }

      final dir = await getDownloadsDirectory();
      if (dir == null) {
        _showSnackBar('Cannot access downloads directory');
        return;
      }

      final taskId = await FlutterDownloader.enqueue( // Make sure FlutterDownloader is correctly imported from downloader.dart
        url: url,
        savedDir: dir.path,
        showNotification: true,
        openFileFromNotification: true,
        saveInPublicStorage: true,
      );

      if (taskId == null) {
        _showSnackBar('Download failed to start');
      }
    } catch (e) {
      _showSnackBar('Download error: ${e.toString()}');
    }
  }

  Future<void> _launchExternal(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      _showSnackBar('Cannot open external link: ${e.toString()}');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // This callback is used by the downloader on mobile/desktop.
  static void downloadCallback(String id, int status, int progress) {
    // Handle download updates here if needed.
    // You need to implement the logic for handling download status and progress if you are using FlutterDownloader.
    // For a basic implementation, you might not need to do anything here.
    if (kDebugMode) {
      print('Download task ($id) is in status: $status and progress: $progress');
    }
  }

  @override
  void dispose() {
    // Optionally, if you need to remove the callback on mobile:
    // if (!kIsWeb) {
    //   FlutterDownloader.removeCallback();
    // }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(
          widget.pageTitle,
          style: TextStyle(
            color: theme.colorScheme.onSurface,
            fontSize: 18,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (_isLoading && !kIsWeb)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading && !kIsWeb)
            Center(
              child: CircularProgressIndicator(
                color: theme.colorScheme.secondary,
              ),
            ),
        ],
      ),
    );
  }
}