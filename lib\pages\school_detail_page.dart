import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'departments_page.dart';

class SchoolDetailPage extends StatefulWidget {
  final Map<String, dynamic> school;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SchoolDetailPage({
    Key? key,
    required this.school,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SchoolDetailPage> createState() => _SchoolDetailPageState();
}

class _SchoolDetailPageState extends State<SchoolDetailPage> {
  late RealtimeChannel _schoolRealtimeChannel;
  List<Map<String, dynamic>> _departments = [];
  bool _isLoadingDepartments = false;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
    _loadDepartmentsForSchool();
  }

  @override
  void dispose() {
    _schoolRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _schoolRealtimeChannel = Supabase.instance.client
        .channel('school_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'schools',
      callback: (payload) async {
        // Manual filtering for the specific school
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.school['id']) {
          print("Realtime update received for school detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshSchool();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshSchool() async {
    try {
      final response = await Supabase.instance.client
          .from('schools')
          .select('*')
          .eq('id', widget.school['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's school with the new data
          widget.school.clear();
          widget.school.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing school: $e");
    }
  }

  Future<void> _loadDepartmentsForSchool() async {
    if (_isLoadingDepartments) return;

    setState(() {
      _isLoadingDepartments = true;
    });

    try {
      final schoolName = widget.school['fullname'];
      if (schoolName == null) {
        setState(() {
          _isLoadingDepartments = false;
        });
        return;
      }

      // Try to find departments that belong to this school
      final response = await Supabase.instance.client
          .from('departments')
          .select('*')
          .or('school.eq.$schoolName,school2.eq.$schoolName,school3.eq.$schoolName')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _departments = List<Map<String, dynamic>>.from(response);
          _isLoadingDepartments = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingDepartments = false;
        });
      }
      print("Error loading departments for school: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      print('Could not launch $emailUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$phoneNumber',
    );
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $whatsappUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.school['fullname'] ?? 'Unknown';
    final String building = widget.school['building'] ?? '';
    final String room = widget.school['room'] ?? '';
    final String about = widget.school['about'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }
    
    // Contact information
    final String phone = widget.school['phone']?.toString() ?? '';
    final String email = widget.school['email']?.toString() ?? '';
    final String fax = widget.school['fax']?.toString() ?? '';
    final String whatsapp = widget.school['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.school['latitude'];
    final dynamic longitude = widget.school['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isFaxAvailable = fax.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // School details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.school,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Location information
                      if (locationText.isNotEmpty)
                        _buildDetailRow(theme, Icons.location_on, 'Location', locationText),
                      
                      // Contact information
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                      if (email.isNotEmpty)
                        _buildDetailRow(theme, Icons.email, 'Email', email, canCopy: true),
                      if (fax.isNotEmpty)
                        _buildDetailRow(theme, Icons.fax, 'Fax', fax, canCopy: true),
                      if (whatsapp.isNotEmpty)
                        _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsapp, canCopy: true),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              // Departments section
              if (_departments.isNotEmpty) ...[
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Departments',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => DepartmentsPage(
                              isDarkMode: currentIsDarkMode,
                              toggleTheme: widget.toggleTheme,
                              collegeNameForTable: '',
                              preloadedDepartments: _departments,
                              isFromDetailPage: true,
                            ),
                          ),
                        );
                      },
                      child: Text('View All'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _departments.length > 3 ? 3 : _departments.length,
                  itemBuilder: (context, index) {
                    final department = _departments[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: currentIsDarkMode 
                            ? Colors.white.withOpacity(0.1) 
                            : Colors.black.withOpacity(0.1),
                        child: Icon(
                          Icons.account_balance,
                          color: currentIsDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      title: Text(
                        department['fullname'] ?? 'Unknown',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      subtitle: department['building'] != null
                          ? Text(
                              department['building'],
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            )
                          : null,
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      onTap: () {
                        // Navigate to department detail page
                      },
                    );
                  },
                ),
              ],
              
              // Loading indicator for departments
              if (_isLoadingDepartments)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isEmailAvailable || isFaxAvailable || isWhatsappAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isEmailAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.email,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchEmail(email),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                    if (isWhatsappAvailable)
                      IconButton(
                        icon: FaIcon(
                          FontAwesomeIcons.whatsapp,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchWhatsapp(whatsapp),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
