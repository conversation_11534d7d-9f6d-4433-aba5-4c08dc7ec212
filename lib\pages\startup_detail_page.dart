// startup_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class StartupDetailPage extends StatefulWidget {
  final Map<String, dynamic> startup;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const StartupDetailPage({
    Key? key,
    required this.startup,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<StartupDetailPage> createState() => _StartupDetailPageState();
}

class _StartupDetailPageState extends State<StartupDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _startupRealtimeChannel; // Realtime channel for startup updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupStartupRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _startupRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.startup['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupStartupRealtimeListener() {
    final startupsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_startups';
    _startupRealtimeChannel = Supabase.instance.client
        .channel('startup_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: startupsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current startup's ID
        if (payload.newRecord['id'] == widget.startup['id']) {
          print("Realtime UPDATE event received for THIS startup (manual filter applied): ${widget.startup['fullname']}");
          _fetchUpdatedStartupData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER startup, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedStartupData() async {
    final startupsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_startups';
    try {
      final updatedStartupResponse = await Supabase.instance.client
          .from(startupsTableName)
          .select('*')
          .eq('id', widget.startup['id'])
          .single();

      if (mounted && updatedStartupResponse != null) {
        Map<String, dynamic> updatedStartup = Map.from(updatedStartupResponse);
        // Update the widget.startup with the new data
        setState(() {
          widget.startup.clear(); // Clear old data
          widget.startup.addAll(updatedStartup); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Startup data updated in detail page for ${widget.startup['fullname']}");
          _updateStartupsCache(updatedStartup); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated startup data: $error");
    }
  }

  Future<void> _updateStartupsCache(Map<String, dynamic> updatedStartup) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'startups_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedStartupsJson = prefs.getString(cacheKey);

    if (cachedStartupsJson != null) {
      List<Map<String, dynamic>> cachedStartups = (jsonDecode(cachedStartupsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the startup in the cached list
      for (int i = 0; i < cachedStartups.length; i++) {
        if (cachedStartups[i]['id'] == updatedStartup['id']) {
          cachedStartups[i] = updatedStartup;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedStartups));
      print("Startups cache updated with realtime change for ${updatedStartup['fullname']}");
    }
  }

  Future<void> _launchWebsite(String websiteLink) async {
    if (websiteLink.isEmpty) return;
    
    Uri url;
    if (websiteLink.startsWith('http://') || websiteLink.startsWith('https://')) {
      url = Uri.parse(websiteLink);
    } else {
      url = Uri.parse('https://$websiteLink');
    }
    
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $websiteLink')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final websiteLink = widget.startup['websitelink'] as String? ?? '';
    final about = widget.startup['about'] as String? ?? '';

    final bool hasWebsite = websiteLink.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.startup['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          if (hasWebsite)
            IconButton(
              icon: Icon(
                Icons.language,
                color: theme.colorScheme.primary,
              ),
              onPressed: () => _launchWebsite(websiteLink),
              tooltip: 'Visit website',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _isLoadingImage
                ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                : CachedNetworkImage(
              imageUrl: _imageUrl,
              placeholder: (context, url) => Image.asset(
                'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                fit: BoxFit.contain,
                height: 250,
              ),
              errorWidget: (context, url, error) => Image.asset(
                'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                fit: BoxFit.contain,
                height: 250,
              ),
              fit: BoxFit.cover,
              height: 250,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.startup['fullname'] ?? '',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      if (hasWebsite) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.language),
                          label: const Text('Visit Website'),
                          onPressed: () => _launchWebsite(websiteLink),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
