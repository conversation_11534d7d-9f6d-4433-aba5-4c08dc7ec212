// calculators.dart
import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:flutter/services.dart';
import 'basic_calculator_page.dart';
import 'scientific_calculator_page.dart';
import 'percentage_calculator_page.dart';
import 'fraction_calculator_page.dart';
import 'ratio_proportion_calculator_page.dart';
import 'geometry_calculator_page.dart';
import 'algebra_solver_page.dart'; // Could be multiple types of solvers
import 'graphing_calculator_page.dart';
import 'statistics_calculator_page.dart';
import 'trigonometry_calculator_page.dart';
import 'compound_interest_calculator_page.dart';
import 'simple_interest_calculator_page.dart';
import 'loan_calculator_page.dart';
import 'discount_calculator_page.dart';
import 'vat_calculator_page.dart';
import 'profit_margin_calculator_page.dart';
import 'bmi_calculator_page.dart';
import 'bmr_calculator_page.dart';
import 'date_time_calculator_page.dart';
import 'age_calculator_page.dart';


class CalculatorsPage extends StatefulWidget { // Renamed from CalculatePage
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const CalculatorsPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<CalculatorsPage> createState() => _CalculatorsPageState(); // Renamed State class
}

class _CalculatorsPageState extends State<CalculatorsPage> { // Renamed State class

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Calculators', // Updated title
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Calculator Items
                _buildGridItem(context, 'Basic Calculator', Icons.calculate_outlined, theme),
                _buildGridItem(context, 'Scientific Calculator', Icons.science_outlined, theme),
                _buildGridItem(context, 'Percentage Calculator', Icons.percent, theme),
                _buildGridItem(context, 'Fraction Calculator', Icons.pie_chart_outline, theme),
                _buildGridItem(context, 'Ratio & Proportion', Icons.trending_up, theme), // Or a more fitting icon
                _buildGridItem(context, 'Geometry Calculator', Icons.hexagon_outlined, theme),
                _buildGridItem(context, 'Algebra Solver', Icons.calculate, theme),
                _buildGridItem(context, 'Graphing Calculator', Icons.show_chart, theme),
                _buildGridItem(context, 'Statistics Calculator', Icons.bar_chart, theme),
                _buildGridItem(context, 'Trigonometry Calculator', Icons.change_history, theme),
                _buildGridItem(context, 'Compound Interest', Icons.attach_money, theme),
                _buildGridItem(context, 'Simple Interest', Icons.money_off, theme),
                _buildGridItem(context, 'Loan Calculator', Icons.payments, theme),
                _buildGridItem(context, 'Discount Calculator', Icons.local_offer_outlined, theme),
                _buildGridItem(context, 'VAT/Tax Calculator', Icons.receipt_long, theme),
                _buildGridItem(context, 'Profit Margin Calculator', Icons.analytics_outlined, theme),
                _buildGridItem(context, 'BMI Calculator', Icons.monitor_weight_outlined, theme),
                _buildGridItem(context, 'BMR & Calorie Calculator', Icons.food_bank_outlined, theme),
                _buildGridItem(context, 'Date & Time Calculator', Icons.calendar_today, theme),
                _buildGridItem(context, 'Age Calculator', Icons.calendar_today, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Theme.of(context).brightness == Brightness.dark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                 ),
               ],
             ),
           ),
         ),
       ),
     );
   }

   Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
     final bool isDarkMode = theme.brightness == Brightness.dark;
     return Card(
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       child: InkWell(
         onTap: () {
           // Navigation Logic - Expand this for all new calculators/converters
           if (title == 'Basic Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BasicCalculatorPage()),
             );
           } else if (title == 'Scientific Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const ScientificCalculatorPage()),
             );
           }
           // New Navigation Items - Add else if blocks for each new title
           else if (title == 'Percentage Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PercentageCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Fraction Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const FractionCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Ratio & Proportion') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const RatioProportionCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Geometry Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const GeometryCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Algebra Solver') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AlgebraSolverPage()), // Placeholder page
             );
           }
           else if (title == 'Graphing Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const GraphingCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Statistics Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const StatisticsCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Trigonometry Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TrigonometryCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Compound Interest') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const CompoundInterestCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Simple Interest') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const SimpleInterestCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Loan Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const LoanCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Discount Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DiscountCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'VAT/Tax Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const VatCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Profit Margin Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const ProfitMarginCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'BMI Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BmiCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'BMR & Calorie Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BmrCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Date & Time Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DateTimeCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Age Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AgeCalculatorPage()), // Placeholder page
             );
           }
         },
         child: Padding(
           padding: const EdgeInsets.all(8.0),
           child: Column(
             mainAxisAlignment: MainAxisAlignment.center,
             mainAxisSize: MainAxisSize.min,
             children: [
               Icon(
                 icon,
                 size: 32,
                 color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
               ),
               const SizedBox(height: 8),
               Flexible(
                 child: Text(
                   title,
                   textAlign: TextAlign.center,
                   overflow: TextOverflow.ellipsis,
                   maxLines: 2,
                   style: TextStyle(
                     fontSize: 14,
                     fontWeight: FontWeight.bold,
                     color: theme.colorScheme.onSurface,
                   ),
                 ),
               ),
             ],
           ),
         ),
       ),
     );
   }
 }