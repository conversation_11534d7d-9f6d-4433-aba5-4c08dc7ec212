// helpdesk_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../main.dart';
import 'login_page.dart';

class HelpdeskDetailPage extends StatefulWidget {
  final Map<String, dynamic> helpdesk;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const HelpdeskDetailPage({
    Key? key,
    required this.helpdesk,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<HelpdeskDetailPage> createState() => _HelpdeskDetailPageState();
}

class _HelpdeskDetailPageState extends State<HelpdeskDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = true;

  @override
  void initState() {
    super.initState();
    _loadHelpdeskImageUrl();
  }

  Future<void> _loadHelpdeskImageUrl() async {
    final fullname = widget.helpdesk['fullname'] as String? ?? '';
    final imageNamePng = '$fullname.png';
    final imageNameJpg = '$fullname.jpg';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = 'assets/placeholder_image.png'; // Default placeholder
    final collegeHelpdeskBucket = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}/helpdesks';


    try {
      final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNamePng);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNamePng);
      }
    } catch (e) {
      try {
        final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameJpg);
        if (file.isNotEmpty) {
          imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameJpg);
        }
      } catch (e) {
        try {
          final file = await Supabase.instance.client.storage.from(collegeHelpdeskBucket).download(imageNameWebp);
          if (file.isNotEmpty) {
            imageUrl = Supabase.instance.client.storage.from(collegeHelpdeskBucket).getPublicUrl(imageNameWebp);
          }
        } catch (e) {
          // Image not found, use placeholder
        }
      }
    }

    if (mounted) {
      setState(() {
        _imageUrl = imageUrl;
        _isLoadingImage = false;
      });
    }
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.helpdesk['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 200, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                          imageUrl: _imageUrl,
                          fit: BoxFit.cover,
                          height: 200,
                          placeholder: (context, url) => const SizedBox(),
                          errorWidget: (context, url, error) => const Icon(Icons.error_outline),
                        ),
                  const SizedBox(height: 16),
                  _buildDetailRow(theme, Icons.info_outline, 'About', widget.helpdesk['about']),
                  _buildDetailRow(theme, Icons.access_time, 'Special Hours', widget.helpdesk['specialhours']),
                  _buildDetailRow(theme, Icons.location_city, 'Building', widget.helpdesk['building']),
                  _buildDetailRow(theme, Icons.meeting_room, 'Room', widget.helpdesk['room']),
                  _buildDetailRow(theme, Icons.schedule, 'Hours', widget.helpdesk['hours']),
                  _buildDetailRow(theme, Icons.payment, 'Payment', widget.helpdesk['payment']),
                  _buildDetailRow(theme, Icons.phone, 'Phone', widget.helpdesk['phone']),
                  _buildDetailRow(theme, Icons.email, 'Email', widget.helpdesk['email']),
                  _buildDetailRow(theme, Icons.fax, 'Fax', widget.helpdesk['fax']),
                  _buildDetailRow(theme, Icons.pin_drop, 'Latitude', widget.helpdesk['latitude']),
                  _buildDetailRow(theme, Icons.pin_drop, 'Longitude', widget.helpdesk['longitude']),
                  _buildDetailRow(theme, Icons.miscellaneous_services, 'Services Count', widget.helpdesk['servicescount']),
                  _buildDetailRow(theme, Icons.person_search, 'Directory Count', widget.helpdesk['directorycount']),
                  _buildDetailRow(theme, Icons.link, 'Links Count', widget.helpdesk['linkscount']),
                  _buildDetailRow(theme, Icons.photo_library, 'Photos Count', widget.helpdesk['photoscount']),
                  _buildDetailRow(theme, Icons.video_library, 'Videos Count', widget.helpdesk['videoscount']),
                  _buildDetailRow(theme, Icons.calendar_today, 'Schedule Count', widget.helpdesk['schedulecount']),

                  // Add more fields as needed
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value.toString(),
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}