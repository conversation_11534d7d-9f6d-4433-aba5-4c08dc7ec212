// books_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'book_detail_page.dart';

class BooksPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedBooks;
  final bool isFromDetailPage;

  const BooksPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedBooks,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> {
  List<Map<String, dynamic>> _books = [];
  List<Map<String, dynamic>> _filteredBooks = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _booksChannel;

  @override
  void initState() {
    super.initState();
    _loadBooks();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _booksChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterBooks();
    });
  }

  void _filterBooks() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredBooks = List.from(_books);
      return;
    }

    _filteredBooks = _books.where((book) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          book['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (book['author']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (book['author2']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (book['author3']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (book['publisher']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (book['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Faculty/Staff') {
          matchesFilter = book['facultyorstaffbook'] == true;
        } else if (_selectedFilter == 'Student') {
          matchesFilter = book['studentbook'] == true;
        } else if (_selectedFilter == 'Year') {
          matchesFilter = book['year'].toString() == _selectedFilter;
        } else if (_selectedFilter == 'Publisher') {
          matchesFilter = book['publisher'] == _selectedFilter;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadBooks() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedBooks.isNotEmpty) {
        setState(() {
          _books = widget.preloadedBooks;
          _filteredBooks = widget.preloadedBooks;
          _isLoading = false;
        });
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _books = cachedData;
          _filteredBooks = cachedData;
          _isLoading = false;
        });
      }

      // Then fetch from Supabase
      final booksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
      final response = await Supabase.instance.client
          .from(booksTableName)
          .select('*')
          .order('year', ascending: false);

      final books = List<Map<String, dynamic>>.from(response);
      
      // Cache the data
      await _saveToCache(books);
      
      setState(() {
        _books = books;
        _filteredBooks = books;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading books: $e';
      });
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'books_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading books from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'books_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving books to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final booksTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_books';
    _booksChannel = Supabase.instance.client
        .channel('books_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: booksTableName,
          callback: (payload) {
            _loadBooks();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> years = {};
    final Set<String> publishers = {};
    
    for (final book in _books) {
      if (book['year'] != null) {
        years.add(book['year'].toString());
      }
      if (book['publisher'] != null && book['publisher'].toString().isNotEmpty) {
        publishers.add(book['publisher'].toString());
      }
    }
    
    final List<String> filters = ['All', 'Faculty/Staff', 'Student'];
    filters.addAll(years);
    filters.addAll(publishers);
    
    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Books',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('books-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _books.isEmpty && !_isLoading) {
            _loadBooks();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search books...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterBooks();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadBooks,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredBooks.isEmpty
                          ? const Center(
                              child: Text('No books found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredBooks.length,
                              itemBuilder: (context, index) {
                                final book = _filteredBooks[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      book['fullname'] ?? 'Untitled Book',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Year: ${book['year'] ?? 'N/A'}'),
                                        Text('Author: ${_formatAuthors(book)}'),
                                        Text('Publisher: ${book['publisher'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => BookDetailPage(
                                            book: book,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatAuthors(Map<String, dynamic> book) {
    final List<String> authors = [];
    
    if (book['author'] != null && book['author'].toString().isNotEmpty) {
      authors.add(book['author'].toString());
    }
    
    if (book['author2'] != null && book['author2'].toString().isNotEmpty) {
      authors.add(book['author2'].toString());
    }
    
    if (book['author3'] != null && book['author3'].toString().isNotEmpty) {
      authors.add(book['author3'].toString());
    }
    
    if (authors.isEmpty) {
      return 'N/A';
    }
    
    return authors.join(', ');
  }
}
