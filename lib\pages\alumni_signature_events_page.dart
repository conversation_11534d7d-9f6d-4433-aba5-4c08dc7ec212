// alumni_signature_events_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'alumni_signature_event_detail_page.dart';

class AlumniSignatureEventsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AlumniSignatureEventsPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AlumniSignatureEventsPageState createState() => _AlumniSignatureEventsPageState();
}

class _AlumniSignatureEventsPageState extends State<AlumniSignatureEventsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('alumni_signature_events_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _signatureEvents = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadSignatureEventsFromCache();
    _loadSignatureEventsFromSupabase();
  }

  void _setupRealtime() {
    final signatureEventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_signatureevents';
    _realtimeChannel = Supabase.instance.client
        .channel('alumni_signature_events_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: signatureEventsTableName,
      callback: (payload) async {
        print("Realtime update received for alumni signature events: ${payload.eventType}");
        _loadSignatureEventsFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadSignatureEventsFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_signature_events_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> signatureEvents = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && signatureEvents.isNotEmpty) {
          setState(() {
            _signatureEvents = signatureEvents;
          });
          print("Loaded ${signatureEvents.length} alumni signature events from cache");
        }
      }
    } catch (e) {
      print("Error loading alumni signature events from cache: $e");
    }
  }

  Future<void> _loadSignatureEventsFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final signatureEventsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_signatureevents';
      var query = Supabase.instance.client
          .from(signatureEventsTableName)
          .select('*')
          .eq('alumnisignatureevent', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('fullname.ilike.%$_searchQuery%,about.ilike.%$_searchQuery%,venue.ilike.%$_searchQuery%');
      }
      
      final response = await query.order('fullname', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _signatureEvents = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Cache the data
      _cacheSignatureEvents(_signatureEvents);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading alumni signature events: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading alumni signature events: $e')),
      );
    }
  }

  Future<void> _cacheSignatureEvents(List<Map<String, dynamic>> signatureEvents) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'alumni_signature_events_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(signatureEvents));
      print("Cached ${signatureEvents.length} alumni signature events");
    } catch (e) {
      print("Error caching alumni signature events: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadSignatureEventsFromSupabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Alumni Signature Events',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search signature events...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Signature events list
          Expanded(
            child: VisibilityDetector(
              key: const Key('alumni_signature_events_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _signatureEvents.isEmpty && !_isLoading) {
                  _loadSignatureEventsFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadSignatureEventsFromSupabase,
                child: _isLoading && _signatureEvents.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _signatureEvents.isEmpty
                        ? Center(
                            child: Text(
                              'No alumni signature events found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _signatureEvents.length,
                            itemBuilder: (context, index) {
                              return _buildSignatureEventCard(
                                _signatureEvents[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSignatureEventCard(
    Map<String, dynamic> signatureEvent,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = signatureEvent['fullname'] ?? 'Unnamed Event';
    final String about = signatureEvent['about'] ?? '';
    final String venue = signatureEvent['venue'] ?? '';
    final String frequency = signatureEvent['frequency'] ?? '';
    final String imageUrl = signatureEvent['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AlumniSignatureEventDetailPage(
                signatureEvent: signatureEvent,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            if (imageUrl.isNotEmpty)
              ClipRRect(
                borderRadius: BorderRadius.vertical(top: Radius.circular(4)),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    height: 150,
                    color: theme.colorScheme.surfaceVariant,
                    child: Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    height: 150,
                    color: theme.colorScheme.surfaceVariant,
                    child: Icon(
                      Icons.event_available,
                      size: 50,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              )
            else
              Container(
                height: 120,
                width: double.infinity,
                color: theme.colorScheme.surfaceVariant,
                child: Center(
                  child: Icon(
                    Icons.event_available,
                    size: 50,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fullname,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (frequency.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.repeat,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          frequency,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                  if (venue.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          venue,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                  if (about.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      about.length > 100 ? '${about.substring(0, 100)}...' : about,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'Learn More',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
