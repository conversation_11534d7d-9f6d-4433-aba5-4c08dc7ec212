// admission_documents_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';

class AdmissionDocumentsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AdmissionDocumentsPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AdmissionDocumentsPageState createState() => _AdmissionDocumentsPageState();
}

class _AdmissionDocumentsPageState extends State<AdmissionDocumentsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('admission_documents_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _documents = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedYearFilter = 'All Years';
  List<String> _yearFilterOptions = ['All Years'];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadDocumentsFromCache();
    _loadDocumentsFromSupabase();
  }

  void _setupRealtime() {
    final documentsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_documents';
    _realtimeChannel = Supabase.instance.client
        .channel('admission_documents_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: documentsTableName,
      callback: (payload) async {
        print("Realtime update received for documents: ${payload.eventType}");
        _loadDocumentsFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadDocumentsFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_documents_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> documents = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && documents.isNotEmpty) {
          setState(() {
            _documents = documents;
            _updateYearFilterOptions(documents);
          });
          print("Loaded ${documents.length} documents from cache");
        }
      }
    } catch (e) {
      print("Error loading documents from cache: $e");
    }
  }

  void _updateYearFilterOptions(List<Map<String, dynamic>> documents) {
    Set<String> years = {'All Years'};
    for (var doc in documents) {
      if (doc['year'] != null) {
        years.add(doc['year'].toString());
      }
    }
    
    setState(() {
      _yearFilterOptions = years.toList()..sort((a, b) {
        if (a == 'All Years') return -1;
        if (b == 'All Years') return 1;
        return int.parse(b).compareTo(int.parse(a)); // Sort years in descending order
      });
    });
  }

  Future<void> _loadDocumentsFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final documentsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_documents';
      var query = Supabase.instance.client
          .from(documentsTableName)
          .select('*')
          .eq('admissionsdocument', true);
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.or('fullname.ilike.%$_searchQuery%,description.ilike.%$_searchQuery%');
      }
      
      // Apply year filter if not "All Years"
      if (_selectedYearFilter != 'All Years') {
        query = query.eq('year', int.parse(_selectedYearFilter));
      }
      
      final response = await query.order('year', ascending: false);

      if (_isDisposed) return;

      setState(() {
        _documents = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Update year filter options
      _updateYearFilterOptions(_documents);

      // Cache the data
      _cacheDocuments(_documents);

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading documents: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading documents: $e')),
      );
    }
  }

  Future<void> _cacheDocuments(List<Map<String, dynamic>> documents) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'admission_documents_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(documents));
      print("Cached ${documents.length} documents");
    } catch (e) {
      print("Error caching documents: $e");
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadDocumentsFromSupabase();
  }

  Future<void> _openDocument(String link) async {
    if (link.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document link is not available')),
      );
      return;
    }

    final Uri uri = Uri.parse(link);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open document: $link')),
      );
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Admission Documents',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search documents...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Year filter
          if (_yearFilterOptions.length > 1)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Text(
                    'Year:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedYearFilter,
                          isExpanded: true,
                          icon: Icon(
                            Icons.arrow_drop_down,
                            color: theme.colorScheme.onSurface,
                          ),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 16,
                          ),
                          dropdownColor: theme.colorScheme.surface,
                          items: _yearFilterOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null && newValue != _selectedYearFilter) {
                              setState(() {
                                _selectedYearFilter = newValue;
                              });
                              _loadDocumentsFromSupabase();
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Documents list
          Expanded(
            child: VisibilityDetector(
              key: const Key('admission_documents_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _documents.isEmpty && !_isLoading) {
                  _loadDocumentsFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadDocumentsFromSupabase,
                child: _isLoading && _documents.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _documents.isEmpty
                        ? Center(
                            child: Text(
                              'No documents found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _documents.length,
                            itemBuilder: (context, index) {
                              return _buildDocumentCard(
                                _documents[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentCard(
    Map<String, dynamic> document,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = document['fullname'] ?? 'Unknown';
    final String link = document['link'] ?? '';
    final int? year = document['year'];
    final String major = document['major'] ?? '';
    final String department = document['department'] ?? '';
    final String school = document['school'] ?? '';
    
    // Determine document type icon
    IconData documentIcon = Icons.description;
    if (link.contains('.pdf')) {
      documentIcon = Icons.picture_as_pdf;
    } else if (link.contains('.doc') || link.contains('.docx')) {
      documentIcon = Icons.description;
    } else if (link.contains('.xls') || link.contains('.xlsx')) {
      documentIcon = Icons.table_chart;
    } else if (link.contains('.ppt') || link.contains('.pptx')) {
      documentIcon = Icons.slideshow;
    } else if (link.contains('.txt')) {
      documentIcon = Icons.text_snippet;
    } else if (link.contains('.zip') || link.contains('.rar')) {
      documentIcon = Icons.folder_zip;
    } else if (link.contains('.jpg') || link.contains('.jpeg') || link.contains('.png')) {
      documentIcon = Icons.image;
    }
    
    // Build document details
    List<Widget> details = [];
    
    if (year != null) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Year: $year',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }
    
    if (major.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Major: $major',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }
    
    if (department.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Department: $department',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }
    
    if (school.isNotEmpty) {
      details.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'School: $school',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openDocument(link),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  documentIcon,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    ...details,
                  ],
                ),
              ),
              Icon(
                Icons.open_in_new,
                size: 20,
                color: theme.colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
