// theses_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'thesis_detail_page.dart';

class ThesesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedTheses;
  final bool isFromDetailPage;

  const ThesesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedTheses,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<ThesesPage> createState() => _ThesesPageState();
}

class _ThesesPageState extends State<ThesesPage> {
  List<Map<String, dynamic>> _theses = [];
  List<Map<String, dynamic>> _filteredTheses = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _thesesChannel;

  @override
  void initState() {
    super.initState();
    _loadTheses();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _thesesChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterTheses();
    });
  }

  void _filterTheses() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All') {
      _filteredTheses = List.from(_theses);
      return;
    }

    _filteredTheses = _theses.where((thesis) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          thesis['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (thesis['author']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (thesis['advisor']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (thesis['advisor2']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (thesis['department']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (thesis['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        if (_selectedFilter == 'Department') {
          matchesFilter = thesis['department'] == _selectedFilter;
        } else if (_selectedFilter == 'Year') {
          matchesFilter = thesis['year'].toString() == _selectedFilter;
        } else if (_selectedFilter == 'Career') {
          matchesFilter = thesis['career'] == _selectedFilter;
        }
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadTheses() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedTheses.isNotEmpty) {
        setState(() {
          _theses = widget.preloadedTheses;
          _filteredTheses = widget.preloadedTheses;
          _isLoading = false;
        });
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _theses = cachedData;
          _filteredTheses = cachedData;
          _isLoading = false;
        });
      }

      // Then fetch from Supabase
      final thesesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
      final response = await Supabase.instance.client
          .from(thesesTableName)
          .select('*')
          .order('year', ascending: false);

      final theses = List<Map<String, dynamic>>.from(response);
      
      // Cache the data
      await _saveToCache(theses);
      
      setState(() {
        _theses = theses;
        _filteredTheses = theses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading theses: $e';
      });
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'theses_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading theses from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'theses_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving theses to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final thesesTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_theses';
    _thesesChannel = Supabase.instance.client
        .channel('theses_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: thesesTableName,
          callback: (payload) {
            _loadTheses();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> departments = {};
    final Set<String> years = {};
    final Set<String> careers = {};
    
    for (final thesis in _theses) {
      if (thesis['department'] != null && thesis['department'].toString().isNotEmpty) {
        departments.add(thesis['department'].toString());
      }
      if (thesis['year'] != null) {
        years.add(thesis['year'].toString());
      }
      if (thesis['career'] != null && thesis['career'].toString().isNotEmpty) {
        careers.add(thesis['career'].toString());
      }
    }
    
    final List<String> filters = ['All'];
    filters.addAll(departments);
    filters.addAll(years);
    filters.addAll(careers);
    
    return filters;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Theses & Dissertations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('theses-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _theses.isEmpty && !_isLoading) {
            _loadTheses();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search theses...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterTheses();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadTheses,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredTheses.isEmpty
                          ? const Center(
                              child: Text('No theses found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredTheses.length,
                              itemBuilder: (context, index) {
                                final thesis = _filteredTheses[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      thesis['fullname'] ?? 'Untitled Thesis',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Year: ${thesis['year'] ?? 'N/A'}'),
                                        Text('Author: ${thesis['author'] ?? 'N/A'}'),
                                        Text('Department: ${thesis['department'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ThesisDetailPage(
                                            thesis: thesis,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
