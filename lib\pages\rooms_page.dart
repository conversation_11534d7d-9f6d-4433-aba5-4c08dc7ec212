import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'room_detail_page.dart';

class RoomsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedRooms;
  final bool isFromDetailPage;
  final String? buildingFilter;

  const RoomsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedRooms,
    this.isFromDetailPage = false,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<RoomsPage> createState() => _RoomsPageState();
}

class _RoomsPageState extends State<RoomsPage> {
  List<Map<String, dynamic>> _rooms = [];
  List<Map<String, dynamic>> _filteredRooms = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _roomsChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadRooms();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _roomsChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterRooms();
    });
  }

  void _filterRooms() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All' && widget.buildingFilter == null) {
      _filteredRooms = List.from(_rooms);
      return;
    }

    _filteredRooms = _rooms.where((room) {
      // Apply building filter if provided
      if (widget.buildingFilter != null && room['building'] != widget.buildingFilter) {
        return false;
      }

      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          room['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (room['building']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (room['roomtype']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (room['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        matchesFilter = room['roomtype'] == _selectedFilter;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadRooms() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedRooms.isNotEmpty) {
        setState(() {
          _rooms = widget.preloadedRooms;
          _filteredRooms = widget.preloadedRooms;
          _isLoading = false;
        });
        print('Using preloaded rooms data for ${widget.collegeNameForTable}');
        // Apply building filter if provided
        if (widget.buildingFilter != null) {
          _filterRooms();
        }
        // Still fetch in background to refresh cache
        _fetchRoomsFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _rooms = cachedData;
          _filteredRooms = cachedData;
          _isLoading = false;
        });
        print('Loaded rooms from cache for ${widget.collegeNameForTable}');
        // Apply building filter if provided
        if (widget.buildingFilter != null) {
          _filterRooms();
        }
      }

      // Then fetch from Supabase
      await _fetchRoomsFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading rooms: $e';
      });
      print('Error in _loadRooms: $e');
    }
  }

  Future<void> _fetchRoomsFromSupabase() async {
    try {
      final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
      print('Fetching from table: $roomsTableName');

      // Create the query
      String query = '*';

      // Fetch the data
      List<Map<String, dynamic>> response;

      if (widget.buildingFilter != null) {
        print('Applying building filter: ${widget.buildingFilter}');
        // Cast to non-nullable String
        String buildingFilter = widget.buildingFilter!;
        response = await Supabase.instance.client
            .from(roomsTableName)
            .select(query)
            .match({'building': buildingFilter})
            .order('fullname', ascending: true);
      } else {
        response = await Supabase.instance.client
            .from(roomsTableName)
            .select(query)
            .order('fullname', ascending: true);
      }

      final rooms = List<Map<String, dynamic>>.from(response);
      print('Fetched ${rooms.length} rooms from Supabase');

      // Cache the data
      await _saveToCache(rooms);

      if (mounted) {
        setState(() {
          _rooms = rooms;
          _filteredRooms = rooms;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching rooms from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_rooms.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading rooms: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rooms_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} rooms in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached rooms found');
      }
    } catch (e) {
      print('Error loading rooms from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rooms_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} rooms to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved rooms to cache');
    } catch (e) {
      print('Error saving rooms to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final roomsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_rooms';
    print('Setting up realtime listener for table: $roomsTableName');
    _roomsChannel = Supabase.instance.client
        .channel('rooms_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomsTableName,
          callback: (payload) {
            print('Realtime update received for rooms');
            _fetchRoomsFromSupabase();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> roomTypes = {'All'};

    for (final room in _rooms) {
      if (room['roomtype'] != null && room['roomtype'].toString().isNotEmpty) {
        roomTypes.add(room['roomtype'].toString());
      }
    }

    return roomTypes.toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.buildingFilter != null ? 'Rooms in ${widget.buildingFilter}' : 'Rooms',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('rooms-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _rooms.isEmpty && !_isLoading) {
            _loadRooms();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search rooms...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterRooms();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadRooms,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredRooms.isEmpty
                          ? const Center(
                              child: Text('No rooms found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredRooms.length,
                              itemBuilder: (context, index) {
                                final room = _filteredRooms[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      room['fullname'] ?? 'Unnamed Room',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Building: ${room['building'] ?? 'N/A'}'),
                                        Text('Type: ${room['roomtype'] ?? 'N/A'}'),
                                        Text('Capacity: ${room['capacity'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => RoomDetailPage(
                                            room: room,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
