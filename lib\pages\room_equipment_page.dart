import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'room_equipment_detail_page.dart';

class RoomEquipmentPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedRoomEquipment;
  final bool isFromDetailPage;
  final String? roomFilter;
  final String? buildingFilter;

  const RoomEquipmentPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedRoomEquipment,
    this.isFromDetailPage = false,
    this.roomFilter,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<RoomEquipmentPage> createState() => _RoomEquipmentPageState();
}

class _RoomEquipmentPageState extends State<RoomEquipmentPage> {
  List<Map<String, dynamic>> _roomEquipment = [];
  List<Map<String, dynamic>> _filteredRoomEquipment = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'All';
  late final RealtimeChannel _roomEquipmentChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadRoomEquipment();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _roomEquipmentChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterRoomEquipment();
    });
  }

  void _filterRoomEquipment() {
    if (_searchQuery.isEmpty && _selectedFilter == 'All' && widget.roomFilter == null && widget.buildingFilter == null) {
      _filteredRoomEquipment = List.from(_roomEquipment);
      return;
    }

    _filteredRoomEquipment = _roomEquipment.where((equipment) {
      // Apply room filter if provided
      if (widget.roomFilter != null && equipment['room'] != widget.roomFilter) {
        return false;
      }

      // Apply building filter if provided
      if (widget.buildingFilter != null && equipment['building'] != widget.buildingFilter) {
        return false;
      }

      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          equipment['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (equipment['building']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['room']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['roomtype']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['assettag']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      // Apply category filter
      bool matchesFilter = true;
      if (_selectedFilter != 'All') {
        matchesFilter = equipment['roomtype'] == _selectedFilter;
      }

      return matchesSearch && matchesFilter;
    }).toList();
  }

  Future<void> _loadRoomEquipment() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedRoomEquipment.isNotEmpty) {
        setState(() {
          _roomEquipment = widget.preloadedRoomEquipment;
          _filteredRoomEquipment = widget.preloadedRoomEquipment;
          _isLoading = false;
        });
        print('Using preloaded room equipment data for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterRoomEquipment();
        }
        // Still fetch in background to refresh cache
        _fetchRoomEquipmentFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _roomEquipment = cachedData;
          _filteredRoomEquipment = cachedData;
          _isLoading = false;
        });
        print('Loaded room equipment from cache for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterRoomEquipment();
        }
      }

      // Then fetch from Supabase
      await _fetchRoomEquipmentFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading room equipment: $e';
      });
      print('Error in _loadRoomEquipment: $e');
    }
  }

  Future<void> _fetchRoomEquipmentFromSupabase() async {
    try {
      final roomEquipmentTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
      print('Fetching from table: $roomEquipmentTableName');

      // Create the query
      String query = '*';

      // Create filter conditions if needed
      Map<String, Object> filterConditions = {};
      if (widget.roomFilter != null) {
        filterConditions['room'] = widget.roomFilter!;
      }
      if (widget.buildingFilter != null) {
        filterConditions['building'] = widget.buildingFilter!;
      }

      // Fetch the data
      List<Map<String, dynamic>> response;

      if (filterConditions.isNotEmpty) {
        print('Applying filters: $filterConditions');
        response = await Supabase.instance.client
            .from(roomEquipmentTableName)
            .select(query)
            .match(filterConditions)
            .order('fullname', ascending: true);
      } else {
        response = await Supabase.instance.client
            .from(roomEquipmentTableName)
            .select(query)
            .order('fullname', ascending: true);
      }

      final roomEquipment = List<Map<String, dynamic>>.from(response);
      print('Fetched ${roomEquipment.length} room equipment items from Supabase');

      // Cache the data
      await _saveToCache(roomEquipment);

      if (mounted) {
        setState(() {
          _roomEquipment = roomEquipment;
          _filteredRoomEquipment = roomEquipment;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching room equipment from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_roomEquipment.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading room equipment: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomequipment_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} room equipment items in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached room equipment found');
      }
    } catch (e) {
      print('Error loading room equipment from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'roomequipment_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} room equipment items to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved room equipment to cache');
    } catch (e) {
      print('Error saving room equipment to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final roomEquipmentTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_roomequipment';
    print('Setting up realtime listener for table: $roomEquipmentTableName');
    _roomEquipmentChannel = Supabase.instance.client
        .channel('roomequipment_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: roomEquipmentTableName,
          callback: (payload) {
            print('Realtime update received for room equipment');
            _fetchRoomEquipmentFromSupabase();
          },
        )
        .subscribe();
  }

  List<String> _getFilterOptions() {
    final Set<String> roomTypes = {'All'};

    for (final equipment in _roomEquipment) {
      if (equipment['roomtype'] != null && equipment['roomtype'].toString().isNotEmpty) {
        roomTypes.add(equipment['roomtype'].toString());
      }
    }

    return roomTypes.toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final filterOptions = _getFilterOptions();

    String title = 'Room Equipment';
    if (widget.roomFilter != null) {
      title = 'Equipment in ${widget.roomFilter}';
    } else if (widget.buildingFilter != null) {
      title = 'Equipment in ${widget.buildingFilter}';
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('roomequipment-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _roomEquipment.isEmpty && !_isLoading) {
            _loadRoomEquipment();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search equipment...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(filter),
                        selected: _selectedFilter == filter,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedFilter = filter;
                              _filterRoomEquipment();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadRoomEquipment,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredRoomEquipment.isEmpty
                          ? const Center(
                              child: Text('No equipment found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredRoomEquipment.length,
                              itemBuilder: (context, index) {
                                final equipment = _filteredRoomEquipment[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      equipment['fullname'] ?? 'Unnamed Equipment',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Building: ${equipment['building'] ?? 'N/A'}'),
                                        Text('Room: ${equipment['room'] ?? 'N/A'}'),
                                        Text('Asset Tag: ${equipment['assettag'] ?? 'N/A'}'),
                                        Text('Status: ${equipment['status'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => RoomEquipmentDetailPage(
                                            equipment: equipment,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
