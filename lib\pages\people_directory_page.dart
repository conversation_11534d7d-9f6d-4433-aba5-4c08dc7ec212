import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'person_detail_page.dart';
import 'login_page.dart';

class PeopleDirectoryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedPeople;
  final bool isFromDetailPage;

  const PeopleDirectoryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedPeople,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _PeopleDirectoryPageState createState() => _PeopleDirectoryPageState();
}

class _PeopleDirectoryPageState extends State<PeopleDirectoryPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('people_directory_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _people = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  String _selectedFilter = 'All';
  List<String> _filterOptions = ['All', 'Faculty', 'Staff', 'Students'];
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    print("PeopleDirectoryPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedPeople != null &&
        widget.preloadedPeople!.isNotEmpty) {
      setState(() {
        _people = List.from(widget.preloadedPeople!);
        _isLoading = false;
      });
    } else {
      _loadPeopleFromDatabase();
    }
  }

  void _setupRealtime() {
    final peopleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_people';
    _realtimeChannel = Supabase.instance.client
        .channel('people_directory_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: peopleTableName,
      callback: (payload) async {
        print("Realtime update received for people directory: ${payload.eventType}");
        _loadPeopleFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadPeopleFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _people = [];
    });

    await _loadMorePeople();
  }

  Future<void> _loadMorePeople() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final peopleTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_people';
      final studentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_currentstudents';
      
      List<Map<String, dynamic>> response = [];
      
      // Filter based on selection
      if (_selectedFilter == 'Faculty' || _selectedFilter == 'Staff') {
        bool isFaculty = _selectedFilter == 'Faculty';
        var query = Supabase.instance.client
            .from(peopleTableName)
            .select('*');
            
        if (_searchQuery.isNotEmpty) {
          query = query.ilike('fullname', '%$_searchQuery%');
        }
        
        if (isFaculty) {
          query = query.eq('facultymember', true);
        } else {
          query = query.eq('staffmember', true);
        }
        
        response = await query
            .order('fullname', ascending: true)
            .range(_page * _pageSize, (_page + 1) * _pageSize - 1);
      } else if (_selectedFilter == 'Students') {
        var query = Supabase.instance.client
            .from(studentsTableName)
            .select('*');
            
        if (_searchQuery.isNotEmpty) {
          query = query.ilike('fullname', '%$_searchQuery%');
        }
        
        response = await query
            .order('fullname', ascending: true)
            .range(_page * _pageSize, (_page + 1) * _pageSize - 1);
      } else {
        // All - combine faculty, staff, and students
        var peopleQuery = Supabase.instance.client
            .from(peopleTableName)
            .select('*');
            
        var studentsQuery = Supabase.instance.client
            .from(studentsTableName)
            .select('*');
            
        if (_searchQuery.isNotEmpty) {
          peopleQuery = peopleQuery.ilike('fullname', '%$_searchQuery%');
          studentsQuery = studentsQuery.ilike('fullname', '%$_searchQuery%');
        }
        
        final peopleResponse = await peopleQuery
            .order('fullname', ascending: true)
            .range(_page * _pageSize, (_page + 1) * _pageSize - 1);
            
        final studentsResponse = await studentsQuery
            .order('fullname', ascending: true)
            .range(_page * _pageSize, (_page + 1) * _pageSize - 1);
            
        // Combine and sort results
        response = [...peopleResponse, ...studentsResponse];
        response.sort((a, b) => (a['fullname'] ?? '').compareTo(b['fullname'] ?? ''));
        
        // Limit to page size
        if (response.length > _pageSize) {
          response = response.sublist(0, _pageSize);
        }
      }

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _people.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading people: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading people: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMorePeople();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
      _page = 0;
      _people = [];
      _hasMore = true;
    });
    _loadMorePeople();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'A-Z Directory',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by name...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Filter dropdown
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  'Filter: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: theme.colorScheme.onSurface,
                        ),
                        style: TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontSize: 16,
                        ),
                        dropdownColor: theme.colorScheme.surface,
                        items: _filterOptions.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != _selectedFilter) {
                            setState(() {
                              _selectedFilter = newValue;
                              _page = 0;
                              _people = [];
                              _hasMore = true;
                            });
                            _loadMorePeople();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // People list
          Expanded(
            child: VisibilityDetector(
              key: const Key('people_directory_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _people.isEmpty && !_isLoading) {
                  _loadPeopleFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadPeopleFromDatabase,
                child: _people.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No people found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _people.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _people.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildPersonCard(
                            _people[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildPersonCard(
    Map<String, dynamic> person,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = person['fullname'] ?? 'Unknown';
    final String title = person['title'] ?? '';
    final String department = person['department'] ?? '';
    
    // Determine if this is a student
    final bool isStudent = person.containsKey('year');
    final String yearInfo = isStudent ? 'Year ${person['year'] ?? ''}' : '';
    
    // Determine if this is faculty or staff
    String roleText = '';
    if (!isStudent) {
      if (person['facultymember'] == true) {
        roleText = 'Faculty';
      } else if (person['staffmember'] == true) {
        roleText = 'Staff';
      }
    } else {
      roleText = 'Student';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PersonDetailPage(
                person: person,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
                isStudent: isStudent,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  isStudent ? Icons.school : Icons.person,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (title.isNotEmpty && !isStudent)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          title,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          department,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (yearInfo.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          yearInfo,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (roleText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          roleText,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
