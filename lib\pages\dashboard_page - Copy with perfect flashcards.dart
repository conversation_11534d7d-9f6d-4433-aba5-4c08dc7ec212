import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flip_card/flip_card.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf_pdf;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui';
import 'package:path/path.dart' as path;
import 'dart:io' as io;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

enum TtsState { playing, stopped, paused, continued }

class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = false;
  bool _isUploading = false;
  bool _isProcessing = false;
  double _uploadProgress = 0.0;
  double _processingProgress = 0.0;
  String? _geminiOutput;
  List<FileWithPageRange> _pickedFiles = [];
  String _processType = 'notes';
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual API key
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;
  List<ChatMessage> _chatMessages = [];
  final TextEditingController _chatController = TextEditingController();
  List<Flashcard> _flashcards = [];
  List<QuizQuestion> _quizQuestions = [];
  List<ExamQuestion> _examQuestions = [];
  int _quizScore = 0;
  int _currentQuestionIndex = 0;
  Map<int, int?> _userAnswers = {};
  String _fileContent = '';
  List<String> _lessonSteps = [];
  int _currentLessonStepIndex = 0;
  bool _lessonPlaying = false;
  double _lessonSpeed = 1.0;
  double _lessonFontSize = 16.0;
  bool _isNarrationMuted = false;

  late FlutterTts flutterTts;
  double volume = 1.0;
  double pitch = 1.0;
  double rate = 0.5;
  TtsState ttsState = TtsState.stopped;
  get isPlaying => ttsState == TtsState.playing;
  get isStopped => ttsState == TtsState.stopped;
  get isPaused => ttsState == TtsState.paused;
  get isContinued => ttsState == TtsState.continued;
  String _ttsLanguage = 'en-US';
  String? _readingGradeLevel;
  String? _difficultyLevel;

  String _displayText = '';
  int _currentCharIndex = 0;
  Timer? _textAnimationTimer;
  bool _isTextAnimationActive = false;

  Color get generalTextColor => widget.isDarkMode ? Colors.white : Colors.black;



  int? _quizTimeLimitMinutes; // Quiz time limit in minutes
  Timer? _quizTimer; // Timer for quiz
  Duration _timeRemaining = Duration.zero; // Time remaining for quiz

  pw.Font? notoSansRegular;
  pw.Font? notoSansBold;
  pw.Font? notoSansItalic;
  pw.Font? notoSansBoldItalic;
  pw.Font? notoSansSymbols;
  pw.Font? stixTwoMathRegular;
  pw.Font? bravura;
  pw.Font? jetBrainsMonoRegular;
  pw.Font? isocpRegular;
  pw.Font? symbola;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _initializeGemini();
    _speech = stt.SpeechToText();
    _initSpeech();
    _setupSupabaseListeners();
    _initTts();
    _loadFonts();
  }

  Future<void> _loadFonts() async {
    // Load Noto Sans variants
    notoSansRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Regular.ttf'));
    notoSansBold = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Bold.ttf'));
    notoSansItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-Italic.ttf'));
    notoSansBoldItalic = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSans-BoldItalic.ttf'));
    notoSansSymbols = pw.Font.ttf(await rootBundle.load('assets/fonts/NotoSansSymbols-Regular.ttf'));
    
    // Load STIX Two Math
    stixTwoMathRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/STIXTwoMath-Regular.ttf'));
    
    // Load special fonts
    bravura = pw.Font.ttf(await rootBundle.load('assets/fonts/Bravura.ttf'));
    jetBrainsMonoRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/JetBrainsMono-Regular.ttf'));
    isocpRegular = pw.Font.ttf(await rootBundle.load('assets/fonts/ISOCP-Regular.ttf')); // Matches pubspec name
    symbola = pw.Font.ttf(await rootBundle.load('assets/fonts/Symbola.ttf'));
  }


  @override
  void didUpdateWidget(covariant DashboardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_lessonSteps.isNotEmpty && !_isTextAnimationActive) {
      _startLessonStepDisplay();
    }
  }

  @override
  void dispose() {
    _textAnimationTimer?.cancel();
    _stopTts();
    _cancelQuizTimer(); // Cancel quiz timer on dispose
    super.dispose();
  }
  
  
  
 List<InlineSpan> parseText(String text, TextStyle defaultStyle) {
  List<InlineSpan> spans = [];
  StringBuffer buffer = StringBuffer();
  int i = 0;

  while (i < text.length) {
    if (text.startsWith('<sup>', i)) {
      if (buffer.isNotEmpty) {
        spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
        buffer.clear();
      }
      int end = text.indexOf('</sup>', i + 5);
      if (end != -1) {
        String content = text.substring(i + 5, end);
        spans.add(WidgetSpan(
          child: Transform.translate(
            offset: const Offset(0, -5), // Move superscript up
            child: Text(
              content,
              style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
            ),
          ),
        ));
        i = end + 6;
      } else {
        buffer.write('<sup>');
        i += 5;
      }
    } else if (text.startsWith('<sub>', i)) {
      if (buffer.isNotEmpty) {
        spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
        buffer.clear();
      }
      int end = text.indexOf('</sub>', i + 5);
      if (end != -1) {
        String content = text.substring(i + 5, end);
        spans.add(WidgetSpan(
          child: Transform.translate(
            offset: const Offset(0, 3), // Move subscript down
            child: Text(
              content,
              style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
            ),
          ),
        ));
        i = end + 6;
      } else {
        buffer.write('<sub>');
        i += 5;
      }
    } else if (text[i] == '^') {
      if (buffer.isNotEmpty) {
        spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
        buffer.clear();
      }
      int end = text.indexOf(' ', i + 1);
      if (end == -1) end = text.length;
      String exponent = text.substring(i + 1, end);
      spans.add(WidgetSpan(
        child: Transform.translate(
          offset: const Offset(0, -5), // Move exponent up
          child: Text(
            exponent,
            style: defaultStyle.copyWith(fontSize: defaultStyle.fontSize! * 0.7),
          ),
        ),
      ));
      i = end;
    } else {
      buffer.write(text[i]);
      i++;
    }
  }

  if (buffer.isNotEmpty) {
    spans.add(TextSpan(text: buffer.toString(), style: defaultStyle));
  }

  return spans;
}

  void _setupSupabaseListeners() {
    Supabase.instance.client
        .from('user_activities')
        .stream(primaryKey: ['id'])
        .listen((List<Map<String, dynamic>> snapshot) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('New activity: ${snapshot.last['type']}')),
        );
      }
    });
  }

  void _initializeGemini() {
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash-exp',
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        maxOutputTokens: 8192,
        temperature: 0.5,
      ),
      safetySettings: [
        SafetySetting(HarmCategory.harassment, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.none),
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
      ],
    );
    _chatSession = _geminiModel.startChat();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (mounted) {
        setState(() {
          _email = user?.email ?? 'No email found';
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _email = 'Error loading email';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'txt', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'mp3'],
        withData: true,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        List<PlatformFile> validFiles = result.files
            .where((file) =>
                (file.bytes != null && file.bytes!.isNotEmpty) ||
                file.path != null)
            .toList();

        if (validFiles.isEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'The selected files could not be processed - no content or path available'),
              ),
            );
          }
          return;
        }

        if (mounted) {
          setState(() {
            _pickedFiles
                .addAll(validFiles.map((file) => FileWithPageRange(file: file)));
            _isUploading = true;
            _uploadProgress = 0.0;
          });
        }

        for (int i = 0; i <= 100; i++) {
          await Future.delayed(const Duration(milliseconds: 20));
          if (mounted) {
            setState(() => _uploadProgress = i / 100);
          }
        }

        if (mounted) {
          setState(() => _isUploading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Files ready: ${_pickedFiles.map((fileRange) => fileRange.file.name).join(", ")}'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  void _deleteFile(FileWithPageRange fileToDeleteRange) {
    setState(() {
      _pickedFiles.remove(fileToDeleteRange);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${fileToDeleteRange.file.name} removed')),
    );
  }

  Future<String?> _extractTextFromPdf(List<int> pdfBytes,
      {int? startPage, int? endPage}) async {
    try {
      final document = sf_pdf.PdfDocument(inputBytes: pdfBytes);
      final pageCount = document.pages.count;
      final extractor = sf_pdf.PdfTextExtractor(document);

      StringBuffer resultText = StringBuffer();

      int start = startPage != null ? startPage - 1 : 0;
      int end = endPage != null ? endPage - 1 : pageCount - 1;

      start = start.clamp(0, pageCount - 1);
      end = end.clamp(0, pageCount - 1);

      for (int i = start; i <= end; i++) {
        try {
          final pageText =
              extractor.extractText(startPageIndex: i, endPageIndex: i);
          if (pageText.isNotEmpty) {
            resultText.writeln(pageText);
            resultText.writeln();
          }
        } catch (e) {
          print('Error extracting text from page $i: $e');
        }
      }

      document.dispose();
      return resultText.toString();
    } catch (e) {
      print('PDF extraction failed: $e');
      throw Exception('PDF extraction failed: $e');
    }
  }

  Future<String?> _extractTextFromAudio(PlatformFile audioFile) async {
    if (!_speechAvailable) {
      _initSpeech();
      if (!_speechAvailable) {
        throw Exception('Speech recognition not available');
      }
    }

    if (audioFile.bytes != null) {
      final tempDir = await getTemporaryDirectory();
      final tempPath = path.join(tempDir.path, audioFile.name);
      final file = File(tempPath);
      await file.writeAsBytes(audioFile.bytes!);

      bool available = await _speech.initialize();
      if (!available) {
        throw Exception('Speech service not initialized');
      }

      String textResult = '';
// In _extractTextFromAudio function:
try {
  await _speech.listen(
    onResult: (result) {
      textResult = result.recognizedWords;
    },
    listenMode: stt.ListenMode.dictation,
    pauseFor: const Duration(seconds: 3),
    localeId: 'en_US',
    partialResults: false,
    cancelOnError: true,
    // Remove the onAudioBuffer parameter
  );
} catch (e) {
  print('Speech recognition error: $e');
  throw Exception('Speech recognition error: $e');
} finally {
  _speech.stop();
  file.delete();
}
      return textResult;
    } else {
      throw Exception('Audio file bytes are null');
    }
  }


  Future<void> _processFile() async {
    if (_pickedFiles.isEmpty) return;

    if (_processType == 'chat') {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _processingProgress = 1.0;
        });
      }
      _startChatSession();
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
      _geminiOutput = null;
      _flashcards = [];
      _quizQuestions = [];
      _examQuestions = [];
      _lessonSteps = [];
      _isTextAnimationActive = false;
      _cancelQuizTimer(); // Cancel any existing quiz timer before processing new file
      _timeRemaining = Duration.zero;
    });

    try {
      String combinedFileContent = '';
      int totalFiles = _pickedFiles.length;

      for (int fileIndex = 0; fileIndex < totalFiles; fileIndex++) {
        final FileWithPageRange fileRange = _pickedFiles[fileIndex];
        final PlatformFile pickedFile = fileRange.file;
        String fileContent = '';
        final fileName = pickedFile.name.toLowerCase();

        if (mounted) {
          setState(() => _processingProgress = fileIndex / totalFiles * 0.5);
        }

        try {
          if (fileName.endsWith('.pdf')) {
            if (pickedFile.bytes != null) {
              fileContent = await _extractTextFromPdf(pickedFile.bytes!,
                      startPage: fileRange.startPage, endPage: fileRange.endPage) ??
                  '';
            } else if (pickedFile.path != null) {
              final bytes = await File(pickedFile.path!).readAsBytes();
              fileContent = await _extractTextFromPdf(bytes,
                      startPage: fileRange.startPage, endPage: fileRange.endPage) ??
                  '';
            }
          } else if (fileName.endsWith('.txt')) {
            if (pickedFile.bytes != null) {
              fileContent = utf8.decode(pickedFile.bytes!, allowMalformed: true);
            }
          } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
            if (pickedFile.bytes != null) {
              try {
                fileContent = utf8.decode(pickedFile.bytes!, allowMalformed: true);
              } catch (e) {
                fileContent = latin1.decode(pickedFile.bytes!);
              }
            }
          } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png')) {
            fileContent = 'Image content of ${pickedFile.name}. OCR functionality not yet fully implemented in this version.';
          } else if (fileName.endsWith('.mp3')) {
            if (pickedFile.bytes != null) {
              try {
                fileContent = await _extractTextFromAudio(pickedFile) ?? ''; // Handle nullable string
              } catch (e) {
                fileContent = 'Error extracting text from audio: $e';
              }
            }
          }

          if (fileContent.isNotEmpty) {
            combinedFileContent += "## ${pickedFile.name}\n\n${fileContent}\n\n";
            print('Successfully extracted content from ${pickedFile.name}');
          } else {
            print('No content extracted from ${pickedFile.name}');
          }
        } catch (e) {
          print('Error processing file ${pickedFile.name}: $e');
        }

        setState(() => _processingProgress = (fileIndex + 1) / totalFiles * 0.5);
      }

      if (combinedFileContent.isEmpty) {
        throw Exception('Content extraction failed from all files');
      }

      if (mounted) {
        setState(() => _fileContent = combinedFileContent);
      }

      if (mounted) {
        setState(() => _processingProgress = 0.6);
      }

      final prompt = _buildPrompt(combinedFileContent);
      final response = await _geminiModel.generateContent([Content.text(prompt)]);

      if (response.text != null) {
        _handleResponse(response.text!);
      } else {
        throw Exception('AI response empty');
      }

      for (int i = 60; i <= 100; i++) {
        await Future.delayed(const Duration(milliseconds: 30));
        if (mounted) {
          setState(() => _processingProgress = i / 100);
        }
      }
    } catch (e) {
      print('Processing error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Processing error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  String _buildPrompt(String content) {
    String gradeLevelText = '';
    if (_readingGradeLevel != null && _readingGradeLevel!.isNotEmpty) {
      gradeLevelText = ' Tailor the content to a ${_readingGradeLevel!.toLowerCase()} reading level.';
    }
    String difficultyLevelText = '';
    if (_difficultyLevel != null && _difficultyLevel!.isNotEmpty) {
      difficultyLevelText = ' Difficulty level: ${_difficultyLevel!.toLowerCase()}.';
    }

    final prompts = {
      'notes': '''Generate ULTRA-DETAILED, COMPREHENSIVE NOTES atleast 50% the length of the original document(s) that teach the subject matter directly:$gradeLevelText
- Clear headings
- no bullet points infront of number like 1. or (1)
- Bullet points
- Key terms in **bold**
- Examples in *italic*
- no lines like **01. Clothing & Apparel Supply:** but 01. Clothing & Apparel Supply:
- no lines like *Example:* but Example:
- Use dash (-) for bullet points in markdown
- do not mention the source name
- in pdf export, the header text of every section of the document should be bold, make topic titles bold, headings bold"
- in pdf export properly italicize and make text bold in the right places, dont use * or surround text with ** or lead any text with #. clean output
- include tables whenever necessary
- For tables, use markdown pipe syntax with headers
- draw images in appropriate places in each output
- Use # prefixes for all section headers
- Never use unicode symbols or special characters
- First create a study guide based on the material(s) then Cover EVERY concept in the given file or files exhaustively without omission in atleast 5 pages with unique content on each page and without repeating anything thats been covered before. leave no stone unturned and the notes should be the condensed but comprehensive and exhaustive version of the material, no omissions
- Include ALL foundational elements needed for complete understanding
- Never use "the document states" or similar meta-references - present as primary knowledge
- For each section, generate an image

Content: $content''',
      'chat': '''Use this document as your ONLY knowledge source:
$content
Respond to ALL subsequent questions using ONLY this information but you can infer from the document to give a response to a question.''',
      'interactive_lesson': '''Generate an interactive lesson from the content provided, suitable for a smartboard presentation for ${gradeLevelText.isNotEmpty ? gradeLevelText : 'a general audience'}.
Structure the lesson in sequential steps, as if teaching the material step-by-step on a whiteboard. Each step should be a concise point or explanation.
Incorporate placeholders where appropriate to indicate where visual or multimedia elements should be added. Use these placeholders:
- [chart] for charts or graphs
- [image] for images
- [video] for videos
- [audio] for audio clips
- [pdf] for PDF documents
- [docx] for Word documents
- [link: URL] for external URLs, replace URL with actual URL
- draw images when asked

Ensure the lesson is easy to parse into individual steps. Focus on clarity and conciseness for each step, suitable for display on a digital whiteboard.
Example output format (step by step, each on a new line):

Dont include the words Step and number when writing on the board e.g Step 1. Malaria example but should simply be written as Malaria example

Introduction to Supply and Demand

What is Demand? - Demand is how much of a product consumers are willing to buy at different prices.
[chart] - Demand Curve showing price vs quantity demanded.

What is Supply? - Supply is how much of a product producers are willing to sell at different prices.
[chart] - Supply Curve showing price vs quantity supplied.

Market Equilibrium - Equilibrium is where supply and demand meet.
[chart] - Equilibrium Point on Supply and Demand Curves.

Factors Affecting Demand - Discuss factors like income, consumer preferences, etc.

Factors Affecting Supply - Discuss factors like cost of production, technology, etc.

Conclusion - Summary of Supply and Demand concepts.

Content: $content''',
      'cheatsheet': '''Generate a concise yet comprehensive cheatsheet for the content.$gradeLevelText
Include a clear topic title, key formulas, definitions, and examples in a well‑structured markdown format.
Use dash (-) for bullet points in markdown

Content: $content''',
      'flashcards': '''Generate at least 50 comprehensive and no repeats when we change reading grade level flashcards (Q: question / A: answer) or generate a comprehensive set of flashcards if the content is less but can be comprehensively covered:$gradeLevelText

Q: [Question]
A: [Answer]

- Each card must contain:
  Front:
  ||Core Concept||: Concise question
  ||Type||: [Definition/Application/Analysis/Connection]
  
  Full Explanation: (1 sentence)

  Requirements:
  1. Cover EVERY concept from source material
  2. 15-50 cards per major topic
  3. Progressive difficulty within topics
  4. Cross-link cards through connection points
  5. before the full explanation in the back dont put any text like :0 A:

Content: $content''',
      'quiz': '''Generate an interactive quiz in TEXT format with at least 50 questions comprehensive and no repeats when we change difficulty level .$difficultyLevelText Use the following EXACT format for each question. Do NOT generate code. Just the plain text quiz. the questions should be different on every difficulty level so there is no repetition of questions

Example Format:
1. What is the capital of France?
A) London
B) Paris
C) Berlin
D) Rome
Answer: B

2. What is the chemical symbol for water?
A) H2O
B) CO2
C) NaCl
D) O2
Answer: A
Now generate a quiz based on the following content, using the EXACT format above:

Content: $content''',
      'transcript': '''Create comprehensive and no repeats when we change reading grade level transcript:
- Speaker labels
- Timestamps
- Paragraph breaks

Content: $content''',
      'summary': '''Generate a summary/brief of the following content.

Instruction: Dynamically adapt the summary based on the content type.

If the content is a research paper or academic paper, provide a detailed summary with these sections:
- 
ground
- Research Question
- Study Method
- Study Limitations
- Global Alignment (if applicable)
- Findings
- include quantitative information where necessary
- Policy Recommendations
- Stakeholder Implications (for donors, government, public, decision-makers, private sector, students, academics)

Otherwise, if the content is not a research paper, provide a concise general summary of the main points.

Content: $content''',
      'exam': '''Generate a comprehensive practice exam with at least 50 NEW questions (different from a quiz, more detailed, paper-based exam style) covering all aspects of the content.$difficultyLevelText
Use this EXACT format for each question and answer:

[Question Number]. [Question Text]
A) [Option 1]
B) [Option 2]
C) [Option 3]
D) [Option 4]
Answer: [Full Correct Answer Text - not just the letter, explain the answer in detail]

Do NOT include any markdown formatting, headers, or explanatory text outside of the question/answer format.

Content: $content''',
      'minutes': '''Generate comprehensive meeting minutes based on the content provided.$gradeLevelText
Include:
- Meeting Title
- Date and Time
- Attendees
- Agenda Items
- Discussions
- Action Items
- Decisions

Content: $content''',
      'lesson_plan': '''Generate a detailed lesson plan based on the content. Include:
- Learning objectives
- Required materials
- Step-by-step teaching instructions
- Classroom activities
- Assessment methods
Content: $content''',
      'worksheet': '''Generate a worksheet with practice questions and exercises based on the content.$gradeLevelText

Content: $content''',
      'homework_guide': '''Generate a homework guide that includes practice problems and solutions based on the content.$gradeLevelText

Content: $content''',
      'project_ideas': '''Generate project ideas based on the content, suitable for students.$gradeLevelText

Content: $content''',
      'exam_free': '''Generate a comprehensive free-response exam with essay questions based on the content.$difficultyLevelText

Content: $content''',
      'exam_case': '''Generate a comprehensive case-based exam with scenario questions based on the content.$difficultyLevelText

Content: $content''',
      'grammar': '''Check the grammar and suggest improvements for the following text. Provide corrections and explanations.$gradeLevelText

Content: $content''',
      'paper_grader': '''Provide detailed writing feedback on this paper. Include:
- Grammar corrections
- Structural improvements
- Argument strength analysis
      - Style suggestions
- Citation feedback
Content: $content''',
      'case_studies': '''Generate relevant case studies or real-life applications based on the content.$gradeLevelText

Content: $content''',
      'experiment': '''Generate an experiment or lab activity based on the content.$gradeLevelText

Content: $content'''
    };


    return prompts[_processType] ?? "Process: $content";
  }

  void _handleResponse(String response) {

    if (mounted) {
      setState(() {
        _geminiOutput = response;
      });
    }
    switch (_processType) {
      case 'flashcards':
        _parseFlashcards(response);
        break;
      case 'quiz':
        _parseQuiz(response);
        _startQuizTimer(); // Start timer when quiz is generated and displayed
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        _parseExam(response);
        break;
      case 'chat':
        _startChatSession();
        break;
      case 'interactive_lesson':
        _parseInteractiveLesson(response);
        break;
      default:
        break;
    }
  }

void _parseInteractiveLesson(String response) {
  final steps = response.split('\n\n').where((s) => s.trim().isNotEmpty).toList();
  setState(() {
    _lessonSteps = steps;
    _currentLessonStepIndex = 0;
    _lessonPlaying = true; // Start playing automatically
    _displayText = '';
    _currentCharIndex = 0;
  });

  if (_lessonSteps.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _startLessonStepDisplay();
      }
    });
  }
}

  void _startChatSession() {
    try {
      _chatSession = _geminiModel.startChat();

      final systemMessage = "Document content:\n$_fileContent\n\n"
          "You are an assistant for this content. "
          "Base all responses strictly on this information. "
          "If asked about something not in the document be sure to infer and give an answer but be sure to, say "
          "'That information is not in the provided document'.";

      _chatSession.sendMessage(Content.text(systemMessage));

      if (mounted) {
        setState(() {
          _chatMessages = [
            ChatMessage(
                "AI: I've analyzed the document and am ready to answer questions",
                false),
          ];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat initialization error: ${e.toString()}')),
        );
      }
    }
  }

void _parseFlashcards(String response) {
  final flashcards = <Flashcard>[];
  final blocks = response.split(RegExp(r'\n\s*(?=Q:|Question|\d+\.)'));

  final qaRegex = RegExp(
    r'^(?:Q:|Question|\d+\.?)\s*(.*?)\s*(?:A:|Answer:?|\n)(.*)',
    caseSensitive: false,
    dotAll: true,
  );

  for (final block in blocks) {
    final match = qaRegex.firstMatch(block);
    if (match != null) {
      String question = match.group(1)?.trim() ?? '';
      String answer = match.group(2)?.trim() ?? '';

      // Clean question: Remove leading colons and spaces
      question = question
          .replaceAll(RegExp(r'\|\|.*?\|\||::.*?::|\*\*|\*|`|#+|- |\[.*?\]|^[:\s]+|[:\s]+$'), '')
          .trim();

      // Clean answer: Remove "::0" and other patterns
      answer = answer
          .replaceAll(RegExp(r'(Definition|Full Explanation|Application|Analysis|Connection|::.*?::|\|\|.*?\|\||[-*#`]|^A:?\s*|::0\s*)'), '')
          .trim();

      // Final trimming: Remove any remaining leading or trailing colons
      question = question.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();
      answer = answer.replaceFirst(RegExp(r'^:+'), '').replaceFirst(RegExp(r':+$'), '').trim();

      if (question.isNotEmpty && answer.isNotEmpty) {
        flashcards.add(Flashcard(
          question: question,
          answer: answer,
        ));
      }
    }
  }

  if (mounted) {
    setState(() => _flashcards = flashcards);
  }
}

  void _parseQuiz(String response) {
    final quizQuestions = <QuizQuestion>[];
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer|Answers|Correct):\s*([A-D]?)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');
    final answerRegex = RegExp(r'Answer:\s*([A-D]?)', caseSensitive: false);

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Quiz Response:\n$response');

    for (final match in matches) {
      print('\n--- Quiz Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Letter Group: ${match.group(3)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];
      final optionLetters = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        optionLetters.add(letter);
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      String correctLetter = match.group(3)?.toUpperCase() ?? '';
      if (correctLetter.isEmpty) {
        final answerMatch = answerRegex.firstMatch(fullQuestionBlock);
        correctLetter = answerMatch?.group(1)?.toUpperCase() ?? '';
      }
      print('Correct Letter: $correctLetter');

      int? correctAnswerIndex;
      if (correctLetter.isNotEmpty) {
        correctAnswerIndex = optionLetters.indexOf(correctLetter);
        if (correctAnswerIndex == -1) {
          print(
              'Warning: Correct letter "$correctLetter" not found in options letters: $optionLetters');
          correctAnswerIndex = null;
        }
      }

      quizQuestions.add(QuizQuestion(
        question: questionText,
        options: options,
        correctAnswerIndex: correctAnswerIndex,
      ));
    }

    if (mounted) {
      setState(() {
        _quizQuestions = quizQuestions;
        _currentQuestionIndex = 0;
        _userAnswers = {};
        _quizScore = 0;
        _timeRemaining = _quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0
            ? Duration(minutes: _quizTimeLimitMinutes!)
            : Duration.zero; // Reset timer on new quiz
      });
    }
  }

  void _parseExam(String response) {
    final examQuestions = <ExamQuestion>[];
    final questionRegex = RegExp(
        r'(?:\n|^)\s*(\d+)\.\s+([^\n]+?)\s*(?:(?:[A-D]\)\s+[^\n]+?\s*)+)?(?:Answer):\s+([^\n]+)',
        dotAll: true);
    final optionRegex = RegExp(r'([A-D])\)\s+([^\n]+)');

    final matches = questionRegex.allMatches(response);

    print('Raw Gemini Exam Response:\n$response');

    for (final match in matches) {
      print('\n--- Exam Question Match ---');
      print('Full Match: ${match.group(0)}');
      print('Question Number: ${match.group(1)}');
      print('Question Text: ${match.group(2)}');
      print('Correct Answer Text Group: ${match.group(3)}');

      String questionText = match.group(2)?.trim() ?? '';
      questionText = questionText.replaceFirst(RegExp(r'^[#\s]*'), '');
      if (questionText.isEmpty) continue;

      final fullQuestionBlock = match.group(0) ?? '';
      final optionMatches = optionRegex.allMatches(fullQuestionBlock);
      final options = <String>[];

      print('Options:');
      for (final optMatch in optionMatches) {
        final letter = optMatch.group(1)?.toUpperCase() ?? '';
        final optionText = optMatch.group(2)?.trim() ?? '';
        options.add(optionText);
        print('  Letter: $letter, Option Text: $optionText');
      }

      final correctAnswerText = match.group(3)?.trim() ?? '';
      print('Correct Answer Text: $correctAnswerText');

      examQuestions.add(ExamQuestion(
        question: questionText,
        options: options,
        correctAnswer: correctAnswerText,
      ));
    }

    if (mounted) {
      setState(() {
        _examQuestions = examQuestions;
      });
    }
  }

  void _startQuizTimer() {
    _cancelQuizTimer();
    if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0) {
      _timeRemaining = Duration(minutes: _quizTimeLimitMinutes!);
      _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining.inSeconds > 0) {
          setState(() => _timeRemaining -= const Duration(seconds: 1));
        } else {
          _cancelQuizTimer();
          _submitQuiz();
        }
      });
    }
  }

  void _cancelQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = null;
  }

  String get _formattedTimeRemaining {
    final minutes = _timeRemaining.inMinutes.remainder(60);
    final seconds = _timeRemaining.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuizView(ThemeData theme, Color generalTextColor) {
    return _quizQuestions.isEmpty
        ? Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                _isProcessing
                    ? 'Generating quiz questions...'
                    : 'No quiz questions generated. Try processing the file first.',
                style: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ),
          )
        : Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Question ${_currentQuestionIndex + 1}/${_quizQuestions.length}',
                        style: GoogleFonts.notoSans(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: generalTextColor),
                      ),
                      if (_quizTimeLimitMinutes != null && _quizTimeLimitMinutes! > 0)
                        Text(
                          'Time: ${_formattedTimeRemaining}',
                          style: GoogleFonts.notoSans(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.secondary),
                        ),
                      Text(
                        'Score: $_quizScore',
                        style: GoogleFonts.notoSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: theme.dividerColor),
                    ),
                    child: Text(_quizQuestions[_currentQuestionIndex].question,
                        style: GoogleFonts.notoSans(
                            fontSize: 16, color: generalTextColor)),
                  ),
                  const SizedBox(height: 20),
                  ..._buildQuizOptions(theme, generalTextColor),
                  const SizedBox(height: 24),
                  _buildQuizNavigationButtons(theme, generalTextColor),
                ],
              ),
            ),
          );
  }

  List<Widget> _buildQuizOptions(ThemeData theme, Color generalTextColor) {
    return List.generate(
        _quizQuestions[_currentQuestionIndex].options.length, (index) {
      if (_quizQuestions[_currentQuestionIndex].options[index].isEmpty)
        return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Material(
          color: _userAnswers[_currentQuestionIndex] == index
              ? theme.colorScheme.primary.withOpacity(0.2)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          child: InkWell(
            onTap: () {
              setState(() => _userAnswers[_currentQuestionIndex] = index);
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Text(
                    '${String.fromCharCode('A'.codeUnitAt(0) + index)}. ',
                    style: GoogleFonts.notoSans(
                        fontWeight: FontWeight.bold, color: generalTextColor),
                  ),
                  Expanded(
                    child: Text(
                      _quizQuestions[_currentQuestionIndex].options[index],
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ),
                  Radio<int>(
                    value: index,
                    groupValue: _userAnswers[_currentQuestionIndex],
                    onChanged: (value) {
                      setState(() => _userAnswers[_currentQuestionIndex] = value);
                    },
                    activeColor: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildQuizNavigationButtons(ThemeData theme, Color generalTextColor) {
    return ElevatedButton(
      onPressed: _userAnswers[_currentQuestionIndex] != null
          ? () {
              if (_userAnswers[_currentQuestionIndex] ==
                  _quizQuestions[_currentQuestionIndex].correctAnswerIndex) {
                setState(() => _quizScore++);
              }
              if (_currentQuestionIndex < _quizQuestions.length - 1) {
                setState(() => _currentQuestionIndex++);
              } else {
                _submitQuiz();
              }
            }
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.isDarkMode ? Colors.white : Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 16),
        disabledBackgroundColor: Colors.grey,
      ),
      child: Text(
        _currentQuestionIndex < _quizQuestions.length - 1
            ? 'Next Question'
            : 'Finish Quiz',
        style: GoogleFonts.notoSans(
            color: _userAnswers[_currentQuestionIndex] != null
                ? widget.isDarkMode
                    ? Colors.black
                    : Colors.white
                : Colors.grey[400],
            fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildExamView(ThemeData theme, Color generalTextColor) {
    return _examQuestions.isEmpty
        ? Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                _isProcessing
                    ? 'Generating exam questions...'
                    : 'No exam questions generated. Process file as Exam.',
                style: GoogleFonts.notoSans(color: generalTextColor),
              ),
            ),
          )
        : Card(
            color: theme.colorScheme.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Exam',
                          style: GoogleFonts.notoSans(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor),
                          textAlign: TextAlign.center),
                      IconButton(
                        icon: Icon(Icons.download, color: generalTextColor),
                        onPressed: () => _exportToPdf(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ..._examQuestions.asMap().entries.map((entry) {
                    int index = entry.key;
                    ExamQuestion question = entry.value;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${index + 1}. ${question.question}',
                            style: GoogleFonts.notoSans(
                                fontWeight: FontWeight.bold,
                                color: generalTextColor)),
                        const SizedBox(height: 8),
                        ...List.generate(question.options.length, (optionIndex) {
                          return Text(
                            '    ${String.fromCharCode('A'.codeUnitAt(0) + optionIndex)}) ${question.options[optionIndex]}',
                            style:
                                GoogleFonts.notoSans(color: generalTextColor),
                          );
                        }),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.cardColor,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: theme.dividerColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Answer:',
                                  style: GoogleFonts.notoSans(
                                      fontWeight: FontWeight.bold,
                                      color: generalTextColor)),
                              Text(question.correctAnswer,
                                  style: GoogleFonts.notoSans(
                                      color: generalTextColor)),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    );
                  }).toList(),
                ],
              ),
            ),
          );
  }

  void _submitQuiz() {
    final incorrectQuestions = _quizQuestions.where((q) {
      final userAnswer = _userAnswers[_quizQuestions.indexOf(q)];
      return userAnswer != q.correctAnswerIndex;
    }).toList();

    showDialog(
      context: context,
      builder: (context) => ExamResultsDialog(
        questions: _quizQuestions,
        incorrectQuestions: incorrectQuestions,
        textColor: generalTextColor,
        onGenerateNotes: () => _generateWeaknessNotes(incorrectQuestions),
        userAnswers: _userAnswers,
      ),
    );
  }


  Future<void> _generateWeaknessNotes(List<QuizQuestion> incorrectQuestions) async {
    final incorrectContent = incorrectQuestions.map((q) => q.question).join('\n');
    final prompt = '''Generate comprehensive notes focused on the topics covered in these quiz questions that the user answered incorrectly. Use the following source material to create the notes. Focus specifically on areas where the user demonstrated weakness in the quiz.

Incorrect Questions:
$incorrectContent

Source Material:
$_fileContent''';

    final response = await _geminiModel.generateContent([Content.text(prompt)]);
    if (response.text != null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Improvement Notes', style: GoogleFonts.notoSans(color: generalTextColor)),
          content: SingleChildScrollView(
            child: MarkdownBody(
              data: response.text!,
              styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
  p: GoogleFonts.notoSans(color: generalTextColor, fontSize: 16),
  h1: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 24),
  h2: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 20),
  h3: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 18),
  h4: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 16),
  h5: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 14),
  h6: GoogleFonts.notoSans(color: generalTextColor, fontWeight: FontWeight.bold, fontSize: 12),
  code: GoogleFonts.sourceCodePro(backgroundColor: Theme.of(context).cardColor, color: generalTextColor),
  blockquote: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
  strong: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
  em: GoogleFonts.notoSans(fontStyle: FontStyle.italic, color: generalTextColor),
  listBullet: GoogleFonts.notoSans(color: generalTextColor),
  // Remove listNumber parameter
  checkbox: GoogleFonts.notoSans(color: generalTextColor),
),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Close', style: GoogleFonts.notoSans(color: generalTextColor)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to generate improvement notes.')),
      );
    }
  }


  List<pw.Widget> _buildPdfContent(String markdownText, String contentType) {
    if (contentType == 'exam' || contentType == 'exam_free' || contentType == 'exam_case') {
      return _buildPdfExamContent(markdownText);
    } else {
      return _buildPdfNotesContent(markdownText);
    }
  }

  List<pw.Widget> _buildPdfNotesContent(String markdownText) {
    String cleanedText = markdownText
        .replaceAll(RegExp(r'^```.*?^```', multiLine: true), '')
        .replaceAll(RegExp(r'^Here is .+?:', multiLine: true), '')
        .replaceAll(RegExp(r'^[#]+ ', multiLine: true), '')
        .trim();

    List<String> lines = cleanedText.split('\n');
    List<pw.Widget> widgets = [];
    widgets.add(pw.Header(
        level: 1,
        child: pw.Text('Refactr AI',
            style: pw.TextStyle(
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
                font: notoSansBold,
                fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]))));

    for (var line in lines) {
      if (line.startsWith('# ')) {
        widgets.add(pw.Text(
          line.substring(2).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              font: notoSansBold,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else if (line.startsWith('## ')) {
        widgets.add(pw.Text(
          line.substring(3).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              font: notoSansBold,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else if (line.startsWith('### ')) {
        widgets.add(pw.Text(
          line.substring(4).replaceFirst(RegExp(r'^[#]+ '), ''),
          style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              font: notoSansBold,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else if (line.trim().startsWith('- ')) {
        widgets.add(pw.Text(
          line.replaceFirst('- ', '  • '),
          style: pw.TextStyle(
    fontSize: 14,
    font: notoSansRegular,
    fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else {
        widgets.add(_buildRichTextFromMarkdown(line));
      }
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  List<pw.Widget> _buildPdfExamContent(String markdownText) {
    List<String> lines = markdownText.split('\n');
    List<pw.Widget> widgets = [];

    for (var line in lines) {
      if (line.trim().isEmpty) continue;
      widgets.add(pw.Text(line,
          style: pw.TextStyle(
              fontSize: 12,
              font: notoSansRegular,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!])));
      widgets.add(pw.SizedBox(height: 8));
    }
    return widgets;
  }

  pw.Widget _buildRichTextFromMarkdown(String text) {
    List<pw.TextSpan> spans = [];
    final pattern =
        RegExp(r'(\*\*.*?\*\*|\*.*?\*|~~.*?~~|`.*?`|\[.*?\]\(.*?\)|[^*~`]+)');

    for (final match in pattern.allMatches(text)) {
      final segment = match.group(0)!;
      if (segment.startsWith('**') && segment.endsWith('**')) {
        spans.add(pw.TextSpan(
          text: segment.substring(2, segment.length - 2),
          style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              font: notoSansBold,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else if (segment.startsWith('*') && segment.endsWith('*')) {
        spans.add(pw.TextSpan(
          text: segment.substring(1, segment.length - 1),
          style: pw.TextStyle(
              fontStyle: pw.FontStyle.italic,
              font: notoSansItalic,
              fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
        ));
      } else {
        spans.add(pw.TextSpan(
            text: segment,
            style: pw.TextStyle(
                font: notoSansRegular,
                fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!])));
      }
    }

    return pw.RichText(
      text: pw.TextSpan(
        children: spans,
        style: pw.TextStyle(
            fontSize: 14,
            font: notoSansRegular,
            fontFallback: [stixTwoMathRegular!,notoSansSymbols!,bravura!,jetBrainsMonoRegular!,isocpRegular!,symbola!]),
      ),
    );
  }

  Future<void> _exportToPdf() async {
    if (_geminiOutput == null) return;

    try {
      final pdf = pw.Document();
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (context) => _buildPdfContent(_geminiOutput!, _processType),
        ),
      );

      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        final blob = html.Blob([pdfBytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
        if (selectedDirectory != null) {
          final fileName = '${_processType}_${DateTime.now().millisecondsSinceEpoch}.pdf';
          final filePath = path.join(selectedDirectory, fileName);

          final file = io.File(filePath);
          await file.writeAsBytes(pdfBytes);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to $filePath')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No directory selected')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('PDF export failed: ${e.toString()}')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<void> _sendChatMessage() async {
    final message = _chatController.text.trim();
    if (message.isEmpty) return;

    if (mounted) {
      setState(() => _chatMessages.add(ChatMessage(message, true)));
    }
    _chatController.clear();

    try {
      final response = await _chatSession.sendMessage(Content.text(message));
      if (mounted) {
        setState(() => _chatMessages
            .add(ChatMessage(response.text ?? 'No response', false)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Chat error: ${e.toString()}')),
        );
      }
    }
  }

  void _initTts() {
    flutterTts = FlutterTts();
    _setAwaitSpeakCompletion();

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  void _setAwaitSpeakCompletion() async {
    flutterTts.awaitSpeakCompletion(true);
  }

Future<void> _speak(String text) async {
  if (text.isNotEmpty && !_isNarrationMuted) {
    await flutterTts.setVolume(volume);
    await flutterTts.setSpeechRate(rate);
    await flutterTts.setPitch(pitch);
    await flutterTts.setLanguage(_ttsLanguage);

    String cleanedText = text
        .replaceAll(RegExp(r'<sup>(.*?)</sup>'), r'$1') // Keep superscript content
        .replaceAll(RegExp(r'<sub>(.*?)</sub>'), r'$1') // Keep subscript content
        .replaceAll(RegExp(r'(\w+)\^(\w+)'), r'$1 to the power of $2') // Handle exponents
        .replaceAll(RegExp(r'[*~`#-]'), '')
        .replaceAll(RegExp(r'[\n\r]'), ' ')
        .trim();

    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    } else {
      var result = await flutterTts.speak(cleanedText);
      if (result == 1) setState(() => ttsState = TtsState.playing);
    }
  }
}

  Future<void> _stop() async {
    var result = await flutterTts.stop();
    if (result == 1) setState(() => ttsState = TtsState.stopped);
  }

  Future<void> _pause() async {
    if (ttsState == TtsState.playing) {
      var result = await flutterTts.pause();
      if (result == 1) setState(() => ttsState = TtsState.paused);
    }
  }

  Widget _buildTtsControls() {
    return Container(
      color: widget.isDarkMode ? Colors.grey[900] : Colors.grey[100],
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: () => isPlaying
                ? _pause()
                : _speak(_geminiOutput ?? 'No text to speak'),
          ),
          IconButton(
            icon: Icon(Icons.stop, color: generalTextColor),
            onPressed: _stop,
          ),
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Colors.grey,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor:
                    Theme.of(context).colorScheme.primary.withOpacity(0.3),
                trackHeight: 4.0,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 10.0),
                overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
              ),
              child: Slider(
                value: rate,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: "Speed",
                activeColor: widget.isDarkMode ? Colors.white : Colors.black,
                inactiveColor: Colors.grey,
                onChanged: (double value) {
                  setState(() => rate = value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'No-Legwork',
          style: GoogleFonts.notoSans(color: generalTextColor),
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        actions: [
          IconButton(
            icon: Icon(
                widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: generalTextColor),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFileSection(theme, generalTextColor,
                      buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildProcessingControls(theme, generalTextColor,
                      buttonTextColor, buttonBackground),
                  const SizedBox(height: 20),
                  _buildContentDisplay(theme, generalTextColor),
                ],
              ),
            ),
      // Bottom navigation bar removed entirely
    );
  }

  Widget _buildFileSection(ThemeData theme, Color generalTextColor,
      Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.cloud_upload, color: buttonTextColor),
              label: Text('Select Learning Material(s)',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              ),
              onPressed: _isUploading ? null : _pickFile,
            ),
            if (_pickedFiles.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Selected Files:',
                        style: GoogleFonts.notoSans(
                            color: generalTextColor.withOpacity(0.8))),
                    ..._pickedFiles.map((fileRange) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('• ${fileRange.file.name}',
                                      style: GoogleFonts.notoSans(
                                          color:
                                              generalTextColor.withOpacity(0.6),
                                          fontSize: 14)),
                                  if (fileRange.file.name
                                      .toLowerCase()
                                      .endsWith('.pdf'))
                                    Row(
                                      children: [
                                        SizedBox(
                                          width: 80,
                                          child: TextField(
                                            keyboardType: TextInputType.number,
                                            decoration: InputDecoration(
                                              hintText: 'Start Page',
                                              hintStyle: GoogleFonts.notoSans(
                                                  fontSize: 12),
                                              border:
                                                  const OutlineInputBorder(),
                                              contentPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                            style: GoogleFonts.notoSans(
                                                fontSize: 12,
                                                color: generalTextColor),
                                            onChanged: (value) => fileRange
                                                    .startPage =
                                                value.isEmpty
                                                    ? null
                                                    : int.tryParse(value),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        SizedBox(
                                          width: 80,
                                          child: TextField(
                                            keyboardType: TextInputType.number,
                                            decoration: InputDecoration(
                                              hintText: 'End Page',
                                              hintStyle: GoogleFonts.notoSans(
                                                  fontSize: 12),
                                              border:
                                                  const OutlineInputBorder(),
                                              contentPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                            style: GoogleFonts.notoSans(
                                                fontSize: 12,
                                                color: generalTextColor),
                                            onChanged: (value) => fileRange
                                                    .endPage =
                                                value.isEmpty
                                                    ? null
                                                    : int.tryParse(value),
                                          ),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _deleteFile(fileRange),
                            ),
                          ],
                        )).toList(),
                  ],
                ),
              ),
            if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _uploadProgress),
              ),
            if (_isUploading)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Uploading: ${(_uploadProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingControls(ThemeData theme, Color generalTextColor,
      Color buttonTextColor, Color buttonBg) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _processType,
              decoration: InputDecoration(
                labelText: 'Processing Type',
                labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                border: const OutlineInputBorder(),
              ),
              style: GoogleFonts.notoSans(color: generalTextColor),
              items: [
                const DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
                const DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
                const DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
                const DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'transcript', child: Text('Create Transcript')),
                if (_hasMP3)
                  const DropdownMenuItem(value: 'minutes', child: Text('Create Meeting Minutes')),
                const DropdownMenuItem(value: 'chat', child: Text('AI Tutor / Chat with Content')),
                const DropdownMenuItem(value: 'summary', child: Text('Create Summary')),
                const DropdownMenuItem(value: 'lesson_plan', child: Text('Create Lesson Plan')),
                const DropdownMenuItem(value: 'worksheet', child: Text('Worksheet')),
                const DropdownMenuItem(value: 'homework_guide', child: Text('Homework Guide')),
                const DropdownMenuItem(value: 'project_ideas', child: Text('Project Ideas')),
                const DropdownMenuItem(value: 'exam', child: Text('Create Exam')),
                const DropdownMenuItem(value: 'exam_free', child: Text('Exam (Free Response)')),
                const DropdownMenuItem(value: 'exam_case', child: Text('Exam (Case Question)')),
                const DropdownMenuItem(value: 'grammar', child: Text('Grammar Checker')),
                const DropdownMenuItem(value: 'paper_grader', child: Text('Paper Grader')),
                const DropdownMenuItem(value: 'case_studies', child: Text('Case Studies')),
                const DropdownMenuItem(value: 'experiment', child: Text('Experiment/Lab')),
                const DropdownMenuItem(value: 'interactive_lesson', child: Text('Interactive Lesson')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _processType = value;
                    _geminiOutput = null;
                    _flashcards = [];
                    _quizQuestions = [];
                    _examQuestions = [];
                    _chatMessages = [];
                    _readingGradeLevel = null;
                    _difficultyLevel = null;
                    _quizTimeLimitMinutes = null;
                    _cancelQuizTimer();
                    _timeRemaining = Duration.zero;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            if (_processType != 'quiz' && _processType != 'exam' && _processType != 'exam_free' && _processType != 'exam_case')
              DropdownButtonFormField<String>(
                value: _readingGradeLevel,
                decoration: InputDecoration(
                  labelText: 'Reading Grade Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Grade 5', child: Text('Grade 5')),
                  DropdownMenuItem(value: 'Grade 8', child: Text('Grade 8')),
                  DropdownMenuItem(value: 'Grade 10', child: Text('Grade 10')),
                  DropdownMenuItem(value: 'Grade 12', child: Text('Grade 12')),
                  DropdownMenuItem(value: 'College', child: Text('College')),
                  DropdownMenuItem(
                      value: 'Professional', child: Text('Professional')),
                ],
                onChanged: (value) {
                  setState(() {
                    _readingGradeLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz' || _processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case')
              DropdownButtonFormField<String>(
                value: _difficultyLevel,
                decoration: InputDecoration(
                  labelText: 'Difficulty Level',
                  labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                  border: const OutlineInputBorder(),
                ),
                style: GoogleFonts.notoSans(color: generalTextColor),
                items: const [
                  DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                  DropdownMenuItem(value: 'Normal', child: Text('Normal')),
                  DropdownMenuItem(
                      value: 'Intermediate', child: Text('Intermediate')),
                  DropdownMenuItem(value: 'Hard', child: Text('Hard')),
                  DropdownMenuItem(
                      value: 'Very Hard', child: Text('Very Hard')),
                ],
                onChanged: (value) {
                  setState(() {
                    _difficultyLevel = value;
                  });
                },
              ),
            if (_processType == 'quiz')
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Quiz Time Limit (minutes)',
                          labelStyle: GoogleFonts.notoSans(color: generalTextColor),
                        ),
                        onChanged: (value) => setState(() => _quizTimeLimitMinutes = int.tryParse(value)),
                      ),
                    ),
                  ],
                ),
              ),

            SizedBox(height: 16), // Added consistent spacing here
            ElevatedButton.icon(
              icon: _isProcessing
                  ? const SizedBox()
                  : Icon(Icons.memory, color: buttonTextColor),
              label: Text(_isProcessing ? 'Processing...' : 'Process with AI',
                  style: GoogleFonts.notoSans(color: buttonTextColor)),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBg,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: _isProcessing ? null : _processFile,
            ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(value: _processingProgress),
              ),
            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.notoSans(color: generalTextColor),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentDisplay(ThemeData theme, Color generalTextColor) {
    String titleText = 'Generated Content';
    bool showDownloadButton = true;
    switch (_processType) {
      case 'notes':
        titleText = 'Notes';
        break;
      case 'cheatsheet':
        titleText = 'Cheatsheet';
        break;
      case 'flashcards':
        titleText = 'Flashcards';
        showDownloadButton = false;
        break;
      case 'quiz':
        titleText = 'Quiz';
        showDownloadButton = false;
        break;
      case 'exam':
      case 'exam_free':
      case 'exam_case':
        titleText = 'Exam';
        break;
      case 'transcript':
        titleText = 'Transcript';
        showDownloadButton = false;
        break;
      case 'chat':
        titleText = 'Chat';
        showDownloadButton = false;
        break;
      case 'summary':
        titleText = 'Summary';
        break;
      case 'minutes':
        titleText = 'Meeting Minutes';
        break;
      case 'interactive_lesson':
        titleText = 'Lesson';
        showDownloadButton = false;
        break;
      case 'lesson_plan':
        titleText = 'Lesson Plan';
        break;
      case 'worksheet':
        titleText = 'Worksheet';
        break;
      case 'homework_guide':
        titleText = 'Homework Guide';
        break;
      case 'project_ideas':
        titleText = 'Project Ideas';
        break;
      case 'grammar':
        titleText = 'Grammar Check';
        break;
      case 'paper_grader':
        titleText = 'Paper Feedback';
        break;
      case 'case_studies':
        titleText = 'Case Studies';
        break;
      case 'experiment':
        titleText = 'Experiment/Lab';
        break;
    }

    if (_processType == 'flashcards') {
      return _buildFlashcardsView(theme, generalTextColor);
    } else if (_processType == 'quiz') {
      return _buildQuizView(theme, generalTextColor);
    } else if (_processType == 'exam' || _processType == 'exam_free' || _processType == 'exam_case') {
      return _buildExamView(theme, generalTextColor);
    } else if (_processType == 'chat') {
      return _buildChatView(theme, generalTextColor);
    } else if (_processType == 'interactive_lesson') {
      return _buildInteractiveLessonView(theme, generalTextColor);
    }

    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(titleText,
                    style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor)),
                if (showDownloadButton)
                  IconButton(
                    icon: Icon(Icons.download, color: generalTextColor),
                    onPressed: () => _exportToPdf(),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(16),
              child: MarkdownBody(
                data: _geminiOutput ?? 'No content generated',
                styleSheet:
MarkdownStyleSheet.fromTheme(theme).copyWith(
  p: GoogleFonts.notoSans(fontSize: 16, color: generalTextColor),
  h1: GoogleFonts.notoSans(fontSize: 24, fontWeight: FontWeight.bold, color: generalTextColor),
  h2: GoogleFonts.notoSans(fontSize: 20, fontWeight: FontWeight.bold, color: generalTextColor),
  h3: GoogleFonts.notoSans(fontSize: 18, fontWeight: FontWeight.bold, color: generalTextColor),
  h4: GoogleFonts.notoSans(fontSize: 16, fontWeight: FontWeight.bold, color: generalTextColor),
  h5: GoogleFonts.notoSans(fontSize: 14, fontWeight: FontWeight.bold, color: generalTextColor),
  h6: GoogleFonts.notoSans(fontSize: 12, fontWeight: FontWeight.bold, color: generalTextColor),
  code: GoogleFonts.sourceCodePro(backgroundColor: theme.cardColor, color: generalTextColor),
  blockquote: GoogleFonts.notoSans(color: generalTextColor.withOpacity(0.6)),
  strong: GoogleFonts.notoSans(fontWeight: FontWeight.bold, color: generalTextColor),
  em: GoogleFonts.notoSans(fontStyle: FontStyle.italic, color: generalTextColor),
  listBullet: GoogleFonts.notoSans(color: generalTextColor),
  // Remove listNumber parameter
  checkbox: GoogleFonts.notoSans(color: generalTextColor),
),
              ),
            ),
          ],
        ),
      ),
    );
  }

Widget _buildInteractiveLessonView(ThemeData theme, Color generalTextColor) {
  TextStyle defaultStyle = GoogleFonts.notoSans(
    fontSize: _lessonFontSize,
    color: Colors.white,
    height: 1.5,
  );

  return Card(
    color: theme.colorScheme.surface,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Page ${_currentLessonStepIndex + 1}/${_lessonSteps.length}',
                style: GoogleFonts.notoSans(
                  color: generalTextColor,
                  fontSize: 16,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.text_decrease, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize > 12) _lessonFontSize -= 2;
                      });
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.text_increase, color: generalTextColor),
                    onPressed: () {
                      setState(() {
                        if (_lessonFontSize < 24) _lessonFontSize += 2;
                      });
                    },
                  ),
                  Opacity(
                    opacity: 0.5,
                    child: IconButton(
                      icon: Icon(Icons.translate, color: generalTextColor),
                      onPressed: null,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      _isNarrationMuted ? Icons.volume_off : Icons.volume_up,
                      color: generalTextColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _isNarrationMuted = !_isNarrationMuted;
                        if (_isNarrationMuted) _pauseTts();
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
          SizedBox(
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black, // Always pure black
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: RichText(
                  text: TextSpan(
                    children: parseText(_displayText, defaultStyle),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 80,
            child: _buildLessonControls(theme),
          ),
        ],
      ),
    ),
  );
}

Widget _buildLessonControls(ThemeData theme) {
  return Column(
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(Icons.skip_previous, color: generalTextColor),
            onPressed: _goToPreviousStep,
          ),
          IconButton(
            icon: Icon(
                _lessonPlaying ? Icons.pause : Icons.play_arrow,
                color: generalTextColor),
            onPressed: _toggleLessonPlay,
          ),
          IconButton(
            icon: Icon(Icons.skip_next, color: generalTextColor),
            onPressed: _goToNextStep,
          ),
        ],
      ),
    ],
  );
}

  void _goToPreviousStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex > 0) {
        _currentLessonStepIndex--;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _goToNextStep() {
    if (_lessonSteps.isEmpty) return;

    _stopTts();
    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
      }
      _lessonPlaying = true; // Autoplay on step change
      _displayText = '';
      _currentCharIndex = 0;
      _startLessonStepDisplay();
    });
  }

  void _stopLessonStepDisplay() {
    _stopTts();
    _stopTextAnimation();
  }

  void _stopTextAnimation() {
    _textAnimationTimer?.cancel();
    _isTextAnimationActive = false;
    _displayText = '';
    _currentCharIndex = 0;
  }

  void _goToNextStepForTts() {
    if (_lessonSteps.isEmpty) return;

    _textAnimationTimer?.cancel();

    setState(() {
      if (_currentLessonStepIndex < _lessonSteps.length - 1) {
        _currentLessonStepIndex++;
        _displayText = '';
        _currentCharIndex = 0;
        _startLessonStepDisplay();
      } else {
        _lessonPlaying = false;
      }
    });
  }

  void _restartLesson() {
    setState(() {
      _currentLessonStepIndex = 0;
      _stopLessonStepDisplay();
      _startLessonStepDisplay();
    });
  }

  void _toggleLessonPlay() {
    setState(() {
      _lessonPlaying = !_lessonPlaying;
      if (_lessonPlaying) {
        _startLessonStepDisplay();
      } else {
        _pauseLessonStepDisplay();
      }
    });
  }

  void _pauseLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _pause();
    setState(() {
      _isTextAnimationActive = false;
    });
  }

  void _startLessonStepDisplay() {
    _textAnimationTimer?.cancel();
    _currentCharIndex = 0;
    _displayText = '';
    _isTextAnimationActive = true;

    final fullText = _lessonSteps[_currentLessonStepIndex]
        .replaceAll(RegExp(r'\[.*?\]'), '')
        .trim();

    if (_lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);

      _textAnimationTimer = Timer.periodic(const Duration(milliseconds: 30), (timer) {
        if (_currentCharIndex < fullText.length) {
          setState(() {
            _displayText += fullText[_currentCharIndex];
            _currentCharIndex++;
          });
        } else {
          timer.cancel();
          _isTextAnimationActive = false;
        }
      });
    }
  }

  void _startTtsForCurrentStep() {
    if (_lessonSteps.isNotEmpty &&
        _currentLessonStepIndex < _lessonSteps.length &&
        _lessonPlaying) {
      _speak(_lessonSteps[_currentLessonStepIndex]);
    }
  }

  void _stopTts() {
    flutterTts.stop();
    setState(() => _lessonPlaying = false);
  }

  void _pauseTts() {
    flutterTts.pause();
    setState(() => _lessonPlaying = false);
  }

  Widget _buildFlashcardsView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 400,
              child: PageView.builder(
                itemCount: _flashcards.length,
                itemBuilder: (context, index) => FlashcardWidget(
                  flashcard: _flashcards[index],
                  textColor: generalTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatView(ThemeData theme, Color generalTextColor) {
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _chatMessages.length,
                itemBuilder: (context, index) => ChatBubble(
                  message: _chatMessages[index],
                  isDarkMode: widget.isDarkMode,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _chatController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                    ),
                    style: GoogleFonts.notoSans(color: generalTextColor),
                    onSubmitted: (_) => _sendChatMessage(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send, color: generalTextColor),
                  onPressed: _sendChatMessage,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool get _hasMP3 => _pickedFiles.any((f) => f.file.name.toLowerCase().endsWith('.mp3'));
}

class FileWithPageRange {
  PlatformFile file;
  int? startPage;
  int? endPage;

  FileWithPageRange({required this.file, this.startPage, this.endPage});
}

class Flashcard {
  final String question;
  final String answer;

  Flashcard({required this.question, required this.answer});
}

class QuizQuestion {
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;

  QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });
}

class ExamQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;

  ExamQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
  });
}

class QuizResult {
  final String question;
  final String userAnswer;
  final String correctAnswer;
  final bool isCorrect;

  QuizResult({
    required this.question,
    required this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;

  ChatMessage(this.text, this.isUser);
}

class FlashcardWidget extends StatelessWidget {
  final Flashcard flashcard;
  final Color textColor;

  const FlashcardWidget({
    Key? key,
    required this.flashcard,
    required this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return FlipCard(
      front: Card(
        color: theme.cardColor,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(
              flashcard.question,
              style: GoogleFonts.notoSans(
                fontSize: 20,
                color: theme.textTheme.bodyLarge?.color,
                fontWeight: FontWeight.w500
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      back: Card(
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(
              flashcard.answer.replaceAll(RegExp(r'^::\w*\s*'), ''), // Clean answer text
              style: GoogleFonts.notoSans(
                fontSize: 16,
                color: theme.textTheme.bodyLarge?.color,
                fontStyle: FontStyle.italic
              ),
              textAlign: TextAlign.center,
			  softWrap: true,  // Important for text wrapping
            ),
          ),
        ),
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bubbleTextColor = isDarkMode ? Colors.white : Colors.black;
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: message.isUser
              ? (isDarkMode ? Colors.blue[800] : Colors.blue[100])
              : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(message.text,
            style: GoogleFonts.notoSans(color: bubbleTextColor)),
      ),
    );
  }
}

class ExamResultsDialog extends StatelessWidget {
  final VoidCallback onGenerateNotes;
  final List<QuizQuestion> incorrectQuestions;
  final List<QuizQuestion> questions;
  final Color textColor;
  final Map<int, int?> userAnswers;

  const ExamResultsDialog({
    Key? key,
    required this.questions,
    required this.textColor,
    required this.incorrectQuestions,
    required this.onGenerateNotes,
    required this.userAnswers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int correctCount = 0;
    for (int i = 0; i < questions.length; i++) {
      if (userAnswers[i] == questions[i].correctAnswerIndex) {
        correctCount++;
      }
    }
    double totalQuestions = questions.length.toDouble();

    return AlertDialog(
      title: Text('Practice Exam Answer Key', style: GoogleFonts.notoSans(color: textColor)),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: ${(correctCount / totalQuestions * 100).toStringAsFixed(1)}%',
                style: TextStyle(color: _getScoreColor(correctCount / totalQuestions), fontSize: 24)),
            const SizedBox(height: 20),
            Text('Answer Key:',
                style: GoogleFonts.notoSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...questions.asMap().entries.map((entry) {
              int index = entry.key;
              QuizQuestion question = entry.value;
              String correctAnswer = question.correctAnswerIndex != null && question.correctAnswerIndex! < question.options.length
                  ? question.options[question.correctAnswerIndex!]
                  : 'Unknown';
              String answerLetter = question.correctAnswerIndex != null ? String.fromCharCode('A'.codeUnitAt(0) + question.correctAnswerIndex!) : '?';
              bool isCorrect = userAnswers[index] == question.correctAnswerIndex;
              String userAnswerText = userAnswers[index] != null && userAnswers[index]! < question.options.length
                  ? question.options[userAnswers[index]!] : 'Not Answered';
              String userAnswerLetter = userAnswers[index] != null ? String.fromCharCode('A'.codeUnitAt(0) + userAnswers[index]!) : ' ';


              return ListTile(
                title: Text('${index + 1}. ${question.question}',
                    style: GoogleFonts.notoSans(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Correct answer: $answerLetter) $correctAnswer',
                        style: GoogleFonts.notoSans(color: Colors.green)),
                    Text('Your answer: $userAnswerLetter) $userAnswerText',
                        style: GoogleFonts.notoSans(color: isCorrect ? Colors.green : Colors.red)),
                    const Divider(),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: onGenerateNotes,
          child: Text('Generate Improvement Notes', style: GoogleFonts.notoSans(color: Colors.blue)),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: GoogleFonts.notoSans(color: textColor)),
        ),
      ],
    );
  }
}

Color _getScoreColor(double percentage) {
  if (percentage >= 0.9) return Colors.green;
  if (percentage >= 0.7) return Colors.orange;
  return Colors.red;
}