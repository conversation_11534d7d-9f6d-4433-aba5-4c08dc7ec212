import 'dart:io';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:file_picker/file_picker.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:syncfusion_flutter_pdf/pdf.dart';

class DashboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DashboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  String? _email;
  bool _isLoading = true;
  String? _geminiOutput;
  File? _pickedFile;
  String _processType = 'notes';
  final String apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s';
  late GenerativeModel _geminiModel;
  late ChatSession _chatSession;
  late stt.SpeechToText _speech;
  bool _speechAvailable = false;

  @override
  void initState() {
    super.initState();
    _loadEmail();
    _geminiModel = GenerativeModel(model: 'gemini-pro', apiKey: apiKey);
    _chatSession = _geminiModel.startChat();
    _speech = stt.SpeechToText();
    _initSpeech();
  }

  Future<void> _initSpeech() async {
    _speechAvailable = await _speech.initialize();
    if (!_speechAvailable) {
      print("Speech recognition not available or not authorized.");
    }
    setState(() {});
  }

  Future<void> _loadEmail() async {
    setState(() => _isLoading = true);
    try {
      final user = Supabase.instance.client.auth.currentUser;
      setState(() {
        _email = user?.email;
        _isLoading = false;
      });
    } catch (error) {
      print("Error loading email: $error");
      setState(() {
        _email = 'Error loading email';
        _isLoading = false;
      });
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'mp3', 'mp4', 'mpeg', 'mov', 'avi', 'doc', 'docx', 'txt'],
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() => _pickedFile = File(result.files.first.path!));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('File picked: ${_pickedFile!.path.split('/').last}')),
        );
      }
    } catch (e) {
      print("Error picking file: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking file: $e')),
      );
    }
  }

  Future<String?> _extractTextFromPdf(File pdfFile) async {
    try {
      final document = PdfDocument(inputBytes: pdfFile.readAsBytesSync());
      final extractor = PdfTextExtractor(document);
      final text = extractor.extractText();
      document.dispose();
      return text;
    } catch (e) {
      print("Error extracting text from PDF: $e");
      return null;
    }
  }

  Future<String?> _transcribeAudio(File audioFile) async {
    if (!_speechAvailable) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Speech recognition not available.')),
      );
      return null;
    }

    try {
      if (await _speech.initialize()) {
        String transcribedText = '';
        _speech.listen(onResult: (result) => transcribedText = result.recognizedWords);
        await Future.delayed(const Duration(seconds: 10));
        _speech.stop();
        return transcribedText;
      }
      return null;
    } catch (e) {
      print("Error transcribing audio: $e");
      return null;
    }
  }

  Future<void> _processFile() async {
    if (_pickedFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please pick a file first.')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _geminiOutput = 'Processing...';
    });

    try {
      String fileContent = '';
      final path = _pickedFile!.path.toLowerCase();

      if (path.endsWith('.pdf')) {
        fileContent = await _extractTextFromPdf(_pickedFile!) ?? '';
      } else if (path.endsWith('.mp3') || 
                 path.endsWith('.mp4') || 
                 path.endsWith('.mpeg') || 
                 path.endsWith('.mov') || 
                 path.endsWith('.avi')) {
        fileContent = await _transcribeAudio(_pickedFile!) ?? '';
      } else if (path.endsWith('.txt') || 
                 path.endsWith('.doc') || 
                 path.endsWith('.docx')) {
        fileContent = await _pickedFile!.readAsString();
      } else {
        setState(() {
          _isLoading = false;
          _geminiOutput = 'Unsupported file type';
        });
        return;
      }

      if (fileContent.isEmpty) {
        setState(() {
          _isLoading = false;
          _geminiOutput = 'Could not extract content from file';
        });
        return;
      }

      final prompt = _buildPrompt(fileContent);
      final response = await _geminiModel.generateContent([
        Content('user', [TextPart(prompt)])
      ]);

      setState(() {
        _geminiOutput = response.text;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _geminiOutput = 'Error processing file: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  String _buildPrompt(String content) {
    switch (_processType) {
      case 'notes':
        return "Create concise notes from:\n$content";
      case 'cheatsheet':
        return "Create a cheatsheet summarizing key points from:\n$content";
      case 'flashcards':
        return "Create Q&A flashcards from:\n$content";
      case 'quiz':
        return "Create multiple-choice quiz from:\n$content";
      case 'transcript':
        return "Provide clean transcript of:\n$content";
      case 'chat':
        return "Discuss this content:\n$content";
      default:
        return "Process this content:\n$content";
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Dashboard'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildUserCard(theme),
            const SizedBox(height: 20),
            _buildFilePickerSection(theme),
            const SizedBox(height: 20),
            _buildProcessControls(theme),
            const SizedBox(height: 20),
            if (_geminiOutput != null) _buildOutputSection(theme),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNav(theme),
    );
  }

  Widget _buildUserCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text('Welcome, ${_email ?? 'User'}!', 
                 style: theme.textTheme.titleLarge),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => Supabase.instance.client.auth.signOut(),
              child: const Text('Sign Out'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilePickerSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.upload_file),
          label: const Text('Select File'),
          onPressed: _pickFile,
        ),
        if (_pickedFile != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text('Selected: ${_pickedFile!.path.split('/').last}',
                        style: theme.textTheme.bodySmall),
          ),
      ],
    );
  }

  Widget _buildProcessControls(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        DropdownButtonFormField<String>(
          value: _processType,
          items: const [
            DropdownMenuItem(value: 'notes', child: Text('Generate Notes')),
            DropdownMenuItem(value: 'cheatsheet', child: Text('Create Cheatsheet')),
            DropdownMenuItem(value: 'flashcards', child: Text('Make Flashcards')),
            DropdownMenuItem(value: 'quiz', child: Text('Generate Quiz')),
            DropdownMenuItem(value: 'transcript', child: Text('Get Transcript')),
            DropdownMenuItem(value: 'chat', child: Text('Chat About Content')),
          ],
          onChanged: (value) => setState(() => _processType = value!),
          decoration: const InputDecoration(
            labelText: 'Processing Type',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          icon: _isLoading 
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(color: Colors.white),
                )
              : const Icon(Icons.memory),
          label: const Text('Process with AI'),
          onPressed: _isLoading ? null : _processFile,
        ),
      ],
    );
  }

  Widget _buildOutputSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('AI Output:', style: theme.textTheme.titleMedium),
            const SizedBox(height: 10),
            Text(_geminiOutput ?? 'No output generated',
                style: theme.textTheme.bodyLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNav(ThemeData theme) {
    return BottomAppBar(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {},
          ),
        ],
      ),
    );
  }
}