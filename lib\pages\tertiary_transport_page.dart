import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'campus_shuttle_page.dart';
import 'shuttle_stops_page.dart';
import 'parking_spaces_page.dart';
import 'local_transport_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryTransportPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryTransportPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryTransportPage> createState() => _TertiaryTransportPageState();
}

class _TertiaryTransportPageState extends State<TertiaryTransportPage> {
  List<Map<String, dynamic>>? _cachedCampusShuttle;
  List<Map<String, dynamic>>? _cachedParkingSpaces;
  List<Map<String, dynamic>>? _cachedLocalTransport;
  String? _lastCollegeName;
  bool _isLoadingCampusShuttle = false;
  bool _isLoadingParkingSpaces = false;
  bool _isLoadingLocalTransport = false;
  late RealtimeChannel _campusShuttleRealtimeChannel;
  late RealtimeChannel _parkingSpacesRealtimeChannel;
  late RealtimeChannel _localTransportRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryTransportPage initState called for ${widget.institutionName}");
    _loadCachedCampusShuttle();
    _loadCachedParkingSpaces();
    _loadCachedLocalTransport();
    _loadCampusShuttleFromDatabaseAndCache();
    _loadParkingSpacesFromDatabaseAndCache();
    _loadLocalTransportFromDatabaseAndCache();
    _setupCampusShuttleRealtimeListener();
    _setupParkingSpacesRealtimeListener();
    _setupLocalTransportRealtimeListener();
  }

  @override
  void dispose() {
    _campusShuttleRealtimeChannel.unsubscribe();
    _parkingSpacesRealtimeChannel.unsubscribe();
    _localTransportRealtimeChannel.unsubscribe();
    super.dispose();
  }

  // Campus Shuttle methods
  Future<List<Map<String, dynamic>>?> _getCachedCampusShuttle(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? campusShuttleJson = prefs.getString(
        'campusshuttle_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (campusShuttleJson != null) {
      List<dynamic> decodedList = jsonDecode(campusShuttleJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheCampusShuttle(String collegeName, List<Map<String, dynamic>> campusShuttle) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String campusShuttleJson = jsonEncode(campusShuttle);
    await prefs.setString(
        'campusshuttle_${collegeName.toLowerCase().replaceAll(' ', '')}', campusShuttleJson);
    print('Campus Shuttle cached for $collegeName.');
  }

  Future<void> _loadCachedCampusShuttle() async {
    final cachedData = await _getCachedCampusShuttle(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedCampusShuttle = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded campus shuttle from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadCampusShuttleFromDatabaseAndCache() async {
    if (_isLoadingCampusShuttle) {
      return;
    }

    setState(() {
      _isLoadingCampusShuttle = true;
    });

    print("Fetching campus shuttle for ${widget.institutionName} from database");
    final campusShuttleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_campusshuttle';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(campusShuttleTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedCampusShuttle = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingCampusShuttle = false;
          _cacheCampusShuttle(widget.institutionName, response);
          print("Campus Shuttle fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingCampusShuttle = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingCampusShuttle = false;
          _cachedCampusShuttle = [];
          print("Error fetching campus shuttle for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingCampusShuttle = false;
      }
    }
  }

  void _setupCampusShuttleRealtimeListener() {
    final campusShuttleTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_campusshuttle';
    _campusShuttleRealtimeChannel = Supabase.instance.client
        .channel('campusshuttle_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: campusShuttleTableName,
      callback: (payload) async {
        print(
            "Realtime update received for campus shuttle of ${widget.institutionName}: ${payload.eventType}");
        _loadCampusShuttleFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Parking Spaces methods
  Future<List<Map<String, dynamic>>?> _getCachedParkingSpaces(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? parkingSpacesJson = prefs.getString(
        'parkingspaces_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (parkingSpacesJson != null) {
      List<dynamic> decodedList = jsonDecode(parkingSpacesJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheParkingSpaces(String collegeName, List<Map<String, dynamic>> parkingSpaces) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String parkingSpacesJson = jsonEncode(parkingSpaces);
    await prefs.setString(
        'parkingspaces_${collegeName.toLowerCase().replaceAll(' ', '')}', parkingSpacesJson);
    print('Parking Spaces cached for $collegeName.');
  }

  Future<void> _loadCachedParkingSpaces() async {
    final cachedData = await _getCachedParkingSpaces(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedParkingSpaces = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded parking spaces from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadParkingSpacesFromDatabaseAndCache() async {
    if (_isLoadingParkingSpaces) {
      return;
    }

    setState(() {
      _isLoadingParkingSpaces = true;
    });

    print("Fetching parking spaces for ${widget.institutionName} from database");
    final parkingSpacesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_parkingspaces';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(parkingSpacesTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedParkingSpaces = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingParkingSpaces = false;
          _cacheParkingSpaces(widget.institutionName, response);
          print("Parking Spaces fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingParkingSpaces = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingParkingSpaces = false;
          _cachedParkingSpaces = [];
          print("Error fetching parking spaces for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingParkingSpaces = false;
      }
    }
  }

  void _setupParkingSpacesRealtimeListener() {
    final parkingSpacesTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_parkingspaces';
    _parkingSpacesRealtimeChannel = Supabase.instance.client
        .channel('parkingspaces_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: parkingSpacesTableName,
      callback: (payload) async {
        print(
            "Realtime update received for parking spaces of ${widget.institutionName}: ${payload.eventType}");
        _loadParkingSpacesFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Local Transport methods
  Future<List<Map<String, dynamic>>?> _getCachedLocalTransport(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? localTransportJson = prefs.getString(
        'localtransport_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (localTransportJson != null) {
      List<dynamic> decodedList = jsonDecode(localTransportJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheLocalTransport(String collegeName, List<Map<String, dynamic>> localTransport) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String localTransportJson = jsonEncode(localTransport);
    await prefs.setString(
        'localtransport_${collegeName.toLowerCase().replaceAll(' ', '')}', localTransportJson);
    print('Local Transport cached for $collegeName.');
  }

  Future<void> _loadCachedLocalTransport() async {
    final cachedData = await _getCachedLocalTransport(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedLocalTransport = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded local transport from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadLocalTransportFromDatabaseAndCache() async {
    if (_isLoadingLocalTransport) {
      return;
    }

    setState(() {
      _isLoadingLocalTransport = true;
    });

    print("Fetching local transport for ${widget.institutionName} from database");
    final localTransportTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_localtransport';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(localTransportTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedLocalTransport = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingLocalTransport = false;
          _cacheLocalTransport(widget.institutionName, response);
          print("Local Transport fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingLocalTransport = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingLocalTransport = false;
          _cachedLocalTransport = [];
          print("Error fetching local transport for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingLocalTransport = false;
      }
    }
  }

  void _setupLocalTransportRealtimeListener() {
    final localTransportTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_localtransport';
    _localTransportRealtimeChannel = Supabase.instance.client
        .channel('localtransport_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localTransportTableName,
      callback: (payload) async {
        print(
            "Realtime update received for local transport of ${widget.institutionName}: ${payload.eventType}");
        _loadLocalTransportFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    // Always show grid items
    return Visibility(
      key: Key('transport_grid_item_$title'),
      visible: true,
      child: Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (title == 'Campus Bus/Shuttle') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CampusShuttlePage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedCampusShuttles: _cachedCampusShuttle,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Bus/Shuttle Stops') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ShuttleStopsPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Parking Spaces/Lots') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ParkingSpacesPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedParkingSpaces: _cachedParkingSpaces,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Local Transport') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LocalTransportPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedLocalTransport: _cachedLocalTransport,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                ),
                ...[
                  const SizedBox(height: 8),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Transport',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Campus Bus/Shuttle', Icons.directions_bus, theme, isFromDetailPage),
                _buildGridItem(context, 'Bus/Shuttle Stops', Icons.place, theme, isFromDetailPage),
                _buildGridItem(context, 'Parking Spaces/Lots', Icons.local_parking, theme, isFromDetailPage),
                _buildGridItem(context, 'Local Transport', Icons.commute, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}