import 'package:flutter/material.dart';
import 'login_page.dart';

class VirtualTourPage extends StatelessWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const VirtualTourPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Virtual Tour',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: const Center(
        child: Text('Coming Soon'),
      ),
      bottomNavigationBar: NavigationBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        height: 65,
        selectedIndex: 0,
        destinations: [
          NavigationDestination(
            icon: Icon(
              Icons.home_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.home,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.person_outline,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.person,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
        ],
        onDestinationSelected: (index) {
          if (index == 0) {
            Navigator.of(context).popUntil((route) => route.isFirst);
          } else if (index == 1) {
            toggleTheme();
          } else if (index == 2) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginPage(
                  isDarkMode: isDarkMode,
                  toggleTheme: toggleTheme,
                ),
              ),
            );
          }
        },
      ),
    );
  }
}
