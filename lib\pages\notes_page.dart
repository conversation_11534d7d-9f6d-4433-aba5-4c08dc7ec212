import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class NoteItem {
  String text;
  DateTime createdAt;

  NoteItem({required this.text, required this.createdAt});

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory NoteItem.fromJson(Map<String, dynamic> json) {
    return NoteItem(
      text: json['text'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

class NotesPage extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const NotesPage({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> {
  List<NoteItem> _notes = [];

  @override
  void initState() {
    super.initState();
    _loadNotes();
  }

  Future<void> _loadNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedNotes = prefs.getStringList('notes');
    if (encodedNotes != null) {
      setState(() {
        _notes = encodedNotes.where((e) => e != null).map((e) {
          try {
            return NoteItem.fromJson(jsonDecode(e));
          } catch (error) {
            print('Error decoding note item: $error');
            return null;
          }
        }).whereType<NoteItem>().toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt)); // Sort by most recent
      });
    }
  }

  Future<void> _saveNotes() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedNotes = _notes.map((note) => jsonEncode(note.toJson())).toList();
    await prefs.setStringList('notes', encodedNotes);
  }

  void _addNote(String note) {
    if (note.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a note.')),
      );
      return;
    }
    setState(() {
      _notes.insert(0, NoteItem(text: note, createdAt: DateTime.now())); // Add to the top
      _saveNotes();
    });
  }

  void _deleteNote(int index) {
    if (index >= 0 && index < _notes.length) {
      setState(() {
        _notes.removeAt(index);
        _saveNotes();
      });
    }
  }

  void _updateNote(int index, String newValue) {
    setState(() {
      _notes[index] = NoteItem(text: newValue, createdAt: _notes[index].createdAt);
      _saveNotes();
    });
  }

  void _showNewNoteDialog(BuildContext context) {
    final theme = Theme.of(context);
    final TextEditingController newNoteController = TextEditingController();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('New Note', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: SingleChildScrollView(
            child: TextField(
              controller: newNoteController,
              maxLines: null,
              style: TextStyle(color: theme.colorScheme.onSurface),
              decoration: const InputDecoration(
                hintText: 'Enter your note here',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Save', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                _addNote(newNoteController.text);
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Notes',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
          color: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          child: Column(
            children: [
              const SizedBox(height: 1), // Add 1px margin
              Expanded(
                child: ListView.separated(
                  itemCount: _notes.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 0.5,
                    thickness: 0.5,
                    color: Colors.grey.shade300, // Subtle grey color for the divider
                  ),
                  itemBuilder: (context, index) {
                    final noteItem = _notes[index];
                    return Dismissible(
                      key: Key(noteItem.createdAt.toString()), // Unique key for Dismissible
                      background: Container(
                        color: Colors.red,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 20.0),
                        child: const Icon(Icons.delete, color: Colors.white),
                      ),
                      direction: DismissDirection.endToStart,
                      onDismissed: (direction) {
                        _deleteNote(index);
                        ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Note deleted')));
                      },
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => NoteDetailView(
                                note: noteItem,
                                index: index, // Pass the index here
                                onNoteUpdated: (updatedText) {
                                  final noteIndex = _notes.indexOf(noteItem);
                                  if (noteIndex != -1) {
                                    setState(() {
                                      _notes[noteIndex] = NoteItem(text: updatedText, createdAt: noteItem.createdAt);
                                      _saveNotes();
                                    });
                                  }
                                },
                                onNoteDeleted: (indexToDelete) {
                                  _handleNoteDeletion(indexToDelete); // Use the new handler
                                },
                              ),
                            ),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                          child: Row(
                            children: [
                              const Icon(Icons.note, color: Colors.grey), // Note icon
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      noteItem.text,
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Created on: ${DateFormat('MMM d, yyyy - h:mm a').format(noteItem.createdAt)}',
                                      style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showNewNoteDialog(context),
        backgroundColor: widget.isDarkMode ? Colors.black : theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        shape: const CircleBorder(),
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, int index) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Confirm Delete', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: Text('Are you sure you want to delete this note?', style: TextStyle(color: theme.colorScheme.onSurface)),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('OK', style: TextStyle(color: theme.colorScheme.error)),
              onPressed: () {
                _deleteNote(index);
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // New handler function to ensure state is updated correctly
  void _handleNoteDeletion(int indexToDelete) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Confirm Delete', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          content: Text('Are you sure you want to delete this note?', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('OK', style: TextStyle(color: Theme.of(context).colorScheme.error)),
              onPressed: () {
                _deleteNote(indexToDelete);
                Navigator.of(context).pop(); // Pop the confirmation dialog
                Navigator.of(context).pop(); // Pop the detail view
              },
            ),
          ],
        );
      },
    );
  }
}

class NoteDetailView extends StatefulWidget {
  final NoteItem note;
  final Function(String) onNoteUpdated;
  final Function(int) onNoteDeleted;
  final int index;

  const NoteDetailView({
    Key? key,
    required this.note,
    required this.onNoteUpdated,
    required this.onNoteDeleted,
    required this.index,
  }) : super(key: key);

  @override
  State<NoteDetailView> createState() => _NoteDetailViewState();
}

class _NoteDetailViewState extends State<NoteDetailView> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.note.text);
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(
          widget.note.text.split('\n').first,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(color: theme.colorScheme.onSurface),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              _showEditDialog(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              widget.onNoteDeleted(widget.index);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox.expand( // Use SizedBox.expand to fill available space
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView( // Keep SingleChildScrollView for content overflow
                child: Text(
                  widget.note.text,
                  style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurface),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Edit Note', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: SingleChildScrollView(
            child: TextField(
              controller: _textController,
              maxLines: null,
              style: TextStyle(color: theme.colorScheme.onSurface),
              decoration: const InputDecoration(
                hintText: 'Enter your note here',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Save', style: TextStyle(color: theme.colorScheme.onSurface)),
              onPressed: () {
                widget.onNoteUpdated(_textController.text);
                Navigator.of(context).pop();
                setState(() {}); // Update the detail view
              },
            ),
          ],
        );
      },
    );
  }
}