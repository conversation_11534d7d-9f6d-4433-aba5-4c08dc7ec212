// symposiums_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';

import 'login_page.dart';
import 'symposiums_detail_page.dart';

class SymposiumsPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SymposiumsPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _SymposiumsPageState createState() => _SymposiumsPageState();
}

class _SymposiumsPageState extends State<SymposiumsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('symposiums_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _symposiums = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
  }

  void _loadInitialData() async {
    await _loadSymposiumsFromCache();
    _loadSymposiumsFromSupabase();
  }

  void _setupRealtime() {
    final symposiumsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_symposiums';
    _realtimeChannel = Supabase.instance.client
        .channel('symposiums_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: symposiumsTableName,
      callback: (payload) async {
        print("Realtime update received for symposiums: ${payload.eventType}");
        _loadSymposiumsFromSupabase();
      },
    ).subscribe();
  }

  Future<void> _loadSymposiumsFromCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'symposiums_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedData = prefs.getString(cacheKey);

      if (cachedData != null) {
        final List<dynamic> decodedData = jsonDecode(cachedData);
        final List<Map<String, dynamic>> symposiums = decodedData.cast<Map<String, dynamic>>();
        
        if (mounted && symposiums.isNotEmpty) {
          setState(() {
            _symposiums = symposiums;
          });
          print("Loaded ${symposiums.length} symposiums from cache");
        }
      }
    } catch (e) {
      print("Error loading symposiums from cache: $e");
    }
  }

  Future<void> _loadSymposiumsFromSupabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final symposiumsTableName = '${widget.collegeData['fullname'].toLowerCase().replaceAll(' ', '')}_symposiums';
      var query = Supabase.instance.client
          .from(symposiumsTableName)
          .select('*');
      
      // Apply search filter if needed
      if (_searchQuery.isNotEmpty) {
        query = query.ilike('fullname', '%$_searchQuery%');
      }
      
      final response = await query.order('fullname', ascending: true);

      if (_isDisposed) return;

      setState(() {
        _symposiums = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });

      // Cache the data
      _cacheSymposiums(_symposiums);
      
      // Preload images
      _preloadImages();

    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading symposiums: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading symposiums: $e')),
      );
    }
  }

  Future<void> _cacheSymposiums(List<Map<String, dynamic>> symposiums) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final collegeNameForTable = widget.collegeData['fullname'];
      final cacheKey = 'symposiums_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(symposiums));
      print("Cached ${symposiums.length} symposiums");
    } catch (e) {
      print("Error caching symposiums: $e");
    }
  }

  void _preloadImages() {
    for (var symposium in _symposiums) {
      if (symposium['image_url'] != null && symposium['image_url'] != 'assets/placeholder_image.png') {
        precacheImage(NetworkImage(symposium['image_url']), context);
      }
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadSymposiumsFromSupabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Symposiums',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search symposiums...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),
          
          // Symposiums list
          Expanded(
            child: VisibilityDetector(
              key: const Key('symposiums_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _symposiums.isEmpty && !_isLoading) {
                  _loadSymposiumsFromSupabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadSymposiumsFromSupabase,
                child: _isLoading && _symposiums.isEmpty
                    ? Center(
                        child: CircularProgressIndicator(),
                      )
                    : _symposiums.isEmpty
                        ? Center(
                            child: Text(
                              'No symposiums found',
                              style: TextStyle(color: theme.colorScheme.onSurface),
                            ),
                          )
                        : ListView.builder(
                            key: _listKey,
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _symposiums.length,
                            itemBuilder: (context, index) {
                              return _buildSymposiumCard(
                                _symposiums[index],
                                theme,
                                currentIsDarkMode,
                              );
                            },
                          ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSymposiumCard(
    Map<String, dynamic> symposium,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = symposium['fullname'] ?? 'Unknown';
    final String about = symposium['about'] ?? '';
    final String phone = symposium['phone'] ?? '';
    final String email = symposium['email'] ?? '';
    final String imageUrl = symposium['image_url'] ?? 'assets/placeholder_image.png';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SymposiumsDetailPage(
                symposium: symposium,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
                collegeNameForBucket: widget.collegeData['fullname'],
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Circular image or icon
              CircleAvatar(
                radius: 30,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                backgroundImage: (imageUrl != 'assets/placeholder_image.png')
                    ? CachedNetworkImageProvider(imageUrl) as ImageProvider
                    : null,
                child: (imageUrl == 'assets/placeholder_image.png')
                    ? Icon(
                        Icons.event_note,
                        size: 30,
                        color: isDarkMode ? Colors.white : Colors.black,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        about.length > 100 ? '${about.substring(0, 100)}...' : about,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    if (phone.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            phone,
                            style: TextStyle(
                              fontSize: 14,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (email.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.email,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              email,
                              style: TextStyle(
                                fontSize: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
