import 'dart:io' as io; // Import for io.Platform, io.File
import 'package:flutter/material.dart';
import 'package:google_mlkit_document_scanner/google_mlkit_document_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // Import kIsWeb
import 'package:pdf/widgets.dart' as pw; // Import pdf library
import 'package:pdf/pdf.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';
import 'dart:typed_data';
import 'dart:ui' as ui; // Import for ui.Codec, ui.FrameInfo, ui.Image, etc.

class DocumentScannerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DocumentScannerPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DocumentScannerPage> createState() => _DocumentScannerPageState();
}

class _DocumentScannerPageState extends State<DocumentScannerPage> {
  List<String> _scannedDocuments = [];
  bool _isScanning = false;
  bool _isWeb = kIsWeb; // Determine if running on web

  // Instantiate DocumentScanner only if not on web
  late final DocumentScanner _documentScanner;

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();
    if (!_isWeb) { // Initialize DocumentScanner only on mobile
      _documentScanner = DocumentScanner(options: DocumentScannerOptions());
    }
  }

  @override
  void dispose() {
    if (!_isWeb) {
      _documentScanner.close();
    }
    super.dispose();
  }

  Future<void> _requestCameraPermission() async {
    if (_isWeb) return; // No camera permission needed on web (and won't work)
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera permission is required for document scanning.')),
      );
    }
  }

  Future<void> _scanDocument() async {
    if (_isWeb) {
      // Show a message if document scanning is attempted on web
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document scanning is not supported on web.')),
      );
      return; // Do not proceed with scanning on web
    }

    setState(() {
      _isScanning = true;
      _scannedDocuments.clear();
    });

    try {
      final DocumentScanningResult result = await _documentScanner.scanDocument();
      if (result.images.isNotEmpty) { 
        setState(() {
          _scannedDocuments = result.images; 
        });
      } else {
        print('No document pages found in scan result.');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No documents found in scan.')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error scanning document: ${e.toString()}')),
      );
    } finally {
      setState(() => _isScanning = false);
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      final status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Storage permission is required to save PDF.')),
        );
        return false;
      }
    }
    return true;
  }

  Future<void> _saveAsPdf() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      if (!await _requestStoragePermission()) {
        return;
      }
    }

    String? outputFile;
    try {
      outputFile = await FilePicker.platform.getDirectoryPath();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open file picker.')),
      );
      return;
    }

    if (outputFile == null) {
      // User cancelled the picker
      return;
    }

    final pdf = pw.Document();

    if (_scannedDocuments.isNotEmpty) {
      for (String docPath in _scannedDocuments) {
        ByteData? imageByteData = await _captureScannedDocumentAsPdfImage(docPath);
        if (imageByteData != null) {
          final pdfImage = pw.MemoryImage(imageByteData.buffer.asUint8List());
          pdf.addPage(pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(pdfImage),
              );
            },
          ));
        }
      }

      if (pdf.document.pdfPageList.pages.isNotEmpty) {
        try {
          final pdfBytes = await pdf.save();
          final fileName = 'scanned_documents_${DateTime.now().millisecondsSinceEpoch}.pdf';
          final filePath = path.join(outputFile, fileName);
          final file = io.File(filePath);
          await file.writeAsBytes(pdfBytes);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to ${file.path}')),
          );
        } catch (e) {
          print("Error saving PDF: $e");
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save PDF.')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No scanned documents to save as PDF.')),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No scanned documents available.')),
      );
    }
  }

  Future<ByteData?> _captureScannedDocumentAsPdfImage(String imagePath) async {
    try {
      final io.File imageFile = io.File(imagePath);
      if (await imageFile.exists()) {
        final Uint8List imageBytes = await imageFile.readAsBytes();
        final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
        final ui.FrameInfo frame = await codec.getNextFrame();
        final ui.Image image = frame.image;
        final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        return byteData;
      } else {
        print('Image file does not exist at path: $imagePath');
        return null;
      }
    } catch (e) {
      print('Error capturing image as PDF: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Scanner'),
        backgroundColor: theme.colorScheme.surface,
        actions: [
          PopupMenuButton<String>(
            icon: Icon(Icons.download_outlined, color: theme.colorScheme.onSurface),
            onSelected: (value) {
              if (value == 'pdf') {
                _saveAsPdf();
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'pdf',
                child: Text('Download PDF'),
              ),
            ],
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                foregroundColor: theme.brightness == Brightness.light ? Colors.black : Colors.white,
              ),
              onPressed: _isWeb ? null : (_isScanning ? null : _scanDocument), // Disable button on web
              child: Text(_isWeb ? 'Not Supported on Web' : (_isScanning ? 'Scanning...' : 'Scan Document')), // Indicate web limitation
            ),
            const SizedBox(height: 20),
            if (_isWeb)
              const Padding( // Message for web users
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Document scanning is not supported in web browsers. Please run this feature on an Android or iOS device.',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            Expanded(
              child: _scannedDocuments.isEmpty
                  ? const Center(child: Text('No documents scanned yet'))
                  : ListView.builder(
                      itemCount: _scannedDocuments.length,
                      itemBuilder: (context, index) {
                        final path = _scannedDocuments[index];
                        return Card(
                          color: theme.colorScheme.surface,
                          surfaceTintColor: Colors.transparent,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.file(io.File(path)), // Use io.File here to fix the error
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('Page ${index + 1}', style: TextStyle(color: theme.colorScheme.onSurface)),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}