import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:speech_to_text/speech_to_text.dart';
import 'package:just_audio/just_audio.dart';

class AudioExtractor {
  /// Extract text from an audio file using speech recognition
  /// Improved version with better error handling and retry logic
  static Future<String> extractText(Uint8List audioBytes, String fileName) async {
    if (kIsWeb) {
      return '[Audio transcription is not fully supported on web platforms]';
    }

    File? audioFile;

    try {
      print("Starting audio extraction for $fileName");

      // Create a temporary directory to store the audio file
      final tempDir = await getTemporaryDirectory();
      final audioPath = path.join(tempDir.path, 'temp_audio_$fileName');

      // Write audio bytes to a temporary file
      audioFile = File(audioPath);
      await audioFile.writeAsBytes(audioBytes);

      print("Audio file saved to temporary location: $audioPath");

      // Get audio duration to set appropriate timeout
      final duration = await _getAudioDuration(audioPath);
      if (duration == null) {
        print("Warning: Could not determine audio duration for $fileName");
      } else {
        print("Audio duration: ${duration.inSeconds} seconds");
      }

      final timeoutDuration = duration != null
          ? Duration(milliseconds: (duration.inMilliseconds * 2).toInt()) // 2x audio length for better results
          : Duration(minutes: 15); // Increased default timeout

      // Initialize speech recognition
      final speech = SpeechToText();
      bool available = await speech.initialize(
        onError: (error) => print("Speech recognition error: $error"),
        onStatus: (status) => print("Speech recognition status: $status"),
      );

      if (!available) {
        print("Speech recognition not available on this device");
        return '[Speech recognition not available on this device]';
      }

      print("Speech recognition initialized successfully");

      // Use a completer to wait for the result
      final completer = Completer<String>();
      String recognizedText = '';
      int retryCount = 0;
      const maxRetries = 3;

      Future<void> attemptRecognition() async {
        try {
          print("Attempt #${retryCount + 1} to recognize speech");

          // Listen to the audio file
          await speech.listen(
            onResult: (result) {
              if (result.recognizedWords.isNotEmpty) {
                recognizedText = result.recognizedWords;
                print("Recognized words: ${result.recognizedWords.length} characters");
              }

              if (result.finalResult) {
                print("Final result received");
                completer.complete(recognizedText);
              }
            },
            listenFor: timeoutDuration,
            pauseFor: Duration(seconds: 5), // Increased pause time
            partialResults: true, // Enable partial results for better feedback
            onSoundLevelChange: (level) {}, // Not using sound level
            cancelOnError: false, // Don't cancel on error, we'll handle it
            listenMode: ListenMode.dictation, // Use dictation mode for better results with audio files
          );
        } catch (e) {
          print("Error during speech recognition attempt: $e");

          // Retry logic
          if (retryCount < maxRetries) {
            retryCount++;
            print("Retrying speech recognition (attempt $retryCount of $maxRetries)");
            await Future.delayed(Duration(seconds: 2)); // Wait before retry
            await attemptRecognition();
          } else {
            print("Max retries reached, giving up");
            if (!completer.isCompleted) {
              completer.complete(recognizedText.isEmpty
                  ? '[Failed to transcribe audio after multiple attempts]'
                  : recognizedText);
            }
          }
        }
      }

      // Start recognition
      await attemptRecognition();

      // Wait for the result or timeout
      final result = await completer.future.timeout(
        timeoutDuration,
        onTimeout: () {
          print("Transcription timed out after ${timeoutDuration.inSeconds} seconds");
          speech.stop();
          return recognizedText.isEmpty
              ? '[Transcription timed out without results]'
              : recognizedText;
        },
      );

      // Clean up
      try {
        speech.stop();
        if (audioFile != null && await audioFile.exists()) {
          await audioFile.delete();
          print("Temporary audio file deleted");
        }
      } catch (e) {
        print('Error during cleanup: $e');
      }

      print("Audio extraction completed with ${result.length} characters of text");
      return result;
    } catch (e) {
      print('Error transcribing audio: $e');

      // Clean up in case of error
      try {
        if (audioFile != null && await audioFile.exists()) {
          await audioFile.delete();
          print("Temporary audio file deleted after error");
        }
      } catch (cleanupError) {
        print('Error during cleanup after failure: $cleanupError');
      }

      return '[Error transcribing audio: $e]';
    }
  }

  /// Get the duration of an audio file
  static Future<Duration?> _getAudioDuration(String audioPath) async {
    AudioPlayer? player;
    try {
      player = AudioPlayer();
      final duration = await player.setFilePath(audioPath);
      return duration;
    } catch (e) {
      print('Error getting audio duration: $e');
      return null;
    } finally {
      try {
        await player?.dispose();
      } catch (e) {
        print('Error disposing audio player: $e');
      }
    }
  }

  /// Alternative method using YouTube Explode for YouTube videos
  static Future<String> extractTextFromYouTubeUrl(String youtubeUrl) async {
    try {
      // This would use youtube_explode_dart package to download audio
      // and then transcribe it, but we'll leave it as a placeholder
      return '[YouTube transcription requires additional implementation]';
    } catch (e) {
      return '[Error extracting text from YouTube: $e]';
    }
  }

  /// Process an audio file in chunks for better transcription of long files
  static Future<String> processLongAudio(Uint8List audioBytes, String fileName) async {
    if (kIsWeb) {
      return '[Long audio processing is not supported on web platforms]';
    }

    File? audioFile;

    try {
      print("Starting long audio processing for $fileName");

      // Create a temporary directory to store the audio file
      final tempDir = await getTemporaryDirectory();
      final audioPath = path.join(tempDir.path, 'temp_long_audio_$fileName');

      // Write audio bytes to a temporary file
      audioFile = File(audioPath);
      await audioFile.writeAsBytes(audioBytes);

      // Get audio duration
      final duration = await _getAudioDuration(audioPath);
      if (duration == null) {
        return await extractText(audioBytes, fileName); // Fall back to regular extraction
      }

      print("Long audio duration: ${duration.inSeconds} seconds");

      // If audio is shorter than 5 minutes, use regular extraction
      if (duration.inMinutes < 5) {
        return await extractText(audioBytes, fileName);
      }

      // For longer audio, process in chunks
      final player = AudioPlayer();
      await player.setFilePath(audioPath);

      final StringBuffer fullTranscript = StringBuffer();
      final int totalChunks = (duration.inMinutes / 3).ceil(); // 3-minute chunks

      for (int i = 0; i < totalChunks; i++) {
        final startPosition = Duration(minutes: i * 3);
        final endPosition = Duration(minutes: (i + 1) * 3);

        print("Processing chunk ${i+1}/$totalChunks (${startPosition.inMinutes}-${endPosition.inMinutes} minutes)");

        // Extract this chunk (implementation would depend on available libraries)
        // This is a placeholder for the actual chunking logic
        fullTranscript.writeln("--- Transcript segment ${i+1} ---");
        fullTranscript.writeln("(Time: ${startPosition.inMinutes}-${endPosition.inMinutes} minutes)");

        // Here we would actually process each chunk
        // For now, we'll just use the regular extraction as a placeholder
        if (i == 0) {
          final chunkText = await extractText(audioBytes, fileName);
          fullTranscript.writeln(chunkText);
        } else {
          fullTranscript.writeln("[Additional audio segments would be processed here]");
        }
      }

      await player.dispose();

      // Clean up
      try {
        if (audioFile != null && await audioFile.exists()) {
          await audioFile.delete();
          print("Temporary long audio file deleted");
        }
      } catch (e) {
        print('Error deleting temporary long audio file: $e');
      }

      return fullTranscript.toString();
    } catch (e) {
      print('Error processing long audio: $e');

      // Clean up in case of error
      try {
        if (audioFile != null && await audioFile.exists()) {
          await audioFile.delete();
        }
      } catch (cleanupError) {
        print('Error during cleanup after long audio failure: $cleanupError');
      }

      // Fall back to regular extraction
      return await extractText(audioBytes, fileName);
    }
  }
}
