name: Harmonizr360
description: A new Flutter project with an Instagram-like interface.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  video_player: ^2.8.1 # Or the latest version
  path_provider: ^2.1.1 # Or the latest version
  path: ^1.9.0 # Or the latest version
  sqflite: ^2.4.1 # Or the latest version
  sqflite_common_ffi: ^2.3.4+4 # Or the latest version
  shared_preferences: ^2.2.0 # Or the latest version
  math_expressions: ^2.3.0  # You can check pub.dev for the latest version
  image_picker: ^1.1.2  # Use the latest version available on pub.dev
  intl: ^0.17.0 # Or the latest version
  crypto: ^3.0.1
  photo_view: ^0.15.0
  flutter_tts: ^3.8.3 # Or the latest version
  #qr_code_scanner: ^1.0.1   # Or the latest version
  fl_chart: ^0.66.0 # Use the latest version
  pdf: ^3.10.4 # Add this line    
  pdfx: ^2.8.0
  file_picker: ^6.1.1
  csv: ^5.0.0
  flutter_pdfview: ^1.3.1
  external_path: ^2.0.1
  geolocator: ^9.0.0 # Use the latest version
  #ar_flutter_plugin: ^0.7.0 # Use the latest version
  permission_handler: ^10.1.0 # For camera permissions
  cupertino_icons: ^1.0.2
  font_awesome_flutter: ^10.6.0
  flutter_colorpicker: ^1.0.3
  url_launcher: ^6.2.1
  # Firebase
  firebase_core: ^3.9.0
  firebase_core_web: ^2.19.0
  firebase_messaging: ^15.1.6 # Add the Firebase Messaging dependency
  google_mobile_ads: ^4.0.0  # Use the latest version
  supabase_flutter: ^2.0.0 # Use the latest version
  flutter_dotenv: ^5.2.1
  provider: ^6.1.2  
  #record: ^4.4.4 # Use the latest version
  #ffmpeg_kit_flutter_full_gpl: ^6.0.3 # Or another variant of ffmpeg_kit_flutter based
  google_fonts: ^6.1.0 # Use the latest version
  flutter_map: ^7.0.2
  latlong2: ^0.9.0
  flutter_map_cancellable_tile_provider: ^3.0.2
  js: ^0.6.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: "^0.13.1" # Add the latest version from pub.dev
  flutter_native_splash: "^2.3.8"  # Add the latest version from pub.dev

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png" # Replace with your icon path
  # min_sdk_android: 21 # Optional, for adaptive icons

flutter_native_splash:
   
  color: "#080808" # Customize your splash screen background color was #ffffff
  image: assets/splash/splash.png # Replace with your splash image path
  # android_12_background_color: "#ffffff" # Optional for Android 12+
  # android_12_splash_image: assets/splash/splash-android12.png # Optional for Android 12+

flutter:
  uses-material-design: true
  assets:
    - assets/ads/ # Add this line to include the entire ads directory
    - assets/library/
    - assets/placeholder_image.png
    
android: # Correct placement - top level
  useAndroidX: true
  minSdkVersion: 21
  targetSdkVersion: 33