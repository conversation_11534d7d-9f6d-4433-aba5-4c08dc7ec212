import 'package:flutter/material.dart';
import 'login_page.dart'; // Assuming this is still needed for bottom nav
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';

class AiAgentPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic>? collegeData; // Accepts college data

  const AiAgentPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    this.collegeData,
  }) : super(key: key);

  @override
  _AiAgentPageState createState() => _AiAgentPageState();
}

class _AiAgentPageState extends State<AiAgentPage> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  bool _isSending = false;
  final ScrollController _scrollController = ScrollController();

  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual key
  final String _model = 'gemini-2.0-flash';

  @override
  void initState() {
    super.initState();
    // *** ADD DEBUG LOGGING HERE ***
    print("AiAgentPage initState received collegeData: ${widget.collegeData}");
    WidgetsBinding.instance.addPostFrameCallback((_) {
       _addInitialGreeting();
    });
  }

  void _addInitialGreeting() {
    // Use the safer check here too for the greeting
    String collegeName = (widget.collegeData?['fullname'] != null && widget.collegeData!['fullname']!.isNotEmpty)
        ? widget.collegeData!['fullname']!
        : 'the selected institution';
     setState(() {
       _messages.add(ChatMessage(text: "Hi! How can I help you with information about $collegeName today?", isUser: false));
     });
  }


  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<String> _getCollegeDataForPrompt() async {
    // *** STRENGTHEN THIS CHECK ***
    if (widget.collegeData == null || widget.collegeData!['fullname'] == null || widget.collegeData!['fullname']!.isEmpty) {
      print("Warning: College data or fullname is null or empty in _getCollegeDataForPrompt.");
      // Optionally add a default message if context is absolutely required
      // _messages.add(ChatMessage(text: "Error: Cannot proceed without college context.", isUser: false));
      return "No specific college data available to provide context. Cannot answer question accurately."; // Return a clear message for the prompt itself
    }

    // If check passes, proceed
    String collegeName = widget.collegeData!['fullname'];
    String collegeTablePrefix = collegeName.toLowerCase().replaceAll(' ', '');
    print("AI Agent: Preparing data for College: $collegeName (Prefix: $collegeTablePrefix)");

    StringBuffer promptData = StringBuffer("Context about $collegeName:\n\n");
    promptData.writeln("## Basic Information:");
    promptData.writeln("- Full Name: ${widget.collegeData!['fullname']}"); // Safe now after check
    promptData.writeln("- Location: ${widget.collegeData!['city'] ?? 'N/A'}, ${widget.collegeData!['state'] ?? 'N/A'}");
    // Limit length of about section for prompt context
    String aboutText = widget.collegeData!['about']?.toString() ?? 'N/A';
    int aboutMaxLength = 500;
    promptData.writeln("- About: ${aboutText.substring(0, aboutText.length > aboutMaxLength ? aboutMaxLength : aboutText.length)}${aboutText.length > aboutMaxLength ? '...' : ''}");
    promptData.writeln("- Website: ${widget.collegeData!['website'] ?? 'N/A'}");
    promptData.writeln("- Institution Type: ${widget.collegeData!['institutiontype'] ?? 'N/A'}");
    // Add other relevant basic fields as needed...


    List<String> collegeTableNames = await _getCollegeTableNames(collegeTablePrefix);

    if (collegeTableNames.isEmpty) {
       print("AI Agent: No specific data tables found for prefix '$collegeTablePrefix'.");
       promptData.writeln("\n## Additional Data Sections:");
       promptData.writeln("No specific data tables (like events, people, etc.) were found for this college in the database.");
    } else {
      print("AI Agent: Found tables: $collegeTableNames");
      promptData.writeln("\n## Additional Data Sections:");

      for (String tableName in collegeTableNames) {
          String tableData = await _fetchTableData(tableName);
          String tableDisplayName = tableName
              .replaceFirst(collegeTablePrefix + '_', '')
              .replaceAll('_', ' ')
              .split(' ')
              .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '') // Prevent error on empty word
              .join(' ');
          promptData.writeln("\n### $tableDisplayName Information:");
          promptData.writeln(tableData.isNotEmpty ? tableData : "No data currently available in this section.");
          promptData.writeln("---");
      }
    }

    print("--- AI Agent: FINAL PROMPT CONTEXT BUILT ---");
    // print(promptData.toString()); // Uncomment to see full context if needed
    print("--- AI Agent: END OF PROMPT CONTEXT ---");
    return promptData.toString();
  }

  // ... _getCollegeTableNames() and _fetchTableData() remain the same ...
   Future<List<String>> _getCollegeTableNames(String collegeTablePrefix) async {
    if (collegeTablePrefix.isEmpty) return [];
    print("AI Agent: Fetching table names with prefix: $collegeTablePrefix");
    try {
      // Use information_schema to list tables (standard SQL way)
      final response = await Supabase.instance.client
          .rpc('list_tables_with_prefix', params: {'prefix_param': '${collegeTablePrefix}_'});

      if (response is List) {
        // The RPC returns a list of objects like {'table_name': 'prefix_xyz'}
        List<String> collegeTables = response
            .map((item) => item['table_name'] as String)
            .toList();
        print("AI Agent: Retrieved college tables via RPC: $collegeTables");
        return collegeTables;
      } else {
        print("AI Agent: Error - RPC 'list_tables_with_prefix' did not return a List. Response: $response");
        // Fallback: Try querying information_schema directly (might need permissions)
        try {
           print("AI Agent: Falling back to information_schema query...");
           final isResponse = await Supabase.instance.client
               .from('information_schema.tables')
               .select('table_name')
               .eq('table_schema', 'public')
               .like('table_name', '${collegeTablePrefix}_%'); // Filter by prefix

           if (isResponse is List) {
               List<String> collegeTables = isResponse.map((item) => item['table_name'] as String).toList();
               print("AI Agent: Retrieved college tables via information_schema: $collegeTables");
               return collegeTables;
           } else {
              print("AI Agent: Error - information_schema query did not return a List. Response: $isResponse");
              return [];
           }
        } catch (e_is) {
           print("AI Agent: Error querying information_schema: $e_is");
           return [];
        }
      }
    } catch (e_rpc) {
      print("AI Agent: Error calling RPC 'list_tables_with_prefix': $e_rpc");
      // Fallback to information_schema as above if RPC fails
        try {
           print("AI Agent: Falling back to information_schema query due to RPC error...");
           final isResponse = await Supabase.instance.client
               .from('information_schema.tables')
               .select('table_name')
               .eq('table_schema', 'public')
               .like('table_name', '${collegeTablePrefix}_%'); // Filter by prefix

           if (isResponse is List) {
               List<String> collegeTables = isResponse.map((item) => item['table_name'] as String).toList();
               print("AI Agent: Retrieved college tables via information_schema: $collegeTables");
               return collegeTables;
           } else {
              print("AI Agent: Error - information_schema query did not return a List. Response: $isResponse");
              return [];
           }
        } catch (e_is) {
           print("AI Agent: Error querying information_schema: $e_is");
           return [];
        }
    }
  }

  Future<String> _fetchTableData(String tableName) async {
    print("AI Agent: Fetching data from table: $tableName");
    try {
      final response = await Supabase.instance.client
          .from(tableName)
          .select() // Select all columns
          .limit(10); // Limit rows to keep context manageable

      if (response is List && response.isNotEmpty) {
        print("AI Agent: Data fetched from '$tableName': ${response.length} rows.");
        // Format data for the prompt - simple key-value list per row
        StringBuffer tableInfo = StringBuffer();
        for (var i = 0; i < response.length; i++) {
          var row = response[i] as Map<String, dynamic>;
          tableInfo.writeln("  Item ${i + 1}:");
          row.forEach((key, value) {
            if (value != null && value.toString().isNotEmpty) {
                // Simple formatting, limit long values
                String displayValue = value.toString();
                if (displayValue.length > 100) {
                  displayValue = displayValue.substring(0, 100) + "...";
                }
                tableInfo.writeln("    - ${key.replaceAll('_', ' ').capitalize()}: $displayValue");
            }
          });
           tableInfo.writeln(); // Add space between items
        }
        return tableInfo.toString();
      } else {
        print("AI Agent: No data found or invalid response for table: $tableName");
        return ""; // Return empty string if no data or error
      }
    } catch (e) {
      // Handle potential errors like table not existing gracefully
      if (e.toString().contains('relation') && e.toString().contains('does not exist')) {
         print("AI Agent: Table '$tableName' does not exist.");
      } else {
         print("AI Agent: Error fetching data from table '$tableName': $e");
      }
      return ""; // Return empty string on error
    }
  }

   Future<void> _sendMessage(String message) async {
    if (message.trim().isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
      _messages.add(ChatMessage(text: message, isUser: true));
    });
    _messageController.clear();
    _scrollToBottom(); // Scroll after adding user message

    // Show typing indicator
    setState(() {
      _messages.add(ChatMessage(text: "...", isUser: false, isTyping: true));
    });
     _scrollToBottom(); // Scroll after adding typing indicator

    print("AI Agent: Getting college context for prompt...");
    String collegeContext = await _getCollegeDataForPrompt();

    // Check if context generation failed (indicated by the specific error message)
     if (collegeContext.startsWith("No specific college data available")) {
        setState(() {
          _messages.removeWhere((m) => m.isTyping); // Remove typing indicator
          _messages.add(ChatMessage(text: "Sorry, I don't have the specific college context needed to answer right now.", isUser: false));
          _isSending = false;
        });
        _scrollToBottom();
        return; // Stop processing if context is missing
     }


    String collegeName = widget.collegeData?['fullname'] ?? 'the institution'; // Get name again (safe due to earlier check)

    String prompt = """
        You are a specialized AI assistant for $collegeName.
        Your primary function is to answer questions based *only* on the information provided below about $collegeName.
        Do not use any external knowledge or information about other institutions or general topics.
        Be concise and helpful, extracting relevant details from the context.
        If the user asks a question that cannot be answered using the provided context, politely state that the information is not available in your current knowledge base for $collegeName. Do not apologize excessively or suggest searching elsewhere. Just state the limitation based on the provided data.

        --- Provided Context for $collegeName ---
        $collegeContext
        --- End of Context ---

        User's Question: $message
        Answer:
        """;


    try {
      print("AI Agent: Calling Gemini API...");
      final responseText = await _callGeminiApi(prompt);
      print("AI Agent: Received response from Gemini.");
      // Remove typing indicator and add AI response
      setState(() {
        _messages.removeWhere((m) => m.isTyping); // Remove the "..."
        _messages.add(ChatMessage(text: responseText, isUser: false));
        _isSending = false;
      });
    } catch (error) {
      print("AI Agent: Gemini API Error: $error");
      // Remove typing indicator and add error message
      setState(() {
         _messages.removeWhere((m) => m.isTyping); // Remove the "..."
         _messages.add(ChatMessage(text: "Sorry, I encountered an error trying to respond. Please try again.", isUser: false));
        _isSending = false;
      });
    } finally {
       _scrollToBottom(); // Scroll after adding AI response or error
    }
  }

   Future<String> _callGeminiApi(String prompt) async {
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$_model:generateContent?key=$_apiKey');
    final headers = {'Content-Type': 'application/json'};
    final body = jsonEncode({
      'contents': [
        {
          'parts': [
            {'text': prompt}
          ]
        }
      ],
      // Adjust generation config as needed
      'generationConfig': {
        'temperature': 0.6, // Slightly more creative but grounded
        'topP': 0.95,
        'topK': 40,
        'maxOutputTokens': 1024, // Limit output length
      },
      // Keep safety settings
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'}
      ]
    });

    print("AI Agent: Sending request to Gemini...");
    // print("Prompt Snippet: ${prompt.substring(0, prompt.length > 500 ? 500 : prompt.length)}..."); // Log snippet

    final response = await http.post(url, headers: headers, body: body).timeout(const Duration(seconds: 45)); // Add timeout

    print("AI Agent: Gemini response status: ${response.statusCode}");

    if (response.statusCode == 200) {
      final decodedResponse = jsonDecode(response.body);
      // print("Gemini Raw Response: ${response.body}"); // Log raw response if needed

      // Check for response content
      if (decodedResponse['candidates'] != null &&
          decodedResponse['candidates'].isNotEmpty &&
          decodedResponse['candidates'][0]['content'] != null &&
          decodedResponse['candidates'][0]['content']['parts'] != null &&
          decodedResponse['candidates'][0]['content']['parts'].isNotEmpty)
      {
        return decodedResponse['candidates'][0]['content']['parts'][0]['text']?.trim() ?? "No text content received from AI.";
      }
      // Check for blocked response due to safety or other reasons
      else if (decodedResponse['promptFeedback'] != null &&
               decodedResponse['promptFeedback']['blockReason'] != null)
      {
         String reason = decodedResponse['promptFeedback']['blockReason'];
         print("AI Agent: Prompt blocked by Gemini. Reason: $reason");
         return "My response was blocked due to safety settings ($reason). Please rephrase your question.";
      }
      else if (decodedResponse['candidates'] != null &&
               decodedResponse['candidates'].isNotEmpty &&
               decodedResponse['candidates'][0]['finishReason'] != null &&
               decodedResponse['candidates'][0]['finishReason'] != 'STOP')
      {
        String reason = decodedResponse['candidates'][0]['finishReason'];
        print("AI Agent: Gemini generation finished unexpectedly. Reason: $reason");
        return "Sorry, I couldn't finish generating the response ($reason).";
      }
      else {
        print("AI Agent: Unexpected Gemini response structure: ${response.body}");
        return "Received an unexpected response format from the AI.";
      }
    } else {
      print("AI Agent: Gemini API request failed. Status: ${response.statusCode}, Body: ${response.body}");
      throw Exception('API request failed with status code: ${response.statusCode}');
    }
  }

  // ... _scrollToBottom remains the same ...
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }


  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // *** DYNAMICALLY SET AppBar TITLE ***
    String appBarTitle = 'AI Agent'; // Default title
    if (widget.collegeData?['fullname'] != null && widget.collegeData!['fullname']!.isNotEmpty) {
      appBarTitle = "${widget.collegeData!['fullname']} AI";
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          appBarTitle, // Use the dynamic title
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis, // Handle long college names
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _messages.length,
              padding: const EdgeInsets.all(10.0),
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ChatBubble(
                  message: message,
                  isDarkMode: currentIsDarkMode,
                  theme: theme);
              },
            ),
          ),
          _buildInputArea(theme),
        ],
      ),
      // Keep your existing bottom navigation bar
       bottomNavigationBar: Container(
         color: theme.colorScheme.surface,
         child: SafeArea(
           child: Padding(
             padding: const EdgeInsets.symmetric(vertical: 8),
             child: Row(
               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
               children: [
                 IconButton(
                   icon: Icon(
                     Icons.home_outlined,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: () {
                     // Navigate home - Adjust if needed
                     Navigator.of(context).popUntil((route) => route.isFirst);
                   },
                 ),
                 const SizedBox(width: 24),
                 IconButton(
                   icon: Icon(
                     currentIsDarkMode
                         ? Icons.light_mode_outlined
                         : Icons.dark_mode_outlined,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: widget.toggleTheme,
                 ),
                 const SizedBox(width: 24),
                 IconButton(
                   icon: Icon(
                     Icons.person_outline,
                     color: theme.colorScheme.onSurface,
                   ),
                   onPressed: () {
                     Navigator.push(
                       context,
                       MaterialPageRoute(
                         builder: (context) => LoginPage( // Ensure LoginPage exists
                           isDarkMode: widget.isDarkMode,
                           toggleTheme: widget.toggleTheme,
                         ),
                       ),
                     );
                   },
                 ),
               ],
             ),
           ),
         ),
       ),
    );
  }

  // ... _buildInputArea remains the same ...
   Widget _buildInputArea(ThemeData theme) {
    String hintText = widget.collegeData?['fullname'] != null
      ? 'Ask about ${widget.collegeData!['fullname']}...'
      : 'Ask a question...';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: BoxDecoration(
         color: theme.colorScheme.surface,
         boxShadow: [
            BoxShadow(
              offset: const Offset(0, -1),
              blurRadius: 2,
              color: Colors.black.withOpacity(0.1)
            )
         ]
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
                fillColor: theme.canvasColor, // Use canvasColor for subtle difference
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25.0),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
              ),
              style: TextStyle(color: theme.colorScheme.onSurface),
              textCapitalization: TextCapitalization.sentences,
              onSubmitted: _sendMessage,
              enabled: !_isSending, // Disable input while sending
            ),
          ),
          const SizedBox(width: 8.0),
          _isSending
              ? Padding(
                  padding: const EdgeInsets.all(12.0), // Adjust padding
                  child: SizedBox(
                    width: 20, // Smaller indicator
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      color: theme.colorScheme.primary),
                  ),
                )
              : IconButton(
                  icon: Icon(Icons.send,
                    color: _messageController.text.trim().isEmpty
                        ? theme.disabledColor // Disabled color if empty
                        : theme.colorScheme.primary // Use primary color when active
                  ),
                  onPressed: _isSending || _messageController.text.trim().isEmpty
                      ? null // Disable button if sending or empty
                      : () => _sendMessage(_messageController.text),
                ),
        ],
      ),
    );
  }
}

// --- Helper Classes (ChatMessage, ChatBubble) ---
// (Keep ChatMessage and ChatBubble classes as they were in the previous correct version)
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping; // Added for typing indicator

  ChatMessage({required this.text, required this.isUser, this.isTyping = false});
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme
   }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Specific style for typing indicator
    if (message.isTyping) {
       return Container(
         margin: const EdgeInsets.symmetric(vertical: 8.0),
         alignment: Alignment.topLeft,
         child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant, // Use a subtle background
              borderRadius: BorderRadius.circular(20.0),
            ),
            child: Text(
              "Typing...",
              style: TextStyle(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
       );
    }

    // Regular chat bubble
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 8.0), // Added horizontal margin
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft, // Use center alignment
      child: ConstrainedBox( // Limit bubble width
         constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
         child: Container(
           padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0), // Adjusted padding
           decoration: BoxDecoration(
             color: message.isUser
                 ? theme.colorScheme.primary // User message color
                 : theme.colorScheme.surfaceVariant, // AI message color (more distinct)
             borderRadius: BorderRadius.only( // Different border radius
                topLeft: Radius.circular(message.isUser ? 18.0 : 4.0),
                topRight: Radius.circular(message.isUser ? 4.0 : 18.0),
                bottomLeft: const Radius.circular(18.0),
                bottomRight: const Radius.circular(18.0),
             ),
              boxShadow: [ // Subtle shadow
                 BoxShadow(
                   color: Colors.black.withOpacity(0.05),
                   blurRadius: 2,
                   offset: const Offset(1, 1),
                 )
              ]
           ),
           child: SelectableText( // Make text selectable
             message.text,
             style: TextStyle(
                color: message.isUser
                  ? theme.colorScheme.onPrimary // Text color on primary
                  : theme.colorScheme.onSurfaceVariant, // Text color on surface variant
                fontSize: 15, // Slightly larger text
             ),
           ),
         ),
      ),
    );
  }
}


// --- Helper Extension ---
extension StringExtension on String {
    String capitalize() {
      if (isEmpty) return "";
      // Handle multiple words if needed, simple first letter cap here
      return "${this[0].toUpperCase()}${substring(1)}";
      // If you need to capitalize each word:
      // return split(' ').map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : '').join(' ');
    }
}