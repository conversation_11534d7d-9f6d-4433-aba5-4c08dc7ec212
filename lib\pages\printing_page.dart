import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'printing_detail_page.dart';
import 'login_page.dart';

class PrintingPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedPrintingLocations;
  final bool isFromDetailPage;

  const PrintingPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedPrintingLocations,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _PrintingPageState createState() => _PrintingPageState();
}

class _PrintingPageState extends State<PrintingPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('printing_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _printingLocations = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PrintingPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedPrintingLocations != null &&
        widget.preloadedPrintingLocations!.isNotEmpty) {
      setState(() {
        _printingLocations = List.from(widget.preloadedPrintingLocations!);
        _isLoading = false;
      });
    } else {
      _loadPrintingLocationsFromDatabase();
    }
  }

  void _setupRealtime() {
    final printingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_printing';
    _realtimeChannel = Supabase.instance.client
        .channel('printing_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: printingTableName,
      callback: (payload) async {
        print("Realtime update received for printing locations: ${payload.eventType}");
        _loadPrintingLocationsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadPrintingLocationsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _printingLocations = [];
    });

    await _loadMorePrintingLocations();
  }

  Future<void> _loadMorePrintingLocations() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final printingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_printing';
      final response = await Supabase.instance.client
          .from(printingTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _printingLocations.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading printing locations: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading printing locations: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMorePrintingLocations();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Printing Locations',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('printing_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _printingLocations.isEmpty && !_isLoading) {
            _loadPrintingLocationsFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadPrintingLocationsFromDatabase,
          child: _printingLocations.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No printing locations found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _printingLocations.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _printingLocations.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildPrintingCard(
                      _printingLocations[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildPrintingCard(
    Map<String, dynamic> location,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = location['fullname'] ?? 'Unknown';
    final String building = location['building'] ?? '';
    final String room = location['room'] ?? '';
    final String hours = location['hours'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PrintingDetailPage(
                printingLocation: location,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.print,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (hours.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          hours,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
