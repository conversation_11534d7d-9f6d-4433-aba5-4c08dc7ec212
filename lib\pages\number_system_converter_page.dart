import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum NumberSystem { decimal, binary, octal, hexadecimal }

class NumberSystemConverterPage extends StatefulWidget {
  const NumberSystemConverterPage({Key? key}) : super(key: key);

  @override
  _NumberSystemConverterPageState createState() => _NumberSystemConverterPageState();
}

class _NumberSystemConverterPageState extends State<NumberSystemConverterPage> {
  String _inputValue = '';
  NumberSystem _fromSystem = NumberSystem.decimal;
  NumberSystem _toSystem = NumberSystem.binary;
  String _outputValue = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Number System Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    decoration: InputDecoration(
                      labelText: 'Enter Value',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _inputValue = value;
                      });
                    },
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<NumberSystem>(
                        value: _fromSystem,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: NumberSystem.values.map((NumberSystem system) {
                          String systemText = system.toString().split('.').last.capitalize();
                          return DropdownMenuItem<NumberSystem>(
                            value: system,
                            child: Text(systemText, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromSystem = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<NumberSystem>(
                        value: _toSystem,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: NumberSystem.values.map((NumberSystem system) {
                          String systemText = system.toString().split('.').last.capitalize();
                          return DropdownMenuItem<NumberSystem>(
                            value: system,
                            child: Text(systemText, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toSystem = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertNumberSystem();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_outputValue',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertNumberSystem() {
    try {
      int decimalValue;
      switch (_fromSystem) {
        case NumberSystem.binary:
          decimalValue = int.parse(_inputValue, radix: 2);
          break;
        case NumberSystem.octal:
          decimalValue = int.parse(_inputValue, radix: 8);
          break;
        case NumberSystem.hexadecimal:
          decimalValue = int.parse(_inputValue, radix: 16);
          break;
        default: // Decimal
          decimalValue = int.parse(_inputValue);
      }

      String result = '';
      switch (_toSystem) {
        case NumberSystem.binary:
          result = decimalValue.toRadixString(2);
          break;
        case NumberSystem.octal:
          result = decimalValue.toRadixString(8);
          break;
        case NumberSystem.hexadecimal:
          result = decimalValue.toRadixString(16).toUpperCase();
          break;
        default: // Decimal
          result = decimalValue.toString();
      }

      setState(() {
        _outputValue = result;
      });
    } catch (e) {
      setState(() {
        _outputValue = 'Invalid Input';
      });
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}