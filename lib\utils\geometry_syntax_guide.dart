import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class GeometrySyntaxGuide {
  static Widget buildGuideWidget(BuildContext context, bool isDarkMode) {
    final textColor = isDarkMode ? Colors.white : Colors.black;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Geometry Syntax Guide',
            style: GoogleFonts.notoSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'You can add geometric shapes to your notes using the following syntax:',
            style: GoogleFonts.notoSans(
              fontSize: 14,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildCodeBlock(
            '```geometry:circle(radius=50, color=#3498db, borderColor=#000000, borderWidth=2, filled=true)```',
            isDarkMode,
          ),
          const SizedBox(height: 12),
          Text(
            'Available Shapes:',
            style: GoogleFonts.notoSans(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildShapeExample('circle', 'radius, color, borderColor, borderWidth, filled', isDarkMode),
          _buildShapeExample('rectangle', 'width, height, color, borderColor, borderWidth, filled, cornerRadius', isDarkMode),
          _buildShapeExample('triangle', 'width, height, color, borderColor, borderWidth, filled', isDarkMode),
          _buildShapeExample('line', 'start, end, color, strokeWidth, strokeCap', isDarkMode),
          _buildShapeExample('arc', 'radius, startAngle, sweepAngle, color, borderColor, borderWidth, filled', isDarkMode),
          _buildShapeExample('polygon', 'points, color, borderColor, borderWidth, filled', isDarkMode),
          const SizedBox(height: 12),
          Text(
            'Examples:',
            style: GoogleFonts.notoSans(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildCodeBlock(
            '```geometry:circle(radius=50, color=#FF5733)```',
            isDarkMode,
          ),
          const SizedBox(height: 4),
          _buildCodeBlock(
            '```geometry:rectangle(width=200, height=100, color=#3498db, cornerRadius=10)```',
            isDarkMode,
          ),
          const SizedBox(height: 4),
          _buildCodeBlock(
            '```geometry:line(start=Offset(0,0), end=Offset(100,100), color=#000000, strokeWidth=3)```',
            isDarkMode,
          ),
          const SizedBox(height: 4),
          _buildCodeBlock(
            '```geometry:polygon(points=[0:0|50:0|25:50], color=#27AE60)```',
            isDarkMode,
          ),
        ],
      ),
    );
  }
  
  static Widget _buildShapeExample(String shapeName, String parameters, bool isDarkMode) {
    final textColor = isDarkMode ? Colors.white : Colors.black;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: '• $shapeName: ',
              style: GoogleFonts.notoSans(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: textColor,
              ),
            ),
            TextSpan(
              text: parameters,
              style: GoogleFonts.notoSans(
                fontSize: 14,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  static Widget _buildCodeBlock(String code, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.black : Colors.grey[100],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
        ),
      ),
      child: Text(
        code,
        style: GoogleFonts.jetBrainsMono(
          fontSize: 12,
          color: isDarkMode ? Colors.lightBlue[300] : Colors.blue[700],
        ),
      ),
    );
  }
  
  static String getGeometryPromptSuggestion() {
    return """
You can add geometric shapes to your notes using the geometry syntax:

```geometry:circle(radius=50, color=#3498db)```
```geometry:rectangle(width=200, height=100, cornerRadius=10)```
```geometry:triangle(width=100, height=100, color=#FF5733)```
```geometry:line(start=Offset(0,0), end=Offset(100,100))```
```geometry:arc(radius=50, startAngle=0, sweepAngle=3.14)```
```geometry:polygon(points=[0:0|50:0|25:50])```

Try adding these to your notes to create diagrams!
""";
  }
}
