// video_detail_page.dart
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import 'login_page.dart';

class VideoDetailPage extends StatefulWidget {
  final Map<String, dynamic> video;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const VideoDetailPage({
    Key? key,
    required this.video,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<VideoDetailPage> createState() => _VideoDetailPageState();
}

class _VideoDetailPageState extends State<VideoDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _videoRealtimeChannel; // Realtime channel for video updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupVideoRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _videoRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.video['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupVideoRealtimeListener() {
    final videosTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_videos';
    _videoRealtimeChannel = Supabase.instance.client
        .channel('video_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: videosTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current video's ID
        if (payload.newRecord['id'] == widget.video['id']) {
          print("Realtime UPDATE event received for THIS video (manual filter applied): ${widget.video['fullname']}");
          _fetchUpdatedVideoData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER video, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedVideoData() async {
    final videosTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_videos';
    try {
      final updatedVideoResponse = await Supabase.instance.client
          .from(videosTableName)
          .select('*')
          .eq('id', widget.video['id'])
          .single();

      if (mounted && updatedVideoResponse != null) {
        Map<String, dynamic> updatedVideo = Map.from(updatedVideoResponse);
        // Update the widget.video with the new data
        setState(() {
          widget.video.clear(); // Clear old data
          widget.video.addAll(updatedVideo); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Video data updated in detail page for ${widget.video['fullname']}");
          _updateVideosCache(updatedVideo); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated video data: $error");
    }
  }

  Future<void> _updateVideosCache(Map<String, dynamic> updatedVideo) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'videos_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedVideosJson = prefs.getString(cacheKey);

    if (cachedVideosJson != null) {
      List<Map<String, dynamic>> cachedVideos = (jsonDecode(cachedVideosJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the video in the cached list
      for (int i = 0; i < cachedVideos.length; i++) {
        if (cachedVideos[i]['id'] == updatedVideo['id']) {
          cachedVideos[i] = updatedVideo;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedVideos));
      print("Videos cache updated with realtime change for ${updatedVideo['fullname']}");
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> video) {
    final day = video['day'] as int?;
    final month = video['month'] as int?;
    final year = video['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final link = widget.video['link'] as String? ?? '';
    final platform = widget.video['platform'] as String? ?? '';
    final dateStr = _formatDate(widget.video);

    final bool isLinkAvailable = link.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.video['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          if (isLinkAvailable)
            IconButton(
              icon: Icon(
                Icons.link,
                color: theme.colorScheme.primary,
              ),
              onPressed: () => _launchURL(link),
              tooltip: 'Watch video',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Stack(
              children: [
                _isLoadingImage
                    ? const SizedBox(height: 300, child: Center(child: CircularProgressIndicator()))
                    : CachedNetworkImage(
                  imageUrl: _imageUrl,
                  placeholder: (context, url) => Image.asset(
                    'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                    fit: BoxFit.contain,
                    height: 300,
                  ),
                  errorWidget: (context, url, error) => Image.asset(
                    'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                    fit: BoxFit.contain,
                    height: 300,
                  ),
                  fit: BoxFit.cover,
                  height: 300,
                ),
                if (isLinkAvailable)
                  Positioned.fill(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _launchURL(link),
                        child: Center(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            padding: const EdgeInsets.all(16),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 40,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.video['fullname'] ?? '',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (dateStr.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          dateStr,
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                      ],
                      if (platform.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Platform: $platform',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.secondary,
                          ),
                        ),
                      ],
                      if (isLinkAvailable) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('Watch Video'),
                          onPressed: () => _launchURL(link),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    widget.isDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
