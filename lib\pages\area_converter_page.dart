// area_converter_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AreaConverterPage extends StatefulWidget {
  const AreaConverterPage({Key? key}) : super(key: key);

  @override
  _AreaConverterPageState createState() => _AreaConverterPageState();
}

class _AreaConverterPageState extends State<AreaConverterPage> {
  final Map<String, double> _areaUnits = {
    'Sq Meters': 1.0, // Abbreviated
    'Sq Kilometers': 1000000.0, // Abbreviated
    'Sq Miles': 2589988.0, // Abbreviated
    'Sq Feet': 0.092903, // Abbreviated
    'Sq Inches': 0.00064516, // Abbreviated
    'Hectares': 10000.0,
    'Acres': 4046.86,
  };

  final Map<String, String> _fullUnitNames = { // Map for full names
    'Sq Meters': 'Square Meters',
    'Sq Kilometers': 'Square Kilometers',
    'Sq Miles': 'Square Miles',
    'Sq Feet': 'Square Feet',
    'Sq Inches': 'Square Inches',
    'Hectares': 'Hectares',
    'Acres': 'Acres',
  };

  String _fromUnit = 'Sq Meters'; // Abbreviated default
  String _toUnit = 'Sq Kilometers'; // Abbreviated default
  double _inputValue = 0.0;
  double _outputValue = 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Area Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Value to Convert',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _inputValue = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _fromUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _areaUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)), // Using abbreviated names
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromUnit = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<String>(
                        value: _toUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: _areaUnits.keys.map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)), // Using abbreviated names
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toUnit = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertArea();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_outputValue ${_fullUnitNames[_toUnit]}', // Using full unit name for result
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertArea() {
    if (_areaUnits[_fromUnit] != null && _areaUnits[_toUnit] != null) {
      setState(() {
        _outputValue = _inputValue * (_areaUnits[_fromUnit]! / _areaUnits[_toUnit]!);
      });
    } else {
      setState(() {
        _outputValue = 0.0;
      });
      // Handle error: unit not found
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Area unit not found.')),
      );
    }
  }
}