import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'partnership_opportunity_detail_page.dart';

class PartnershipOpportunitiesPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedPartnershipOpportunities;
  final bool isFromDetailPage;

  const PartnershipOpportunitiesPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedPartnershipOpportunities,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<PartnershipOpportunitiesPage> createState() => _PartnershipOpportunitiesPageState();
}

class _PartnershipOpportunitiesPageState extends State<PartnershipOpportunitiesPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('partnership_opportunities_list');
  List<Map<String, dynamic>> _partnershipOpportunities = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("PartnershipOpportunitiesPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant PartnershipOpportunitiesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("PartnershipOpportunitiesPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("PartnershipOpportunitiesPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("PartnershipOpportunitiesPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedPartnershipOpportunities != null && widget.preloadedPartnershipOpportunities!.isNotEmpty) {
      print("Preloaded partnership opportunities found, using them.");
      setState(() {
        _partnershipOpportunities = List<Map<String, dynamic>>.from(widget.preloadedPartnershipOpportunities!);
        _partnershipOpportunities.forEach((opportunity) {
          opportunity['_isImageLoading'] = false;
        });
        _partnershipOpportunities.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedPartnershipOpportunities!.length == _pageSize;
      });
      _loadOpportunitiesFromSupabase(initialLoad: false);
    } else {
      print("No preloaded partnership opportunities or empty list, loading from database.");
      _loadOpportunitiesFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadOpportunitiesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadOpportunitiesFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final partnershipOpportunitiesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(partnershipOpportunitiesTableName)
          .select('*');

      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedOpportunities =
          await _updateOpportunityImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _partnershipOpportunities = updatedOpportunities;
        } else {
          _partnershipOpportunities.addAll(updatedOpportunities);
        }
        _partnershipOpportunities.forEach((opportunity) {
          opportunity['_isImageLoading'] = false;
        });
        _partnershipOpportunities.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });

      // Cache the partnership opportunities
      _cachePartnershipOpportunities(_partnershipOpportunities);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching partnership opportunities: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateOpportunityImageUrls(
      List<Map<String, dynamic>> opportunities) async {
    List<Future<void>> futures = [];
    for (final opportunity in opportunities) {
      if (opportunity['image_url'] == null ||
          opportunity['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(opportunity));
      }
    }
    await Future.wait(futures);
    return opportunities;
  }

  Future<void> _cachePartnershipOpportunities(List<Map<String, dynamic>> partnershipOpportunities) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String partnershipOpportunitiesJson = jsonEncode(partnershipOpportunities);
      await prefs.setString(
          'partnershipopportunities_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          partnershipOpportunitiesJson);
    } catch (e) {
      print('Error caching partnership opportunities: $e');
    }
  }

  void _setupRealtime() {
    final partnershipOpportunitiesTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_partnershipopportunities';
    _realtimeChannel = Supabase.instance.client
        .channel('partnership_opportunities')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: partnershipOpportunitiesTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newOpportunityId = payload.newRecord['id'];
          final newOpportunityResponse = await Supabase.instance.client
              .from(partnershipOpportunitiesTableName)
              .select('*')
              .eq('id', newOpportunityId)
              .single();
          if (mounted) {
            Map<String, dynamic> newOpportunity = Map.from(newOpportunityResponse);
            final updatedOpportunity = await _updateOpportunityImageUrls([newOpportunity]);
            setState(() {
              _partnershipOpportunities = [..._partnershipOpportunities, updatedOpportunity.first];
              updatedOpportunity.first['_isImageLoading'] = false;
              _partnershipOpportunities.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedOpportunityId = payload.newRecord['id'];
          final updatedOpportunityResponse = await Supabase.instance.client
              .from(partnershipOpportunitiesTableName)
              .select('*')
              .eq('id', updatedOpportunityId)
              .single();
          if (mounted) {
            final updatedOpportunity = Map<String, dynamic>.from(updatedOpportunityResponse);
            setState(() {
              _partnershipOpportunities = _partnershipOpportunities.map((opportunity) {
                return opportunity['id'] == updatedOpportunity['id'] ? updatedOpportunity : opportunity;
              }).toList();
              _partnershipOpportunities.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedOpportunityId = payload.oldRecord['id'];
          setState(() {
            _partnershipOpportunities.removeWhere((opportunity) => opportunity['id'] == deletedOpportunityId);
          });
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreOpportunities();
    }
  }

  Future<void> _loadMoreOpportunities() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadOpportunitiesFromSupabase(initialLoad: false);
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> opportunity) async {
    if (opportunity['_isImageLoading'] == true) {
      print('Image loading already in progress for ${opportunity['fullname']}, skipping.');
      return;
    }
    if (opportunity['image_url'] != null &&
        opportunity['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${opportunity['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      opportunity['_isImageLoading'] = true;
    });

    final fullname = opportunity['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeOpportunityBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/partnershipopportunities';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeOpportunityBucket');
    print('Image URL before fetch: ${opportunity['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeOpportunityBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeOpportunityBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        opportunity['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        opportunity['_isImageLoading'] = false;
        print('Setting image_url for ${opportunity['fullname']} to: ${opportunity['image_url']}');
      });
    } else {
      opportunity['_isImageLoading'] = false;
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> opportunity) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PartnershipOpportunityDetailPage(
            opportunity: opportunity,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _launchPhone(String phone) async {
    final url = 'tel:$phone';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch phone app')),
      );
    }
  }

  void _launchWhatsApp(String whatsapp) async {
    final url = 'https://wa.me/$whatsapp';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch WhatsApp')),
      );
    }
  }

  void _launchEmail(String email) async {
    final url = 'mailto:$email';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch email app')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("PartnershipOpportunitiesPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Partnership Opportunities',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _partnershipOpportunities.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadOpportunitiesFromSupabase(initialLoad: true),
              child: _partnershipOpportunities.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No partnership opportunities available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _partnershipOpportunities.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _partnershipOpportunities.length) {
                          final opportunity = _partnershipOpportunities[index];
                          return VisibilityDetector(
                            key: Key('opportunity_${opportunity['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (opportunity['image_url'] == null ||
                                      opportunity['image_url'] == 'assets/placeholder_image.png') &&
                                  !opportunity['_isImageLoading']) {
                                _fetchImageUrl(opportunity);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: opportunity['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  opportunity['fullname'] ?? 'Unnamed Opportunity',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    opportunity['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, opportunity),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
