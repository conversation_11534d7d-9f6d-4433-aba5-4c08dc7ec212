import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'menu_page.dart';

class ShopEateryDetailPage extends StatefulWidget {
  final Map<String, dynamic> shopEatery;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ShopEateryDetailPage({
    Key? key,
    required this.shopEatery,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ShopEateryDetailPage> createState() => _ShopEateryDetailPageState();
}

class _ShopEateryDetailPageState extends State<ShopEateryDetailPage> {
  late RealtimeChannel _shopEateryRealtimeChannel;
  bool _hasMenus = false;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
    _checkForMenus();
  }

  @override
  void dispose() {
    _shopEateryRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _shopEateryRealtimeChannel = Supabase.instance.client
        .channel('shop_eatery_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'shopsoreateries',
      callback: (payload) async {
        // Manual filtering for the specific shop/eatery
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.shopEatery['id']) {
          print("Realtime update received for shop/eatery detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshShopEatery();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshShopEatery() async {
    try {
      final response = await Supabase.instance.client
          .from('shopsoreateries')
          .select('*')
          .eq('id', widget.shopEatery['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's shopEatery with the new data
          widget.shopEatery.clear();
          widget.shopEatery.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing shop/eatery: $e");
    }
  }

  Future<void> _checkForMenus() async {
    if (widget.shopEatery['type'] == 'Eatery') {
      try {
        final response = await Supabase.instance.client
            .from('menus')
            .select('id')
            .eq('dininglocation', widget.shopEatery['fullname'])
            .limit(1);

        if (mounted) {
          setState(() {
            _hasMenus = response.isNotEmpty;
          });
        }
      } catch (e) {
        print("Error checking for menus: $e");
      }
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      print('Could not launch $emailUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$phoneNumber',
    );
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $whatsappUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.shopEatery['fullname'] ?? 'Unknown';
    final String type = widget.shopEatery['type'] ?? '';
    final String building = widget.shopEatery['building'] ?? '';
    final String room = widget.shopEatery['room'] ?? '';
    final String hours = widget.shopEatery['hours'] ?? '';
    final String payment = widget.shopEatery['payment'] ?? '';
    final String about = widget.shopEatery['about'] ?? '';
    final String imgLink = widget.shopEatery['img_link'] ?? '';
    
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }
    
    // Contact information
    final String phone = widget.shopEatery['phone']?.toString() ?? '';
    final String email = widget.shopEatery['email']?.toString() ?? '';
    final String fax = widget.shopEatery['fax']?.toString() ?? '';
    final String whatsapp = widget.shopEatery['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.shopEatery['latitude'];
    final dynamic longitude = widget.shopEatery['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          if (_hasMenus)
            IconButton(
              icon: Icon(
                Icons.restaurant_menu,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MenuPage(
                      diningLocation: fullname,
                      isDarkMode: currentIsDarkMode,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ),
                );
              },
              tooltip: 'View Menu',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image if available
            if (imgLink.isNotEmpty)
              CachedNetworkImage(
                imageUrl: imgLink,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  height: 200,
                  color: theme.colorScheme.surfaceVariant,
                  child: Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  height: 200,
                  color: theme.colorScheme.surfaceVariant,
                  child: Center(
                    child: Icon(
                      type == 'Shop' ? Icons.shopping_bag : Icons.restaurant,
                      size: 50,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Shop/Eatery details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode 
                                    ? Colors.white.withOpacity(0.1) 
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  type == 'Shop' ? Icons.shopping_bag : Icons.restaurant,
                                  size: 30,
                                  color: currentIsDarkMode ? Colors.white : Colors.black,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      fullname,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    if (type.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          type,
                                          style: TextStyle(
                                            color: theme.colorScheme.primary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    if (locationText.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          locationText,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Hours and payment information
                          if (hours.isNotEmpty)
                            _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                          if (payment.isNotEmpty)
                            _buildDetailRow(theme, Icons.payment, 'Payment Methods', payment),
                          
                          // Contact information
                          if (phone.isNotEmpty)
                            _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                          if (email.isNotEmpty)
                            _buildDetailRow(theme, Icons.email, 'Email', email, canCopy: true),
                          if (fax.isNotEmpty)
                            _buildDetailRow(theme, Icons.fax, 'Fax', fax, canCopy: true),
                          if (whatsapp.isNotEmpty)
                            _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsapp, canCopy: true),
                          
                          // About section
                          if (about.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              'About:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              about,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isEmailAvailable || isWhatsappAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isEmailAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.email,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchEmail(email),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                    if (isWhatsappAvailable)
                      IconButton(
                        icon: FaIcon(
                          FontAwesomeIcons.whatsapp,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchWhatsapp(whatsapp),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
