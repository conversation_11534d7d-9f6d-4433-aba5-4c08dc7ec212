import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class TodoItem {
  String text;
  DateTime createdAt;
  bool isDone;

  TodoItem({required this.text, required this.createdAt, this.isDone = false});

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'createdAt': createdAt.toIso8601String(),
      'isDone': isDone,
    };
  }

  factory TodoItem.fromJson(Map<String, dynamic> json) {
    return TodoItem(
      text: json['text'],
      createdAt: DateTime.parse(json['createdAt']),
      isDone: json['isDone'] ?? false,
    );
  }
}

class TodoApp extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const TodoApp({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<TodoApp> createState() => _TodoAppState();
}

class _TodoAppState extends State<TodoApp> {
  List<TodoItem> _todos = [];
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTodos();
  }

  Future<void> _loadTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedTodos = prefs.getStringList('todos');
    if (encodedTodos != null) {
      setState(() {
        _todos = encodedTodos.where((e) => e != null).map((e) {
          try {
            return TodoItem.fromJson(jsonDecode(e));
          } catch (error) {
            print('Error decoding todo item: $error');
            return null;
          }
        }).whereType<TodoItem>().toList();
      });
    }
  }

  Future<void> _saveTodos() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedTodos = _todos.map((todo) => jsonEncode(todo.toJson())).toList();
    await prefs.setStringList('todos', encodedTodos);
  }

  void _addTodo(String todo) {
    if (todo.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a todo item.')),
      );
      return;
    }
    setState(() {
      _todos.add(TodoItem(text: todo, createdAt: DateTime.now()));
      _controller.clear();
      _saveTodos();
    });
  }

  void _deleteTodo(int index) {
    setState(() {
      _todos.removeAt(index);
      _saveTodos();
    });
  }

  void _updateTodo(int index, String newValue, bool isDone) {
    setState(() {
      _todos[index] = TodoItem(text: newValue, createdAt: _todos[index].createdAt, isDone: isDone);
      _saveTodos();
    });
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final TodoItem item = _todos.removeAt(oldIndex);
      _todos.insert(newIndex, item);
      _saveTodos();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final appBarColor = isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: appBarColor,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Todo List',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              decoration: BoxDecoration(
                color: appBarColor,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(isDarkMode ? 0.1 : 0.2),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        decoration: InputDecoration(
                          hintText: 'Enter a todo',
                          hintStyle: TextStyle(color: theme.hintColor),
                          border: InputBorder.none,
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            _addTodo(value);
                          }
                        },
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.add, color: theme.colorScheme.onSurface),
                      onPressed: () {
                        _addTodo(_controller.text);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ReorderableListView(
              onReorder: _onReorder,
              children: <Widget>[
                for (int index = 0; index < _todos.length; index += 1)
                  Dismissible(
                    key: Key(_todos[index].text),
                    background: Container(color: Colors.red),
                    onDismissed: (direction) {
                      _deleteTodo(index);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${_todos[index].text} deleted')),
                      );
                    },
                    child: Container(
                      key: ValueKey(_todos[index].text), // Important for ReorderableListView
                      color: appBarColor,
                      child: Column(
                        children: [
                          ListTile(
                            leading: Icon(Icons.list, color: theme.colorScheme.onSurface),
                            title: Text(
                              _todos[index].text,
                              style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                decoration: _todos[index].isDone ? TextDecoration.lineThrough : null,
                              ),
                            ),
                            subtitle: Text(
                              'Created on: ${DateFormat('yyyy-MM-dd – kk:mm').format(_todos[index].createdAt)}',
                              style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: Icon(Icons.edit, color: theme.colorScheme.onSurface),
                                  onPressed: () => _showEditTodoDialog(context, index, _todos[index]),
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete, color: theme.colorScheme.onSurface),
                                  onPressed: () => _deleteTodo(index),
                                ),
                              ],
                            ),
                          ),
                          if (index < _todos.length - 1)
                            Divider(
                              height: 0.5,
                              thickness: 0.5,
                              color: isDarkMode ? Colors.grey.shade600 : const Color(0xFFC0C0C0), // Silver color
                            ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showEditTodoDialog(BuildContext context, int index, TodoItem oldTodo) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final appBarColor = isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface;
    final editController = TextEditingController(text: oldTodo.text);
    bool isDone = oldTodo.isDone; // Keep track of the done state locally

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder( // Use StatefulBuilder to manage the checkbox state
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              backgroundColor: appBarColor,
              surfaceTintColor: Colors.transparent,
              title: Text('Edit Todo', style: TextStyle(color: theme.colorScheme.onSurface)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: editController,
                    autofocus: true,
                    style: TextStyle(
                        color: theme.colorScheme.onSurface,
                        decoration: isDone ? TextDecoration.lineThrough : null),
                    decoration: InputDecoration(
                      labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                      enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.grey.shade600 : Colors.grey)),
                      focusedBorder: UnderlineInputBorder(borderSide: BorderSide(color: isDarkMode ? Colors.blueAccent : Colors.blue)),
                    ),
                    onChanged: (newValue) {
                      // Optionally update the strike-through in the dialog as well
                      setState(() {});
                    },
                    onSubmitted: (newValue) {
                      if (newValue.isNotEmpty) {
                        _updateTodo(index, newValue, isDone);
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: isDone,
                        onChanged: (bool? value) {
                          setState(() {
                            isDone = value ?? false;
                          });
                        },
                      ),
                      Text('Done', style: TextStyle(color: theme.colorScheme.onSurface)),
                    ],
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('Cancel', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text('Save', style: TextStyle(color: theme.colorScheme.onSurface)),
                  onPressed: () {
                    final newValue = editController.text;
                    if (newValue.isNotEmpty) {
                      _updateTodo(index, newValue, isDone);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}