import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryFeedbackPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryFeedbackPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryFeedbackPage> createState() => _TertiaryFeedbackPageState();
}

class _TertiaryFeedbackPageState extends State<TertiaryFeedbackPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _feedbackForms = [];
  TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredFeedbackForms = [];

  @override
  void initState() {
    super.initState();
    _fetchFeedbackForms();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchFeedbackForms() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_feedback';

      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final feedbackForms = List<Map<String, dynamic>>.from(response);

      setState(() {
        _feedbackForms = feedbackForms;
        _filteredFeedbackForms = feedbackForms;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading feedback forms: $e';
      });
      print('Error fetching feedback forms: $e');
    }
  }

  void _filterFeedbackForms(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredFeedbackForms = _feedbackForms;
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    setState(() {
      _filteredFeedbackForms = _feedbackForms.where((form) {
        final name = form['fullname']?.toString().toLowerCase() ?? '';
        return name.contains(lowercaseQuery);
      }).toList();
    });
  }

  Future<void> _launchFormUrl(String url) async {
    if (url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No form URL available')),
      );
      return;
    }

    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Feedback Forms',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchFeedbackForms,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search feedback forms...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _filterFeedbackForms('');
                          });
                        },
                      )
                    : null,
              ),
              onChanged: _filterFeedbackForms,
            ),
          ),

          // Feedback forms list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchFeedbackForms,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredFeedbackForms.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.feedback_outlined,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _searchController.text.isEmpty
                                      ? 'No feedback forms available'
                                      : 'No feedback forms match your search',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredFeedbackForms.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            itemBuilder: (context, index) {
                              final form = _filteredFeedbackForms[index];
                              final String formName = form['fullname'] ?? 'Unnamed Form';
                              final String formLink = form['link'] ?? '';

                              return Card(
                                margin: const EdgeInsets.only(bottom: 12.0),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                    child: Icon(
                                      Icons.rate_review,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  title: Text(
                                    formName,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  subtitle: formLink.isNotEmpty
                                      ? Text(
                                          'Tap to open feedback form',
                                          style: TextStyle(
                                            color: theme.colorScheme.primary,
                                          ),
                                        )
                                      : Text(
                                          'No link available',
                                          style: TextStyle(
                                            color: theme.colorScheme.error,
                                          ),
                                        ),
                                  trailing: Icon(
                                    Icons.open_in_new,
                                    color: formLink.isNotEmpty
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.onSurface.withOpacity(0.3),
                                  ),
                                  onTap: () => _launchFormUrl(formLink),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}