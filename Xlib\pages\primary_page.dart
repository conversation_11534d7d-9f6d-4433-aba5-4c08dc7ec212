import 'package:flutter/material.dart';
import 'login_page.dart';

class PrimaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const PrimaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<PrimaryPage> createState() => _PrimaryPageState();
}

class _PrimaryPageState extends State<PrimaryPage> {
  void _navigateToDetail(BuildContext context, String schoolName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold( // Placeholder detail page
          appBar: AppBar(
            title: Text(schoolName),
          ),
          body: Center(
            child: Text('Details for $schoolName'),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    const schools = [
      {'name': 'Lilongwe LEA Primary School', 'location': 'Lilongwe, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Blantyre Primary School', 'location': 'Blantyre, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Zomba CCAP Primary School', 'location': 'Zomba, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Mzuzu Primary School', 'location': 'Mzuzu, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Kasungu LEA Primary School', 'location': 'Kasungu, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Dedza Primary School', 'location': 'Dedza, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Thyolo Primary School', 'location': 'Thyolo, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Mulanje Primary School', 'location': 'Mulanje, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Ntcheu Primary School', 'location': 'Ntcheu, Malawi', 'image': 'assets/placeholder_image.png'},
      {'name': 'Mangochi Primary School', 'location': 'Mangochi, Malawi', 'image': 'assets/placeholder_image.png'},
    ];

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Primary',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          itemCount: schools.length,
          itemBuilder: (context, index) {
            final school = schools[index];
            return Card(
              color: theme.colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              margin: const EdgeInsets.only(bottom: 16),
              child: ListTile(
                contentPadding: const EdgeInsets.all(16),
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.secondary.withOpacity(0.1),
                  child: Image.asset(
                    school['image']!,
                    fit: BoxFit.cover,
                  ),
                ),
                title: Text(
                  school['name']!,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    school['location']!,
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.secondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                onTap: () => _navigateToDetail(context, school['name']!),
              ),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}