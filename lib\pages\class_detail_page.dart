import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';

class ClassDetailPage extends StatelessWidget {
  final Map<String, dynamic> classData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ClassDetailPage({
    Key? key,
    required this.classData,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    
    // Extract class details
    final String className = classData['fullname'] ?? 'Unnamed Class';
    final String instructor = classData['instructor'] ?? 'Not specified';
    final String ta = classData['ta'] ?? 'None';
    final String building = classData['building'] ?? 'Not specified';
    final String room = classData['room'] ?? 'Not specified';
    final String startTime = classData['starttime'] ?? 'Not specified';
    final String endTime = classData['endtime'] ?? 'Not specified';
    final String department = classData['department'] ?? 'Not specified';
    final String major = classData['major'] ?? 'Not specified';
    final String instructionMode = classData['instructionmode'] ?? 'Not specified';
    final String books = classData['books'] ?? 'Not specified';
    final String about = classData['about'] ?? 'No description available';
    final int capacity = classData['enrollmentcapacity'] ?? 0;
    
    // Schedule days
    final bool monday = classData['_mon'] == true;
    final bool tuesday = classData['_tue'] == true;
    final bool wednesday = classData['_wed'] == true;
    final bool thursday = classData['_thur'] == true;
    final bool friday = classData['_fri'] == true;
    final bool saturday = classData['_sat'] == true;
    final bool sunday = classData['_sun'] == true;
    
    // Format schedule days
    final List<String> scheduleDays = [];
    if (monday) scheduleDays.add('Monday');
    if (tuesday) scheduleDays.add('Tuesday');
    if (wednesday) scheduleDays.add('Wednesday');
    if (thursday) scheduleDays.add('Thursday');
    if (friday) scheduleDays.add('Friday');
    if (saturday) scheduleDays.add('Saturday');
    if (sunday) scheduleDays.add('Sunday');
    
    // Contact information
    final String phone = classData['phone'] ?? '';
    final String email = classData['email'] ?? '';
    final String whatsapp = classData['whatsapp'] ?? '';
    
    // Location coordinates
    final double? latitude = classData['latitude'];
    final double? longitude = classData['longitude'];
    final bool hasLocation = latitude != null && longitude != null && 
                            latitude != 0.0 && longitude != 0.0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          className,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Class header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: theme.colorScheme.primary.withOpacity(0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    className,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Instructor: $instructor',
                    style: TextStyle(
                      fontSize: 16,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  if (department.isNotEmpty && department != 'Not specified')
                    Text(
                      'Department: $department',
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  if (major.isNotEmpty && major != 'Not specified')
                    Text(
                      'Major: $major',
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                ],
              ),
            ),
            
            // Schedule information
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Schedule',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.calendar_today, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Text(
                                scheduleDays.isEmpty 
                                  ? 'No scheduled days' 
                                  : scheduleDays.join(', '),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.access_time, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Text(
                                '$startTime - $endTime',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(Icons.location_on, 
                                color: theme.colorScheme.primary),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '$building, Room $room',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (instructionMode.isNotEmpty && 
                              instructionMode != 'Not specified')
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                children: [
                                  Icon(Icons.school, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Mode: $instructionMode',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (capacity > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                children: [
                                  Icon(Icons.people, 
                                    color: theme.colorScheme.primary),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Capacity: $capacity students',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Contact information
            if (phone.isNotEmpty || email.isNotEmpty || whatsapp.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            if (phone.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.phone, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  phone,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('tel:$phone'),
                              ),
                            if (email.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.email, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  email,
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('mailto:$email'),
                              ),
                            if (whatsapp.isNotEmpty)
                              ListTile(
                                leading: Icon(Icons.message, 
                                  color: theme.colorScheme.primary),
                                title: Text(
                                  'WhatsApp: $whatsapp',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                onTap: () => _launchUrl('https://wa.me/$whatsapp'),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Course materials
            if (books.isNotEmpty && books != 'Not specified')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Course Materials',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.book, 
                                  color: theme.colorScheme.primary),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Required Books:',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              books,
                              style: TextStyle(
                                fontSize: 16,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // About/Description
            if (about.isNotEmpty && about != 'No description available')
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About This Class',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Map
            if (hasLocation)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: FlutterMap(
                          options: MapOptions(
                            initialCenter: LatLng(latitude!, longitude!),
                            initialZoom: 15.0,
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.harmonizr.app',
                            ),
                            MarkerLayer(
                              markers: [
                                Marker(
                                  point: LatLng(latitude, longitude),
                                  width: 40,
                                  height: 40,
                                  child: Icon(
                                    Icons.location_on,
                                    color: theme.colorScheme.primary,
                                    size: 40,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
