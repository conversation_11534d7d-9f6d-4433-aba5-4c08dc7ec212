import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'department_detail_page.dart';
import 'login_page.dart';

class DepartmentsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedDepartments;
  final bool isFromDetailPage;

  const DepartmentsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedDepartments,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _DepartmentsPageState createState() => _DepartmentsPageState();
}

class _DepartmentsPageState extends State<DepartmentsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('departments_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _departments = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("DepartmentsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedDepartments != null &&
        widget.preloadedDepartments!.isNotEmpty) {
      setState(() {
        _departments = List.from(widget.preloadedDepartments!);
        _isLoading = false;
      });
    } else {
      _loadDepartmentsFromDatabase();
    }
  }

  void _setupRealtime() {
    final departmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_departments';
    _realtimeChannel = Supabase.instance.client
        .channel('departments_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: departmentsTableName,
      callback: (payload) async {
        print("Realtime update received for departments: ${payload.eventType}");
        _loadDepartmentsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadDepartmentsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _departments = [];
    });

    await _loadMoreDepartments();
  }

  Future<void> _loadMoreDepartments() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final departmentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_departments';
      final response = await Supabase.instance.client
          .from(departmentsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _departments.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading departments: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading departments: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreDepartments();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Departments',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('departments_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _departments.isEmpty && !_isLoading) {
            _loadDepartmentsFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadDepartmentsFromDatabase,
          child: _departments.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No departments found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _departments.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _departments.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildDepartmentCard(
                      _departments[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildDepartmentCard(
    Map<String, dynamic> department,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = department['fullname'] ?? 'Unknown';
    final String building = department['building'] ?? '';
    final String room = department['room'] ?? '';
    final String school = department['school'] ?? '';
    final String about = department['about'] ?? '';

    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DepartmentDetailPage(
                department: department,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.account_balance,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (locationText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          locationText,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    if (school.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'School: $school',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
