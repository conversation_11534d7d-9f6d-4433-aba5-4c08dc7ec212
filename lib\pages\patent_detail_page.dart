// patent_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class PatentDetailPage extends StatefulWidget {
  final Map<String, dynamic> patent;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const PatentDetailPage({
    Key? key,
    required this.patent,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<PatentDetailPage> createState() => _PatentDetailPageState();
}

class _PatentDetailPageState extends State<PatentDetailPage> {
  late RealtimeChannel _patentRealtimeChannel; // Realtime channel for patent updates

  @override
  void initState() {
    super.initState();
    _setupPatentRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _patentRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupPatentRealtimeListener() {
    final patentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
    _patentRealtimeChannel = Supabase.instance.client
        .channel('patent_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: patentsTableName,
          callback: (payload) {
            // Check if this change is for the current patent
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.patent['id']) {
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedPatentData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the patent is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedPatentData() async {
    try {
      final patentsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_patents';
      final response = await Supabase.instance.client
          .from(patentsTableName)
          .select('*')
          .eq('id', widget.patent['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedPatent = Map.from(response);
        // Update the widget.patent with the new data
        setState(() {
          widget.patent.clear(); // Clear old data
          widget.patent.addAll(updatedPatent); // Add updated data
          print("Patent data updated in detail page for ${widget.patent['fullname']}");
          _updatePatentsCache(updatedPatent); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated patent data: $e');
    }
  }

  Future<void> _updatePatentsCache(Map<String, dynamic> updatedPatent) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'patents_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedPatents = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific patent in the cache
        bool found = false;
        for (var patent in cachedPatents) {
          if (patent['id'] == updatedPatent['id']) {
            updatedCache.add(updatedPatent);
            found = true;
          } else {
            updatedCache.add(Map<String, dynamic>.from(patent));
          }
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
      }
    } catch (e) {
      print('Error updating patents cache: $e');
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = widget.patent['fullname'] as String? ?? 'Patent';
    final String year = widget.patent['year']?.toString() ?? 'N/A';
    final String inventor = widget.patent['inventor'] as String? ?? '';
    final String inventor2 = widget.patent['inventor2'] as String? ?? '';
    final String inventor3 = widget.patent['inventor3'] as String? ?? '';
    final bool facultyOrStaffPatent = widget.patent['facultyorstaffpatent'] as bool? ?? false;
    final bool studentPatent = widget.patent['studentpatent'] as bool? ?? false;
    final String about = widget.patent['about'] as String? ?? '';
    final String link = widget.patent['link'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Patent Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Year: $year',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Inventor(s):',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (inventor.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            inventor,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (inventor2.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            inventor2,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (inventor3.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            inventor3,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        children: [
                          if (facultyOrStaffPatent)
                            Chip(
                              label: const Text('Faculty/Staff Patent'),
                              backgroundColor: theme.colorScheme.primaryContainer,
                            ),
                          if (studentPatent)
                            Chip(
                              label: const Text('Student Patent'),
                              backgroundColor: theme.colorScheme.secondaryContainer,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (about.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (link.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Link',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.primary,
                                  decoration: TextDecoration.underline,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _copyToClipboard(link),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _launchURL(link),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
