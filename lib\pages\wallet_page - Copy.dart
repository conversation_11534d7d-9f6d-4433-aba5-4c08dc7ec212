// lib/pages/wallet_page.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:intl/intl.dart';
import 'package:crypto/crypto.dart';
import 'package:photo_view/photo_view.dart';

class WalletItem {
  String name;
  String category;
  String? frontImagePath;
  String? backImagePath;
  DateTime createdAt;
  String? pin;

  WalletItem({
    required this.name,
    required this.category,
    this.frontImagePath,
    this.backImagePath,
    required this.createdAt,
    this.pin,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'category': category,
        'frontImagePath': frontImagePath,
        'backImagePath': backImagePath,
        'createdAt': createdAt.toIso8601String(),
        'pin': pin,
      };

  factory WalletItem.fromJson(Map<String, dynamic> json) => WalletItem(
        name: json['name'],
        category: json['category'],
        frontImagePath: json['frontImagePath'],
        backImagePath: json['backImagePath'],
        createdAt: DateTime.parse(json['createdAt']),
        pin: json['pin'],
      );
}

class WalletPage extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isDarkMode;
  const WalletPage({Key? key, required this.toggleTheme, required this.isDarkMode}) : super(key: key);

  @override
  State<WalletPage> createState() => _WalletPageState();
}

class _WalletPageState extends State<WalletPage> {
  // ... (rest of your _WalletPageState code is the same)
  List<WalletItem> _walletItems = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadWalletItems();
  }

  Future<void> _loadWalletItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? encodedItems = prefs.getStringList('wallet_items');
    if (encodedItems != null) {
      setState(() {
        _walletItems = encodedItems.map((item) => WalletItem.fromJson(jsonDecode(item))).toList();
      });
    }
  }

  Future<void> _saveWalletItems() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> encodedItems = _walletItems.map((item) => jsonEncode(item.toJson())).toList();
    await prefs.setStringList('wallet_items', encodedItems);
  }

  Future<bool> _authenticateItem(String inputPin, String? storedPin) async {
    if (storedPin == null || storedPin.isEmpty) {
      return true; // No PIN set, consider authenticated
    }
    final inputHash = sha256.convert(utf8.encode(inputPin)).toString();
    return inputHash == storedPin;
  }

  void _showItemPasswordDialog({required VoidCallback onVerified, String? storedPin}) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;
    final passwordController = TextEditingController();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text('Enter Item PIN', style: TextStyle(color: theme.colorScheme.onSurface)),
          content: TextField(
            controller: passwordController,
            keyboardType: TextInputType.number,
            obscureText: true,
            style: TextStyle(color: theme.colorScheme.onSurface),
            decoration: InputDecoration(
              labelText: 'PIN',
              labelStyle: TextStyle(color: theme.colorScheme.onSurface),
            ),
            onSubmitted: (value) async {
              if (await _authenticateItem(value, storedPin)) {
                Navigator.of(context).pop();
                onVerified();
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Incorrect PIN.')),
                );
              }
            },
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Verify', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
              onPressed: () async {
                if (await _authenticateItem(passwordController.text, storedPin)) {
                  Navigator.of(context).pop();
                  onVerified();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Incorrect PIN.')),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _addItem() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditWalletItemPage(
          isDarkMode: widget.isDarkMode,
          onSave: (newItem) {
            setState(() {
              _walletItems.add(newItem);
              _saveWalletItems();
            });
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  void _editItem(int index) {
    _showItemPasswordDialog(
      onVerified: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AddEditWalletItemPage(
              item: _walletItems[index],
              isDarkMode: widget.isDarkMode,
              onSave: (updatedItem) {
                setState(() {
                  _walletItems[index] = updatedItem;
                  _saveWalletItems();
                });
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
      storedPin: _walletItems[index].pin,
    );
  }

  void _deleteItem(int index) {
    setState(() {
      _walletItems.removeAt(index);
      _saveWalletItems();
    });
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final WalletItem item = _walletItems.removeAt(oldIndex);
      _walletItems.insert(newIndex, item);
      _saveWalletItems();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Wallet',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        // tag: 'walletPageFab', // ADDED: Unique tag for FloatingActionButton - Remove if using older Flutter versions
        onPressed: () => _addItem(),
        backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
        foregroundColor: isDarkMode ? Colors.white : Colors.black,
        shape: const CircleBorder(),
        child: Icon(Icons.add, color: isDarkMode ? Colors.white : Colors.black),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      body: _walletItems.isEmpty
          ? Center(child: Text('Your wallet is empty.', style: TextStyle(color: theme.colorScheme.onSurface)))
          : ReorderableListView.builder(
              onReorder: _onReorder,
              itemCount: _walletItems.length,
              itemBuilder: (context, index) {
                final item = _walletItems[index];
                return Dismissible(
                  key: Key(item.name),
                  background: Container(color: Colors.red),
                  onDismissed: (direction) {
                    _deleteItem(index);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('${item.name} deleted')),
                    );
                  },
                  child: Container(
                    key: ValueKey(item.name),
                    color: isDarkMode ? const Color(0xFF202020) : Colors.white,
                    child: ListTile(
                      onTap: () {
                        if (item.pin != null && item.pin!.isNotEmpty) {
                          _showItemPasswordDialog(
                            onVerified: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => ViewWalletItemPage(item: item),
                                ),
                              );
                            },
                            storedPin: item.pin,
                          );
                        } else {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => ViewWalletItemPage(item: item),
                            ),
                          );
                        }
                      },
                      leading: item.frontImagePath != null
                          ? SizedBox(
                              width: 40,
                              height: 40,
                              child: Image.file(
                                File(item.frontImagePath!),
                                fit: BoxFit.cover,
                              ),
                            )
                          : Icon(Icons.credit_card, color: theme.colorScheme.onSurface),
                      title: Text(
                        item.name,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                      ),
                      subtitle: Text(
                        'Created on: ${DateFormat('yyyy-MM-dd').format(item.createdAt)}',
                        style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.edit),
                        color: theme.colorScheme.onSurface,
                        onPressed: () => _editItem(index),
                      ),
                    ),
                  ),
                );
              },
            ),
    );
  }
}

class AddEditWalletItemPage extends StatefulWidget {
  final WalletItem? item;
  final Function(WalletItem) onSave;
  final bool isDarkMode;

  const AddEditWalletItemPage({Key? key, this.item, required this.onSave, required this.isDarkMode}) : super(key: key);

  @override
  State<AddEditWalletItemPage> createState() => _AddEditWalletItemPageState();
}

class _AddEditWalletItemPageState extends State<AddEditWalletItemPage> {
  // ... (rest of your _AddEditWalletItemPageState code is the same)
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  String? _selectedCategory;
  String? _frontImagePath;
  String? _backImagePath;
  final ImagePicker _picker = ImagePicker();

  List<String> categories = [
    'Payment card',
    'Loyalty card',
    'Transit card',
    'Gift card',
    'ID card',
    'Key',
    'Ticket',
    'Pass',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _nameController.text = widget.item!.name;
      _selectedCategory = widget.item!.category;
      _frontImagePath = widget.item!.frontImagePath;
      _backImagePath = widget.item!.backImagePath;
      _pinController.text = widget.item?.pin ?? '';
    }
  }

  Future<void> _pickImage(ImageSource source, bool isFront) async {
    final pickedFile = await _picker.pickImage(source: source);
    if (pickedFile != null) {
      setState(() {
        if (isFront) {
          _frontImagePath = pickedFile.path;
        } else {
          _backImagePath = pickedFile.path;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = widget.isDarkMode;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(widget.item == null ? 'Add Wallet Item' : 'Edit Wallet Item', style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: Container(
        color: isDarkMode ? const Color(0xFF090909) : Colors.white,
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(labelText: 'Name', labelStyle: TextStyle(color: theme.colorScheme.onSurface)),
                validator: (value) => value == null || value.isEmpty ? 'Please enter a name' : null,
                style: TextStyle(color: theme.colorScheme.onSurface),
              ),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: InputDecoration(labelText: 'Category', labelStyle: TextStyle(color: theme.colorScheme.onSurface)),
                items: categories.map((String category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category, style: TextStyle(color: theme.colorScheme.onSurface)),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
                validator: (value) => value == null ? 'Please select a category' : null,
              ),
              TextFormField(
                controller: _pinController,
                keyboardType: TextInputType.number,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: 'Set PIN (Optional)',
                  labelStyle: TextStyle(color: theme.colorScheme.onSurface),
                  hintText: 'Enter a PIN to protect this item',
                  hintStyle: TextStyle(color: theme.hintColor),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Text('Front Image', style: TextStyle(color: theme.colorScheme.onSurface)),
                      _frontImagePath != null
                          ? GestureDetector(
                              onTap: () => _pickImage(ImageSource.camera, true),
                              child: Image.file(File(_frontImagePath!), height: 100),
                            )
                          : IconButton(
                              icon: Icon(Icons.camera_alt, color: theme.colorScheme.onSurface),
                              onPressed: () => _pickImage(ImageSource.camera, true),
                            ),
                      IconButton(
                        icon: Icon(Icons.photo_library, color: theme.colorScheme.onSurface),
                        onPressed: () => _pickImage(ImageSource.gallery, true),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text('Back Image', style: TextStyle(color: theme.colorScheme.onSurface)),
                      _backImagePath != null
                          ? GestureDetector(
                              onTap: () => _pickImage(ImageSource.camera, false),
                              child: Image.file(File(_backImagePath!), height: 100),
                            )
                          : IconButton(
                              icon: Icon(Icons.camera_alt, color: theme.colorScheme.onSurface),
                              onPressed: () => _pickImage(ImageSource.camera, false),
                            ),
                      IconButton(
                        icon: Icon(Icons.photo_library, color: theme.colorScheme.onSurface),
                        onPressed: () => _pickImage(ImageSource.gallery, false),
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    final pinHash = _pinController.text.isNotEmpty
                        ? sha256.convert(utf8.encode(_pinController.text)).toString()
                        : null;
                    final newItem = WalletItem(
                      name: _nameController.text,
                      category: _selectedCategory!,
                      frontImagePath: _frontImagePath,
                      backImagePath: _backImagePath,
                      createdAt: widget.item?.createdAt ?? DateTime.now(),
                      pin: pinHash,
                    );
                    widget.onSave(newItem);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDarkMode ? const Color(0xFF202020) : Colors.white,
                  foregroundColor: isDarkMode ? Colors.white : Colors.black,
                ),
                child: Text('Save', style: TextStyle(color: isDarkMode ? Colors.white : Colors.black)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ViewWalletItemPage extends StatefulWidget {
  final WalletItem item;

  const ViewWalletItemPage({Key? key, required this.item}) : super(key: key);

  @override
  State<ViewWalletItemPage> createState() => _ViewWalletItemPageState();
}

class _ViewWalletItemPageState extends State<ViewWalletItemPage> {
  final PageController _pageController = PageController(initialPage: 0);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: isDarkMode ? const Color(0xFF202020) : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        title: Text(widget.item.name, style: TextStyle(color: theme.colorScheme.onSurface)),
      ),
      body: PageView(
        controller: _pageController,
        children: [
          if (widget.item.frontImagePath != null)
            InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.file(File(widget.item.frontImagePath!)),
            ),
          if (widget.item.backImagePath != null)
            InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.file(File(widget.item.backImagePath!)),
            ),
        ],
      ),
    );
  }
}