import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'minor_detail_page.dart';
import 'login_page.dart';

class MinorsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedMinors;
  final bool isFromDetailPage;

  const MinorsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedMinors,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MinorsPageState createState() => _MinorsPageState();
}

class _MinorsPageState extends State<MinorsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('minors_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _minors = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedSchool = 'All Schools';
  List<String> _schools = ['All Schools'];
  String _selectedDepartment = 'All Departments';
  List<String> _departments = ['All Departments'];

  @override
  void initState() {
    super.initState();
    print("MinorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedMinors != null &&
        widget.preloadedMinors!.isNotEmpty) {
      setState(() {
        _minors = List.from(widget.preloadedMinors!);
        _isLoading = false;
        _extractSchoolsAndDepartments();
      });
    } else {
      _loadMinorsFromDatabase();
    }
  }

  void _extractSchoolsAndDepartments() {
    Set<String> schoolsSet = {'All Schools'};
    Set<String> departmentsSet = {'All Departments'};

    for (var minor in _minors) {
      // Extract schools
      if (minor['school'] != null && minor['school'].toString().isNotEmpty) {
        schoolsSet.add(minor['school']);
      }
      if (minor['school2'] != null && minor['school2'].toString().isNotEmpty) {
        schoolsSet.add(minor['school2']);
      }
      if (minor['school3'] != null && minor['school3'].toString().isNotEmpty) {
        schoolsSet.add(minor['school3']);
      }

      // Extract departments
      if (minor['department'] != null && minor['department'].toString().isNotEmpty) {
        departmentsSet.add(minor['department']);
      }
      if (minor['department2'] != null && minor['department2'].toString().isNotEmpty) {
        departmentsSet.add(minor['department2']);
      }
      if (minor['department3'] != null && minor['department3'].toString().isNotEmpty) {
        departmentsSet.add(minor['department3']);
      }
    }

    setState(() {
      _schools = schoolsSet.toList()..sort();
      _departments = departmentsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final minorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_minors';
    _realtimeChannel = Supabase.instance.client
        .channel('minors_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: minorsTableName,
      callback: (payload) async {
        print("Realtime update received for minors: ${payload.eventType}");
        _loadMinorsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadMinorsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _minors = [];
    });

    await _loadMoreMinors();
  }

  Future<void> _loadMoreMinors() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final minorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_minors';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply school filter
      if (_selectedSchool != 'All Schools') {
        conditions.add("or(school.eq.${_selectedSchool},school2.eq.${_selectedSchool},school3.eq.${_selectedSchool})");
      }

      // Apply department filter
      if (_selectedDepartment != 'All Departments') {
        conditions.add("or(department.eq.${_selectedDepartment},department2.eq.${_selectedDepartment},department3.eq.${_selectedDepartment})");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(minorsTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _minors.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractSchoolsAndDepartments();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading minors: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading minors: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreMinors();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadMinorsFromDatabase();
  }

  void _filterBySchool(String school) {
    setState(() {
      _selectedSchool = school;
    });
    _loadMinorsFromDatabase();
  }

  void _filterByDepartment(String department) {
    setState(() {
      _selectedDepartment = department;
    });
    _loadMinorsFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Minors',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search minors...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'School',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: theme.colorScheme.surface,
                    ),
                    value: _selectedSchool,
                    items: _schools.map((String school) {
                      return DropdownMenuItem<String>(
                        value: school,
                        child: Text(
                          school,
                          style: TextStyle(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _filterBySchool(newValue);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'Department',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: theme.colorScheme.surface,
                    ),
                    value: _selectedDepartment,
                    items: _departments.map((String department) {
                      return DropdownMenuItem<String>(
                        value: department,
                        child: Text(
                          department,
                          style: TextStyle(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _filterByDepartment(newValue);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Minors list
          Expanded(
            child: VisibilityDetector(
              key: const Key('minors_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _minors.isEmpty && !_isLoading) {
                  _loadMinorsFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadMinorsFromDatabase,
                child: _minors.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No minors found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _minors.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _minors.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildMinorCard(
                            _minors[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildMinorCard(
    Map<String, dynamic> minor,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = minor['fullname'] ?? 'Unknown';
    final String department = minor['department'] ?? '';
    final String school = minor['school'] ?? '';
    final String duration = minor['duration'] ?? '';
    final String about = minor['about'] ?? '';
    final bool isAccredited = minor['accredited'] == true;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MinorDetailPage(
                minor: minor,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            fullname,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isAccredited)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Accredited',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onPrimaryContainer,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (school.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'School: $school',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (duration.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Duration: $duration',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
