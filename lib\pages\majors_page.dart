import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'major_detail_page.dart';
import 'login_page.dart';

class MajorsPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedMajors;
  final bool isFromDetailPage;

  const MajorsPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedMajors,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _MajorsPageState createState() => _MajorsPageState();
}

class _MajorsPageState extends State<MajorsPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('majors_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _majors = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedSchool = 'All Schools';
  List<String> _schools = ['All Schools'];
  String _selectedDepartment = 'All Departments';
  List<String> _departments = ['All Departments'];

  @override
  void initState() {
    super.initState();
    print("MajorsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedMajors != null &&
        widget.preloadedMajors!.isNotEmpty) {
      setState(() {
        _majors = List.from(widget.preloadedMajors!);
        _isLoading = false;
        _extractSchoolsAndDepartments();
      });
    } else {
      _loadMajorsFromDatabase();
    }
  }

  void _extractSchoolsAndDepartments() {
    Set<String> schoolsSet = {'All Schools'};
    Set<String> departmentsSet = {'All Departments'};

    for (var major in _majors) {
      // Extract schools
      if (major['school'] != null && major['school'].toString().isNotEmpty) {
        schoolsSet.add(major['school']);
      }
      if (major['school2'] != null && major['school2'].toString().isNotEmpty) {
        schoolsSet.add(major['school2']);
      }
      if (major['school3'] != null && major['school3'].toString().isNotEmpty) {
        schoolsSet.add(major['school3']);
      }

      // Extract departments
      if (major['department'] != null && major['department'].toString().isNotEmpty) {
        departmentsSet.add(major['department']);
      }
      if (major['department2'] != null && major['department2'].toString().isNotEmpty) {
        departmentsSet.add(major['department2']);
      }
      if (major['department3'] != null && major['department3'].toString().isNotEmpty) {
        departmentsSet.add(major['department3']);
      }
    }

    setState(() {
      _schools = schoolsSet.toList()..sort();
      _departments = departmentsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final majorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_majors';
    _realtimeChannel = Supabase.instance.client
        .channel('majors_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: majorsTableName,
      callback: (payload) async {
        print("Realtime update received for majors: ${payload.eventType}");
        _loadMajorsFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadMajorsFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _majors = [];
    });

    await _loadMoreMajors();
  }

  Future<void> _loadMoreMajors() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final majorsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_majors';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply school filter
      if (_selectedSchool != 'All Schools') {
        conditions.add("or(school.eq.${_selectedSchool},school2.eq.${_selectedSchool},school3.eq.${_selectedSchool})");
      }

      // Apply department filter
      if (_selectedDepartment != 'All Departments') {
        conditions.add("or(department.eq.${_selectedDepartment},department2.eq.${_selectedDepartment},department3.eq.${_selectedDepartment})");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(majorsTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _majors.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractSchoolsAndDepartments();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading majors: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading majors: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreMajors();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadMajorsFromDatabase();
  }

  void _filterBySchool(String school) {
    setState(() {
      _selectedSchool = school;
    });
    _loadMajorsFromDatabase();
  }

  void _filterByDepartment(String department) {
    setState(() {
      _selectedDepartment = department;
    });
    _loadMajorsFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Majors',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search majors...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'School',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: theme.colorScheme.surface,
                    ),
                    value: _selectedSchool,
                    items: _schools.map((String school) {
                      return DropdownMenuItem<String>(
                        value: school,
                        child: Text(
                          school,
                          style: TextStyle(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _filterBySchool(newValue);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      labelText: 'Department',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: theme.colorScheme.surface,
                    ),
                    value: _selectedDepartment,
                    items: _departments.map((String department) {
                      return DropdownMenuItem<String>(
                        value: department,
                        child: Text(
                          department,
                          style: TextStyle(fontSize: 14),
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _filterByDepartment(newValue);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Majors list
          Expanded(
            child: VisibilityDetector(
              key: const Key('majors_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _majors.isEmpty && !_isLoading) {
                  _loadMajorsFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadMajorsFromDatabase,
                child: _majors.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No majors found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _majors.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _majors.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildMajorCard(
                            _majors[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildMajorCard(
    Map<String, dynamic> major,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = major['fullname'] ?? 'Unknown';
    final String department = major['department'] ?? '';
    final String school = major['school'] ?? '';
    final String duration = major['duration'] ?? '';
    final String about = major['about'] ?? '';
    final bool isAccredited = major['accredited'] == true;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MajorDetailPage(
                major: major,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.school,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            fullname,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isAccredited)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Accredited',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onPrimaryContainer,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (department.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Department: $department',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (school.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'School: $school',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (duration.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Duration: $duration',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
