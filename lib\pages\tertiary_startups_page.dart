import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'accelerators_page.dart';
import 'makerspaces_page.dart';
import 'startupfunds_page.dart';
import 'startups_page.dart';

class TertiaryStartupsPage extends StatefulWidget {
  final Map<String, dynamic>? collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage; // Added isFromDetailPage

  const TertiaryStartupsPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false, // Initialize isFromDetailPage
  }) : super(key: key);

  @override
  State<TertiaryStartupsPage> createState() => _TertiaryStartupsPageState();



}

class _TertiaryStartupsPageState extends State<TertiaryStartupsPage> {
  // Data for each section
  List<Map<String, dynamic>> _accelerators = [];
  List<Map<String, dynamic>> _makerspaces = [];
  List<Map<String, dynamic>> _startupFunds = [];
  List<Map<String, dynamic>> _startups = [];

  // Loading states
  bool _isLoadingAccelerators = false;
  bool _isLoadingMakerspaces = false;
  bool _isLoadingStartupFunds = false;
  bool _isLoadingStartups = false;

  // Realtime channels
  late final RealtimeChannel _acceleratorsChannel;
  late final RealtimeChannel _makerspacesChannel;
  late final RealtimeChannel _startupFundsChannel;
  late final RealtimeChannel _startupsChannel;

  @override
  void initState() {
    super.initState();
    _preloadAndCacheData();
    _setupRealtimeChannels();
  }

  @override
  void dispose() {
    _acceleratorsChannel.unsubscribe();
    _makerspacesChannel.unsubscribe();
    _startupFundsChannel.unsubscribe();
    _startupsChannel.unsubscribe();
    super.dispose();
  }

  Future<void> _preloadAndCacheData() async {
    await Future.wait([
      _preloadAccelerators(),
      _preloadMakerspaces(),
      _preloadStartupFunds(),
      _preloadStartups(),
    ]);
  }

  Future<void> _preloadAccelerators() async {
    if (_isLoadingAccelerators) return;
    setState(() => _isLoadingAccelerators = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('accelerators');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _accelerators = cachedData;
          _isLoadingAccelerators = false;
        });
      }

      // Then fetch from Supabase
      final acceleratorsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_accelerators';
      final response = await Supabase.instance.client
          .from(acceleratorsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final accelerators = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('accelerators', accelerators);

      setState(() {
        _accelerators = accelerators;
        _isLoadingAccelerators = false;
      });
    } catch (e) {
      print('Error preloading accelerators: $e');
      setState(() => _isLoadingAccelerators = false);
    }
  }

  Future<void> _preloadMakerspaces() async {
    if (_isLoadingMakerspaces) return;
    setState(() => _isLoadingMakerspaces = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('makerspaces');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _makerspaces = cachedData;
          _isLoadingMakerspaces = false;
        });
      }

      // Then fetch from Supabase
      final makerspacesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_makerspaces';
      final response = await Supabase.instance.client
          .from(makerspacesTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final makerspaces = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('makerspaces', makerspaces);

      setState(() {
        _makerspaces = makerspaces;
        _isLoadingMakerspaces = false;
      });
    } catch (e) {
      print('Error preloading makerspaces: $e');
      setState(() => _isLoadingMakerspaces = false);
    }
  }

  Future<void> _preloadStartupFunds() async {
    if (_isLoadingStartupFunds) return;
    setState(() => _isLoadingStartupFunds = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('startupfunds');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _startupFunds = cachedData;
          _isLoadingStartupFunds = false;
        });
      }

      // Then fetch from Supabase
      final startupFundsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_startupfunds';
      final response = await Supabase.instance.client
          .from(startupFundsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final startupFunds = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('startupfunds', startupFunds);

      setState(() {
        _startupFunds = startupFunds;
        _isLoadingStartupFunds = false;
      });
    } catch (e) {
      print('Error preloading startup funds: $e');
      setState(() => _isLoadingStartupFunds = false);
    }
  }

  Future<void> _preloadStartups() async {
    if (_isLoadingStartups) return;
    setState(() => _isLoadingStartups = true);

    try {
      // Try to load from cache first
      final cachedData = await _loadFromCache('startups');
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _startups = cachedData;
          _isLoadingStartups = false;
        });
      }

      // Then fetch from Supabase
      final startupsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_startups';
      final response = await Supabase.instance.client
          .from(startupsTableName)
          .select('*')
          .order('fullname', ascending: true)
          .limit(20);

      final startups = List<Map<String, dynamic>>.from(response);

      // Cache the data
      await _saveToCache('startups', startups);

      setState(() {
        _startups = startups;
        _isLoadingStartups = false;
      });
    } catch (e) {
      print('Error preloading startups: $e');
      setState(() => _isLoadingStartups = false);
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache(String tableName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        return decoded.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      print('Error loading $tableName from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(String tableName, List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${tableName}_${widget.institutionName.toLowerCase().replaceAll(' ', '')}';
      await prefs.setString(cacheKey, jsonEncode(data));
    } catch (e) {
      print('Error saving $tableName to cache: $e');
    }
  }

  void _setupRealtimeChannels() {
    _setupAcceleratorsRealtimeChannel();
    _setupMakerspacesRealtimeChannel();
    _setupStartupFundsRealtimeChannel();
    _setupStartupsRealtimeChannel();
  }

  void _setupAcceleratorsRealtimeChannel() {
    final acceleratorsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_accelerators';
    _acceleratorsChannel = Supabase.instance.client
        .channel('accelerators_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: acceleratorsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadAccelerators();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadAccelerators();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadAccelerators();
            }
          },
        )
        .subscribe();
  }

  void _setupMakerspacesRealtimeChannel() {
    final makerspacesTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_makerspaces';
    _makerspacesChannel = Supabase.instance.client
        .channel('makerspaces_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: makerspacesTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadMakerspaces();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadMakerspaces();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadMakerspaces();
            }
          },
        )
        .subscribe();
  }

  void _setupStartupFundsRealtimeChannel() {
    final startupFundsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_startupfunds';
    _startupFundsChannel = Supabase.instance.client
        .channel('startupfunds_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: startupFundsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadStartupFunds();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadStartupFunds();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadStartupFunds();
            }
          },
        )
        .subscribe();
  }

  void _setupStartupsRealtimeChannel() {
    final startupsTableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_startups';
    _startupsChannel = Supabase.instance.client
        .channel('startups_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: startupsTableName,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              await _preloadStartups();
            } else if (payload.eventType == PostgresChangeEvent.update) {
              await _preloadStartups();
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              await _preloadStartups();
            }
          },
        )
        .subscribe();
  }

  Widget _buildGridItem(
    BuildContext context,
    String title,
    IconData icon,
    ThemeData theme,
    bool isFromDetailPage,
  ) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    void navigateToPage() {
      if (title == 'Accelerators') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AcceleratorsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedAccelerators: _accelerators,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Makerspaces') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MakerspacesPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedMakerspaces: _makerspaces,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Startup Funds') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => StartupFundsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedStartupFunds: _startupFunds,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      } else if (title == 'Startups') {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => StartupsPage(
              collegeNameForTable: widget.institutionName,
              isDarkMode: isDarkMode,
              toggleTheme: widget.toggleTheme,
              preloadedStartups: _startups,
              isFromDetailPage: isFromDetailPage,
            ),
          ),
        );
      }
    }

    return Card(
      key: Key('startups_grid_item_$title'),
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: navigateToPage,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    // Define all grid items with their title and icon.
    final List<Map<String, dynamic>> gridItems = [
      {'title': 'Accelerators', 'icon': Icons.rocket_launch},
      {'title': 'Makerspaces', 'icon': Icons.build},
      {'title': 'Startup Funds', 'icon': Icons.attach_money},
      {'title': 'Startups', 'icon': Icons.business},
    ];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Startups & Innovation',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 4;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: gridItems
                  .map((item) => _buildGridItem(
                        context,
                        item['title'] as String,
                        item['icon'] as IconData,
                        theme,
                        isFromDetailPage,
                      ))
                  .toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
