// periodical_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class PeriodicalDetailPage extends StatefulWidget {
  final Map<String, dynamic> periodical;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const PeriodicalDetailPage({
    Key? key,
    required this.periodical,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<PeriodicalDetailPage> createState() => _PeriodicalDetailPageState();
}

class _PeriodicalDetailPageState extends State<PeriodicalDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _periodicalRealtimeChannel; // Realtime channel for periodical updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupPeriodicalRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _periodicalRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.periodical['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }

  void _setupPeriodicalRealtimeListener() {
    final periodicalsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_periodicals';
    _periodicalRealtimeChannel = Supabase.instance.client
        .channel('periodical_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: periodicalsTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current periodical's ID
        if (payload.newRecord['id'] == widget.periodical['id']) {
          print("Realtime UPDATE event received for THIS periodical (manual filter applied): ${widget.periodical['fullname']}");
          _fetchUpdatedPeriodicalData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER periodical, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedPeriodicalData() async {
    final periodicalsTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_periodicals';
    try {
      final updatedPeriodicalResponse = await Supabase.instance.client
          .from(periodicalsTableName)
          .select('*')
          .eq('id', widget.periodical['id'])
          .single();

      if (mounted && updatedPeriodicalResponse != null) {
        Map<String, dynamic> updatedPeriodical = Map.from(updatedPeriodicalResponse);
        // Update the widget.periodical with the new data
        setState(() {
          widget.periodical.clear(); // Clear old data
          widget.periodical.addAll(updatedPeriodical); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Periodical data updated in detail page for ${widget.periodical['fullname']}");
          _updatePeriodicalsCache(updatedPeriodical); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated periodical data: $error");
    }
  }

  Future<void> _updatePeriodicalsCache(Map<String, dynamic> updatedPeriodical) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'periodicals_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedPeriodicalsJson = prefs.getString(cacheKey);

    if (cachedPeriodicalsJson != null) {
      List<Map<String, dynamic>> cachedPeriodicals = (jsonDecode(cachedPeriodicalsJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the periodical in the cached list
      for (int i = 0; i < cachedPeriodicals.length; i++) {
        if (cachedPeriodicals[i]['id'] == updatedPeriodical['id']) {
          cachedPeriodicals[i] = updatedPeriodical;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedPeriodicals));
      print("Periodicals cache updated with realtime change for ${updatedPeriodical['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.periodical['phone'] as String? ?? '';
    final whatsappNumber = widget.periodical['whatsapp'] as String? ?? '';
    final email = widget.periodical['email'] as String? ?? '';
    final about = widget.periodical['about'] as String? ?? '';

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.periodical['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _isLoadingImage
                      ? const SizedBox(height: 250, child: Center(child: CircularProgressIndicator()))
                      : CachedNetworkImage(
                    imageUrl: _imageUrl,
                    placeholder: (context, url) => Image.asset(
                      'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 150,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                      fit: BoxFit.contain,
                      width:150,
                      height: 250,
                    ),
                    fit: BoxFit.cover,
                    height: 250,
                  ),
                  const SizedBox(height: 16),
                  if (about.isNotEmpty)
                    _buildDetailRow(theme, Icons.info_outline, 'About', about),
                  if (email.isNotEmpty)
                    _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                  if (phone.isNotEmpty)
                    _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                  if (whatsappNumber.isNotEmpty)
                    _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isWhatsappAvailable ? 1.0 : 0.5,
                    child: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email' || title == 'WhatsApp';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
