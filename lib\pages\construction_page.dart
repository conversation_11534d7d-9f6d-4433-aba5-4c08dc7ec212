import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';
// import 'dart:io'; // Not explicitly used, can be removed if not needed elsewhere

import 'construction_detail_page.dart';
import 'login_page.dart'; // For bottom navigation

class ConstructionPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedConstructionProjects;
  final bool isFromDetailPage;

  const ConstructionPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedConstructionProjects,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ConstructionPageState createState() => _ConstructionPageState();
}

class _ConstructionPageState extends State<ConstructionPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('construction_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _constructionProjects = [];
  bool _isLoading = false;
  RealtimeChannel? _realtimeChannel; // Made nullable
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  String get _constructionTableName =>
      '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_construction';

  @override
  void initState() {
    super.initState();
    print("ConstructionPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel?.unsubscribe();
    print("ConstructionPage dispose called");
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedConstructionProjects != null &&
        widget.preloadedConstructionProjects!.isNotEmpty &&
        !widget.isFromDetailPage) { // Only use preloaded if not coming back from detail
      print("Preloaded construction projects found, using them.");
      setState(() {
        _constructionProjects = List<Map<String, dynamic>>.from(widget.preloadedConstructionProjects!);
        // Sort if necessary, e.g., by startdate
         _constructionProjects.sort((a, b) {
            final DateTime? dateA = a['startdate'] != null ? DateTime.tryParse(a['startdate']) : null;
            final DateTime? dateB = b['startdate'] != null ? DateTime.tryParse(b['startdate']) : null;
            if (dateA == null && dateB == null) return 0;
            if (dateA == null) return 1; // nulls last
            if (dateB == null) return -1; // nulls last
            return dateB.compareTo(dateA); // descending
        });
        _hasMore = widget.preloadedConstructionProjects!.length >= _pageSize; // Check if there might be more
        _page = (_constructionProjects.length / _pageSize).ceil(); // Estimate current page
      });
      // Optionally, trigger a background fetch to ensure data is fresh if preloaded is stale
      // _loadMoreConstructionProjects(isRefresh: true); // Example: force a check for new data
    } else {
      print("No preloaded projects or coming from detail page, loading from database.");
      await _refreshProjects();
    }
  }

  void _setupRealtime() {
    // Ensure channel is only created once or properly managed if collegeNameForTable can change
    if (_realtimeChannel != null) {
      _realtimeChannel!.unsubscribe();
    }
    _realtimeChannel = Supabase.instance.client
        .channel('public:$_constructionTableName')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: _constructionTableName,
      callback: (payload) async {
        if (_isDisposed) return;
        print("Realtime update received for construction projects: ${payload.eventType}");
        // A simple way to handle this is to refresh the list.
        // More sophisticated handling could involve updating/inserting/deleting specific items.
        await _refreshProjects();
      },
    ).subscribe((status, [_]) {
        print('Construction Realtime status: $status for table: $_constructionTableName');
         if (status == RealtimeSubscribeStatus.subscribed) {
            print('Successfully subscribed to construction updates.');
            if (_constructionProjects.isEmpty && !_isLoading) { // If list is empty after initial load, try fetching
              _refreshProjects();
            }
        } else if (status == RealtimeSubscribeStatus.channelError || status == RealtimeSubscribeStatus.timedOut) { // CORRECTED LINE
            print('Error subscribing to construction updates: $status');
             if (mounted) {
                _showErrorSnackbar("Error with real-time updates ($status). Please try refreshing.");
            }
        }
    });
  }

  Future<void> _refreshProjects() async {
    if (_isLoading || _isDisposed) return;
    print("Refreshing construction projects...");
    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _constructionProjects.clear(); // Clear existing projects before refresh
    });
    await _loadMoreConstructionProjects(isRefresh: true);
  }

  Future<void> _loadMoreConstructionProjects({bool isRefresh = false}) async {
    if ((_isLoading && !isRefresh) || !_hasMore || _isDisposed) {
      if(!_hasMore) print("No more construction projects to load.");
      return;
    }

    print("_loadMoreConstructionProjects called - fetching page $_page");
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await Supabase.instance.client
          .from(_constructionTableName)
          .select('*')
          .order('startdate', ascending: false) // Newest first
          .order('id', ascending: false) // Secondary sort for consistent pagination
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      final newProjects = List<Map<String, dynamic>>.from(response);

      setState(() {
        if (newProjects.isEmpty) {
          _hasMore = false;
        } else {
          // Avoid duplicates during refresh or rapid scrolling
          final existingIds = _constructionProjects.map((p) => p['id']).toSet();
          _constructionProjects.addAll(newProjects.where((p) => !existingIds.contains(p['id'])));
          _page++;
        }
        // Ensure list remains sorted after adding new items
         _constructionProjects.sort((a, b) {
            final DateTime? dateA = a['startdate'] != null ? DateTime.tryParse(a['startdate']) : null;
            final DateTime? dateB = b['startdate'] != null ? DateTime.tryParse(b['startdate']) : null;
            if (dateA == null && dateB == null) return 0;
            if (dateA == null) return 1;
            if (dateB == null) return -1;
            return dateB.compareTo(dateA);
        });
        _isLoading = false;
      });
    } catch (error) {
      if (_isDisposed) return;
      print("Error loading construction projects: $error");
      setState(() {
        _isLoading = false;
        _hasMore = false; // Stop trying to load more on error
      });
      _showErrorSnackbar("Error fetching projects: ${error.toString()}");
    }
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 && // Threshold
        !_isLoading &&
        _hasMore) {
      _loadMoreConstructionProjects();
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> project) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ConstructionDetailPage(
            constructionProject: project,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForTable: widget.collegeNameForTable, // Pass this
          ),
        ),
      ).then((_) {
        // Optional: refresh list when returning, in case data was stale or changed
        // _refreshProjects();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Important for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Construction', // Simplified title
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshProjects,
        child: (_isLoading && _constructionProjects.isEmpty)
            ? const Center(child: CircularProgressIndicator())
            : _constructionProjects.isEmpty
                ? LayoutBuilder( // To make "No projects" scrollable for RefreshIndicator
                    builder: (context, constraints) => SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                        child: Center(
                          child: Text(
                            'No construction projects found.',
                            style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  )
                : ListView.builder(
                    key: _listKey, // For preserving scroll position
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _constructionProjects.length + (_hasMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index < _constructionProjects.length) {
                        final project = _constructionProjects[index];
                        // VisibilityDetector could be used here if images were being loaded
                        return _buildConstructionCard(project, theme, currentIsDarkMode);
                      } else if (_hasMore) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      } else {
                        return Container(); // Should not happen if _hasMore is false
                      }
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConstructionCard(
    Map<String, dynamic> project,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = project['fullname'] ?? 'Unnamed Project';
    final String location = project['location'] ?? '';
    
    String dateInfo = 'Date not specified';
    if (project['startdate'] != null) {
      try {
        final startDate = DateTime.parse(project['startdate']);
        final DateFormat formatter = DateFormat('MMM d, yyyy');
        dateInfo = 'Starts: ${formatter.format(startDate)}';
        
        if (project['enddate'] != null) {
          final endDate = DateTime.parse(project['enddate']);
          // Check if end date is same as start date or earlier (data issue)
          if (endDate.isAfter(startDate)) {
             dateInfo = '${formatter.format(startDate)} - ${formatter.format(endDate)}';
          } else {
             dateInfo = 'Ongoing from: ${formatter.format(startDate)}';
          }
        } else {
            dateInfo = 'Ongoing from: ${formatter.format(startDate)}';
        }
      } catch (e) {
        print("Error parsing date for project ${project['id']}: $e");
        dateInfo = "Date parsing error";
      }
    }


    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent, // Consistent with HelpdesksPage
      elevation: 1, // Subtle elevation
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)), // Softer corners
      child: InkWell(
        onTap: () => _navigateToDetail(context, project),
        borderRadius: BorderRadius.circular(12), // Match card shape
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
                child: Icon(
                  Icons.construction_outlined, // Using outlined version
                  color: theme.colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (location.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          location,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                           maxLines: 1,
                           overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        dateInfo,
                        style: TextStyle(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8), // Space before arrow
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
              ),
            ],
          ),
        ),
      ),
    );
  }
}