import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../main.dart';
import 'login_page.dart';
import 'tertiary_detail_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:async';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/cache_manager.dart';

class TertiaryPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryPage> createState() => _TertiaryPageState();
}

class _TertiaryPageState extends State<TertiaryPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
   final PageStorageKey _listKey = const PageStorageKey('tertiary_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _colleges = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<Map<String, dynamic>> _filteredColleges = [];
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _loadPreloadedColleges();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchQuery = _searchController.text;
        _filterColleges();
      });
    });
  }

  void _filterColleges() {
    if (_searchQuery.isEmpty) {
      _filteredColleges = List<Map<String, dynamic>>.from(_colleges);
    } else {
      _filteredColleges = _colleges.where((college) {
        final fullName = (college['fullname'] as String? ?? '').toLowerCase();
        final city = (college['city'] as String? ?? '').toLowerCase();
        final state = (college['state'] as String? ?? '').toLowerCase();
        final searchQueryLower = _searchQuery.toLowerCase();
        return fullName.contains(searchQueryLower) ||
            city.contains(searchQueryLower) ||
            state.contains(searchQueryLower);
      }).toList();
     }
   }

  void _loadPreloadedColleges() {
    if (MyApp.preloadedColleges != null) {
      setState(() {
        _colleges = List<Map<String, dynamic>>.from(MyApp.preloadedColleges!);
        _colleges.sort((a, b) => (a['fullname'] ?? '')
            .toLowerCase()
            .compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = MyApp.preloadedColleges!.length == _pageSize;
        _filterColleges();
      });
      for (var college in _colleges) {
        if (college['image_url'] == null ||
            college['image_url'] == 'assets/placeholder_image.png') {
          _fetchImageUrl(college);
        }
      }
    } else {
      _loadCollegesFromSupabase();
    }
  }

  Future<void> _loadCollegesFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    setState(() {
      _isLoading = true;
    });

    // Cache key for colleges
    final cacheKey = 'colleges_page_${_page}';

    try {
      List<Map<String, dynamic>> response;
      bool isFromCache = false;

      // Try to get data from cache first
      if (initialLoad) {
        final cachedData = await CacheManager.getCachedData(cacheKey);
        if (cachedData != null) {
          response = cachedData;
          isFromCache = true;
          print('Using cached colleges data for page $_page');
        } else {
          // Fetch from Supabase if no cache
          final startIndex = _page * _pageSize;
          final endIndex = startIndex + _pageSize - 1;

          response = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .order('fullname', ascending: true)
              .range(startIndex, endIndex);

          // Cache the response
          await CacheManager.cacheData(cacheKey, response);
        }
      } else {
        // For pagination, always try network first, fallback to cache
        try {
          final startIndex = _page * _pageSize;
          final endIndex = startIndex + _pageSize - 1;

          response = await Supabase.instance.client
              .from('colleges')
              .select('*')
              .order('fullname', ascending: true)
              .range(startIndex, endIndex);

          // Cache the response
          await CacheManager.cacheData(cacheKey, response);
        } catch (e) {
          // If network fails, try cache
          final cachedData = await CacheManager.getCachedData(cacheKey);
          if (cachedData != null) {
            response = cachedData;
            isFromCache = true;
            print('Using cached colleges data for page $_page (network failed)');
          } else {
            throw e; // Re-throw if no cache available
          }
        }
      }

      // Update image URLs if not from cache (images should be cached already)
      final updatedColleges = isFromCache ? response : await _updateCollegeImageUrls(response);

      setState(() {
        if (initialLoad) {
          _colleges = updatedColleges;
        } else {
          _colleges.addAll(updatedColleges);
        }
        _colleges.sort((a, b) => (a['fullname'] ?? '')
            .toLowerCase()
            .compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
        _filterColleges();
      });

      // Update preloaded colleges in MyApp for global access
      if (initialLoad || MyApp.preloadedColleges == null) {
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(_colleges);
      } else if (!initialLoad && MyApp.preloadedColleges != null) {
        MyApp.preloadedColleges!.addAll(updatedColleges);
        MyApp.preloadedColleges!.sort((a, b) => (a['fullname'] ?? '')
            .toLowerCase()
            .compareTo((b['fullname'] ?? '').toLowerCase()));
      }

      // If we're online but used cached data, refresh in background
      if (isFromCache && initialLoad) {
        _refreshDataInBackground();
      }
    } catch (error) {
      if (!_isDisposed) {
        // Check error message to see if it's a connectivity issue or table missing
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') ||
            errorStr.contains('offline')) {
          errorMsg = "Offline. Using cached data if available.";

          // Try to load from cache as fallback
          final cachedData = await CacheManager.getCachedData(cacheKey, maxAgeMinutes: 43200); // Allow 30-day old cache when offline
          if (cachedData != null) {
            setState(() {
              if (initialLoad) {
                _colleges = cachedData;
              } else {
                _colleges.addAll(cachedData);
              }
              _colleges.sort((a, b) => (a['fullname'] ?? '')
                  .toLowerCase()
                  .compareTo((b['fullname'] ?? '').toLowerCase()));
              _isLoading = false;
              _hasMore = cachedData.length == _pageSize;
              _filterColleges();
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Offline. Using cached data."), backgroundColor: Colors.orange),
            );
            return;
          }
        } else if (errorStr.contains('relation') &&
            errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
        } else {
          errorMsg = "Error fetching colleges: $error";
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  // Refresh data in background without blocking UI
  Future<void> _refreshDataInBackground() async {
    try {
      final startIndex = _page * _pageSize;
      final endIndex = startIndex + _pageSize - 1;

      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from('colleges')
          .select('*')
          .order('fullname', ascending: true)
          .range(startIndex, endIndex);

      // Cache the fresh response
      await CacheManager.cacheData('colleges_page_${_page}', response);

      // Only update UI if component is still mounted
      if (!_isDisposed) {
        final updatedColleges = await _updateCollegeImageUrls(response);

        setState(() {
          _colleges = updatedColleges;
          _colleges.sort((a, b) => (a['fullname'] ?? '')
              .toLowerCase()
              .compareTo((b['fullname'] ?? '').toLowerCase()));
          _filterColleges();
        });

        // Update preloaded colleges
        MyApp.preloadedColleges = List<Map<String, dynamic>>.from(_colleges);
      }
    } catch (e) {
      print('Background refresh failed: $e');
    }
  }

  Future<List<Map<String, dynamic>>> _updateCollegeImageUrls(
      List<Map<String, dynamic>> colleges) async {
    List<Future<void>> futures = [];
    for (final college in colleges) {
      if (college['image_url'] == null ||
          college['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(college));
      }
    }
    await Future.wait(futures);
    return colleges;
  }

  void _setupRealtime() {
    _realtimeChannel = Supabase.instance.client
        .channel('colleges')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'colleges',
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> newCollegeResponse =
              await Supabase.instance.client
                  .from('colleges')
                  .select('*')
                  .eq('id', newCollegeId);
          if (newCollegeResponse.isNotEmpty) {
            final newCollege = newCollegeResponse.first;
            final updatedCollege = await _updateCollegeImageUrls([newCollege]);
            setState(() {
              _colleges = [..._colleges, updatedCollege.first];
              _colleges.sort((a, b) => (a['fullname'] ?? '')
                  .toLowerCase()
                  .compareTo((b['fullname'] ?? '').toLowerCase()));
              _filterColleges();
            });
            MyApp.preloadedColleges = [
              ...MyApp.preloadedColleges ?? [],
              updatedCollege.first
            ];
            MyApp.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '')
                .toLowerCase()
                .compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedCollegeId = payload.newRecord['id'];
          final List<Map<String, dynamic>> updatedCollegeResponse =
              await Supabase.instance.client
                  .from('colleges')
                  .select('*')
                  .eq('id', updatedCollegeId);
          if (updatedCollegeResponse.isNotEmpty) {
            final updatedCollege = updatedCollegeResponse.first;
            setState(() {
              _colleges = _colleges.map((college) {
                return college['id'] == updatedCollege['id']
                    ? updatedCollege
                    : college;
              }).toList();
              _colleges.sort((a, b) => (a['fullname'] ?? '')
                  .toLowerCase()
                  .compareTo((b['fullname'] ?? '').toLowerCase()));
              _filterColleges();
            });
            MyApp.preloadedColleges = MyApp.preloadedColleges?.map((college) {
              return college['id'] == updatedCollege['id']
                  ? updatedCollege
                  : college;
            }).toList();
            MyApp.preloadedColleges?.sort((a, b) => (a['fullname'] ?? '')
                .toLowerCase()
                .compareTo((b['fullname'] ?? '').toLowerCase()));
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedCollegeId = payload.oldRecord['id'];
          setState(() {
            _colleges.removeWhere((college) => college['id'] == deletedCollegeId);
            _filterColleges();
          });
          MyApp.preloadedColleges
              ?.removeWhere((college) => college['id'] == deletedCollegeId);
        }
      },
    ).subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreColleges();
    }
  }

  Future<void> _loadMoreColleges() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadCollegesFromSupabase(initialLoad: false);
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> college) {
    if (_isDisposed) return;

    // Determine the current theme mode dynamically.
    final currentIsDarkMode = Theme.of(context).brightness == Brightness.dark;

    _checkTodayEventsAvailability(college).then((hasTodayEvents) {
      if (!_isDisposed) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TertiaryDetailPage(
              college: college,
              isDarkMode: currentIsDarkMode,
              toggleTheme: widget.toggleTheme,
              hasTodayEventsPreloaded: hasTodayEvents,
              isFromTertiaryPage: true, // Passing the flag as true when navigating from TertiaryPage
            ),
          ),
        );
      }
    });
  }

  Future<bool> _checkTodayEventsAvailability(
      Map<String, dynamic> college) async {
    final now = DateTime.now();
    final todayDay = DateFormat('dd').format(now);
     final todayMonth = DateFormat('MM').format(now);
    final todayYear = DateFormat('yyyy').format(now);

    final eventsTableName = '${college['fullname']
            .toString()
            .toLowerCase()
            .replaceAll(' ', '')}_events';

    try {
      final response = await Supabase.instance.client
          .from(eventsTableName)
          .select('id')
          .eq('startday', todayDay)
          .eq('startmonth', todayMonth)
          .eq('startyear', todayYear)
          .limit(1);

      if (response is List) {
        return response.isNotEmpty;
      } else {
        return false;
      }
    } catch (error) {
      // Check if error is due to offline connectivity or missing table.
      String errorMsg;
      final errorStr = error.toString().toLowerCase();
      if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
        errorMsg = "Offline. Please check your internet connection.";
      } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
      } else {
        errorMsg = "Error checking events: $error";
      }
      // Optionally show a snackbar (only once) if not disposed.
      if (!_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMsg), backgroundColor: Colors.redAccent),
        );
      }
      return false;
    }
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> college) async {
    if (college['image_url'] != null &&
        college['image_url'] != 'assets/placeholder_image.png') {
      return;
    }

    final fullname = college['fullname'] as String? ?? '';
    final collegeId = college['id']?.toString() ?? '';
    final cacheKey = 'college_image_${collegeId}_${fullname.replaceAll(' ', '_')}';

    // Try to get image URL from cache first
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedImageUrl = prefs.getString(cacheKey);

      if (cachedImageUrl != null && cachedImageUrl.isNotEmpty) {
        if (mounted) {
          setState(() {
            college['image_url'] = cachedImageUrl;
          });
        }
        return;
      }
    } catch (e) {
      print('Error retrieving cached image URL: $e');
    }

    // If not in cache, fetch from Supabase
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';

    try {
      final file = await Supabase.instance.client
          .storage
          .from('colleges')
          .download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client
            .storage
            .from('colleges')
            .getPublicUrl(imageNameWebp);

        // Cache the image URL
        if (imageUrl.isNotEmpty) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(cacheKey, imageUrl);
        }
      }
    } catch (e) {
      print('Error fetching image from Supabase: $e');
    }

    if (mounted) {
      setState(() {
        college['image_url'] =
            imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    // Dynamically determine the current theme mode.
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: _isSearching
            ? IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                  });
                },
              )
            : IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () => Navigator.pop(context),
              ),
        title: _isSearching
            ? Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.background,
                  borderRadius: BorderRadius.circular(8),
                  ),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: TextField(
				  controller: _searchController,
				  autofocus: true,
				  style: TextStyle(color: theme.colorScheme.onBackground),
				  decoration: InputDecoration(
					hintText: 'Search colleges...',
					hintStyle: TextStyle(
					  color: theme.colorScheme.onBackground.withOpacity(0.6),
					),
					border: InputBorder.none,
					enabledBorder: InputBorder.none, // Add this line
					focusedBorder: InputBorder.none, // Add this line
				  ),
				  cursorColor: theme.colorScheme.onBackground,
				),
              )
            : Text(
                'Tertiary',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => _loadCollegesFromSupabase(initialLoad: true),
        child: (_searchQuery.isNotEmpty ? _filteredColleges : _colleges)
                    .isEmpty &&
                _isLoading
            ? const Center(child: CircularProgressIndicator())
            : (_searchQuery.isNotEmpty ? _filteredColleges : _colleges)
                        .isEmpty &&
                    !_isLoading
                ? LayoutBuilder(
                    builder: (BuildContext context, BoxConstraints constraints) {
                      return SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: constraints.maxHeight,
                          child: const Center(
                            child: Text('No colleges available.'),
                          ),
                        ),
                      );
                    },
                  )
                : ListView.builder(
                    key: _listKey,
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    itemCount: (_searchQuery.isNotEmpty ? _filteredColleges : _colleges)
                            .length +
                        (_hasMore && _searchQuery.isEmpty ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index < (_searchQuery.isNotEmpty ? _filteredColleges : _colleges).length) {
                        final university = (_searchQuery.isNotEmpty ? _filteredColleges : _colleges)[index];
                        return VisibilityDetector(
                          key: Key('college_${university['id']}'),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1 &&
                                (university['image_url'] == null ||
                                    university['image_url'] ==
                                        'assets/placeholder_image.png')) {
                              _fetchImageUrl(university);
                            }
                          },
                          child: Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor: Colors.transparent,
                            margin: const EdgeInsets.only(bottom: 16),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: ClipOval(
                                child: SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: CachedNetworkImage(
                                    imageUrl: university['image_url'] ??
                                        'assets/placeholder_image.png',
                                    errorWidget: (context, url, error) =>
                                        Image.asset('assets/placeholder_image.png'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              title: Text(
                                university['fullname'] ?? 'Unnamed College',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Text(
                                  '${university['city'] ?? ''}${university['state'] != null && university['city'] != null ? ', ' : ''}${university['state'] ?? ''}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              onTap: () => _navigateToDetail(context, university),
                            ),
                          ),
                        );
                      } else if (_hasMore && _searchQuery.isEmpty) {
                        return const Center(
                            child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator()));
                      } else {
                        return Container();
                      }
                    },
                  ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                     color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                 const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
}