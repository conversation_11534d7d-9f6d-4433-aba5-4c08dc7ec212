import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:sqflite_common_ffi/sqflite_ffi.dart' as sqflite_ffi;
import 'dart:io';

class BudgetTrackerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const BudgetTrackerPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<BudgetTrackerPage> createState() => _BudgetTrackerPageState();
}

class _BudgetTrackerPageState extends State<BudgetTrackerPage> {
  final TextEditingController _budgetAmountController = TextEditingController();
  final TextEditingController _expenseDescriptionController = TextEditingController();
  final TextEditingController _expenseAmountController = TextEditingController();
  String? _selectedBudgetCateogry;
  String? _selectedExpenseCategory;
  DateTime? _selectedDate;

  List<BudgetItem> _budgets = [];
  List<ExpenseItem> _expenses = [];
  List<String> _categories = [];

  DateTime? _selectedPeriodStartDate;
  DateTime? _selectedPeriodEndDate;

  late DatabaseHelper dbHelper;

  @override
  void initState() {
    super.initState();
    dbHelper = DatabaseHelper.instance;
    _loadData();
    _selectedDate = DateTime.now();
  }

  Future<void> _loadData() async {
    await _loadCategories();
    await _loadBudgetsFromDb();
    await _loadExpensesFromDb();
  }

  Future<void> _loadCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoryData = prefs.getStringList('categories');

    if (categoryData != null) {
      setState(() {
        _categories = List<String>.from(jsonDecode(categoryData.join('')));
      });
    } else {
      setState(() {
        _categories.addAll(['Food', 'Transportation', 'Entertainment', 'Utilities', 'Other']);
        _saveCategories();
      });
    }
  }

  Future<void> _saveCategories() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('categories', [jsonEncode(_categories)]);
  }

  Future<void> _loadBudgetsFromDb() async {
    final budgets = await dbHelper.getBudgets();
    setState(() {
      _budgets = budgets;
    });
  }

  Future<void> _loadExpensesFromDb() async {
    final expenses = await dbHelper.getExpenses();
    setState(() {
      _expenses = expenses;
    });
  }

  void _addBudget() async {
    final amount = double.tryParse(_budgetAmountController.text.trim());
    if (_selectedBudgetCateogry != null && amount != null && amount > 0) {
      final budget = BudgetItem(category: _selectedBudgetCateogry!, amount: amount);
      await dbHelper.insertBudget(budget);
      _loadBudgetsFromDb();
      _budgetAmountController.clear();
      _selectedBudgetCateogry = null;
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a category and enter a valid budget amount.')),
      );
    }
  }

  void _addExpense() async {
    final description = _expenseDescriptionController.text.trim();
    final amount = double.tryParse(_expenseAmountController.text.trim());
    if (_selectedExpenseCategory != null && description.isNotEmpty && amount != null && amount > 0 && _selectedDate != null) {
      final expense = ExpenseItem(
        category: _selectedExpenseCategory!,
        description: description,
        amount: amount,
        date: _selectedDate!,
      );
      await dbHelper.insertExpense(expense);
      _loadExpensesFromDb();
      _expenseDescriptionController.clear();
      _expenseAmountController.clear();
      _selectedExpenseCategory = null;
      _selectedDate = DateTime.now();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a category, enter description and amount, and select a date.')),
      );
    }
  }

  Map<String, double> _calculateCategorySpending([DateTime? startDate, DateTime? endDate]) {
    Map<String, double> spending = {};
    for (var expense in _expenses) {
      if ((startDate == null || expense.date.isAtSameMomentAs(startDate) || expense.date.isAfter(startDate)) &&
          (endDate == null || expense.date.isAtSameMomentAs(endDate) || expense.date.isBefore(endDate.add(const Duration(days: 1))))) {
        spending[expense.category] = (spending[expense.category] ?? 0) + expense.amount;
      }
    }
    return spending;
  }

  double _getTotalBudget() {
    return _budgets.fold(0, (sum, item) => sum + item.amount);
  }

  double _getTotalSpending([DateTime? startDate, DateTime? endDate]) {
    return _expenses.where((expense) =>
        (startDate == null || expense.date.isAtSameMomentAs(startDate) || expense.date.isAfter(startDate)) &&
        (endDate == null || expense.date.isAtSameMomentAs(endDate) || expense.date.isBefore(endDate.add(const Duration(days: 1)))))
        .fold(0, (sum, item) => sum + item.amount);
  }

  void _editBudget(BudgetItem budget) {
    final TextEditingController editController = TextEditingController(text: budget.amount.toString());
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Edit Budget Amount'),
        content: TextField(
          controller: editController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(labelText: 'New Amount'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newAmount = double.tryParse(editController.text.trim());
              if (newAmount != null && newAmount > 0) {
                final updatedBudget = BudgetItem(category: budget.category, amount: newAmount);
                await dbHelper.updateBudget(updatedBudget);
                _loadBudgetsFromDb();
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a valid amount.')),
                );
              }
            },
            child: Text('Update', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
  }

  List<ExpenseItem> _getExpensesForPeriod(DateTime? startDate, DateTime? endDate) {
    return _expenses.where((expense) {
      bool afterOrOnStartDate = startDate == null || !expense.date.isBefore(startDate);
      bool beforeOrOnEndDate = endDate == null || !expense.date.isAfter(endDate);
      return afterOrOnStartDate && beforeOrOnEndDate;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final categorySpending = _calculateCategorySpending();
    final totalBudget = _getTotalBudget();
    final totalSpending = _getTotalSpending();
    final remainingBudget = totalBudget - totalSpending;

    final periodSpending = _calculateCategorySpending(_selectedPeriodStartDate, _selectedPeriodEndDate);
    final totalPeriodSpending = _getTotalSpending(_selectedPeriodStartDate, _selectedPeriodEndDate);
    final periodExpenses = _getExpensesForPeriod(_selectedPeriodStartDate, _selectedPeriodEndDate);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget & Expense Tracker'),
        backgroundColor: widget.isDarkMode ? null : Colors.white,
        foregroundColor: widget.isDarkMode ? null : Colors.black,
        scrolledUnderElevation: widget.isDarkMode ? null : 0.0, // Remove shadow on scroll in light mode
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Budget Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Add/Edit Budget', style: Theme.of(context).textTheme.titleMedium),
                    DropdownButtonFormField<String>(
                      value: _selectedBudgetCateogry,
                      onChanged: (newValue) {
                        setState(() {
                          _selectedBudgetCateogry = newValue;
                        });
                      },
                      items: _categories.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Category'),
                    ),
                    TextField(
                      controller: _budgetAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(labelText: 'Budgeted Amount'),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _addBudget,
                      child: Text('Add Budget', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                    ),
                    const SizedBox(height: 10),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _budgets.length,
                      itemBuilder: (context, index) {
                        final budget = _budgets[index];
                        return ListTile(
                          title: Text(budget.category),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text('\$${budget.amount.toStringAsFixed(2)}'),
                              IconButton(
                                icon: Icon(Icons.edit, color: widget.isDarkMode ? Colors.white : Colors.black,),
                                onPressed: () => _editBudget(budget),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Expense Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Add Expense', style: Theme.of(context).textTheme.titleMedium),
                    DropdownButtonFormField<String>(
                      value: _selectedExpenseCategory,
                      onChanged: (newValue) {
                        setState(() {
                          _selectedExpenseCategory = newValue;
                        });
                      },
                      items: _categories.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      decoration: const InputDecoration(labelText: 'Category'),
                    ),
                    TextField(
                      controller: _expenseDescriptionController,
                      decoration: const InputDecoration(labelText: 'Description'),
                    ),
                    TextField(
                      controller: _expenseAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(labelText: 'Amount'),
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      onTap: () async {
                        final DateTime? pickedDate = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now(),
                        );
                        if (pickedDate != null) {
                          setState(() {
                            _selectedDate = pickedDate;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Date',
                          hintText: 'Select Date',
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Text(
                              _selectedDate == null
                                  ? ''
                                  : DateFormat('yyyy-MM-dd').format(_selectedDate!),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: widget.isDarkMode ? Colors.white : Colors.black,
                              ),
                            ),
                            const Icon(Icons.calendar_today),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: _addExpense,
                      child: Text('Add Expense', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Report View Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('View Spending Periodically', style: Theme.of(context).textTheme.titleMedium),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            final DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: _selectedPeriodStartDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                _selectedPeriodStartDate = pickedDate;
                                _selectedPeriodEndDate = pickedDate;
                              });
                            }
                          },
                          child: Text('Select Date', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            final DateTimeRange? pickedRange = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (pickedRange != null) {
                              setState(() {
                                _selectedPeriodStartDate = pickedRange.start;
                                _selectedPeriodEndDate = pickedRange.end;
                              });
                            }
                          },
                          child: Text('Select Range', style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                        ),
                      ],
                    ),
                    if (_selectedPeriodStartDate != null && _selectedPeriodEndDate != null)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                            'Spending from ${DateFormat('yyyy-MM-dd').format(_selectedPeriodStartDate!)} to ${DateFormat('yyyy-MM-dd').format(_selectedPeriodEndDate!)}: \$${totalPeriodSpending.toStringAsFixed(2)}'),
                      ),
                    if (periodExpenses.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Expenses in this period:'),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: periodExpenses.length,
                            itemBuilder: (context, index) {
                              final expense = periodExpenses[index];
                              return ListTile(
                                title: Text('${expense.description} (${expense.category})'),
                                trailing: Text('\$${expense.amount.toStringAsFixed(2)}'),
                              );
                            },
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Summary Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text('Summary', style: Theme.of(context).textTheme.titleMedium),
                    ListTile(
                      title: const Text('Total Budget'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(totalBudget)),
                    ),
                    ListTile(
                      title: const Text('Total Spending'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(totalSpending)),
                    ),
                    ListTile(
                      title: const Text('Remaining Budget'),
                      trailing: Text(NumberFormat.currency(symbol: '').format(remainingBudget)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Charts Section
            if (categorySpending.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text('Spending by Category', style: Theme.of(context).textTheme.titleMedium),
                      SizedBox(
                        height: 200,
                        child: PieChart(
                          PieChartData(
                            sections: categorySpending.entries.map((entry) {
                              return PieChartSectionData(
                                value: entry.value,
                                title: '${entry.key}\n${entry.value.toStringAsFixed(2)}',
                                titleStyle: const TextStyle(fontSize: 10, color: Colors.white),
                                color: _getColorForCategory(entry.key),
                              );
                            }).toList(),
                            borderData: FlBorderData(show: false),
                            sectionsSpace: 2,
                            centerSpaceRadius: 40,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Color _getColorForCategory(String category) {
    final availableColors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.amber,
      Colors.purple,
      Colors.teal,
      Colors.orange,
      Colors.brown,
      Colors.cyan,
      Colors.indigo,
    ];
    return availableColors[_categories.indexOf(category) % availableColors.length];
  }
}

class DatabaseHelper {
  static const _databaseName = "BudgetTracker.db";
  static const _databaseVersion = 1;

  static const tableBudgets = 'budgets';
  static const columnCategory = 'category';
  static const columnAmount = 'amount';

  static const tableExpenses = 'expenses';
  static const columnId = 'id';
  static const columnDescription = 'description';
  static const columnExpenseAmount = 'amount';
  static const columnDate = 'date';

  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;
  Future<Database> get database async {
    if (_database != null) return _database!;
    return _initDatabase();
  }

  Future<Database> _initDatabase() async {
    if (kIsWeb) {
      sqflite_ffi.sqfliteFfiInit();
      var databaseFactory = sqflite_ffi.databaseFactoryFfi;
      return await databaseFactory.openDatabase(
        _databaseName,
        options: OpenDatabaseOptions(
          version: _databaseVersion,
          onCreate: _onCreate,
        ),
      );
    } else {
      final databasesPath = await getDatabasesPath();
      final path = p.join(databasesPath, _databaseName);
      return await openDatabase(path,
          version: _databaseVersion, onCreate: _onCreate);
    }
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableBudgets (
        $columnCategory TEXT PRIMARY KEY,
        $columnAmount REAL NOT NULL
      )
    ''');
    await db.execute('''
      CREATE TABLE $tableExpenses (
        $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
        $columnCategory TEXT NOT NULL,
        $columnDescription TEXT NOT NULL,
        $columnExpenseAmount REAL NOT NULL,
        $columnDate INTEGER NOT NULL
      )
    ''');
  }

  Future<int> insertBudget(BudgetItem budget) async {
    final db = await database;
    return await db.insert(tableBudgets, budget.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<BudgetItem>> getBudgets() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(tableBudgets);
    return List.generate(maps.length, (i) {
      return BudgetItem.fromMap(maps[i]);
    });
  }

  Future<int> updateBudget(BudgetItem budget) async {
    final db = await database;
    return await db.update(tableBudgets, budget.toMap(),
        where: '$columnCategory = ?', whereArgs: [budget.category]);
  }

  Future<int> insertExpense(ExpenseItem expense) async {
    final db = await database;
    return await db.insert(tableExpenses, expense.toMap());
  }

  Future<List<ExpenseItem>> getExpenses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(tableExpenses);
    return List.generate(maps.length, (i) {
      return ExpenseItem.fromMap(maps[i]);
    });
  }
}

class BudgetItem {
  final String category;
  double amount;

  BudgetItem({required this.category, required this.amount});

  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'amount': amount,
    };
  }

  factory BudgetItem.fromMap(Map<String, dynamic> map) {
    return BudgetItem(
      category: map['category'],
      amount: map['amount'],
    );
  }
}

class ExpenseItem {
  final int? id;
  final String category;
  final String description;
  final double amount;
  final DateTime date;

  ExpenseItem({this.id, required this.category, required this.description, required this.amount, required this.date});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category': category,
      'description': description,
      'amount': amount,
      'date': date.millisecondsSinceEpoch,
    };
  }

  factory ExpenseItem.fromMap(Map<String, dynamic> map) {
    return ExpenseItem(
      id: map['id'],
      category: map['category'],
      description: map['description'],
      amount: map['amount'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
    );
  }
}