import 'package:flutter/material.dart';
import 'login_page.dart'; // Assuming this is still needed for navigation elsewhere
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';

class AiAgentPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic>? collegeData; // Accepts college data

  const AiAgentPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    this.collegeData,
  }) : super(key: key);

  @override
  _AiAgentPageState createState() => _AiAgentPageState();
}

class _AiAgentPageState extends State<AiAgentPage> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  bool _isSending = false;
  final ScrollController _scrollController = ScrollController();

  // Gemini API configuration
  final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your actual key
  final String _model = 'gemini-2.0-flash';

  // Daily token tracking and limit (for both input and output messages)
  int _dailyTokenCount = 0;
  final int _maxDailyTokens = 100000; // Increased daily limit

  // To store the last Gemini prompt token count for display.
  int _lastPromptTokenCount = 0;

  // Token cost rates per token.
  final double _inputCostPerToken = 0.075 / 1000000;
  final double _outputCostPerToken = 0.30 / 1000000;
  // Exchange rate: $1 = MKW2000.
  final double _exchangeRate = 2000.0;

  // Audio input & output
  late stt.SpeechToText _speechToText;
  bool _isListening = false;
  late FlutterTts _flutterTts;
  bool _isMuted = false; // If true, audio output is disabled

  @override
  void initState() {
    super.initState();
    print("AiAgentPage initState received collegeData: ${widget.collegeData}");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _addInitialGreeting();
    });
    // Initialize speech recognition and TTS.
    _speechToText = stt.SpeechToText();
    _initSpeech();
    _flutterTts = FlutterTts();
  }

  Future<void> _initSpeech() async {
    await _speechToText.initialize();
  }

  void _addInitialGreeting() {
    String collegeName = (widget.collegeData?['fullname'] != null &&
            widget.collegeData!['fullname']!.isNotEmpty)
        ? widget.collegeData!['fullname']!
        : 'the selected institution';
    setState(() {
      _messages.add(ChatMessage(
          text:
              "Hi! How can I help you with information about $collegeName today?",
          isUser: false));
      // Update daily token count with greeting message (output)
      _dailyTokenCount += _calculateTokenCount(
          "Hi! How can I help you with information about $collegeName today?");
    });
  }

  // Starts a fresh conversation (clears conversation history only).
  void _startNewConversation() {
    setState(() {
      _messages.clear();
      _addInitialGreeting();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Helper function to estimate token count (approx. 1 token per 4 characters).
  int _calculateTokenCount(String text) =>
      (text.trim().length / 4).ceil();

  /// Computes the total thread tokens and cost by summing tokens in every message.
  Map<String, dynamic> _calculateThreadMetrics() {
    int totalInputTokens = 0;
    int totalOutputTokens = 0;

    for (var msg in _messages) {
      int tokens = _calculateTokenCount(msg.text);
      if (msg.isUser) {
        totalInputTokens += tokens;
      } else {
        totalOutputTokens += tokens;
      }
    }

    double inputCost = totalInputTokens * _inputCostPerToken;
    double outputCost = totalOutputTokens * _outputCostPerToken;
    int totalTokens = totalInputTokens + totalOutputTokens;
    double totalCost = inputCost + outputCost;

    return {
      'inputTokens': totalInputTokens,
      'outputTokens': totalOutputTokens,
      'totalTokens': totalTokens,
      'inputCost': inputCost,
      'outputCost': outputCost,
      'totalCost': totalCost,
    };
  }

  /// Build conversation history from the thread (excluding typing indicators)
  String _buildConversationHistory() {
    StringBuffer history = StringBuffer();
    for (var msg in _messages) {
      if (msg.text.trim().isNotEmpty && !msg.isTyping) {
        if (msg.isUser) {
          history.writeln("User: ${msg.text}");
        } else {
          history.writeln("Assistant: ${msg.text}");
        }
      }
    }
    return history.toString();
  }

  Future<String> _getCollegeDataForPrompt() async {
    if (widget.collegeData == null ||
        widget.collegeData!['fullname'] == null ||
        widget.collegeData!['fullname']!.isEmpty) {
      print("Warning: College data or fullname is null or empty in _getCollegeDataForPrompt.");
      return "No specific college data available to provide context. Cannot answer question accurately.";
    }

    String collegeName = widget.collegeData!['fullname'];
    String collegeTablePrefix = collegeName.toLowerCase().replaceAll(' ', '');
    print("AI Agent: Preparing data for College: $collegeName (Prefix: $collegeTablePrefix)");

    StringBuffer promptData = StringBuffer("Context about $collegeName:\n\n");
    promptData.writeln("## Basic Information:");
    promptData.writeln("- Full Name: ${widget.collegeData!['fullname']}");
    promptData.writeln("- Location: ${widget.collegeData!['city'] ?? 'N/A'}, ${widget.collegeData!['state'] ?? 'N/A'}");
    String aboutText = widget.collegeData!['about']?.toString() ?? 'N/A';
    int aboutMaxLength = 500;
    promptData.writeln("- About: ${aboutText.substring(0, aboutText.length > aboutMaxLength ? aboutMaxLength : aboutText.length)}${aboutText.length > aboutMaxLength ? '...' : ''}");
    promptData.writeln("- Website: ${widget.collegeData!['website'] ?? 'N/A'}");
    promptData.writeln("- Institution Type: ${widget.collegeData!['institutiontype'] ?? 'N/A'}");

    List<String> collegeTableNames = await _getCollegeTableNames(collegeTablePrefix);

    if (collegeTableNames.isEmpty) {
      print("AI Agent: No specific data tables found for prefix '$collegeTablePrefix'.");
      promptData.writeln("\n## Additional Data Sections:");
      promptData.writeln("No specific data tables (like events, people, etc.) were found for this college in the database.");
    } else {
      print("AI Agent: Found tables: $collegeTableNames");
      promptData.writeln("\n## Additional Data Sections:");

      for (String tableName in collegeTableNames) {
        String tableData = await _fetchTableData(tableName);
        String tableDisplayName = tableName
            .replaceFirst(collegeTablePrefix + '_', '')
            .replaceAll('_', ' ')
            .split(' ')
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
            .join(' ');
        promptData.writeln("\n### $tableDisplayName Information:");
        promptData.writeln(tableData.isNotEmpty ? tableData : "No data currently available in this section.");
        promptData.writeln("---");
      }
    }

    print("--- AI Agent: FINAL PROMPT CONTEXT BUILT ---");
    print("--- AI Agent: END OF PROMPT CONTEXT ---");
    return promptData.toString();
  }

  Future<List<String>> _getCollegeTableNames(String collegeTablePrefix) async {
    if (collegeTablePrefix.isEmpty) return [];
    print("AI Agent: Fetching table names with prefix: $collegeTablePrefix");
    try {
      final response = await Supabase.instance.client
          .rpc('list_tables_with_prefix', params: {'prefix_param': '${collegeTablePrefix}_'});

      if (response is List) {
        List<String> collegeTables = response
            .map((item) => item['table_name'] as String)
            .toList();
        print("AI Agent: Retrieved college tables via RPC: $collegeTables");
        return collegeTables;
      } else {
        print("AI Agent: Error - RPC 'list_tables_with_prefix' did not return a List. Response: $response");
        try {
          print("AI Agent: Falling back to information_schema query...");
          final isResponse = await Supabase.instance.client
              .from('information_schema.tables')
              .select('table_name')
              .eq('table_schema', 'public')
              .like('table_name', '${collegeTablePrefix}_%');

          if (isResponse is List) {
            List<String> collegeTables = isResponse.map((item) => item['table_name'] as String).toList();
            print("AI Agent: Retrieved college tables via information_schema: $collegeTables");
            return collegeTables;
          } else {
            print("AI Agent: Error - information_schema query did not return a List. Response: $isResponse");
            return [];
          }
        } catch (e_is) {
          print("AI Agent: Error querying information_schema: $e_is");
          return [];
        }
      }
    } catch (e_rpc) {
      print("AI Agent: Error calling RPC 'list_tables_with_prefix': $e_rpc");
      try {
        print("AI Agent: Falling back to information_schema query due to RPC error...");
        final isResponse = await Supabase.instance.client
            .from('information_schema.tables')
            .select('table_name')
            .eq('table_schema', 'public')
            .like('table_name', '${collegeTablePrefix}_%');

        if (isResponse is List) {
          List<String> collegeTables = isResponse.map((item) => item['table_name'] as String).toList();
          print("AI Agent: Retrieved college tables via information_schema: $collegeTables");
          return collegeTables;
        } else {
          print("AI Agent: Error - information_schema query did not return a List. Response: $isResponse");
          return [];
        }
      } catch (e_is) {
        print("AI Agent: Error querying information_schema: $e_is");
        return [];
      }
    }
  }

  Future<String> _fetchTableData(String tableName) async {
    print("AI Agent: Fetching data from table: $tableName");
    try {
      final response = await Supabase.instance.client
          .from(tableName)
          .select()
          .limit(10);

      if (response is List && response.isNotEmpty) {
        print("AI Agent: Data fetched from '$tableName': ${response.length} rows.");
        StringBuffer tableInfo = StringBuffer();
        for (var i = 0; i < response.length; i++) {
          var row = response[i] as Map<String, dynamic>;
          tableInfo.writeln("  Item ${i + 1}:");
          row.forEach((key, value) {
            if (value != null && value.toString().isNotEmpty) {
              String displayValue = value.toString();
              if (displayValue.length > 100) {
                displayValue = displayValue.substring(0, 100) + "...";
              }
              tableInfo.writeln("    - ${key.replaceAll('_', ' ').capitalize()}: $displayValue");
            }
          });
          tableInfo.writeln();
        }
        return tableInfo.toString();
      } else {
        print("AI Agent: No data found or invalid response for table: $tableName");
        return "";
      }
    } catch (e) {
      if (e.toString().contains('relation') && e.toString().contains('does not exist')) {
        print("AI Agent: Table '$tableName' does not exist.");
      } else {
        print("AI Agent: Error fetching data from table '$tableName': $e");
      }
      return "";
    }
  }

  Future<void> _sendMessage(String message) async {
    // Do not process empty messages.
    if (message.trim().isEmpty || _isSending) return;

    // Check daily token limit (for input tokens including the user message)
    int userMessageTokens = _calculateTokenCount(message);
    if (_dailyTokenCount + userMessageTokens > _maxDailyTokens) {
      setState(() {
        _messages.add(ChatMessage(
            text: "Daily token limit reached. Please try again tomorrow.",
            isUser: false));
      });
      return;
    }

    setState(() {
      _isSending = true;
      _messages.add(ChatMessage(text: message, isUser: true));
      _dailyTokenCount += userMessageTokens;
    });
    _messageController.clear();
    _scrollToBottom();

    // Show typing indicator.
    setState(() {
      _messages.add(ChatMessage(text: "...", isUser: false, isTyping: true));
    });
    _scrollToBottom();

    print("AI Agent: Getting college context for prompt...");
    String collegeContext = await _getCollegeDataForPrompt();

    if (collegeContext.startsWith("No specific college data available")) {
      setState(() {
        _messages.removeWhere((m) => m.isTyping);
        _messages.add(ChatMessage(
            text:
                "Sorry, I don't have the specific college context needed to answer right now.",
            isUser: false));
        _isSending = false;
      });
      _scrollToBottom();
      return;
    }

    String collegeName = widget.collegeData?['fullname'] ?? 'the institution';
    // Build conversation history for context.
    String conversationHistory = _buildConversationHistory();

    String prompt = """
You are a specialized AI assistant for $collegeName.
Your primary function is to answer questions based *only* on the information provided below about $collegeName and the ongoing conversation context.
Be concise and helpful. If a question cannot be answered using the provided context, state that the information is not available in your current knowledge base for $collegeName.

--- Provided Context for $collegeName ---
$collegeContext
--- End of Context ---

--- Conversation History ---
$conversationHistory
--- End of Conversation History ---

User's Question: $message
Answer:
""";

    // Calculate prompt token count and update state.
    _lastPromptTokenCount = _calculateTokenCount(prompt);
    // Also count prompt tokens as input.
    _dailyTokenCount += _lastPromptTokenCount;

    try {
      print("AI Agent: Calling Gemini API...");
      final responseText = await _callGeminiApi(prompt);
      print("AI Agent: Received response from Gemini.");
      // Update daily token count with assistant's response (output tokens).
      _dailyTokenCount += _calculateTokenCount(responseText);
      setState(() {
        _messages.removeWhere((m) => m.isTyping);
        _messages.add(ChatMessage(text: responseText, isUser: false));
        _isSending = false;
      });
      // Speak the response if not muted.
      if (!_isMuted) {
        await _flutterTts.speak(responseText);
      }
    } catch (error) {
      print("AI Agent: Gemini API Error: $error");
      setState(() {
        _messages.removeWhere((m) => m.isTyping);
        _messages.add(ChatMessage(
            text:
                "Sorry, I encountered an error trying to respond. Please try again.",
            isUser: false));
        _isSending = false;
      });
    } finally {
      _scrollToBottom();
    }
  }

  Future<String> _callGeminiApi(String prompt) async {
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$_model:generateContent?key=$_apiKey');
    final headers = {'Content-Type': 'application/json'};
    final body = jsonEncode({
      'contents': [
        {
          'parts': [
            {'text': prompt}
          ]
        }
      ],
      'generationConfig': {
        'temperature': 0.6,
        'topP': 0.95,
        'topK': 40,
        'maxOutputTokens': 1024,
      },
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_MEDIUM_AND_ABOVE'}
      ]
    });

    print("AI Agent: Sending request to Gemini...");
    final response = await http.post(url, headers: headers, body: body)
        .timeout(const Duration(seconds: 45));

    print("AI Agent: Gemini response status: ${response.statusCode}");

    if (response.statusCode == 200) {
      final decodedResponse = jsonDecode(response.body);

      if (decodedResponse['candidates'] != null &&
          decodedResponse['candidates'].isNotEmpty &&
          decodedResponse['candidates'][0]['content'] != null &&
          decodedResponse['candidates'][0]['content']['parts'] != null &&
          decodedResponse['candidates'][0]['content']['parts'].isNotEmpty) {
        return decodedResponse['candidates'][0]['content']['parts'][0]['text']?.trim() ??
            "No text content received from AI.";
      } else if (decodedResponse['promptFeedback'] != null &&
          decodedResponse['promptFeedback']['blockReason'] != null) {
        String reason = decodedResponse['promptFeedback']['blockReason'];
        print("AI Agent: Prompt blocked by Gemini. Reason: $reason");
        return "My response was blocked due to safety settings ($reason). Please rephrase your question.";
      } else if (decodedResponse['candidates'] != null &&
          decodedResponse['candidates'].isNotEmpty &&
          decodedResponse['candidates'][0]['finishReason'] != null &&
          decodedResponse['candidates'][0]['finishReason'] != 'STOP') {
        String reason = decodedResponse['candidates'][0]['finishReason'];
        print("AI Agent: Gemini generation finished unexpectedly. Reason: $reason");
        return "Sorry, I couldn't finish generating the response ($reason).";
      } else {
        print("AI Agent: Unexpected Gemini response structure: ${response.body}");
        return "Received an unexpected response format from the AI.";
      }
    } else {
      print("AI Agent: Gemini API request failed. Status: ${response.statusCode}, Body: ${response.body}");
      throw Exception('API request failed with status code: ${response.statusCode}');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Toggle listening for speech.
  void _toggleListening() async {
    if (!_isListening) {
      bool available = await _speechToText.initialize();
      if (available) {
        setState(() {
          _isListening = true;
        });
        _speechToText.listen(
          onResult: (result) {
            setState(() {
              _messageController.text = result.recognizedWords;
            });
          },
        );
      }
    } else {
      _speechToText.stop();
      setState(() {
        _isListening = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    // Choose icon color: black in light mode, white in dark mode.
    final iconColor = isDark ? Colors.white : Colors.black;

    // Dynamically set the AppBar title to "[College Name] GPT".
    String appBarTitle = 'GPT';
    if (widget.collegeData?['fullname'] != null &&
        widget.collegeData!['fullname']!.isNotEmpty) {
      appBarTitle = "${widget.collegeData!['fullname']} GPT";
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: iconColor,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: iconColor,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          // New conversation button.
          IconButton(
            icon: Icon(
              Icons.create,
              color: iconColor,
            ),
            tooltip: "Start New Conversation",
            onPressed: _startNewConversation,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _messages.length,
              padding: const EdgeInsets.all(10.0),
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ChatBubble(
                  message: message,
                  isDarkMode: isDark,
                  theme: theme,
                );
              },
            ),
          ),
          _buildInputArea(theme),
        ],
      ),
    );
  }

  Widget _buildInputArea(ThemeData theme) {
    String hintText = widget.collegeData?['fullname'] != null
        ? 'Ask about ${widget.collegeData!['fullname']}...'
        : 'Ask a question...';

    // Calculate token count and cost for the text being typed.
    int currentMessageTokens = _calculateTokenCount(_messageController.text);
    double currentInputCost = currentMessageTokens * _inputCostPerToken;
    double currentInputCostMkw = currentInputCost * _exchangeRate;

    // Get thread metrics.
    Map<String, dynamic> threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = threadMetrics['totalCost'] * _exchangeRate;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
              offset: const Offset(0, -1),
              blurRadius: 2,
              color: Colors.black.withOpacity(0.1))
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Display token and cost metrics.
          ValueListenableBuilder<TextEditingValue>(
            valueListenable: _messageController,
            builder: (context, value, child) {
              int newMessageTokens = _calculateTokenCount(value.text);
              double newMessageCost = newMessageTokens * _inputCostPerToken;
              double newMessageCostMkw = newMessageCost * _exchangeRate;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Current message tokens: $newMessageTokens, Cost: \$${newMessageCost.toStringAsFixed(8)} (MKW${newMessageCostMkw.toStringAsFixed(2)})",
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                  if (_lastPromptTokenCount > 0)
                    Text(
                      "Last prompt token count: $_lastPromptTokenCount",
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  Text(
                    "Thread Token Count: ${threadMetrics['totalTokens']} (Input: ${threadMetrics['inputTokens']}, Output: ${threadMetrics['outputTokens']})",
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    "Total Thread Cost: \$${(threadMetrics['totalCost']).toStringAsFixed(8)} (MKW${(threadMetrics['totalCost'] * _exchangeRate).toStringAsFixed(2)}) (Input: \$${(threadMetrics['inputCost']).toStringAsFixed(8)}, Output: \$${(threadMetrics['outputCost']).toStringAsFixed(8)})",
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    "Daily Tokens Used: $_dailyTokenCount / $_maxDailyTokens",
                    style: TextStyle(
                      color: _dailyTokenCount >= _maxDailyTokens
                          ? Colors.red
                          : theme.colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
              );
            },
          ),
          Row(
            children: [
              // Expanded text field with embedded mic button as suffix.
              Expanded(
                child: TextField(
                  controller: _messageController,
                  onChanged: (text) {
                    setState(() {}); // Refresh UI so send button can update.
                  },
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.6)),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 10.0),
                    // Suffix icon for audio input.
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isListening ? Icons.mic : Icons.mic_none,
                        color: theme.brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black,
                      ),
                      onPressed: _toggleListening,
                    ),
                  ),
                  style: TextStyle(color: theme.colorScheme.onSurface),
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: _sendMessage,
                  enabled: !_isSending && _dailyTokenCount < _maxDailyTokens,
                  cursorColor: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 8.0),
              // Send button.
              _isSending
                  ? Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          color: theme.brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.send,
                        color: _messageController.text.trim().isEmpty
                            ? theme.disabledColor
                            : (theme.brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black),
                      ),
                      onPressed: _isSending ||
                              _messageController.text.trim().isEmpty ||
                              _dailyTokenCount >= _maxDailyTokens
                          ? null
                          : () => _sendMessage(_messageController.text),
                    ),
              // Mute toggle button.
              IconButton(
                icon: Icon(
                  _isMuted ? Icons.volume_off : Icons.volume_up,
                  color: theme.brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                ),
                onPressed: () {
                  setState(() {
                    _isMuted = !_isMuted;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// --- Helper Classes (ChatMessage, ChatBubble) ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping; // For typing indicator

  ChatMessage({required this.text, required this.isUser, this.isTyping = false});
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Special styling for typing indicator.
    if (message.isTyping) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        alignment: Alignment.topLeft,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Text(
            "Thinking...",
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Regular chat bubble styling.
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 8.0),
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints:
            BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
          decoration: BoxDecoration(
              color: message.isUser
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surfaceVariant,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(message.isUser ? 18.0 : 4.0),
                topRight: Radius.circular(message.isUser ? 4.0 : 18.0),
                bottomLeft: const Radius.circular(18.0),
                bottomRight: const Radius.circular(18.0),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 2,
                  offset: const Offset(1, 1),
                )
              ]),
          child: SelectableText(
            message.text,
            style: TextStyle(
              color: message.isUser
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurfaceVariant,
              fontSize: 15,
            ),
          ),
        ),
      ),
    );
  }
}

// --- Helper Extension ---
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
