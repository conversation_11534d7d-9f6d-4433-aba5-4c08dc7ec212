1. START
CREATE TABLE `thecollegeofwoosterhelpdesks` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `fullname` varchar(255) NOT NULL,
  `building` varchar(100) NOT NULL,
  `room` varchar(100) NOT NULL,
  `hours` varchar(100) NOT NULL,
  `payment` varchar(200) NOT NULL,
  `phone` varchar(200) NOT NULL,
  `email` varchar(200) NOT NULL,
  `fax` varchar(200) NOT NULL,
  `whatsapp` varchar(15) NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` varchar(200) NOT NULL,
  `longitude` varchar(200) NOT NULL,
  PRIMARY KEY (`id`)


CREATE TABLE `thecollegeofwoosteraccessibility` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `phone` varchar(20) NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterfaq` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `topic` mediumtext NOT NULL,
  `studentquestion` mediumtext NOT NULL,
  `facultyorstaffquestion` mediumtext NOT NULL,
  `parentquestion` mediumtext NOT NULL,
  `admissionsquestion` mediumtext NOT NULL,
  `orientationquestion` mediumtext NOT NULL,
  `symposiumquestion` mediumtext NOT NULL,
  `graduationquestion` mediumtext NOT NULL,
  `alumniquestion` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterlinks` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `link` mediumtext NOT NULL,
  `admissionslink` mediumtext NOT NULL,
  `orientationlink` mediumtext NOT NULL,
  `symposiumlink` mediumtext NOT NULL,
  `alumnilink` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `img_link` mediumtext NOT NULL,
  `about` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterconstruction` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `location` varchar(2000) NOT NULL,
  `startdate` varchar(10) NOT NULL,
  `enddate` varchar(10) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `latitude` varchar(10) NOT NULL,
  `longitude` varchar(10) NOT NULL,
  `about` varchar(5000) NOT NULL


CREATE TABLE `thecollegeofwoosterprinting` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` varchar(20) NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterdaycares` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `boxnumber` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` varchar(5000) NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  PRIMARY KEY (`id`)

CREATE TABLE `thecollegeofwoostersustainability` (
  `id` int(10) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL

2. UPDATES
CREATE TABLE `thecollegeofwoosternotices` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` mediumtext,
  `day` varchar(10) DEFAULT NULL,
  `month` varchar(10) NOT NULL,
  `year` varchar(4) NOT NULL,
  `link` mediumtext

CREATE TABLE `thecollegeofwoostersocialmediafeeds` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `platform` mediumtext
  `link` mediumtext

3.PATH
a. admissions
CREATE TABLE `thecollegeofwoosterselection` (
  `id` int(255) NOT NULL,
  `firstname` mediumtext NOT NULL,
  `lastname` mediumtext NOT NULL,
  `gender` mediumtext NOT NULL,
  `year` mediumtext NOT NULL,
  `major` mediumtext NOT NULL,
  `nationality` mediumtext NOT NULL,
  `yearofentry` mediumtext NOT NULL

b. affordability
CREATE TABLE `thecollegeofwoostercostsorrates` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `amount` mediumtext NOT NULL,
  `about` varchar(500) DEFAULT NULL,
  `categoryormajor` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterscholarships` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `about` varchar(300) NOT NULL,
  `major` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterpayments` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `link` varchar(500) DEFAULT NULL,
  `about` mediumtext NOT NULL

c. orientation
CREATE TABLE `thecollegeofwoosterorientations` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` mediumtext,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL
d. symposium
CREATE TABLE `thecollegeofwoostersymposiums` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` mediumtext,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL
e. graduation
  `id` int(10) NOT NULL,
  `venue` mediumtext NOT NULL,
  `alternatelocation` mediumtext NOT NULL,
  `speaker` mediumtext NOT NULL,
  `starttime` mediumtext NOT NULL,
  `endtime` mediumtext NOT NULL,
  `day` mediumtext NOT NULL,
  `month` mediumtext NOT NULL,
  `year` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

4.PEOPLE
CREATE TABLE `thecollegeofwoosterpeople` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `title` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `officehours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `ext` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `facultymember` mediumtext NOT NULL,
  `staffmember` mediumtext NOT NULL,
  `orientationcontact` mediumtext NOT NULL,
  `graduationcontact` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `services` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostercurrentstudents` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `year` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,


5.LODGING
CREATE TABLE `thecollegeofwoosterhousing` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `hours` mediumtext NOT NULL,
  `accessmethod` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `residentorgorclub` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterlocallodging` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `hours` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

6.SHOP OR EAT
CREATE TABLE `thecollegeofwoostershopsoreateries` (
			"id":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["id"]))).'", 
			"fullname":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", str_replace('é', "e", str_replace('"', "", str_replace('/', "_", $row["fullname"])  ))  )    )).'",
			"type":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["type"]))).'",
			"building":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["building"]))).'",	
			"room":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["room"]))).'",					
			"hours":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["hours"]))).'",	
			"payment":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["payment"]))).'",	
			"phone":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["phone"]))).'",		
			"email":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["email"]))).'",	
			"fax":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["fax"]))).'",
			"whatsapp":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["whatsapp"]))).'",
			"img_link":"https://storage.googleapis.com/streamlinexmbucket/'.strtolower(str_replace(' ', '', $_SESSION['selectedschool'])).'/shoporeat/'.$row["fullname"].'.jpg",						
			"about":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["about"]))).'",				
			"latitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["latitude"]))).'",	
			"longitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["longitude"]))).'",

CREATE TABLE `thecollegeofwoostermealplans` (
  `id` int(11) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext


CREATE TABLE `thecollegeofwoosterlocalareadining` (

			"id":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["id"]))).'", 
			"fullname":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", str_replace('é', "e", str_replace('"', "", str_replace('/', "_", $row["fullname"])  ))  )    )).'",
			"hours":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["hours"]))).'",	
			"payment":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["payment"]))).'",	
			"phone":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["phone"]))).'",			
			"email":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["email"]))).'",	
			"whatsapp":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["whatsapp"]))).'",	
			"img_link":"https://storage.googleapis.com/streamlinexmbucket/'.strtolower(str_replace(' ', '', $_SESSION['selectedschool'])).'/localareadining/'.$row["fullname"].'.jpg",						
			"about":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["about"]))).'",		
			"latitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["latitude"]))).'",			
			"longitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["longitude"]))).'",

CREATE TABLE `thecollegeofwoosterstudentdiscounts` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `hours` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterinventory` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `price` varchar(500) DEFAULT NULL,
  `seller` varchar(255) DEFAULT NULL,
  `category` varchar(255) NOT NULL,
  `onsale` mediumtext NOT NULL,
  `about` varchar(500) DEFAULT NULL,
  `originalprice` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoostermenus` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `price` varchar(10) NOT NULL,
  `dininglocation` mediumtext NOT NULL,
  `diningstation` varchar(255) DEFAULT NULL,
  `breakfast` mediumtext NOT NULL,
  `lunch` mediumtext NOT NULL,
  `dinner` mediumtext NOT NULL,
  `_mon` mediumtext NOT NULL,
  `_tue` mediumtext NOT NULL,
  `_wed` mediumtext NOT NULL,
  `_thur` mediumtext NOT NULL,
  `_fri` mediumtext NOT NULL,
  `_sat` mediumtext NOT NULL,
  `_sun` mediumtext NOT NULL,
  `about` mediumtext,
  `allergens` mediumtext NOT NULL,
  `ingredients` mediumtext NOT NULL

7.TRANSPORT
CREATE TABLE `thecollegeofwoostercampusshuttle` (
			"fullname":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", str_replace('é', "e", str_replace('"', "", str_replace('/', "_", $row["fullname"])  ))  )    )).'",
			"phone":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["phone"]))).'",			
			"email":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["email"]))).'",	
			"whatsapp":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["whatsapp"]))).'",
			"fax":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["fax"]))).'",		
			"latitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["latitude"]))).'",			
			"longitude":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["longitude"]))).'",	
			"about":"'.trim(preg_replace('/[\t\n\r\s]+/', ' ', str_replace('"', "", $row["about"]))).'",	

CREATE TABLE `thecollegeofwoosterparkingspaces` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `hours` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `building2` mediumtext NOT NULL,
  `building3` mediumtext NOT NULL,
  `building4` mediumtext NOT NULL,
  `building5` mediumtext NOT NULL,
  `building6` mediumtext NOT NULL,
  `building7` mediumtext NOT NULL,
  `studentparking` mediumtext NOT NULL,
  `facultyorstaffparking` mediumtext NOT NULL,
  `about` mediumtext

CREATE TABLE `thecollegeofwoosterlocaltransport` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `fax` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `about` mediumtext

8.CORE
CREATE TABLE `thecollegeofwoosterschools` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `about` varchar(255) DEFAULT NULL

CREATE TABLE `thecollegeofwoosterdepartments` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `alumnidepartment` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostercenters` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `about` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterdocuments` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `link` mediumtext NOT NULL,
  `admissionsdocument` mediumtext NOT NULL,
  `graduationdocument` mediumtext NOT NULL,
  `statsdocument` mediumtext NOT NULL,
  `statsfinancial` mediumtext NOT NULL,
  `year` varchar(4) NOT NULL,
  `major` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `school` mediumtext NOT NULL

9.ACADEMICS
CREATE TABLE `thecollegeofwoostermajors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fullname` varchar(255) NOT NULL,
  `department` mediumtext NOT NULL,
  `department2` mediumtext NOT NULL,
  `department3` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `duration` mediumtext NOT NULL,
  `about` mediumtext,
  `whoitsfor` mediumtext NOT NULL,
  `goalsandobjectives` mediumtext NOT NULL,
  `aims` mediumtext NOT NULL,
  `careerprospects` mediumtext NOT NULL,
  `learningoutcomes` mediumtext NOT NULL,
  `learningmethods` mediumtext NOT NULL,
  `assessment` mediumtext NOT NULL,
  `studentsupport` mediumtext NOT NULL,
  `accredited` mediumtext NOT NULL,

CREATE TABLE `thecollegeofwoosterminors` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `department` mediumtext NOT NULL,
  `department2` mediumtext NOT NULL,
  `department3` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `duration` mediumtext NOT NULL,
  `about` mediumtext,
  `whoitsfor` mediumtext NOT NULL,
  `goalsandobjectives` mediumtext NOT NULL,
  `aims` mediumtext NOT NULL,
  `careerprospects` mediumtext NOT NULL,
  `learningoutcomes` mediumtext NOT NULL,
  `learningmethods` mediumtext NOT NULL,
  `assessment` mediumtext NOT NULL,
  `studentsupport` mediumtext NOT NULL,
  `accredited` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterfunding` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `about` varchar(300) NOT NULL,
  `major` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostercoursecatalog` (
  `id` int(255) NOT NULL,
  `modulecode` varchar(15) NOT NULL,
  `modulename` varchar(150) NOT NULL,
  `about` mediumtext NOT NULL,
  `major` mediumtext NOT NULL,
  `minor` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `department2` mediumtext NOT NULL,
  `department3` mediumtext NOT NULL,
  `prerequisites` mediumtext NOT NULL,
  `corequisites` mediumtext NOT NULL,
  `corecourse` mediumtext NOT NULL,
  `minorrequirement` varchar(20) NOT NULL,
  `electivecourse` mediumtext NOT NULL,
  `year` mediumtext NOT NULL,
  `term` mediumtext NOT NULL,
  `credits` mediumtext NOT NULL,
  `classesperweek` mediumtext NOT NULL,
  `labhoursperweek` mediumtext NOT NULL,
  `tutorialhoursperweek` mediumtext NOT NULL,
  `aim` mediumtext NOT NULL,
  `learningoutcomes` mediumtext NOT NULL,
  `assessment` mediumtext NOT NULL,
  `substitutemodulecode` varchar(20) NOT NULL,
  `substitutemodulename` varchar(150) NOT NULL,
  `offered` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterentryrequirements` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `major` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterenrollmentexercise` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `major` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosteracademicresources` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosteracademichonors` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` mediumtext,
  `major` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosteracademicprizes` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` mediumtext


CREATE TABLE `thecollegeofwoosteracademicdress` (
  `id` int(11) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `category` mediumtext NOT NULL,
  `style` mediumtext NOT NULL,
  `color` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostergradingscale` (
  `id` int(255) NOT NULL,
  `mark` varchar(5) NOT NULL,
  `lettergrade` varchar(5) NOT NULL,
  `graderemark` varchar(20) NOT NULL,
  `programlevel` varchar(20) NOT NULL

10.PROGRAMS
CREATE TABLE `thecollegeofwoosterprograms` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostersignatureevents` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `alumnisignatureevent` varchar(25) NOT NULL,
  `about` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostertraditions` (
  `id` int(11) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext

CREATE TABLE `thecollegeofwoosterpartnershipopportunities` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext NOT NULL


11.ATHLETICS AND GROUPS
CREATE TABLE `thecollegeofwoosterathletics` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext,
  `link` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterorgsorclubs` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `about` mediumtext

CREATE TABLE `thecollegeofwoosterresearchgroups` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `about` mediumtext

CREATE TABLE `thecollegeofwoostercommittees` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` mediumtext NOT NULL,
  `about` mediumtext NOT NULL




12.MEDIA
CREATE TABLE `thecollegeofwoosternews` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `day` varchar(10) NOT NULL,
  `month` varchar(10) NOT NULL,
  `year` varchar(4) NOT NULL,
  `publisher` mediumtext,
  `link` mediumtext

CREATE TABLE `thecollegeofwoosterperiodicals` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `thecollegeofwoosterradio` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `thecollegeofwoostertelevision` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `thecollegeofwoosterphotos` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `day` varchar(10) NOT NULL,
  `month` varchar(10) NOT NULL,
  `year` varchar(4) NOT NULL,
  `platform` mediumtext,
  `link` mediumtext

CREATE TABLE `thecollegeofwoostervideos` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `day` varchar(10) NOT NULL,
  `month` varchar(10) NOT NULL,
  `year` varchar(4) NOT NULL,
  `platform` mediumtext,
  `link` mediumtext

13.STARTUPS
CREATE TABLE `thecollegeofwoosteraccelerators` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostermakerspaces` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterstartupfunds` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `about` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterstartups` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `about` varchar(500) DEFAULT NULL,
  `websitelink` mediumtext

14.PROJECTS AND PUBLICATION
CREATE TABLE `thecollegeofwoosterresearchprojects` (
  `id` int(255) NOT NULL,
  `fullname` varchar(500) NOT NULL,
  `year` varchar(10) NOT NULL,
  `researcher` mediumtext NOT NULL,
  `researcher2` mediumtext NOT NULL,
  `researcher3` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `facultyorstaffresearch` mediumtext NOT NULL,
  `studentresearch` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `link` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostertheses` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) CHARACTER SET utf8 NOT NULL,
  `year` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `author` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `advisor` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `advisor2` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `department` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `career` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL,
  `about` varchar(5000) CHARACTER SET utf8 DEFAULT NULL,
  `link` mediumtext COLLATE utf8_general_mysql500_ci NOT NULL

CREATE TABLE `thecollegeofwoosterbooks` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `year` varchar(255) DEFAULT NULL,
  `author` mediumtext NOT NULL,
  `author2` mediumtext NOT NULL,
  `author3` mediumtext NOT NULL,
  `facultyorstaffbook` mediumtext NOT NULL,
  `studentbook` mediumtext NOT NULL,
  `publisher` mediumtext NOT NULL,
  `about` varchar(5000) NOT NULL,
  `link` varchar(255) DEFAULT NULL


CREATE TABLE `thecollegeofwoosterarticles` (
  `id` int(255) NOT NULL,
  `fullname` varchar(120) NOT NULL,
  `year` mediumtext NOT NULL,
  `author` mediumtext NOT NULL,
  `author2` mediumtext NOT NULL,
  `author3` mediumtext NOT NULL,
  `facultyorstaffarticle` mediumtext NOT NULL,
  `studentarticle` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `link` varchar(1500) NOT NULL


CREATE TABLE `thecollegeofwoosterpatents` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `year` varchar(500) DEFAULT NULL,
  `inventor` mediumtext NOT NULL,
  `inventor2` mediumtext NOT NULL,
  `inventor3` mediumtext NOT NULL,
  `facultyorstaffpatent` mediumtext NOT NULL,
  `studentpatent` mediumtext NOT NULL,
  `about` mediumtext,
  `link` mediumtext

15.BUILDING AND SPACES
CREATE TABLE `thecollegeofwoosterbuilding` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `location` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `accessmethod` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `mapreferencepoint` mediumtext NOT NULL,
  `about` mediumtext,
  `building` mediumtext NOT NULL,
  `housing` mediumtext NOT NULL,
  `parking` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `residence` mediumtext NOT NULL,
  `library` mediumtext NOT NULL,
  `lab` mediumtext NOT NULL,
  `studio` mediumtext NOT NULL,
  `gallery` mediumtext NOT NULL,
  `theater` mediumtext NOT NULL,
  `coworkingspace` mediumtext NOT NULL,
  `studyspace` mediumtext NOT NULL,
  `fitnessspace` mediumtext NOT NULL,
  `funspace` mediumtext NOT NULL,
  `storagespace` mediumtext NOT NULL,
  `mailingspace` mediumtext NOT NULL,
  `museum` mediumtext NOT NULL,
  `sacredspace` mediumtext NOT NULL,
  `outdoorspace` mediumtext NOT NULL,
  `researchstation` mediumtext NOT NULL,
  `clinic` mediumtext NOT NULL,
  `laundryspace` mediumtext NOT NULL,
  `campusdumpsters` mediumtext NOT NULL,
  `watertanks` mediumtext NOT NULL,
  `other` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterrooms` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `roomtype` mediumtext NOT NULL,
  `dimensions` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterroomequipment` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `roomtype` mediumtext NOT NULL,
  `assettag` mediumtext NOT NULL,
  `dateinstalled` mediumtext NOT NULL,
  `status` mediumtext NOT NULL,
  `about` mediumtext

CREATE TABLE `thecollegeofwoosterroomassignments` (
  `id` int(11) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `gender` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterpublicart` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosteremergencyequipment` (
  `id` int(255) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `about` mediumtext NOT NULL


16.STATISTICS
- uses documents table

17.CALENDAR
CREATE TABLE `thecollegeofwoosterclassschedules` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `starttime` mediumtext NOT NULL,
  `endtime` mediumtext NOT NULL,
  `major` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `_mon` mediumtext NOT NULL,
  `_tue` mediumtext NOT NULL,
  `_wed` mediumtext NOT NULL,
  `_thur` mediumtext NOT NULL,
  `_fri` mediumtext NOT NULL,
  `_sat` mediumtext NOT NULL,
  `_sun` mediumtext NOT NULL,
  `instructor` mediumtext NOT NULL,
  `ta` mediumtext NOT NULL,
  `enrollmentcapacity` mediumtext NOT NULL,
  `instructionmode` mediumtext NOT NULL,
  `books` mediumtext NOT NULL,
  `startday` varchar(12) NOT NULL,
  `startmonth` varchar(12) NOT NULL,
  `startyear` varchar(12) NOT NULL,
  `endday` varchar(12) NOT NULL,
  `endmonth` varchar(12) NOT NULL,
  `endyear` varchar(12) NOT NULL,
  `location` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` mediumtext NOT NULL,
  `ticketslink` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterweeklyschedule` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `teamororg` mediumtext NOT NULL,
  `starttime` mediumtext NOT NULL,
  `endtime` mediumtext NOT NULL,
  `_mon` mediumtext NOT NULL,
  `_tue` mediumtext NOT NULL,
  `_wed` mediumtext NOT NULL,
  `_thur` mediumtext NOT NULL,
  `_fri` mediumtext NOT NULL,
  `_sat` mediumtext NOT NULL,
  `_sun` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `startday` varchar(12) NOT NULL,
  `startmonth` varchar(12) NOT NULL,
  `startyear` varchar(12) NOT NULL,
  `endday` varchar(12) NOT NULL,
  `endmonth` varchar(12) NOT NULL,
  `endyear` varchar(12) NOT NULL,
  `location` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` mediumtext NOT NULL,
  `ticketslink` mediumtext NOT NULL,
  `major` mediumtext NOT NULL,
  `instructor` mediumtext NOT NULL,
  `ta` mediumtext NOT NULL,
  `enrollmentcapacity` mediumtext NOT NULL,
  `instructionmode` mediumtext NOT NULL,
  `syllabuscount` int(11) NOT NULL,
  `books` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterevents` (
  `id` bigint(255) unsigned NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `location` mediumtext NOT NULL,
  `starttime` mediumtext NOT NULL,
  `endtime` mediumtext NOT NULL,
  `startday` mediumtext NOT NULL,
  `startmonth` mediumtext NOT NULL,
  `startyear` mediumtext NOT NULL,
  `endday` mediumtext NOT NULL,
  `endmonth` mediumtext NOT NULL,
  `endyear` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `whatsapp` varchar(20) NOT NULL,
  `teamororg` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `admissionskeydate` mediumtext NOT NULL,
  `paymentkeydate` mediumtext NOT NULL,
  `orientationevent` mediumtext NOT NULL,
  `graduationevent` mediumtext NOT NULL,
  `graduationkeydate` mediumtext NOT NULL,
  `alumnievent` mediumtext NOT NULL,
  `communityrentalevent` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `ticketslink` mediumtext NOT NULL,
  `latitude` varchar(20) NOT NULL,
  `longitude` varchar(20) NOT NULL,
  `_mon` mediumtext NOT NULL,
  `_tue` mediumtext NOT NULL,
  `_wed` mediumtext NOT NULL,
  `_thur` mediumtext NOT NULL,
  `_fri` mediumtext NOT NULL,
  `_sat` mediumtext NOT NULL,
  `_sun` mediumtext NOT NULL,
  `major` mediumtext NOT NULL,
  `instructor` mediumtext NOT NULL,
  `ta` mediumtext NOT NULL,
  `enrollmentcapacity` mediumtext NOT NULL,
  `instructionmode` mediumtext NOT NULL,
  `books` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosteracademiccalendar` (
  `id` int(20) unsigned NOT NULL,
  `fullname` mediumtext NOT NULL,
  `starttime` mediumtext NOT NULL,
  `endtime` mediumtext NOT NULL,
  `startday` mediumtext NOT NULL,
  `startmonth` mediumtext NOT NULL,
  `startyear` mediumtext NOT NULL,
  `endday` mediumtext NOT NULL,
  `endmonth` mediumtext NOT NULL,
  `endyear` mediumtext NOT NULL





18.MAP
- uses buildings table

19.FEEDBACK
CREATE TABLE `thecollegeofwoosterfeedback` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `link` mediumtext


20.HISTORICAL TIMELINE
CREATE TABLE `thecollegeofwoostersocialmediafeeds` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `year` mediumtext
  `about` mediumtext


20.RENTALS
CREATE TABLE `thecollegeofwoosterrentals` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `dimensions` mediumtext NOT NULL,
  `capacity` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `whatsapp` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `facilityrental` mediumtext NOT NULL,
  `equipmentrental` mediumtext NOT NULL,
  `about` varchar(255) DEFAULT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL
  `pricing` mediumtext NOT NULL


CREATE TABLE `thecollegeofwoosterrentalequipmentcalendar` (
  `id` int(255) NOT NULL,
  `asset` mediumtext NOT NULL,
  `tagoridentifier` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `renter` mediumtext NOT NULL,
  `checkoutday` mediumtext NOT NULL,
  `checkoutmonth` mediumtext NOT NULL,
  `checkoutyear` mediumtext NOT NULL,
  `returnday` mediumtext NOT NULL,
  `returnmonth` mediumtext NOT NULL,
  `returnyear` mediumtext NOT NULL

21.JOBS

CREATE TABLE `thecollegeofwoosterjobs` (
  `id` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `openingday` mediumtext NOT NULL,
  `openingmonth` mediumtext NOT NULL,
  `openingyear` mediumtext NOT NULL,
  `closingday` mediumtext NOT NULL,
  `closingmonth` mediumtext NOT NULL,
  `closingyear` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `link` mediumtext NOT NULL,
  `adminjob` mediumtext NOT NULL,
  `academicjob` mediumtext NOT NULL,
  `about` mediumtext NOT NULL
22.SERVICES
CREATE TABLE `thecollegeofwoosterservices` (
  `id` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `price` mediumtext NOT NULL,
  `requirements` mediumtext NOT NULL,
  `time` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `link` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,

23.MONEY
CREATE TABLE `thecollegeofwoosteratms` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `about` mediumtext,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoosterpayments` (
  `id` int(11) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `link` varchar(500) DEFAULT NULL,
  `about` mediumtext NOT NULL

24.HEALTH
CREATE TABLE `thecollegeofwoosterclinicsorhospitals` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `whatsapp` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL,
  `daysnhours` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `address` mediumtext NOT NULL,
  `postaladdress` mediumtext NOT NULL,
  `payment` mediumtext NOT NULL,
  `mission` mediumtext NOT NULL,
  `vision` mediumtext NOT NULL,
  `corevalues` mediumtext NOT NULL,
  `acceptableclients` mediumtext NOT NULL,
  `acceptablehealthinsurance` mediumtext NOT NULL,
  `admissions` mediumtext NOT NULL,
  `visitors` mediumtext NOT NULL,
  `visitinghours` mediumtext NOT NULL,
  `discharge` mediumtext NOT NULL,
  `parking` mediumtext NOT NULL,
  PRIMARY KEY (`id`)

CREATE TABLE `thecollegeofwoostercounselingservices` (
  `id` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `price` mediumtext NOT NULL,
  `requirements` mediumtext NOT NULL,
  `time` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `link` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,

25.SAFETY
CREATE TABLE `thecollegeofwoosteremergencycontacts` (
  `id` int(255) NOT NULL,
  `fullname` mediumtext NOT NULL,
  `title` mediumtext NOT NULL,
  `department` mediumtext NOT NULL,
  `school` mediumtext NOT NULL,
  `school2` mediumtext NOT NULL,
  `school3` mediumtext NOT NULL,
  `building` mediumtext NOT NULL,
  `room` mediumtext NOT NULL,
  `officehours` mediumtext NOT NULL,
  `phone` mediumtext NOT NULL,
  `ext` mediumtext NOT NULL,
  `email` mediumtext NOT NULL,
  `fax` mediumtext NOT NULL,
  `hours` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
  `services` mediumtext NOT NULL,
  `latitude` mediumtext NOT NULL,
  `longitude` mediumtext NOT NULL

CREATE TABLE `thecollegeofwoostersafetyprocedures` (
  `id` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `about` mediumtext NOT NULL,

25.CONNECTIVITY
CREATE TABLE `thecollegeofwoosterconnectivity` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,

26.GIVING
CREATE TABLE `thecollegeofwoostergiving` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `fullname` mediumtext NOT NULL,
  `about` mediumtext NOT NULL,
27.VIRTUAL TOUR
- use buildings and housing tables by adding a 'virtualtourlocation' field







**I. Updated Schema Outline (Fields Only)**

*(Based on your provided `CREATE TABLE` statements, interpreting syntax)*

**1. START**
*   `thecollegeofwooster_helpdesks`: id, fullname, building, room, hours, payment, phone, email, fax, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_accessibility`: id, fullname, building, room, hours, phone, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_faq`: id, fullname, about, department, school, topic, studentquestion, facultyorstaffquestion, parentquestion, admissionsquestion, orientationquestion, symposiumquestion, graduationquestion, alumniquestion
*   `thecollegeofwooster_links`: id, fullname, link, admissionslink, orientationlink, symposiumlink, alumnilink, department, img_link, about
*   `thecollegeofwooster_construction`: id, fullname, location, startdate, enddate, phone, whatsapp, latitude, longitude, about
*   `thecollegeofwooster_printing`: id, fullname, building, room, hours, payment, phone, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_daycares`: id, fullname, building, room, hours, phone, boxnumber, email, fax, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_sustainability`: id, fullname, about

**2. UPDATES**
*   `thecollegeofwooster_notices`: id, fullname, about, day, month, year, link
*   `thecollegeofwooster_socialmediafeeds`: id, fullname, platform, link *

**3. PATH**
*   `thecollegeofwooster_selection`: id, firstname, lastname, gender, year, major, nationality, yearofentry
*   `thecollegeofwooster_costsorrates`: id, fullname, amount, about, categoryormajor
*   `thecollegeofwooster_scholarships`: id, fullname, about, major
*   `thecollegeofwooster_payments`: id, fullname, link, about 
*   `thecollegeofwooster_orientations`: id, fullname, about, phone, email, fax
*   `thecollegeofwooster_symposiums`: id, fullname, about, phone, email, fax
*   thecollegeofwooster_graduation: id, venue, alternatelocation, speaker, starttime, endtime, day, month, year, about, latitude, longitude

**4. PEOPLE**
*   `thecollegeofwooster_people`: id, fullname, title, department, school, school2, school3, building, room, officehours, phone, ext, email, fax, hours, facultymember, staffmember, orientationcontact, graduationcontact, about, services, latitude, longitude
*   `thecollegeofwooster_currentstudents`: id, fullname, year, department, building, room

**5. LODGING**
*   `thecollegeofwooster_housing`: id, fullname, hours, accessmethod, capacity, residentorgorclub, phone, about, latitude, longitude
*   `thecollegeofwooster_locallodging`: id, fullname, hours, payment, phone, email, whatsapp, about, latitude, longitude

**6. SHOP OR EAT**
*   `thecollegeofwooster_shopsoreateries`: id, fullname, type, building, room, hours, payment, phone, email, fax, whatsapp, img_link, about, latitude, longitude
*   `thecollegeofwooster_mealplans`: id, fullname, about
*   `thecollegeofwooster_localareadining`: id, fullname, hours, payment, phone, email, whatsapp, img_link, about, latitude, longitude
*   `thecollegeofwooster_studentdiscounts`: id, fullname, hours, payment, phone, email, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_inventory`: id, fullname, price, seller, category, onsale, about, originalprice
*   `thecollegeofwooster_menus`: id, fullname, price, dininglocation, diningstation, breakfast, lunch, dinner, _mon, _tue, _wed, _thur, _fri, _sat, _sun, about, allergens, ingredients

**7. TRANSPORT**
*   `thecollegeofwooster_campusshuttle`: fullname, phone, email, whatsapp, fax, latitude, longitude, about
*   `thecollegeofwooster_parkingspaces`: id, fullname, hours, payment, capacity, phone, whatsapp, latitude, longitude, building, building2, building3, building4, building5, building6, building7, studentparking, facultyorstaffparking, about
*   `thecollegeofwooster_localtransport`: id, fullname, payment, phone, email, whatsapp, fax, latitude, longitude, about

**8. CORE**
*   `thecollegeofwooster_schools`: id, fullname, building, room, phone, email, fax, whatsapp, latitude, longitude, about
*   `thecollegeofwooster_departments`: id, fullname, building, room, payment, phone, email, fax, whatsapp, school, school2, school3, alumnidepartment, about, latitude, longitude
*   `thecollegeofwooster_centers`: id, fullname, building, room, hours, payment, phone, email, fax, whatsapp, latitude, longitude, about
*   `thecollegeofwooster_documents`: id, fullname, link, admissionsdocument, graduationdocument, statsdocument, statsfinancial, year, major, department, school

**9. ACADEMICS**
*   `thecollegeofwooster_majors`: id, fullname, department, department2, department3, school, school2, school3, duration, about, whoitsfor, goalsandobjectives, aims, careerprospects, learningoutcomes, learningmethods, assessment, studentsupport, accredited
*   `thecollegeofwooster_minors`: id, fullname, department, department2, department3, school, school2, school3, duration, about, whoitsfor, goalsandobjectives, aims, careerprospects, learningoutcomes, learningmethods, assessment, studentsupport, accredited
*   `thecollegeofwooster_funding`: id, fullname, about, major
*   `thecollegeofwooster_coursecatalog`: id, modulecode, modulename, about, major, minor, school, school2, school3, department, department2, department3, prerequisites, corequisites, corecourse, minorrequirement, electivecourse, year, term, credits, classesperweek, labhoursperweek, tutorialhoursperweek, aim, learningoutcomes, assessment, substitutemodulecode, substitutemodulename, offered
*   `thecollegeofwooster_entryrequirements`: id, fullname, about, major
*   `thecollegeofwooster_enrollmentexercise`: id, fullname, about, major
*   `thecollegeofwooster_academicresources`: id, fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_academichonors`: id, fullname, about, major
*   `thecollegeofwooster_academicprizes`: id, fullname, about
*   `thecollegeofwooster_academicdress`: id, fullname, category, style, color
*   `thecollegeofwooster_gradingscale`: id, mark, lettergrade, graderemark, programlevel

**10. PROGRAMS**
*   `thecollegeofwooster_programs`: id, fullname, phone, email, whatsapp, about
*   `thecollegeofwooster_signatureevents`: id, fullname, alumnisignatureevent, about
*   `thecollegeofwooster_traditions`: id, fullname, about
*   `thecollegeofwooster_partnershipopportunities`: id, fullname, phone, email, whatsapp, about

**11. ATHLETICS AND GROUPS**
*   `thecollegeofwooster_athletics`: id, fullname, phone, email, whatsapp, about, link
*   `thecollegeofwooster_orgsorclubs`: id, fullname, phone, email, about
*   `thecollegeofwooster_researchgroups`: id, fullname, phone, email, about
*   `thecollegeofwooster_committees`: id, fullname, phone, email, about

**12. MEDIA**
*   `thecollegeofwooster_news`: id, fullname, day, month, year, publisher, link
*   `thecollegeofwooster_periodicals`: id, fullname, phone, email, whatsapp, about
*   `thecollegeofwooster_radio`: id, fullname, phone, email, whatsapp, about
*   `thecollegeofwooster_television`: id, fullname, phone, email, whatsapp, about
*   `thecollegeofwooster_photos`: id, fullname, day, month, year, platform, link
*   `thecollegeofwooster_videos`: id, fullname, day, month, year, platform, link

**13. STARTUPS**
*   `thecollegeofwooster_accelerators`: id, fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_makerspaces`: id, fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude
*   `thecollegeofwooster_startupfunds`: id, fullname, phone, email, about
*   `thecollegeofwooster_startups`: id, fullname, about, websitelink

**14. PROJECTS AND PUBLICATION**
*   `thecollegeofwooster_researchprojects`: id, fullname, year, researcher, researcher2, researcher3, department, facultyorstaffresearch, studentresearch, about, link
*   `thecollegeofwooster_theses`: id, fullname, year, author, advisor, advisor2, department, career, about, link
*   `thecollegeofwooster_books`: id, fullname, year, author, author2, author3, facultyorstaffbook, studentbook, publisher, about, link
*   `thecollegeofwooster_articles`: id, fullname, year, author, author2, author3, facultyorstaffarticle, studentarticle, about, link
*   `thecollegeofwooster_patents`: id, fullname, year, inventor, inventor2, inventor3, facultyorstaffpatent, studentpatent, about, link

**15. BUILDING AND SPACES**
*   `thecollegeofwooster_building`: id, fullname, location, hours, accessmethod, capacity, phone, latitude, longitude, mapreferencepoint, about, building, housing, parking, school, residence, library, lab, studio, gallery, theater, coworkingspace, studyspace, fitnessspace, funspace, storagespace, mailingspace, museum, sacredspace, outdoorspace, researchstation, clinic, laundryspace, campusdumpsters, watertanks, other
*   `thecollegeofwooster_rooms`: id, fullname, building, roomtype, dimensions, capacity, phone, fax, about, latitude, longitude
*   `thecollegeofwooster_roomequipment`: id, fullname, building, room, roomtype, assettag, dateinstalled, status, about
*   `thecollegeofwooster_roomassignments`: id, fullname, building, room, gender
*   `thecollegeofwooster_publicart`: id, fullname, building, room, email, about, latitude, longitude
*   `thecollegeofwooster_emergencyequipment`: id, fullname, building, room, latitude, longitude, about


**17. CALENDAR**
*   `thecollegeofwooster_classschedules`: id, fullname, building, room, starttime, endtime, major, department, _mon, _tue, _wed, _thur, _fri, _sat, _sun, instructor, ta, enrollmentcapacity, instructionmode, books, startday, startmonth, startyear, endday, endmonth, endyear, location, payment, about, capacity, phone, email, whatsapp, ticketslink, latitude, longitude
*   `thecollegeofwooster_weeklyschedule`: id, fullname, building, room, teamororg, starttime, endtime, _mon, _tue, _wed, _thur, _fri, _sat, _sun, about, startday, startmonth, startyear, endday, endmonth, endyear, location, payment, capacity, phone, email, whatsapp, ticketslink, major, instructor, ta, enrollmentcapacity, instructionmode, syllabuscount, books, latitude, longitude
*   `thecollegeofwooster_events`: id, fullname, building, room, location, starttime, endtime, startday, startmonth, startyear, endday, endmonth, endyear, payment, capacity, phone, email, whatsapp, teamororg, department, admissionskeydate, paymentkeydate, orientationevent, graduationevent, graduationkeydate, alumnievent, communityrentalevent, about, ticketslink, latitude, longitude, _mon, _tue, _wed, _thur, _fri, _sat, _sun, major, instructor, ta, enrollmentcapacity, instructionmode, books
*   `thecollegeofwooster_academiccalendar`: id, fullname, starttime, endtime, startday, startmonth, startyear, endday, endmonth, endyear


**19. FEEDBACK**
*   `thecollegeofwooster_feedback`: id, fullname, link

**20. HISTORICAL TIMELINE**
*   `thecollegeofwooster_historicaltimeline`: id, fullname, year, about 

**21. RENTALS**
*   `thecollegeofwooster_rentals`: id, fullname, dimensions, capacity, phone, whatsapp, payment, department, facilityrental, equipmentrental, about, latitude, longitude, pricing
*   `thecollegeofwooster_rentalequipmentcalendar`: id, asset, tagoridentifier, department, renter, checkoutday, checkoutmonth, checkoutyear, returnday, returnmonth, returnyear

**22. JOBS**
*   `thecollegeofwooster_jobs`: id, fullname, openingday, openingmonth, openingyear, closingday, closingmonth, closingyear, email, link, adminjob, academicjob, about

**23. SERVICES**
*   `thecollegeofwooster_services`: id, fullname, price, requirements, time, department, link, about

**24. MONEY**
*   `thecollegeofwooster_atms`: id, fullname, building, room, about, latitude, longitude
*   `thecollegeofwooster_payments`: id, fullname, link, about *(Note: Duplicate name from Section 3)*

**25. HEALTH**
*   `thecollegeofwooster_clinicsorhospitals`: id, fullname, about, phone, email, fax, whatsapp, latitude, longitude, daysnhours, building, room, address, postaladdress, payment, mission, vision, corevalues, acceptableclients, acceptablehealthinsurance, admissions, visitors, visitinghours, discharge, parking
*   `thecollegeofwooster_counselingservices`: id, fullname, price, requirements, time, department, link, about

**26. SAFETY**
*   `thecollegeofwooster_emergencycontacts`: id, fullname, title, department, school, school2, school3, building, room, officehours, phone, ext, email, fax, hours, about, services, latitude, longitude
*   `thecollegeofwooster_safetyprocedures`: id, fullname, about

**27. CONNECTIVITY**
*   `thecollegeofwooster_connectivity`: id, fullname, about

**28. GIVING**
*   `thecollegeofwooster_giving`: id, fullname, about




html
<!DOCTYPE html>
<html lang="en">
<head>
    <base href=".">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Data Dashboard</title>
    <!-- Google Fonts and Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS (for map placeholder) -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css">
    <style>
        /* --- Styles Adapted from Provided Example --- */
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #000000;
            transition: background-color 0.3s, color 0.3s;
            font-size: 16px;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .container {
           display: flex;
           width: 100%;
           height: 100%;
        }

        /* Scrollbar */
        ::-webkit-scrollbar {width: 8px; cursor:pointer;}
        ::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px;}
        ::-webkit-scrollbar-thumb { background: #aaa; border-radius: 4px;}
        ::-webkit-scrollbar-thumb:hover { background: #888; }

        /* Sidebar */
        .sidebar-container {
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 240px;
            z-index: 1001;
            background-color: #ffffff;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar {
           display: flex;
           flex-direction: column;
           height: 100%;
           width: 100%;
        }

        .sidebar-brand {
            flex-shrink: 0;
            font-weight: 700;
            font-size: 20px;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 10px;
            height: 59px;
            line-height: 29px;
            box-sizing: border-box;
            color: #000000;
            text-align: center;
        }

        .sidebar-items {
            flex-grow: 1;
            overflow-y: auto;
            padding: 0 10px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 10px 10px;
            cursor: pointer;
            font-size: 16px;
            position: relative;
            border-radius: 4px;
            margin-bottom: 2px;
             color: #000000;
             transition: background-color 0.2s, color 0.2s;
        }

        .sidebar-item.active {
            background-color: rgba(0, 0, 0, 0.05);
            font-weight: bold;
             color: #000000;
        }

        .sidebar-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 5px;
            bottom: 5px;
            width: 4px;
            background-color: #000;
            border-radius: 2px 0 0 2px;
        }

         .sidebar-item:hover {
             background-color: rgba(0, 0, 0, 0.08);
         }

        .sidebar-item i.material-icons {
            margin-right: 15px;
            font-size: 22px;
            width: 24px;
            text-align: center;
        }
         .sidebar-item span {
             white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
         }


        /* Main Content Area */
        .main-content-wrapper {
            margin-left: 240px;
            width: calc(100% - 240px);
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

         .content-header {
            display: flex;
            background-color: #ffffff;
            padding: 0 20px;
            margin-bottom: 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            height: 59px;
            align-items: center;
            box-sizing: border-box;
             border-bottom: 1px solid #e0e0e0;
             flex-shrink: 0;
        }

         .header-title-container {
            display: flex;
            align-items: center;
            height: 100%;
            justify-content: flex-start;
            flex-grow: 1;
            overflow: hidden; /* Prevent title pushing buttons */
        }

         .header-title {
            font-size: 20px;
            font-weight: 500;
            margin: 0;
            line-height: 29px;
            text-align: left;
             color: #000000;
             white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
             margin-right: 15px; /* Space before buttons */
        }


         .main-content-area { /* The scrollable area */
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px 25px;
            background-color: #f5f5f5;
         }

         /* Content Views */
         .view-container { display: none; } /* Base class for views */
         .view-container.active { display: block; }

        /* Grid View Specific Styles */
         .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Adjust minmax */
            gap: 20px;
            padding-bottom: 20px; /* Space at bottom */
        }

        .grid-item {
            background-color: #fff;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            font-size: 15px; /* Slightly smaller grid item text */
             color: #333;
        }
         .grid-item:hover {
             transform: translateY(-3px);
             box-shadow: 0 4px 8px rgba(0,0,0,0.08);
         }

        .grid-item i.material-icons {
            font-size: 32px; /* Icon size in grid */
            margin-bottom: 10px;
            display: block; /* Center icon */
            color: #555; /* Icon color */
        }


        /* Table View Specific Styles */
        .table-view-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .back-arrow {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 10px;
            padding: 5px;
            border-radius: 50%;
             color: #555;
             transition: background-color 0.2s;
        }
         .back-arrow:hover {
             background-color: rgba(0,0,0,0.08);
         }

        /* Table Management Blocks */
        .table-management {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .table-management h3 { /* Table Title */
            margin-top: 0;
            color: #333333;
            font-size: 1.3em; /* Make table title slightly larger */
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        /* Upload Buttons */
        .upload-buttons {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #e0e0e0;
        }

        .upload-buttons button {
            padding: 7px 14px;
            margin-right: 8px;
            cursor: pointer;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
            background-color: #f9f9f9;
            color: #333;
            transition: background-color 0.2s, border-color 0.2s;
        }
        .upload-buttons button:hover {
            background-color: #eee;
            border-color: #bbb;
        }

        /* Data Tables */
        .table-wrapper { /* Added wrapper for horizontal scroll on small screens */
            overflow-x: auto;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.9em;
            min-width: 600px; /* Prevent excessive shrinking */
        }

        th, td {
            border: 1px solid #e0e0e0;
            padding: 9px 12px;
            text-align: left;
            color: #000000;
            vertical-align: top;
            word-wrap: break-word;
        }

        th {
            background-color: #f9f9f9;
            font-weight: 500;
            white-space: nowrap;
            position: sticky; /* Make headers sticky within wrapper */
            top: 0; /* Stick to top of wrapper */
            z-index: 1;
        }

        tbody tr:hover {
            background-color: #f0f0f0;
        }

        /* Dark Mode */
        body.dark-mode { background-color: #222; color: #e0e0e0;}
        body.dark-mode ::-webkit-scrollbar-track { background: #333; }
        body.dark-mode ::-webkit-scrollbar-thumb { background: #666; }
        body.dark-mode ::-webkit-scrollbar-thumb:hover { background: #888; }
        body.dark-mode .sidebar-container { background-color: #2d2d2d; border-right-color: #444; }
        body.dark-mode .sidebar-brand { color: #ffffff; border-bottom-color: #444; }
        body.dark-mode .sidebar-item { color: #c0c0c0; }
        body.dark-mode .sidebar-item:hover { background-color: #3a3a3a; color: #ffffff; }
        body.dark-mode .sidebar-item.active { background-color: #444; color: #ffffff; }
        body.dark-mode .sidebar-item.active::after { background-color: #eee; }
        body.dark-mode .content-header { background-color: #2d2d2d; border-bottom-color: #444; }
        body.dark-mode .header-title { color: #ffffff; }
        body.dark-mode .main-content-area { background-color: #222; }
        body.dark-mode .grid-item { background-color: #333; border-color: #4a4a4a; color: #e0e0e0; }
        body.dark-mode .grid-item i.material-icons { color: #bbb; }
        body.dark-mode .grid-item:hover { background-color: #3f3f3f; }
        body.dark-mode .table-view-header .back-arrow { color: #bbb; }
        body.dark-mode .table-view-header .back-arrow:hover { background-color: rgba(255, 255, 255, 0.1); }
        body.dark-mode .table-management { background-color: #333; border-color: #4a4a4a; box-shadow: 0 1px 3px rgba(0,0,0,0.2); }
        body.dark-mode .table-management h3 { color: #f0f0f0; border-bottom-color: #4a4a4a; }
        body.dark-mode .upload-buttons { border-bottom-color: #555; }
        body.dark-mode .upload-buttons button { background-color: #444; color: #e0e0e0; border-color: #666; }
        body.dark-mode .upload-buttons button:hover { background-color: #555; border-color: #777; }
        body.dark-mode th, body.dark-mode td { color: #e0e0e0; border-color: #555; }
        body.dark-mode th { background-color: #3a3a3a; }
        body.dark-mode tbody tr:hover { background-color: #404040; }
        body.dark-mode #mode-toggle + .slider { background-color: #666; }
        body.dark-mode #mode-toggle:checked + .slider { background-color: #bbb; }
        body.dark-mode #fullscreen-button:hover { background-color: rgba(255, 255, 255, 0.1); }
        body.dark-mode #mapContainer { background-color: #444; color: #aaa; border-color: #666; }


        /* Fullscreen Button */
         #fullscreen-button { background: none; border: none; color: inherit; cursor: pointer; padding: 0 5px 0 15px; margin-left: auto; display: flex; align-items: center; justify-content: center; height: 100%; transition: background-color 0.2s ease; border-radius: 4px; }
         #fullscreen-button:focus { outline: none; }
         #fullscreen-button:hover { background-color: rgba(0, 0, 0, 0.08); }
         #fullscreen-button i.material-icons { font-size: 26px; }

         /* Dark mode Fullscreen button */
         body.dark-mode #fullscreen-button:hover { background-color: rgba(255, 255, 255, 0.1); }

        /* Mode Toggle Switch */
         .mode-switch-container { display: flex; align-items: center; height: 100%; padding-left: 15px; }
        .switch { position: relative; display: inline-block; width: 44px; height: 24px; }
        .switch input { display: none; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; box-shadow: 0 1px 2px rgba(0,0,0,0.2); }
        input:checked + .slider { background-color: #555; }
        input:checked + .slider:before { transform: translateX(20px); }

        /* Overlays */
        .rotate-overlay, .small-device-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: black; color: white; z-index: 9999; flex-direction: column; justify-content: center; align-items: center; text-align: center; }
        .rotate-overlay i, .small-device-overlay i { font-size: 80px; margin-bottom: 20px; }
        .rotate-overlay p, .small-device-overlay p { font-size: 20px; max-width: 80%; }

        /* Overlay Triggers */
        @media (max-width: 555px) and (orientation: portrait) { .rotate-overlay { display: flex; } .container { display: none !important; } }
        @media (max-width: 554px) and (max-height: 319px) { .small-device-overlay { display: flex; } .container, .rotate-overlay { display: none !important; } }

         /* Map Placeholder Style */
        #mapContainer { height: 300px; background-color: #e5e5e5; display: flex; align-items: center; justify-content: center; color: #888; border: 1px solid #ccc; border-radius: 4px; margin-top: 15px; font-style: italic; }
        body.dark-mode #mapContainer { background-color: #444; color: #aaa; border-color: #666; }

    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar-container">
            <div class="sidebar">
                <div class="sidebar-brand">University Data</div>
                <div class="sidebar-items">
                    <div class="sidebar-item active" onclick="showGridView('start', 'START')"><i class="material-icons">stars</i><span>START</span></div>
                    <div class="sidebar-item" onclick="showGridView('updates', 'UPDATES')"><i class="material-icons">notifications</i><span>UPDATES</span></div>
                    <div class="sidebar-item" onclick="showGridView('path', 'PATH')"><i class="material-icons">school</i><span>PATH</span></div>
                    <div class="sidebar-item" onclick="showGridView('people', 'PEOPLE')"><i class="material-icons">people</i><span>PEOPLE</span></div>
                    <div class="sidebar-item" onclick="showGridView('lodging', 'LODGING')"><i class="material-icons">hotel</i><span>LODGING</span></div>
                    <div class="sidebar-item" onclick="showGridView('shoporeat', 'SHOP OR EAT')"><i class="material-icons">restaurant</i><span>SHOP OR EAT</span></div>
                    <div class="sidebar-item" onclick="showGridView('transport', 'TRANSPORT')"><i class="material-icons">directions_bus</i><span>TRANSPORT</span></div>
                    <div class="sidebar-item" onclick="showGridView('core', 'CORE')"><i class="material-icons">layers</i><span>CORE</span></div>
                    <div class="sidebar-item" onclick="showGridView('academics', 'ACADEMICS')"><i class="material-icons">book</i><span>ACADEMICS</span></div>
                    <div class="sidebar-item" onclick="showGridView('programs', 'PROGRAMS')"><i class="material-icons">lens</i><span>PROGRAMS</span></div>
                    <div class="sidebar-item" onclick="showGridView('athletics', 'ATHLETICS & GROUPS')"><i class="material-icons">sports_soccer</i><span>ATHLETICS & GROUPS</span></div>
                    <div class="sidebar-item" onclick="showGridView('media', 'MEDIA')"><i class="material-icons">photo_library</i><span>MEDIA</span></div>
                    <div class="sidebar-item" onclick="showGridView('startups', 'STARTUPS')"><i class="material-icons">rocket_launch</i><span>STARTUPS</span></div>
                    <div class="sidebar-item" onclick="showGridView('projects', 'PROJECTS & PUBLICATION')"><i class="material-icons">grain</i><span>PROJECTS & PUBLICATION</span></div>
                    <div class="sidebar-item" onclick="showGridView('buildings', 'BUILDINGS & SPACES')"><i class="material-icons">apartment</i><span>BUILDINGS & SPACES</span></div>
                    <div class="sidebar-item" onclick="showGridView('statistics_nav', 'STATISTICS')"><i class="material-icons">bar_chart</i><span>STATISTICS</span></div>
                    <div class="sidebar-item" onclick="showGridView('calendar', 'CALENDAR')"><i class="material-icons">calendar_today</i><span>CALENDAR</span></div>
                    <div class="sidebar-item" onclick="showGridView('map_nav', 'MAP')"><i class="material-icons">map</i><span>MAP</span></div>
                    <div class="sidebar-item" onclick="showGridView('feedback_nav', 'FEEDBACK')"><i class="material-icons">feedback</i><span>FEEDBACK</span></div>
                    <div class="sidebar-item" onclick="showGridView('history', 'HISTORICAL TIMELINE')"><i class="material-icons">history</i><span>HISTORICAL TIMELINE</span></div>
                    <div class="sidebar-item" onclick="showGridView('rentals', 'RENTALS')"><i class="material-icons">storefront</i><span>RENTALS</span></div>
                    <div class="sidebar-item" onclick="showGridView('jobs', 'JOBS')"><i class="material-icons">work</i><span>JOBS</span></div>
                    <div class="sidebar-item" onclick="showGridView('services', 'SERVICES')"><i class="material-icons">build_circle</i><span>SERVICES</span></div>
                    <div class="sidebar-item" onclick="showGridView('money', 'MONEY')"><i class="material-icons">attach_money</i><span>MONEY</span></div>
                    <div class="sidebar-item" onclick="showGridView('health', 'HEALTH')"><i class="material-icons">health_and_safety</i><span>HEALTH</span></div>
                    <div class="sidebar-item" onclick="showGridView('safety', 'SAFETY')"><i class="material-icons">security</i><span>SAFETY</span></div>
                    <div class="sidebar-item" onclick="showGridView('connectivity', 'CONNECTIVITY')"><i class="material-icons">wifi</i><span>CONNECTIVITY</span></div>
                    <div class="sidebar-item" onclick="showGridView('giving_nav', 'GIVING')"><i class="material-icons">volunteer_activism</i><span>GIVING</span></div>
                    <div class="sidebar-item" onclick="showGridView('virtual_tour', 'VIRTUAL TOUR')"><i class="material-icons">tour</i><span>VIRTUAL TOUR</span></div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content-wrapper">
            <!-- Content Header -->
            <div class="content-header">
                <div class="header-title-container">
                    <h2 id="main-header" class="header-title">START</h2>
                </div>
                <button id="fullscreen-button" aria-label="Toggle fullscreen"><i class="material-icons">fullscreen</i></button>
                <div class="mode-switch-container">
                    <label class="switch">
                        <input type="checkbox" id="mode-toggle">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <!-- Scrollable Content Area -->
            <div class="main-content-area">

                <!-- Grid Views (Initially hidden, shown by JS) -->
                <div id="start-grid" class="view-container grid-view active">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterhelpdesks', 'Help Desks', 'start')"><i class="material-icons">help</i><span>Help Desks</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteraccessibility', 'Accessibility', 'start')"><i class="material-icons">accessible</i><span>Accessibility</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterfaq', 'FAQ', 'start')"><i class="material-icons">question_answer</i><span>FAQ</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterlinks', 'Links', 'start')"><i class="material-icons">link</i><span>Links</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterconstruction', 'Construction', 'start')"><i class="material-icons">build</i><span>Construction</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterprinting', 'Printing', 'start')"><i class="material-icons">print</i><span>Printing</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterdaycares', 'Daycares', 'start')"><i class="material-icons">child_care</i><span>Daycares</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersustainability', 'Sustainability', 'start')"><i class="material-icons">eco</i><span>Sustainability</span></div>
                    </div>
                </div>

                <div id="updates-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosternotices', 'Notices', 'updates')"><i class="material-icons">announcement</i><span>Notices</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersocialmediafeeds', 'Social Media Feeds', 'updates')"><i class="material-icons">dynamic_feed</i><span>Social Media Feeds</span></div>
                     </div>
                </div>

                 <div id="path-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterselection', 'Admissions Selection', 'path')"><i class="material-icons">how_to_reg</i><span>Admissions Selection</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercostsorrates', 'Costs or Rates', 'path')"><i class="material-icons">attach_money</i><span>Costs or Rates</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterscholarships', 'Scholarships', 'path')"><i class="material-icons">military_tech</i><span>Scholarships</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpayments-path', 'Payments (Affordability)', 'path')"><i class="material-icons">payment</i><span>Payments</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterorientations', 'Orientations', 'path')"><i class="material-icons">explore</i><span>Orientations</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersymposiums', 'Symposiums', 'path')"><i class="material-icons">groups</i><span>Symposiums</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostergraduation', 'Graduation Data', 'path')"><i class="material-icons">school</i><span>Graduation Data</span></div>
                     </div>
                 </div>

                 <div id="people-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpeople', 'People Directory', 'people')"><i class="material-icons">badge</i><span>People Directory</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercurrentstudents', 'Current Students', 'people')"><i class="material-icons">face</i><span>Current Students</span></div>
                     </div>
                 </div>

                 <div id="lodging-grid" class="view-container grid-view">
                     <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterhousing', 'Campus Housing', 'lodging')"><i class="material-icons">apartment</i><span>Campus Housing</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterlocallodging', 'Local Lodging', 'lodging')"><i class="material-icons">hotel</i><span>Local Lodging</span></div>
                     </div>
                 </div>

                 <div id="shoporeat-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostershopsoreateries', 'Shops or Eateries', 'shoporeat')"><i class="material-icons">store</i><span>Shops/Eateries</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterinventory', 'Inventory', 'shoporeat')"><i class="material-icons">inventory</i><span>Inventory</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostermenus', 'Menus', 'shoporeat')"><i class="material-icons">menu_book</i><span>Menus</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostermealplans', 'Meal Plans', 'shoporeat')"><i class="material-icons">ramen_dining</i><span>Meal Plans</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterlocalareadining', 'Local Area Dining', 'shoporeat')"><i class="material-icons">restaurant_menu</i><span>Local Area Dining</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterstudentdiscounts', 'Student Discounts', 'shoporeat')"><i class="material-icons">local_offer</i><span>Student Discounts</span></div>
                     </div>
                 </div>

                <div id="transport-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercampusshuttle', 'Campus Shuttle', 'transport')"><i class="material-icons">directions_bus</i><span>Campus Shuttle</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterparkingspaces', 'Parking Spaces', 'transport')"><i class="material-icons">local_parking</i><span>Parking Spaces</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterlocaltransport', 'Local Transport', 'transport')"><i class="material-icons">train</i><span>Local Transport</span></div>
                    </div>
                </div>

                 <div id="core-grid" class="view-container grid-view">
                    <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterschools', 'Schools', 'core')"><i class="material-icons">school</i><span>Schools</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterdepartments', 'Departments', 'core')"><i class="material-icons">business</i><span>Departments</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercenters', 'Centers', 'core')"><i class="material-icons">hub</i><span>Centers</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterdocuments', 'Documents', 'core')"><i class="material-icons">description</i><span>Documents</span></div>
                    </div>
                </div>

                 <div id="academics-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostermajors', 'Majors', 'academics')"><i class="material-icons">science</i><span>Majors</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterminors', 'Minors', 'academics')"><i class="material-icons">biotech</i><span>Minors</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterfunding', 'Funding', 'academics')"><i class="material-icons">savings</i><span>Funding</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercoursecatalog', 'Course Catalog', 'academics')"><i class="material-icons">menu_book</i><span>Course Catalog</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterentryrequirements', 'Entry Requirements', 'academics')"><i class="material-icons">rule</i><span>Entry Requirements</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterenrollmentexercise', 'Enrollment Exercise', 'academics')"><i class="material-icons">assignment_ind</i><span>Enrollment Exercise</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteracademicresources', 'Academic Resources', 'academics')"><i class="material-icons">lightbulb</i><span>Academic Resources</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteracademichonors', 'Academic Honors', 'academics')"><i class="material-icons">military_tech</i><span>Academic Honors</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteracademicprizes', 'Academic Prizes', 'academics')"><i class="material-icons">emoji_events</i><span>Academic Prizes</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteracademicdress', 'Academic Dress', 'academics')"><i class="material-icons">styler</i><span>Academic Dress</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostergradingscale', 'Grading Scale', 'academics')"><i class="material-icons">grade</i><span>Grading Scale</span></div>
                    </div>
                 </div>

                 <div id="programs-grid" class="view-container grid-view">
                     <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterprograms', 'Programs', 'programs')"><i class="material-icons">event_note</i><span>Programs</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersignatureevents', 'Signature Events', 'programs')"><i class="material-icons">celebration</i><span>Signature Events</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostertraditions', 'Traditions', 'programs')"><i class="material-icons">history_edu</i><span>Traditions</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpartnershipopportunities', 'Partnership Opportunities', 'programs')"><i class="material-icons">handshake</i><span>Partnership Opportunities</span></div>
                     </div>
                 </div>

                 <div id="athletics-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterathletics', 'Athletics', 'athletics')"><i class="material-icons">sports</i><span>Athletics</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterorgsorclubs', 'Orgs or Clubs', 'athletics')"><i class="material-icons">groups</i><span>Orgs or Clubs</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterresearchgroups', 'Research Groups', 'athletics')"><i class="material-icons">science</i><span>Research Groups</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercommittees', 'Committees', 'athletics')"><i class="material-icons">people_alt</i><span>Committees</span></div>
                    </div>
                 </div>

                 <div id="media-grid" class="view-container grid-view">
                    <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosternews', 'News', 'media')"><i class="material-icons">feed</i><span>News</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterperiodicals', 'Periodicals', 'media')"><i class="material-icons">newspaper</i><span>Periodicals</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterradio', 'Radio', 'media')"><i class="material-icons">radio</i><span>Radio</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostertelevision', 'Television', 'media')"><i class="material-icons">tv</i><span>Television</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterphotos', 'Photos', 'media')"><i class="material-icons">photo_camera</i><span>Photos</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostervideos', 'Videos', 'media')"><i class="material-icons">videocam</i><span>Videos</span></div>
                     </div>
                 </div>

                <div id="startups-grid" class="view-container grid-view">
                     <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteraccelerators', 'Accelerators', 'startups')"><i class="material-icons">speed</i><span>Accelerators</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoostermakerspaces', 'Makerspaces', 'startups')"><i class="material-icons">construction</i><span>Makerspaces</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterstartupfunds', 'Startup Funds', 'startups')"><i class="material-icons">paid</i><span>Startup Funds</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterstartups', 'Startups', 'startups')"><i class="material-icons">emoji_objects</i><span>Startups</span></div>
                     </div>
                 </div>

                 <div id="projects-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterresearchprojects', 'Research Projects', 'projects')"><i class="material-icons">biotech</i><span>Research Projects</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostertheses', 'Theses', 'projects')"><i class="material-icons">description</i><span>Theses</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterbooks', 'Books', 'projects')"><i class="material-icons">auto_stories</i><span>Books</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterarticles', 'Articles', 'projects')"><i class="material-icons">article</i><span>Articles</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpatents', 'Patents', 'projects')"><i class="material-icons">verified</i><span>Patents</span></div>
                    </div>
                 </div>

                 <div id="buildings-grid" class="view-container grid-view">
                     <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterbuilding', 'Building', 'buildings')"><i class="material-icons">location_city</i><span>Building</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterrooms', 'Rooms', 'buildings')"><i class="material-icons">meeting_room</i><span>Rooms</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterroomequipment', 'Room Equipment', 'buildings')"><i class="material-icons">devices_other</i><span>Room Equipment</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterroomassignments', 'Room Assignments', 'buildings')"><i class="material-icons">assignment</i><span>Room Assignments</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpublicart', 'Public Art', 'buildings')"><i class="material-icons">palette</i><span>Public Art</span></div>
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteremergencyequipment', 'Emergency Equipment', 'buildings')"><i class="material-icons">emergency</i><span>Emergency Equipment</span></div>
                     </div>
                 </div>

                 <div id="statistics_nav-grid" class="view-container grid-view">
                     <div class="grid-container">
                         <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterdocuments-stats', 'Statistical Documents', 'statistics_nav')"><i class="material-icons">description</i><span>Statistical Documents</span></div>
                         <!-- Add more specific stat views if needed -->
                     </div>
                 </div>

                 <div id="calendar-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterclassschedules', 'Class Schedules', 'calendar')"><i class="material-icons">schedule</i><span>Class Schedules</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterweeklyschedule', 'Weekly Schedule', 'calendar')"><i class="material-icons">date_range</i><span>Weekly Schedule</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterevents', 'Events', 'calendar')"><i class="material-icons">event</i><span>Events</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteracademiccalendar', 'Academic Calendar', 'calendar')"><i class="material-icons">calendar_month</i><span>Academic Calendar</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterrentalequipmentcalendar', 'Rental Equipment Calendar', 'calendar')"><i class="material-icons">inventory</i><span>Rental Equipment Calendar</span></div>
                    </div>
                 </div>

                 <div id="map_nav-grid" class="view-container grid-view">
                     <div class="grid-container">
                          <div class="grid-item" onclick="showTableView('table-map-placeholder', 'Campus Map Data', 'map_nav')"><i class="material-icons">map</i><span>Campus Map Data</span></div>
                     </div>
                 </div>

                 <div id="feedback_nav-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterfeedback', 'Feedback Collection', 'feedback_nav')"><i class="material-icons">rate_review</i><span>Feedback Collection</span></div>
                     </div>
                 </div>

                 <div id="history-grid" class="view-container grid-view">
                      <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersocialmediafeeds-hist', 'Historical Timeline', 'history')"><i class="material-icons">timeline</i><span>Historical Timeline</span></div>
                      </div>
                 </div>

                 <div id="rentals-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterrentals', 'Rentals', 'rentals')"><i class="material-icons">storefront</i><span>Rentals</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterrentalequipmentcalendar', 'Rental Equipment Calendar', 'rentals')"><i class="material-icons">inventory</i><span>Rental Equipment Calendar</span></div>
                     </div>
                 </div>

                 <div id="jobs-grid" class="view-container grid-view">
                      <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterjobs', 'Jobs', 'jobs')"><i class="material-icons">work_outline</i><span>Jobs</span></div>
                      </div>
                 </div>

                <div id="services-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterservices', 'Services', 'services')"><i class="material-icons">design_services</i><span>Services</span></div>
                    </div>
                </div>

                 <div id="money-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteratms', 'ATMs', 'money')"><i class="material-icons">local_atm</i><span>ATMs</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterpayments-money', 'Payment Portals/Methods', 'money')"><i class="material-icons">payment</i><span>Payments</span></div>
                    </div>
                 </div>

                <div id="health-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterclinicsorhospitals', 'Clinics or Hospitals', 'health')"><i class="material-icons">local_hospital</i><span>Clinics/Hospitals</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostercounselingservices', 'Counseling Services', 'health')"><i class="material-icons">psychology</i><span>Counseling Services</span></div>
                    </div>
                 </div>

                <div id="safety-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosteremergencycontacts', 'Emergency Contacts', 'safety')"><i class="material-icons">contact_phone</i><span>Emergency Contacts</span></div>
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostersafetyprocedures', 'Safety Procedures', 'safety')"><i class="material-icons">gpp_good</i><span>Safety Procedures</span></div>
                    </div>
                 </div>

                <div id="connectivity-grid" class="view-container grid-view">
                     <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoosterconnectivity', 'Connectivity', 'connectivity')"><i class="material-icons">settings_ethernet</i><span>Connectivity</span></div>
                     </div>
                </div>

                 <div id="giving_nav-grid" class="view-container grid-view">
                    <div class="grid-container">
                        <div class="grid-item" onclick="showTableView('table-thecollegeofwoostergiving', 'Giving Opportunities', 'giving_nav')"><i class="material-icons">volunteer_activism</i><span>Giving Opportunities</span></div>
                     </div>
                 </div>

                <div id="virtual_tour-grid" class="view-container grid-view">
                     <div class="grid-container">
                          <div class="grid-item" onclick="showTableView('table-virtualtour-placeholder', 'Virtual Tour Locations', 'virtual_tour')"><i class="material-icons">tour</i><span>Virtual Tour Locations</span></div>
                     </div>
                 </div>


                <!-- Table Views (Initially hidden, shown by JS) -->
                <!-- START -->
                <div id="table-thecollegeofwoosterhelpdesks" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Help Desks</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>1</td><td>IT Help Desk</td><td>Andrews Library</td><td>LL01</td><td>M-F 8am-5pm</td><td>N/A</td><td>(*************</td><td><EMAIL></td><td>(*************</td><td>+1330...</td><td>Provides tech support...</td><td>40.7952</td><td>-81.9365</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosteraccessibility" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Accessibility</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>phone</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>2</td><td>Accessibility Services</td><td>APEX Center</td><td>G03</td><td>M-F 8:30am-4:30pm</td><td>(*************</td><td>...</td><td>Coordination of accommodations...</td><td>40.793</td><td>-81.938</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterfaq" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>FAQ</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>department</th><th>school</th><th>topic</th><th>studentquestion</th><th>facultyorstaffquestion</th><th>parentquestion</th><th>admissionsquestion</th><th>orientationquestion</th><th>symposiumquestion</th><th>graduationquestion</th><th>alumniquestion</th></tr></thead><tbody><tr><td>3</td><td>How to Register</td><td>Process for registering for classes...</td><td>Registrar</td><td>N/A</td><td>Registration</td><td>true</td><td>true</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterlinks" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Links</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th><th>admissionslink</th><th>orientationlink</th><th>symposiumlink</th><th>alumnilink</th><th>department</th><th>img_link</th><th>about</th></tr></thead><tbody><tr><td>4</td><td>Academic Calendar</td><td>/calendar/academic</td><td>false</td><td>false</td><td>false</td><td>false</td><td>Registrar</td><td>N/A</td><td>Official dates for the academic year...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterconstruction" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Construction</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>location</th><th>startdate</th><th>enddate</th><th>phone</th><th>whatsapp</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>5</td><td>Library Renovation</td><td>Andrews Library West Wing</td><td>2024-06-01</td><td>2024-12-15</td><td>(*************</td><td>...</td><td>40.795</td><td>-81.936</td><td>Phase 1 renovation impacting study areas...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterprinting" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Printing</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>payment</th><th>phone</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>6</td><td>Library Color Printer</td><td>Andrews Library</td><td>1st Floor</td><td>Library Hours</td><td>Campus Card ($0.25/pg)</td><td>(*************</td><td>...</td><td>Color laser printer...</td><td>40.795</td><td>-81.936</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterdaycares" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Daycares</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>phone</th><th>boxnumber</th><th>email</th><th>fax</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>7</td><td>Wooster Little Scots</td><td>Westminster Church House</td><td>N/A</td><td>M-F 7:30am-5:30pm</td><td>(*************</td><td>...</td><td><EMAIL></td><td>...</td><td>...</td><td>Campus affiliated daycare...</td><td>40.791</td><td>-81.939</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostersustainability" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('start')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Sustainability</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>8</td><td>Campus Recycling Program</td><td>Details about accepted materials and drop-off locations...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- UPDATES -->
                 <div id="table-thecollegeofwoosternotices" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('updates')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Notices</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>day</th><th>month</th><th>year</th><th>link</th></tr></thead><tbody><tr><td>9</td><td>Campus Closure - Weather</td><td>Due to severe weather...</td><td>15</td><td>Jan</td><td>2025</td><td>/news/weather-closure</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostersocialmediafeeds" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('updates')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Social Media Feeds</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>platform</th><th>link</th></tr></thead><tbody><tr><td>10</td><td>@WoosterAdmissions</td><td>Twitter</td><td>https://twitter.com/woosteradmissions</td></tr></tbody></table></div>
                    </div>
                </div>

                 <!-- PATH -->
                <div id="table-thecollegeofwoosterselection" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Admissions Selection</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>firstname</th><th>lastname</th><th>gender</th><th>year</th><th>major</th><th>nationality</th><th>yearofentry</th></tr></thead><tbody><tr><td>11</td><td>Priya</td><td>Singh</td><td>Female</td><td>2024</td><td>Biology</td><td>India</td><td>2025</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostercostsorrates" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Costs or Rates</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>amount</th><th>about</th><th>categoryormajor</th></tr></thead><tbody><tr><td>12</td><td>Room & Board 24-25</td><td>15200</td><td>Standard double room and full meal plan</td><td>Housing</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterscholarships" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Scholarships</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>major</th></tr></thead><tbody><tr><td>13</td><td>Music Performance Scholarship</td><td>Awarded based on audition...</td><td>Music</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterpayments-path" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Payments (Affordability)</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th><th>about</th></tr></thead><tbody><tr><td>14</td><td>Nelnet Payment Plan</td><td>/afford/nelnet</td><td>Monthly payment plan option...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterorientations" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Orientations</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>phone</th><th>email</th><th>fax</th></tr></thead><tbody><tr><td>15</td><td>International Student Orientation</td><td>Program for incoming international students...</td><td>(*************</td><td><EMAIL></td><td>...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostersymposiums" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Symposiums</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>phone</th><th>email</th><th>fax</th></tr></thead><tbody><tr><td>16</td><td>Digital Wooster Symposium</td><td>Showcase of digital projects...</td><td>(*************</td><td><EMAIL></td><td>...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostergraduation" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('path')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Graduation Data</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>venue</th><th>alternatelocation</th><th>speaker</th><th>starttime</th><th>endtime</th><th>day</th><th>month</th><th>year</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>17</td><td>Oak Grove</td><td>Scot Center</td><td>Alumnus John Legend '99</td><td>10:00 AM</td><td>12:00 PM</td><td>15</td><td>May</td><td>2025</td><td>Commencement Ceremony...</td><td>40.794</td><td>-81.937</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- PEOPLE -->
                 <div id="table-thecollegeofwoosterpeople" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('people')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>People Directory</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>title</th><th>department</th><th>school</th><th>school2</th><th>school3</th><th>building</th><th>room</th><th>officehours</th><th>phone</th><th>ext</th><th>email</th><th>fax</th><th>hours</th><th>facultymember</th><th>staffmember</th><th>orientationcontact</th><th>graduationcontact</th><th>about</th><th>services</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>18</td><td>Wayne Webster</td><td>Provost</td><td>Office of the Provost</td><td>College of Arts & Sciences</td><td>N/A</td><td>N/A</td><td>Galpin Hall</td><td>101</td><td>By Appointment</td><td>(*************</td><td>2441</td><td><EMAIL></td><td>...</td><td>M-F 8-5</td><td>true</td><td>true</td><td>false</td><td>false</td><td>Chief academic officer...</td><td>Academic Affairs</td><td>40.794</td><td>-81.938</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostercurrentstudents" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('people')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Current Students</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>department</th><th>building</th><th>room</th></tr></thead><tbody><tr><td>19</td><td>Jordan Lee</td><td>2026</td><td>Computer Science</td><td>Kenarden Lodge</td><td>305</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- LODGING -->
                <div id="table-thecollegeofwoosterhousing" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('lodging')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Campus Housing</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>hours</th><th>accessmethod</th><th>capacity</th><th>residentorgorclub</th><th>phone</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>20</td><td>Kenarden Lodge</td><td>24/7 Access, Desk M-F 8a-10p</td><td>Campus ID Card</td><td>120</td><td>Kenarden Hall Council</td><td>(*************</td><td>Upperclass residence hall...</td><td>40.796</td><td>-81.935</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterlocallodging" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('lodging')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Local Lodging</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>21</td><td>Best Western Plus Wooster Hotel</td><td>24/7</td><td>Credit Card, Cash</td><td>(*************</td><td>...</td><td>...</td><td>Standard hotel near downtown...</td><td>40.805</td><td>-81.933</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- SHOP OR EAT -->
                <div id="table-thecollegeofwoostershopsoreateries" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Shops or Eateries</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>type</th><th>building</th><th>room</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>img_link</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>22</td><td>Mom's Truckstop</td><td>Cafe</td><td>Lowry Center</td><td>Lower Level</td><td>M-F 11am-1am, Sa-Su 6pm-1am</td><td>Campus Card, Credit Card, Cash</td><td>(*************</td><td>...</td><td>...</td><td>...</td><td>/img/moms.jpg</td><td>Late night dining options...</td><td>40.793</td><td>-81.937</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostermealplans" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Meal Plans</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>23</td><td>Flex Plan</td><td>Provides a set amount of dining dollars per semester...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterlocalareadining" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Local Area Dining</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>whatsapp</th><th>img_link</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>24</td><td>Spoon Market & Deli</td><td>M-Sa 8am-6pm</td><td>Credit Card, Cash</td><td>(*************</td><td>...</td><td>...</td><td>/img/spoon.jpg</td><td>Sandwiches, soups, salads...</td><td>40.808</td><td>-81.940</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterstudentdiscounts" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Student Discounts</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>25</td><td>Movie Theater Discount</td><td>Varies</td><td>Varies</td><td>(*************</td><td>...</td><td>...</td><td>$2 off tickets Sun-Thurs w/ ID</td><td>40.810</td><td>-81.930</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterinventory" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Inventory</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>price</th><th>seller</th><th>category</th><th>onsale</th><th>about</th><th>originalprice</th></tr></thead><tbody><tr><td>26</td><td>Wooster T-Shirt</td><td>24.99</td><td>Wilson Bookstore</td><td>Apparel</td><td>false</td><td>Cotton blend t-shirt...</td><td>24.99</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostermenus" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('shoporeat')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Menus</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>price</th><th>dininglocation</th><th>diningstation</th><th>breakfast</th><th>lunch</th><th>dinner</th><th>_mon</th><th>_tue</th><th>_wed</th><th>_thur</th><th>_fri</th><th>_sat</th><th>_sun</th><th>about</th><th>allergens</th><th>ingredients</th></tr></thead><tbody><tr><td>27</td><td>Macaroni & Cheese</td><td>6.50</td><td>Lowry Center</td><td>Comfort Food</td><td>false</td><td>true</td><td>true</td><td>true</td><td>false</td><td>true</td><td>false</td><td>true</td><td>false</td><td>false</td><td>Classic comfort food...</td><td>Dairy, Gluten</td><td>Pasta, cheese sauce...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- TRANSPORT -->
                <div id="table-thecollegeofwoostercampusshuttle" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('transport')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Campus Shuttle</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>fax</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>Campus Shuttle</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>40.794</td><td>-81.937</td><td>Shuttle service details...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterparkingspaces" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('transport')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Parking Spaces</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>hours</th><th>payment</th><th>capacity</th><th>phone</th><th>whatsapp</th><th>latitude</th><th>longitude</th><th>building</th><th>building2</th><th>building3</th><th>building4</th><th>building5</th><th>building6</th><th>building7</th><th>studentparking</th><th>facultyorstaffparking</th><th>about</th></tr></thead><tbody><tr><td>28</td><td>Kauke Parking Lot</td><td>24/7</td><td>Permit Required (Faculty/Staff)</td><td>50</td><td>(*************</td><td>...</td><td>40.794</td><td>-81.939</td><td>Kauke Hall</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>false</td><td>true</td><td>Main F/S lot on East Quad...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterlocaltransport" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('transport')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Local Transport</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>payment</th><th>phone</th><th>email</th><th>whatsapp</th><th>fax</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>29</td><td>Wooster Express Taxi</td><td>Cash, Credit Card</td><td>(*************</td><td>...</td><td>...</td><td>...</td><td>N/A</td><td>N/A</td><td>Local taxi service...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- CORE -->
                 <div id="table-thecollegeofwoosterschools" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('core')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Schools</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>30</td><td>Conservatory of Music</td><td>Scheide Music Center</td><td>Main Office</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>40.792</td><td>-81.938</td><td>Music programs and performances...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterdepartments" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('core')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Departments</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>payment</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>school</th><th>school2</th><th>school3</th><th>alumnidepartment</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>31</td><td>Chemistry Department</td><td>Severance Hall</td><td>101</td><td>N/A</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>College of Arts & Sciences</td><td>N/A</td><td>N/A</td><td>true</td><td>Chemistry programs and research...</td><td>40.793</td><td>-81.936</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostercenters" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('core')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Centers</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>payment</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>32</td><td>Center for Diversity & Inclusion</td><td>Lowry Center</td><td>111</td><td>M-F 9am-5pm</td><td>N/A</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>40.793</td><td>-81.937</td><td>Supports diversity initiatives...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterdocuments" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('core')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Documents</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th><th>admissionsdocument</th><th>graduationdocument</th><th>statsdocument</th><th>statsfinancial</th><th>year</th><th>major</th><th>department</th><th>school</th></tr></thead><tbody><tr><td>33</td><td>I.S. Handbook</td><td>/docs/is-handbook.pdf</td><td>false</td><td>false</td><td>false</td><td>false</td><td>2024</td><td>N/A</td><td>Dean for Curriculum</td><td>N/A</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- ACADEMICS -->
                 <div id="table-thecollegeofwoostermajors" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Majors</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>department</th><th>department2</th><th>department3</th><th>school</th><th>school2</th><th>school3</th><th>duration</th><th>about</th><th>whoitsfor</th><th>goalsandobjectives</th><th>aims</th><th>careerprospects</th><th>learningoutcomes</th><th>learningmethods</th><th>assessment</th><th>studentsupport</th><th>accredited</th></tr></thead><tbody><tr><td>34</td><td>Computer Science</td><td>Math & Comp Sci</td><td>N/A</td><td>N/A</td><td>College of Arts & Sciences</td><td>N/A</td><td>N/A</td><td>4 Years</td><td>Study of computation...</td><td>Students interested in tech...</td><td>Develop programming skills...</td><td>Prepare for tech careers...</td><td>Software dev, AI...</td><td>Analyze algorithms...</td><td>Lectures, labs, projects...</td><td>Exams, projects...</td><td>Tutoring, faculty advising...</td><td>false</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterminors" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Minors</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>department</th><th>department2</th><th>department3</th><th>school</th><th>school2</th><th>school3</th><th>duration</th><th>about</th><th>whoitsfor</th><th>goalsandobjectives</th><th>aims</th><th>careerprospects</th><th>learningoutcomes</th><th>learningmethods</th><th>assessment</th><th>studentsupport</th><th>accredited</th></tr></thead><tbody><tr><td>35</td><td>Environmental Studies</td><td>Env Studies Program</td><td>N/A</td><td>N/A</td><td>College of Arts & Sciences</td><td>N/A</td><td>N/A</td><td>18 Credits</td><td>Interdisciplinary study of env issues...</td><td>Students interested in environment...</td><td>Understand env systems...</td><td>Promote sustainability...</td><td>Policy, advocacy...</td><td>Analyze env data...</td><td>Field work, seminars...</td><td>Papers, presentations...</td><td>Program director advising...</td><td>false</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterfunding" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Funding</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>major</th></tr></thead><tbody><tr><td>36</td><td>Copeland Fund for I.S. Research</td><td>Supports student research expenses for Independent Study...</td><td>All</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostercoursecatalog" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Course Catalog</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>modulecode</th><th>modulename</th><th>about</th><th>major</th><th>minor</th><th>school</th><th>school2</th><th>school3</th><th>department</th><th>department2</th><th>department3</th><th>prerequisites</th><th>corequisites</th><th>corecourse</th><th>minorrequirement</th><th>electivecourse</th><th>year</th><th>term</th><th>credits</th><th>classesperweek</th><th>labhoursperweek</th><th>tutorialhoursperweek</th><th>aim</th><th>learningoutcomes</th><th>assessment</th><th>substitutemodulecode</th><th>substitutemodulename</th><th>offered</th></tr></thead><tbody><tr><td>37</td><td>HIST 201</td><td>American History to 1877</td><td>Survey of US history...</td><td>History</td><td>N/A</td><td>College of Arts & Sciences</td><td>N/A</td><td>N/A</td><td>History Department</td><td>N/A</td><td>N/A</td><td>None</td><td>None</td><td>true</td><td>false</td><td>true</td><td>Sophomore+</td><td>Fall</td><td>4</td><td>3</td><td>0</td><td>0</td><td>Understand early US hist...</td><td>Analyze primary sources...</td><td>Exams, papers...</td><td>N/A</td><td>N/A</td><td>Yearly</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterentryrequirements" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Entry Requirements</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>major</th></tr></thead><tbody><tr><td>38</td><td>Calculus I Requirement</td><td>Completion of Calculus I or equivalent is required...</td><td>Computer Science</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterenrollmentexercise" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Enrollment Exercise</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>major</th></tr></thead><tbody><tr><td>39</td><td>Sophomore I.S. Planning</td><td>Workshop for sophomores to begin planning their I.S. project...</td><td>All</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosteracademicresources" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Academic Resources</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>40</td><td>Quantitative Skills Center</td><td>Taylor Hall</td><td>101</td><td>M-Th 7pm-9pm</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>Tutoring for quantitative courses...</td><td>40.793</td><td>-81.936</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosteracademichonors" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Academic Honors</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>major</th></tr></thead><tbody><tr><td>41</td><td>Phi Beta Kappa</td><td>National academic honor society...</td><td>All</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosteracademicprizes" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Academic Prizes</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>42</td><td>Urlama Slice Prize in Mathematics</td><td>Awarded for outstanding work in mathematics...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosteracademicdress" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Academic Dress</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>category</th><th>style</th><th>color</th></tr></thead><tbody><tr><td>43</td><td>Bachelor's Hood</td><td>Hood</td><td>College Colors</td><td>Black, Gold</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostergradingscale" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('academics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Grading Scale</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>mark</th><th>lettergrade</th><th>graderemark</th><th>programlevel</th></tr></thead><tbody><tr><td>44</td><td>87-89</td><td>B+</td><td>Good</td><td>Undergraduate</td></tr></tbody></table></div>
                    </div>
                </div>

                 <!-- PROGRAMS -->
                 <div id="table-thecollegeofwoosterprograms" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('programs')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Programs</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th></tr></thead><tbody><tr><td>45</td><td>Entrepreneurship Pathway</td><td>(*************</td><td><EMAIL></td><td>...</td><td>Program combining coursework and experience...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostersignatureevents" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('programs')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Signature Events</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>alumnisignatureevent</th><th>about</th></tr></thead><tbody><tr><td>46</td><td>Black & Gold Weekend</td><td>true</td><td>Homecoming and Alumni reunion weekend...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostertraditions" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('programs')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Traditions</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>47</td><td>Senior Bells</td><td>Seniors ring the Kauke Hall bells after submitting I.S...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterpartnershipopportunities" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('programs')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Partnership Opportunities</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th></tr></thead><tbody><tr><td>48</td><td>Community Service Partnership</td><td>(*************</td><td><EMAIL></td><td>...</td><td>Connect students with local non-profits...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- ATHLETICS & GROUPS -->
                 <div id="table-thecollegeofwoosterathletics" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('athletics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Athletics</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>49</td><td>Men's Basketball</td><td>(*************</td><td><EMAIL></td><td>...</td><td>NCAA Division III team...</td><td>/athletics/mens-basketball</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterorgsorclubs" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('athletics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Orgs or Clubs</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>about</th></tr></thead><tbody><tr><td>50</td><td>Debate Union</td><td>...</td><td><EMAIL></td><td>Competitive debate team...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterresearchgroups" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('athletics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Research Groups</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>about</th></tr></thead><tbody><tr><td>51</td><td>Archaeology Field Group</td><td>...</td><td><EMAIL></td><td>Conducts local field work...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostercommittees" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('athletics')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Committees</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>about</th></tr></thead><tbody><tr><td>52</td><td>Educational Policy Committee (EPC)</td><td>...</td><td><EMAIL></td><td>Faculty committee overseeing curriculum...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- MEDIA -->
                <div id="table-thecollegeofwoosternews" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>News</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>day</th><th>month</th><th>year</th><th>publisher</th><th>link</th></tr></thead><tbody><tr><td>53</td><td>Wooster Ranked Top Liberal Arts College</td><td>10</td><td>Sep</td><td>2024</td><td>US News & World Report</td><td>/news/usnwr-ranking-2024</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterperiodicals" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Periodicals</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th></tr></thead><tbody><tr><td>54</td><td>Wooster Magazine</td><td>(*************</td><td><EMAIL></td><td>...</td><td>Quarterly alumni magazine...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterradio" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Radio</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th></tr></thead><tbody><tr><td>55</td><td>WCWS 90.9 FM</td><td>(*************</td><td><EMAIL></td><td>...</td><td>Student-run campus radio...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostertelevision" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Television</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>whatsapp</th><th>about</th></tr></thead><tbody><tr><td>56</td><td>WooTV</td><td>(*************</td><td><EMAIL></td><td>...</td><td>Student TV production...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterphotos" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Photos</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>day</th><th>month</th><th>year</th><th>platform</th><th>link</th></tr></thead><tbody><tr><td>57</td><td>IS Monday 2024</td><td>15</td><td>Apr</td><td>2024</td><td>Flickr</td><td>/photos/ismonday2024</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostervideos" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('media')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Videos</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>day</th><th>month</th><th>year</th><th>platform</th><th>link</th></tr></thead><tbody><tr><td>58</td><td>Why Wooster? Student Voices</td><td>01</td><td>Mar</td><td>2024</td><td>YouTube</td><td>/videos/whywooster</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- STARTUPS -->
                 <div id="table-thecollegeofwoosteraccelerators" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('startups')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Accelerators</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>59</td><td>Wooster LaunchPad</td><td>Gault Library</td><td>CoRE</td><td>By Appointment</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>Supports student entrepreneurs...</td><td>40.795</td><td>-81.936</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoostermakerspaces" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('startups')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Makerspaces</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>hours</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>60</td><td>The CoRE Makerspace</td><td>Ruth Williams Hall</td><td>LL</td><td>M-F 1pm-5pm</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>Access to 3D printers, tools...</td><td>40.793</td><td>-81.935</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterstartupfunds" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('startups')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Startup Funds</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>phone</th><th>email</th><th>about</th></tr></thead><tbody><tr><td>61</td><td>Woo Tank Pitch Competition</td><td>(*************</td><td><EMAIL></td><td>Annual competition with seed funding prizes...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterstartups" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('startups')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Startups</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>websitelink</th></tr></thead><tbody><tr><td>62</td><td>Sustainable Solutions Ltd.</td><td>Wooster alumni startup focused on green tech...</td><td>https://sustainablesolutions.example</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- PROJECTS & PUBLICATION -->
                 <div id="table-thecollegeofwoosterresearchprojects" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('projects')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Research Projects</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>researcher</th><th>researcher2</th><th>researcher3</th><th>department</th><th>facultyorstaffresearch</th><th>studentresearch</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>63</td><td>Genetic Analysis of Fruit Fly Development</td><td>2024</td><td>Dr. Gene Pool</td><td>Student A</td><td>N/A</td><td>Biology</td><td>true</td><td>true</td><td>Investigating gene expression...</td><td>/research/fruitfly2024</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoostertheses" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('projects')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Theses</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>author</th><th>advisor</th><th>advisor2</th><th>department</th><th>career</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>64</td><td>The Role of Satire in Postmodern Literature</td><td>2024</td><td>Alex Writer</td><td>Prof. Paige Turner</td><td>N/A</td><td>English</td><td>Publishing</td><td>Examining satirical techniques...</td><td>/thesis/writer2024</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterbooks" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('projects')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Books</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>author</th><th>author2</th><th>author3</th><th>facultyorstaffbook</th><th>studentbook</th><th>publisher</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>65</td><td>A History of Ohio Liberal Arts</td><td>2022</td><td>Prof. Olive Branch</td><td>N/A</td><td>N/A</td><td>true</td><td>false</td><td>Ohio University Press</td><td>Exploring the history of small colleges...</td><td>/books/ohioliberalarts</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterarticles" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('projects')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Articles</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>author</th><th>author2</th><th>author3</th><th>facultyorstaffarticle</th><th>studentarticle</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>66</td><td>New Methods in Organic Synthesis</td><td>2024</td><td>Dr. Piper Ett</td><td>N/A</td><td>N/A</td><td>true</td><td>false</td><td>Published in JACS...</td><td>https://doi.org/10.1021/jacs...</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterpatents" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('projects')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Patents</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>inventor</th><th>inventor2</th><th>inventor3</th><th>facultyorstaffpatent</th><th>studentpatent</th><th>about</th><th>link</th></tr></thead><tbody><tr><td>67</td><td>System for Efficient Solar Cell</td><td>2023</td><td>Prof. Ray Light</td><td>Student B</td><td>N/A</td><td>true</td><td>true</td><td>US Patent #12,345,678...</td><td>/patents/solarcell</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- BUILDINGS & SPACES -->
                 <div id="table-thecollegeofwoosterbuilding" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Building</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>location</th><th>hours</th><th>accessmethod</th><th>capacity</th><th>phone</th><th>latitude</th><th>longitude</th><th>mapreferencepoint</th><th>about</th><th>building</th><th>housing</th><th>parking</th><th>school</th><th>residence</th><th>library</th><th>lab</th><th>studio</th><th>gallery</th><th>theater</th><th>coworkingspace</th><th>studyspace</th><th>fitnessspace</th><th>funspace</th><th>storagespace</th><th>mailingspace</th><th>museum</th><th>sacredspace</th><th>outdoorspace</th><th>researchstation</th><th>clinic</th><th>laundryspace</th><th>campusdumpsters</th><th>watertanks</th><th>other</th></tr></thead><tbody><tr><td>68</td><td>Lowry Center</td><td>Central Campus</td><td>M-Su 7am-1am</td><td>Open Access / ID Card (Late)</td><td>2000</td><td>(330) 263-2 Lowry</td><td>40.793</td><td>-81.937</td><td>LC</td><td>Main student center...</td><td>true</td><td>false</td><td>true</td><td>true</td><td>false</td><td>false</td><td>true</td><td>true</td><td>true</td><td>true</td><td>true</td><td>true</td><td>true</td><td>false</td><td>true</td><td>false</td><td>true</td><td>true</td><td>false</td><td>false</td><td>true</td><td>true</td><td>false</td><td>false</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterrooms" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Rooms</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>roomtype</th><th>dimensions</th><th>capacity</th><th>phone</th><th>fax</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>69</td><td>Lowry 101</td><td>Lowry Center</td><td>Meeting Room</td><td>20x30 ft</td><td>25</td><td>...</td><td>...</td><td>Bookable meeting space...</td><td>40.793</td><td>-81.937</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterroomequipment" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Room Equipment</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>roomtype</th><th>assettag</th><th>dateinstalled</th><th>status</th><th>about</th></tr></thead><tbody><tr><td>70</td><td>Smart Projector</td><td>Lowry Center</td><td>101</td><td>Meeting Room</td><td>WOO-EQ-20101</td><td>2022-08-15</td><td>Operational</td><td>Ceiling mounted projector...</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterroomassignments" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Room Assignments</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>gender</th></tr></thead><tbody><tr><td>71</td><td>Alex Student</td><td>Kenarden Lodge</td><td>305A</td><td>Male</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterpublicart" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Public Art</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>email</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>72</td><td>'Balance' Sculpture</td><td>Taylor Hall</td><td>Lawn</td><td><EMAIL></td><td>Abstract metal sculpture...</td><td>40.793</td><td>-81.936</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosteremergencyequipment" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('buildings')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Emergency Equipment</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>latitude</th><th>longitude</th><th>about</th></tr></thead><tbody><tr><td>73</td><td>Fire Extinguisher ABC</td><td>Taylor Hall</td><td>Hallway 2nd Fl</td><td>40.793</td><td>-81.936</td><td>Standard fire extinguisher...</td></tr></tbody></table></div>
                    </div>
                </div>

                <!-- STATISTICS -->
                 <div id="table-thecollegeofwoosterdocuments-stats" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('statistics_nav')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Statistical Documents</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV to Documents</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th><th>admissionsdocument</th><th>graduationdocument</th><th>statsdocument</th><th>statsfinancial</th><th>year</th><th>major</th><th>department</th><th>school</th></tr></thead><tbody><tr><td>74</td><td>Fact Book 2024</td><td>/stats/factbook2024.pdf</td><td>true</td><td>true</td><td>true</td><td>true</td><td>2024</td><td>N/A</td><td>Institutional Research</td><td>N/A</td></tr></tbody></table></div>
                     </div>
                </div>

                <!-- CALENDAR -->
                 <div id="table-thecollegeofwoosterclassschedules" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('calendar')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Class Schedules</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>starttime</th><th>endtime</th><th>major</th><th>department</th><th>_mon</th><th>_tue</th><th>_wed</th><th>_thur</th><th>_fri</th><th>_sat</th><th>_sun</th><th>instructor</th><th>ta</th><th>enrollmentcapacity</th><th>instructionmode</th><th>books</th><th>startday</th><th>startmonth</th><th>startyear</th><th>endday</th><th>endmonth</th><th>endyear</th><th>location</th><th>payment</th><th>about</th><th>capacity</th><th>phone</th><th>email</th><th>whatsapp</th><th>ticketslink</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>75</td><td>ART 101 - Intro Drawing</td><td>Freedlander Theatre</td><td>Studio A</td><td>01:00 PM</td><td>03:00 PM</td><td>Studio Art</td><td>Art Dept</td><td>true</td><td>false</td><td>true</td><td>false</td><td>false</td><td>false</td><td>false</td><td>Prof. Sketch</td><td>N/A</td><td>15</td><td>In-Person</td><td>Drawing Supplies List</td><td>26</td><td>Aug</td><td>2024</td><td>06</td><td>Dec</td><td>2024</td><td>...</td><td>N/A</td><td>Fundamentals of drawing...</td><td>15</td><td>...</td><td>...</td><td>...</td><td>N/A</td><td>40.792</td><td>-81.938</td></tr></tbody></table></div>
                    </div>
                </div>
                <div id="table-thecollegeofwoosterweeklyschedule" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('calendar')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Weekly Schedule</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>teamororg</th><th>starttime</th><th>endtime</th><th>_mon</th><th>_tue</th><th>_wed</th><th>_thur</th><th>_fri</th><th>_sat</th><th>_sun</th><th>about</th><th>startday</th><th>startmonth</th><th>startyear</th><th>endday</th><th>endmonth</th><th>endyear</th><th>location</th><th>payment</th><th>capacity</th><th>phone</th><th>email</th><th>whatsapp</th><th>ticketslink</th><th>major</th><th>instructor</th><th>ta</th><th>enrollmentcapacity</th><th>instructionmode</th><th>syllabuscount</th><th>books</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>76</td><td>Weekly Staff Meeting</td><td>Galpin Hall</td><td>Conf Room</td><td>Admin Staff</td><td>09:00 AM</td><td>10:00 AM</td><td>true</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td><td>Regular admin meeting...</td><td>01</td><td>Jan</td><td>2024</td><td>31</td><td>Dec</td><td>2024</td><td>...</td><td>N/A</td><td>20</td><td>...</td><td>...</td><td>...</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>0</td><td>N/A</td><td>40.794</td><td>-81.938</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterevents" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('calendar')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Events</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>location</th><th>starttime</th><th>endtime</th><th>startday</th><th>startmonth</th><th>startyear</th><th>endday</th><th>endmonth</th><th>endyear</th><th>payment</th><th>capacity</th><th>phone</th><th>email</th><th>whatsapp</th><th>teamororg</th><th>department</th><th>admissionskeydate</th><th>paymentkeydate</th><th>orientationevent</th><th>graduationevent</th><th>graduationkeydate</th><th>alumnievent</th><th>communityrentalevent</th><th>about</th><th>ticketslink</th><th>latitude</th><th>longitude</th><th>_mon</th><th>_tue</th><th>_wed</th><th>_thur</th><th>_fri</th><th>_sat</th><th>_sun</th><th>major</th><th>instructor</th><th>ta</th><th>enrollmentcapacity</th><th>instructionmode</th><th>books</th></tr></thead><tbody><tr><td>77</td><td>Homecoming Football Game</td><td>John P. Papp Stadium</td><td>Field</td><td>Stadium</td><td>01:00 PM</td><td>04:00 PM</td><td>19</td><td>Oct</td><td>2024</td><td>19</td><td>Oct</td><td>2024</td><td>Tickets Required</td><td>3000</td><td>...</td><td><EMAIL></td><td>...</td><td>Football Team</td><td>Athletics</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td><td>true</td><td>false</td><td>Annual homecoming game...</td><td>/tickets/football</td><td>40.790</td><td>-81.937</td><td>false</td><td>false</td><td>false</td><td>false</td><td>false</td><td>true</td><td>false</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td><td>N/A</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosteracademiccalendar" class="view-container table-view">
                    <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('calendar')">arrow_back</button></div>
                    <div class="table-management">
                        <h3>Academic Calendar</h3>
                        <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                        <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>starttime</th><th>endtime</th><th>startday</th><th>startmonth</th><th>startyear</th><th>endday</th><th>endmonth</th><th>endyear</th></tr></thead><tbody><tr><td>78</td><td>Thanksgiving Break</td><td>N/A</td><td>N/A</td><td>27</td><td>Nov</td><td>2024</td><td>01</td><td>Dec</td><td>2024</td></tr></tbody></table></div>
                    </div>
                </div>
                 <div id="table-thecollegeofwoosterrentalequipmentcalendar" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('calendar')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Rental Equipment Calendar</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>asset</th><th>tagoridentifier</th><th>department</th><th>renter</th><th>checkoutday</th><th>checkoutmonth</th><th>checkoutyear</th><th>returnday</th><th>returnmonth</th><th>returnyear</th></tr></thead><tbody><tr><td>79</td><td>Portable Projector</td><td>EQ-PROJ-05</td><td>IT Services</td><td>History Dept</td><td>10</td><td>Oct</td><td>2024</td><td>12</td><td>Oct</td><td>2024</td></tr></tbody></table></div>
                     </div>
                 </div>


                <!-- MAP -->
                 <div id="table-map-placeholder" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('map_nav')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Campus Map Data</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload Point Data CSV</button><button class="img-btn">Upload Map Layer</button></div>
                         <p><i>This section would typically display map data derived from other tables or allow managing map layers. A functional map is below.</i></p>
                         <div id="mapContainer">Map will load here if Leaflet is configured.</div>
                     </div>
                 </div>

                <!-- FEEDBACK -->
                 <div id="table-thecollegeofwoosterfeedback" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('feedback_nav')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Feedback Collection</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th></tr></thead><tbody><tr><td>80</td><td>Website Feedback Form</td><td>/feedback/website</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- HISTORY -->
                 <div id="table-thecollegeofwoostersocialmediafeeds-hist" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('history')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Historical Timeline</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>year</th><th>about</th></tr></thead><tbody><tr><td>81</td><td>Independent Study Established</td><td>1947</td><td>The cornerstone of Wooster's curriculum was formally established...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- RENTALS -->
                 <div id="table-thecollegeofwoosterrentals" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('rentals')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Rentals</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>dimensions</th><th>capacity</th><th>phone</th><th>whatsapp</th><th>payment</th><th>department</th><th>facilityrental</th><th>equipmentrental</th><th>about</th><th>latitude</th><th>longitude</th><th>pricing</th></tr></thead><tbody><tr><td>82</td><td>Lean Lecture Room</td><td>30x40</td><td>50</td><td>(*************</td><td>...</td><td>Internal Charge/External Fee</td><td>Conference Services</td><td>true</td><td>false</td><td>Tiered lecture room...</td><td>40.793</td><td>-81.936</td><td>See Rate Sheet</td></tr></tbody></table></div>
                     </div>
                 </div>
                 <div id="table-thecollegeofwoosterrentalequipmentcalendar" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('rentals')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Rental Equipment Calendar</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>asset</th><th>tagoridentifier</th><th>department</th><th>renter</th><th>checkoutday</th><th>checkoutmonth</th><th>checkoutyear</th><th>returnday</th><th>returnmonth</th><th>returnyear</th></tr></thead><tbody><tr><td>83</td><td>PA System</td><td>EQ-AUDIO-01</td><td>IT Services</td><td>Music Dept</td><td>11</td><td>Oct</td><td>2024</td><td>14</td><td>Oct</td><td>2024</td></tr></tbody></table></div>
                     </div>
                 </div>


                <!-- JOBS -->
                 <div id="table-thecollegeofwoosterjobs" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('jobs')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Jobs</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>openingday</th><th>openingmonth</th><th>openingyear</th><th>closingday</th><th>closingmonth</th><th>closingyear</th><th>email</th><th>link</th><th>adminjob</th><th>academicjob</th><th>about</th></tr></thead><tbody><tr><td>84</td><td>Administrative Assistant - APEX</td><td>15</td><td>Oct</td><td>2024</td><td>15</td><td>Nov</td><td>2024</td><td><EMAIL></td><td>/jobs/apex-aa</td><td>true</td><td>false</td><td>Support role in the APEX center...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- SERVICES -->
                 <div id="table-thecollegeofwoosterservices" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('services')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Services</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>price</th><th>requirements</th><th>time</th><th>department</th><th>link</th><th>about</th></tr></thead><tbody><tr><td>85</td><td>Campus Mail Forwarding</td><td>Varies</td><td>Graduating Senior/Leave</td><td>1 Week Setup</td><td>Post Office</td><td>/services/mail</td><td>Forward mail after leaving campus...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- MONEY -->
                 <div id="table-thecollegeofwoosteratms" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('money')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>ATMs</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>building</th><th>room</th><th>about</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>86</td><td>Huntington ATM</td><td>Scot Center</td><td>Lobby</td><td>Provides cash withdrawals...</td><td>40.790</td><td>-81.937</td></tr></tbody></table></div>
                     </div>
                 </div>
                 <div id="table-thecollegeofwoosterpayments-money" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('money')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Payment Portals/Methods</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>link</th><th>about</th></tr></thead><tbody><tr><td>87</td><td>Campus Card Deposit</td><td>/pay/campuscard</td><td>Add funds to student Campus Card...</td></tr></tbody></table></div>
                     </div>
                 </div>

                 <!-- HEALTH -->
                 <div id="table-thecollegeofwoosterclinicsorhospitals" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('health')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Clinics or Hospitals</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th><th>phone</th><th>email</th><th>fax</th><th>whatsapp</th><th>latitude</th><th>longitude</th><th>daysnhours</th><th>building</th><th>room</th><th>address</th><th>postaladdress</th><th>payment</th><th>mission</th><th>vision</th><th>corevalues</th><th>acceptableclients</th><th>acceptablehealthinsurance</th><th>admissions</th><th>visitors</th><th>visitinghours</th><th>discharge</th><th>parking</th></tr></thead><tbody><tr><td>88</td><td>Longbrake Wellness Center</td><td>Primary health and counseling services...</td><td>(*************</td><td><EMAIL></td><td>...</td><td>...</td><td>40.792</td><td>-81.935</td><td>M-F 8am-4:30pm</td><td>Longbrake Wellness Ctr</td><td>Main</td><td>...</td><td>...</td><td>Insurance, Self-Pay</td><td>...</td><td>...</td><td>...</td><td>Students</td><td>Most major plans</td><td>By Appointment</td><td>...</td><td>...</td><td>...</td><td>Adjacent Lot</td></tr></tbody></table></div>
                     </div>
                 </div>
                <div id="table-thecollegeofwoostercounselingservices" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('health')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Counseling Services</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>price</th><th>requirements</th><th>time</th><th>department</th><th>link</th><th>about</th></tr></thead><tbody><tr><td>89</td><td>Individual Counseling</td><td>Included in Fees</td><td>Enrolled Student</td><td>By Appointment</td><td>Longbrake Wellness Ctr</td><td>/wellness/counseling</td><td>Confidential therapy sessions...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- SAFETY -->
                 <div id="table-thecollegeofwoosteremergencycontacts" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('safety')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Emergency Contacts</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>title</th><th>department</th><th>school</th><th>school2</th><th>school3</th><th>building</th><th>room</th><th>officehours</th><th>phone</th><th>ext</th><th>email</th><th>fax</th><th>hours</th><th>about</th><th>services</th><th>latitude</th><th>longitude</th></tr></thead><tbody><tr><td>90</td><td>Campus Safety Dispatch</td><td>Dispatcher</td><td>Campus Safety</td><td>N/A</td><td>N/A</td><td>N/A</td><td>Security Office</td><td>Dispatch</td><td>24/7</td><td>(*************</td><td>2590</td><td><EMAIL></td><td>...</td><td>24/7</td><td>Primary contact for emergencies...</td><td>Dispatch, Patrol...</td><td>40.791</td><td>-81.938</td></tr></tbody></table></div>
                     </div>
                 </div>
                 <div id="table-thecollegeofwoostersafetyprocedures" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('safety')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Safety Procedures</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>91</td><td>Tornado Safety Procedure</td><td>Seek shelter in lowest level, interior room...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- CONNECTIVITY -->
                 <div id="table-thecollegeofwoosterconnectivity" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('connectivity')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Connectivity</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>92</td><td>Guest Wi-Fi Access</td><td>Connect to the 'Woo-Guest' network, agree to terms...</td></tr></tbody></table></div>
                     </div>
                 </div>

                 <!-- GIVING -->
                 <div id="table-thecollegeofwoostergiving" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('giving_nav')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Giving Opportunities</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload CSV</button><button class="img-btn">Upload Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>fullname</th><th>about</th></tr></thead><tbody><tr><td>93</td><td>Endowed Scholarship Fund</td><td>Create a lasting legacy by endowing a scholarship...</td></tr></tbody></table></div>
                     </div>
                 </div>

                <!-- VIRTUAL TOUR -->
                 <div id="table-virtualtour-placeholder" class="view-container table-view">
                     <div class="table-view-header"><button class="back-arrow material-icons" onclick="goBack('virtual_tour')">arrow_back</button></div>
                     <div class="table-management">
                         <h3>Virtual Tour Locations</h3>
                         <div class="upload-buttons"><button class="csv-btn">Upload Tour Point CSV</button><button class="img-btn">Upload 360 Image</button></div>
                         <div class="table-wrapper"><table><thead><tr><th>id</th><th>location_name</th><th>description</th><th>related_building_id</th><th>virtual_tour_url</th></tr></thead><tbody><tr><td>94</td><td>Lowry Center Main Lounge</td><td>360 view of the main gathering space</td><td>68</td><td>https://tour.wooster.edu/lowry</td></tr></tbody></table></div>
                         <p><i>Data derived from building/housing tables or a dedicated virtual tour table.</i></p>
                     </div>
                 </div>

                <!-- Add more table-view divs for ALL other tables here -->

            </div> <!-- main-content-area -->
        </div> <!-- main-content-wrapper -->
    </div> <!-- container -->

    <!-- Overlays for small/rotated screens -->
    <div id="rotate-message" class="rotate-overlay">
        <i class="material-icons">screen_rotation</i>
        <p>Please rotate your device to landscape mode.</p>
    </div>
    <div id="small-device-message" class="small-device-overlay">
        <i class="material-icons">devices</i>
        <p>This dashboard is best viewed on a larger screen.</p>
    </div>

    <!-- Leaflet JS (for map placeholder) -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        let currentViewId = 'start-grid'; // Default view
        let currentCategory = 'start'; // Default category
        let map; // Leaflet map instance
        let navigationHistory = []; // Simple history for back button

        function showView(viewId, headerTitle, category, isTable = false) {
            const previousViewId = currentViewId;

            // Deactivate previous view and sidebar item (if applicable)
            document.getElementById(currentViewId)?.classList.remove('active');
            if (!isTable) { // Only change sidebar highlight if moving to a new grid/category
                 document.querySelector(`.sidebar-item[onclick*="showGridView('${currentCategory}'"]`)?.classList.remove('active');
            }

            // Activate new view
             const newView = document.getElementById(viewId);
             if (newView) {
                 newView.classList.add('active');
                 currentViewId = viewId;
                 currentCategory = category; // Update current category

                 // Update header
                 document.getElementById('main-header').textContent = headerTitle;

                 // Update sidebar highlight
                 const newSidebarItem = document.querySelector(`.sidebar-item[onclick*="showGridView('${category}'"]`);
                 if (newSidebarItem) {
                      // Deactivate all first
                      document.querySelectorAll('.sidebar-item').forEach(item => item.classList.remove('active'));
                      newSidebarItem.classList.add('active');
                 }

                 // Push to history if it's a new view (and not the initial load)
                 if (navigationHistory.length === 0 || navigationHistory[navigationHistory.length - 1].id !== viewId) {
                     if (previousViewId !== viewId) { // Avoid pushing same view twice
                        navigationHistory.push({ id: viewId, title: headerTitle, category: category, isTable: isTable });
                     }
                 }


                 // Initialize or refresh map if needed
                 if (viewId === 'table-map-placeholder') {
                     initializeMapIfNeeded();
                 }
             } else {
                 console.error("View not found:", viewId);
                 // Optionally revert to the previous view or a default view
                 // showView(previousViewId, document.getElementById('main-header').textContent, currentCategory, document.getElementById(previousViewId)?.classList.contains('table-view'));
             }
             // Scroll content area to top
             document.querySelector('.main-content-area').scrollTop = 0;
        }

        function showGridView(category, headerTitle) {
            showView(`${category}-grid`, headerTitle, category, false);
            // Clear history when going back to a grid view
            navigationHistory = [{ id: `${category}-grid`, title: headerTitle, category: category, isTable: false }];
        }

        function showTableView(tableId, headerTitle, category) {
            showView(tableId, headerTitle, category, true);
        }

       function goBack(fallbackCategory) {
            if (navigationHistory.length > 1) {
                navigationHistory.pop(); // Remove current view
                const previousState = navigationHistory[navigationHistory.length - 1];
                if (previousState) {
                    // Show the previous view without adding it back to history
                    showView(previousState.id, previousState.title, previousState.category, previousState.isTable);
                     // Ensure the history reflects the back action correctly
                     if (navigationHistory.length > 1 && navigationHistory[navigationHistory.length - 1].id === previousState.id) {
                       // If going back resulted in the same state being top (e.g., rapid clicks), pop again
                       // This is a safety measure, might need refinement based on behavior
                       // navigationHistory.pop();
                     }
                } else {
                    // Fallback if history is somehow broken
                    showGridView(fallbackCategory || 'start', fallbackCategory ? fallbackCategory.toUpperCase() : 'START');
                }
            } else {
                // If only one item in history (usually the initial grid view), go to default
                 showGridView(fallbackCategory || 'start', fallbackCategory ? fallbackCategory.toUpperCase() : 'START');
            }
        }


        function initializeMapIfNeeded() {
            const mapContainer = document.getElementById('mapContainer');
            // Only initialize if container exists, is visible, and map isn't already initialized
            if (mapContainer && mapContainer.offsetParent !== null && !map) {
                // Wait brief moment for layout reflow after view change
                setTimeout(() => {
                    if (mapContainer.offsetWidth > 0 && mapContainer.offsetHeight > 0) {
                        try {
                             map = L.map('mapContainer', {
                                zoomControl: true,
                                attributionControl: false
                            }).setView([40.7947, -81.9369], 16); // Wooster coordinates

                            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

                            L.marker([40.7947, -81.9369]).addTo(map)
                                .bindPopup('The College of Wooster (Approx. Center)');

                            console.log("Map Initialized");
                        } catch (e) {
                             console.error("Leaflet map initialization failed:", e);
                             mapContainer.innerHTML = "Map loading failed."; // Show error to user
                        }
                    } else {
                         console.warn("Map container still not visible/sized, delaying init.");
                         // Optionally retry or inform user map cannot load in this state
                    }
                 }, 50); // Delay helps ensure container is ready

            } else if (map && mapContainer && mapContainer.offsetParent !== null) {
                 // If map exists and container is visible, invalidate its size
                 setTimeout(() => { map.invalidateSize(); console.log("Map Invalidated Size"); }, 50);
            } else if (map && (!mapContainer || mapContainer.offsetParent === null)) {
                 console.log("Map container not visible, skipping size invalidation.");
            }
        }


        function toggleFullscreen() { /* ... (keep existing function) ... */ }
        function handleOrientationChange() { /* ... (keep existing function) ... */ }

        // Initial Setup on DOM Load
        document.addEventListener('DOMContentLoaded', function() {
            showGridView('start', 'START'); // Show initial grid view

            const modeToggle = document.getElementById('mode-toggle');
            modeToggle.addEventListener('change', () => document.body.classList.toggle('dark-mode'));

             const fullscreenButton = document.getElementById('fullscreen-button');
             fullscreenButton.addEventListener('click', toggleFullscreen);

             document.addEventListener('fullscreenchange', () => {
                const icon = document.getElementById('fullscreen-button').querySelector('i');
                icon.textContent = document.fullscreenElement ? 'fullscreen_exit' : 'fullscreen';
                if (map) { setTimeout(() => map.invalidateSize(), 100); }
            });

            handleOrientationChange();
            window.addEventListener('orientationchange', handleOrientationChange);
            window.addEventListener('resize', handleOrientationChange);
        });
    </script>
</body>
</html>
fix the fullscreen button to work. in the body where a specific table is shown, the back button shouldnt be on its own. it should be on the same line as the name of the table in the body. we should also have download csv and download image with icons if possible.






START
---------------------------
-- Table: thecollegeofwooster_helpdesks
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_helpdesks;
CREATE TABLE thecollegeofwooster_helpdesks (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_helpdesks (fullname, building, room, hours, payment, phone, email, fax, whatsapp, about, latitude, longitude) VALUES
-- IT Help Desk (located in the Computer Center)
('IT Help Desk', 'Computer Center', 'Room 101', 'Mon-Fri 8am-8pm', 'Free', '************', '<EMAIL>', '************', '************', 'Technical support for campus computing and network issues.', 41.9940, -81.5550),
-- Student Services Help Desk (in the Student Center)
('Student Services Help Desk', 'Student Center', 'Main Desk', 'Mon-Fri 9am-5pm', 'Free', '************', '<EMAIL>', '************', '************', 'Assistance with registration, scheduling, and campus resources.', 41.9955, -81.5565),
-- Financial Aid Help Desk (located in the Administration Building)
('Financial Aid Help Desk', 'Administration Building', 'Suite 12', 'Mon-Fri 8am-4pm', 'Free', '************', '<EMAIL>', '************', '************', 'Guidance on financial aid options, scholarships, and tuition payment plans.', 41.9960, -81.5570),
-- Library Information Desk (located in the William A. Henry Library)
('Library Information Desk', 'William A. Henry Library', 'Lobby', 'Mon-Fri 8am-10pm', 'Free', '************', '<EMAIL>', '************', '************', 'Assistance with research, circulation, and library services.', 41.9970, -81.5580);

---------------------------
-- Table: thecollegeofwooster_accessibility
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_accessibility;
CREATE TABLE thecollegeofwooster_accessibility (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_accessibility (fullname, building, room, hours, phone, whatsapp, about, latitude, longitude) VALUES
-- Accessible Services Office (in the Academic Center)
('Accessible Services Office', 'Academic Center', 'Room 230', 'Mon-Fri 8am-5pm', '************', '************', 'Coordinates support for students requiring accessibility accommodations.', 41.9980, -81.5590),
-- Disability Support Center (in the Center for Student Affairs)
('Disability Support Center', 'Center for Student Affairs', 'Office 105', 'Mon-Fri 8am-5pm', '************', '************', 'Provides resources and counseling for students with disabilities.', 41.9985, -81.5600),
-- Health & Accessibility Unit (at the Wellness Center)
('Health & Accessibility Unit', 'Wellness Center', 'Room B12', 'Mon-Fri 9am-5pm', '************', '************', 'Integrates health services with accessibility support.', 41.9990, -81.5610),
-- Campus Access Liaison (at the Community Building)
('Campus Access Liaison', 'Community Building', 'Room 305', 'Mon-Fri 8am-6pm', '************', '************', 'Advises on campus navigation and facility access.', 42.0000, -81.5620);

---------------------------
-- Table: thecollegeofwooster_faq
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_faq;
CREATE TABLE thecollegeofwooster_faq (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    department TEXT,
    school TEXT,
    topic TEXT,
    studentquestion TEXT,
    facultyorstaffquestion TEXT,
    parentquestion TEXT,
    admissionsquestion TEXT,
    orientationquestion TEXT,
    symposiumquestion TEXT,
    graduationquestion TEXT,
    alumniquestion TEXT
);

INSERT INTO thecollegeofwooster_faq (fullname, about, department, school, topic, studentquestion, facultyorstaffquestion, parentquestion, admissionsquestion, orientationquestion, symposiumquestion, graduationquestion, alumniquestion) VALUES
-- Admissions FAQ
('Admissions FAQ', 'Details about applying and joining The College of Wooster.', 'Admissions', 'Undergraduate', 'Application Process', 
 'How do I apply for admission?', 
 'What should faculty mention when recommending applicants?', 
 'What are the admission requirements for my child?', 
 'When is the application deadline?', 
 'What orientation events are scheduled for new students?', 
 'Will there be an admissions symposium?', 
 'What are the criteria for graduation?', 
 'How can alumni remain involved?'),
-- Financial Aid FAQ
('Financial Aid FAQ', 'Information on tuition, aid, and scholarships.', 'Financial Aid', 'Undergraduate', 'Tuition & Scholarships', 
 'How do I apply for financial aid?', 
 'How can staff support students in need?', 
 'What financial aid options exist for parents?', 
 'Is there an early admission financial aid process?', 
 'How is orientation addressing financial planning?', 
 'Are there symposium sessions on funding education?', 
 'What are the scholarship renewal requirements?', 
 'How do alumni access alumni loans?'),
-- Academic Advising FAQ
('Academic Advising FAQ', 'Guidance for course selection and academic planning.', 'Academic Affairs', 'Undergraduate', 'Course Registration', 
 'Who can help me plan my schedule?', 
 'What resources are available for faculty advising?', 
 'Where can parents find academic progress updates?', 
 'Do admissions advisors help with course selection?', 
 'What orientation sessions cover academic policies?', 
 'Is there a symposium on academic success?', 
 'What academic requirements must be completed for graduation?', 
 'Can alumni provide mentorship in course planning?'),
-- Student Life FAQ
('Student Life FAQ', 'Answers to common questions about campus life and extracurricular activities.', 'Student Affairs', 'Undergraduate', 'Campus Involvement', 
 'What clubs and organizations are available?', 
 'How can faculty get involved in student activities?', 
 'How is campus life structured to support students?', 
 'Do admissions events include student life presentations?', 
 'What orientation activities focus on student life?', 
 'Will there be a symposium on student engagement?', 
 'What are the prerequisites for graduation events?', 
 'How can alumni participate in campus life?');

---------------------------
-- Table: thecollegeofwooster_links
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_links;
CREATE TABLE thecollegeofwooster_links (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT,
    admissionslink TEXT,
    orientationlink TEXT,
    symposiumlink TEXT,
    alumnilink TEXT,
    department TEXT,
    img_link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_links (fullname, link, admissionslink, orientationlink, symposiumlink, alumnilink, department, img_link, about) VALUES
-- Admissions & Financial Aid Portal
('Admissions & Financial Aid', 'https://www.wooster.edu/admissions', 'https://www.wooster.edu/admissions/apply', 'https://www.wooster.edu/admissions/orientation', 'https://www.wooster.edu/admissions/symposium', 'https://www.wooster.edu/alumni', 'Admissions', 'https://www.wooster.edu/assets/img/admissions.jpg', 'Portal for all things related to admission and financial aid.'),
-- Academic Departments
('Academic Departments', 'https://www.wooster.edu/academics', 'https://www.wooster.edu/academics/admissions', 'https://www.wooster.edu/academics/orientation', 'https://www.wooster.edu/academics/symposium', 'https://www.wooster.edu/alumni/departments', 'Academics', 'https://www.wooster.edu/assets/img/academics.jpg', 'Information on academic programs and departmental resources.'),
-- Student Life and Campus Engagement
('Student Life & Campus Engagement', 'https://www.wooster.edu/studentlife', 'https://www.wooster.edu/studentlife/admissions', 'https://www.wooster.edu/studentlife/orientation', 'https://www.wooster.edu/studentlife/symposium', 'https://www.wooster.edu/alumni/studentlife', 'Student Affairs', 'https://www.wooster.edu/assets/img/studentlife.jpg', 'Resources and events that enrich campus life.'),
-- Campus Facilities & Infrastructure
('Campus Facilities', 'https://www.wooster.edu/campus', 'https://www.wooster.edu/campus/admissions', 'https://www.wooster.edu/campus/orientation', 'https://www.wooster.edu/campus/symposium', 'https://www.wooster.edu/alumni/campus', 'Facilities', 'https://www.wooster.edu/assets/img/facilities.jpg', 'Updates on campus construction, renovations, and facility management.');

---------------------------
-- Table: thecollegeofwooster_construction
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_construction;
CREATE TABLE thecollegeofwooster_construction (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    location TEXT,
    startdate DATE,
    enddate DATE,
    phone TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_construction (fullname, location, startdate, enddate, phone, whatsapp, latitude, longitude, about) VALUES
-- Library Renovation at the William A. Henry Library
('Library Renovation', 'William A. Henry Library', '2025-07-01', '2025-12-15', '************', '************', 41.9970, -81.5585, 'Renovation of study spaces and archival facilities to improve modern research support.'),
-- Science Center Expansion
('Science Center Expansion', 'Science Building', '2025-06-15', '2025-11-30', '************', '************', 41.9980, -81.5595, 'Expanding laboratories and lecture halls to accommodate growing STEM programs.'),
-- Student Center Update
('Student Center Update', 'Student Center', '2025-05-20', '2025-10-15', '************', '************', 41.9990, -81.5605, 'Upgrading common areas and meeting rooms to foster student engagement and activities.'),
-- Athletic Complex Upgrade
('Athletic Complex Upgrade', 'Athletic Center', '2025-08-01', '2025-12-01', '************', '************', 42.0000, -81.5615, 'Modernizing gymnasiums, fields, and exercise facilities to support athletics.');

---------------------------
-- Table: thecollegeofwooster_printing
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_printing;
CREATE TABLE thecollegeofwooster_printing (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_printing (fullname, building, room, hours, payment, phone, whatsapp, about, latitude, longitude) VALUES
-- Campus Printing Services at the University Print Center
('Campus Printing Services', 'University Print Center', 'Room 101', 'Mon-Fri 8am-8pm', 'Cash/Card', '************', '************', 'Provides fast, high-quality printing and copying services for students and staff.', 42.0010, -81.5620),
-- Digital Print Lab (located in the Media Center)
('Digital Print Lab', 'Media Center', 'Lab 201', 'Mon-Fri 9am-7pm', 'Card Only', '************', '************', 'Offers digital printing, scanning, and binding services.', 42.0020, -81.5630),
-- Student Copy Services (located in the Library)
('Student Copy Services', 'William A. Henry Library', 'Copy Room', 'Mon-Fri 8am-10pm', 'Free for enrolled students', '************', '************', 'Supports academic and research copy needs on campus.', 42.0030, -81.5640),
-- Print & Bind (in the Administration Building)
('Print & Bind', 'Administration Building', 'Office 302', 'Mon-Fri 8am-5pm', 'Cash/Card', '************', '************', 'Specializes in document printing, binding, and finishing services.', 42.0040, -81.5650);

---------------------------
-- Table: thecollegeofwooster_daycares
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_daycares;
CREATE TABLE thecollegeofwooster_daycares (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    boxnumber TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_daycares (fullname, building, room, hours, phone, boxnumber, email, fax, whatsapp, about, latitude, longitude) VALUES
-- Wooster Child Care Center
('Wooster Child Care Center', 'Family Life Center', 'Room A1', 'Mon-Fri 7am-6pm', '************', 'BOX-101', '<EMAIL>', '************', '************', 'Offers quality care for children of students and staff with age-appropriate programs.', 42.0050, -81.5660),
-- Campus Daycare
('Campus Daycare', 'Community Center', 'Room B2', 'Mon-Fri 7:30am-5:30pm', '************', 'BOX-102', '<EMAIL>', '************', '************', 'Provides a safe and engaging environment for young children during the day.', 42.0060, -81.5670),
-- Early Learning Center
('Early Learning Center', 'Academic Center', 'Room C3', 'Mon-Fri 8am-5pm', '************', 'BOX-103', '<EMAIL>', '************', '************', 'Focuses on early childhood development and educational enrichment programs.', 42.0070, -81.5680),
-- Infant Care Program
('Infant Care Program', 'Student Services Building', 'Room D4', 'Mon-Fri 7am-4pm', '330-672-4600', 'BOX-104', '<EMAIL>', '330-672-4601', '330-672-4602', 'Specializes in infant care with customized programs for babies and toddlers.', 42.0080, -81.5690);

---------------------------
-- Table: thecollegeofwooster_sustainability
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_sustainability;
CREATE TABLE thecollegeofwooster_sustainability (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_sustainability (fullname, about) VALUES
-- Office of Sustainability
('Office of Sustainability', 'Leads campus initiatives on energy efficiency, waste reduction, and environmental stewardship.'),
-- Green Campus Initiative
('Green Campus Initiative', 'Promotes recycling, water conservation, and sustainable practices across campus.'),
-- Environmental Research Center
('Environmental Research Center', 'Conducts research and community outreach on climate change and sustainable development.'),
-- Sustainable Transportation Office
('Sustainable Transportation Office', 'Coordinates bike programs, carpooling, and alternative transit to reduce the campus carbon footprint.');
