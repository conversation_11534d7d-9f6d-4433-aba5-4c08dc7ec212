import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';
import 'housing_page.dart';
import 'local_lodging_page.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TertiaryLodgingPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryLodgingPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<TertiaryLodgingPage> createState() => _TertiaryLodgingPageState();
}

class _TertiaryLodgingPageState extends State<TertiaryLodgingPage> {
  List<Map<String, dynamic>>? _cachedHousing;
  List<Map<String, dynamic>>? _cachedLocalLodging;
  String? _lastCollegeName;
  bool _isLoadingHousing = false;
  bool _isLoadingLocalLodging = false;
  late RealtimeChannel _housingRealtimeChannel;
  late RealtimeChannel _localLodgingRealtimeChannel;

  @override
  void initState() {
    super.initState();
    print("TertiaryLodgingPage initState called for ${widget.institutionName}");
    _loadCachedHousing();
    _loadCachedLocalLodging();
    _loadHousingFromDatabaseAndCache();
    _loadLocalLodgingFromDatabaseAndCache();
    _setupHousingRealtimeListener();
    _setupLocalLodgingRealtimeListener();
  }

  @override
  void dispose() {
    _housingRealtimeChannel.unsubscribe();
    _localLodgingRealtimeChannel.unsubscribe();
    super.dispose();
  }

  // Housing methods
  Future<List<Map<String, dynamic>>?> _getCachedHousing(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? housingJson = prefs.getString(
        'housing_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (housingJson != null) {
      List<dynamic> decodedList = jsonDecode(housingJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheHousing(String collegeName, List<Map<String, dynamic>> housing) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String housingJson = jsonEncode(housing);
    await prefs.setString(
        'housing_${collegeName.toLowerCase().replaceAll(' ', '')}', housingJson);
    print('Housing cached for $collegeName.');
  }

  Future<void> _loadCachedHousing() async {
    final cachedData = await _getCachedHousing(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedHousing = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded housing from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadHousingFromDatabaseAndCache() async {
    if (_isLoadingHousing) {
      return;
    }

    setState(() {
      _isLoadingHousing = true;
    });

    print("Fetching housing for ${widget.institutionName} from database");
    final housingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_housing';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(housingTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedHousing = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingHousing = false;
          _cacheHousing(widget.institutionName, response);
          print("Housing fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingHousing = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingHousing = false;
          _cachedHousing = [];
          print("Error fetching housing for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingHousing = false;
      }
    }
  }

  void _setupHousingRealtimeListener() {
    final housingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_housing';
    _housingRealtimeChannel = Supabase.instance.client
        .channel('housing_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: housingTableName,
      callback: (payload) async {
        print(
            "Realtime update received for housing of ${widget.institutionName}: ${payload.eventType}");
        _loadHousingFromDatabaseAndCache();
      },
    ).subscribe();
  }

  // Local Lodging methods
  Future<List<Map<String, dynamic>>?> _getCachedLocalLodging(String collegeName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? localLodgingJson = prefs.getString(
        'locallodging_${collegeName.toLowerCase().replaceAll(' ', '')}');
    if (localLodgingJson != null) {
      List<dynamic> decodedList = jsonDecode(localLodgingJson);
      return decodedList.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> _cacheLocalLodging(String collegeName, List<Map<String, dynamic>> localLodging) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String localLodgingJson = jsonEncode(localLodging);
    await prefs.setString(
        'locallodging_${collegeName.toLowerCase().replaceAll(' ', '')}', localLodgingJson);
    print('Local lodging cached for $collegeName.');
  }

  Future<void> _loadCachedLocalLodging() async {
    final cachedData = await _getCachedLocalLodging(widget.institutionName);
    if (cachedData != null) {
      setState(() {
        _cachedLocalLodging = cachedData;
        _lastCollegeName = widget.institutionName;
        print("Loaded local lodging from cache for ${widget.institutionName}");
      });
    }
  }

  Future<void> _loadLocalLodgingFromDatabaseAndCache() async {
    if (_isLoadingLocalLodging) {
      return;
    }

    setState(() {
      _isLoadingLocalLodging = true;
    });

    print("Fetching local lodging for ${widget.institutionName} from database");
    final localLodgingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_locallodging';

    try {
      final List<Map<String, dynamic>> response = await Supabase.instance.client
          .from(localLodgingTableName)
          .select('*')
          .order('fullname', ascending: true);

      if (mounted) {
        setState(() {
          _cachedLocalLodging = response;
          _lastCollegeName = widget.institutionName;
          _isLoadingLocalLodging = false;
          _cacheLocalLodging(widget.institutionName, response);
          print("Local lodging fetched and cached for ${widget.institutionName}");
        });
      } else {
        _isLoadingLocalLodging = false;
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoadingLocalLodging = false;
          _cachedLocalLodging = [];
          print("Error fetching local lodging for ${widget.institutionName}: $error");
        });
      } else {
        _isLoadingLocalLodging = false;
      }
    }
  }

  void _setupLocalLodgingRealtimeListener() {
    final localLodgingTableName =
        '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_locallodging';
    _localLodgingRealtimeChannel = Supabase.instance.client
        .channel('locallodging_cache_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localLodgingTableName,
      callback: (payload) async {
        print(
            "Realtime update received for local lodging of ${widget.institutionName}: ${payload.eventType}");
        _loadLocalLodgingFromDatabaseAndCache();
      },
    ).subscribe();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    // Always show grid items
    return Visibility(
      key: Key('lodging_grid_item_$title'),
      visible: true,
      child: Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (title == 'Housing') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HousingPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedHousing: _cachedHousing,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            } else if (title == 'Local Lodging') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LocalLodgingPage(
                    collegeNameForTable: widget.institutionName,
                    isDarkMode: isDarkMode,
                    toggleTheme: widget.toggleTheme,
                    preloadedLocalLodging: _cachedLocalLodging,
                    isFromDetailPage: widget.isFromDetailPage,
                  ),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                ),
                ...[
                  const SizedBox(height: 8),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = widget.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Lodging',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Housing', Icons.apartment, theme, isFromDetailPage),
                _buildGridItem(context, 'Local Lodging', Icons.house, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}