import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AiCreditsWalletPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AiCreditsWalletPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AiCreditsWalletPage> createState() => _AiCreditsWalletPageState();
}

class _AiCreditsWalletPageState extends State<AiCreditsWalletPage> {
  int _credits = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCredits();
  }

  Future<void> _loadCredits() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading credits from a database
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _credits = 0; // Default to 0 credits for new users
      _isLoading = false;
    });
  }

  Future<void> _buyCredits(int amount) async {
    setState(() {
      _isLoading = true;
    });

    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _credits += amount;
      _isLoading = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Successfully purchased $amount credits!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'AI Credits Wallet',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Credits card
                  Card(
                    color: theme.colorScheme.surface,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Icon(
                            Icons.account_balance_wallet,
                            size: 48,
                            color: generalTextColor,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Your Credits',
                            style: GoogleFonts.notoSans(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '$_credits',
                            style: GoogleFonts.notoSans(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Credits are used for AI processing tasks',
                            style: GoogleFonts.notoSans(
                              fontSize: 14,
                              color: generalTextColor.withOpacity(0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Buy credits section
                  Card(
                    color: theme.colorScheme.surface,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Buy Credits',
                            style: GoogleFonts.notoSans(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Credit packages
                          _buildCreditPackage(
                            context,
                            amount: 100,
                            price: '4.99',
                            isPopular: false,
                            buttonBackground: buttonBackground,
                            buttonTextColor: buttonTextColor,
                            generalTextColor: generalTextColor,
                          ),

                          const SizedBox(height: 12),

                          _buildCreditPackage(
                            context,
                            amount: 500,
                            price: '19.99',
                            isPopular: true,
                            buttonBackground: buttonBackground,
                            buttonTextColor: buttonTextColor,
                            generalTextColor: generalTextColor,
                          ),

                          const SizedBox(height: 12),

                          _buildCreditPackage(
                            context,
                            amount: 1000,
                            price: '34.99',
                            isPopular: false,
                            buttonBackground: buttonBackground,
                            buttonTextColor: buttonTextColor,
                            generalTextColor: generalTextColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Usage information
                  Card(
                    color: theme.colorScheme.surface,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Credit Usage',
                            style: GoogleFonts.notoSans(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: generalTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          _buildUsageItem(
                            'Generate Notes',
                            '5 credits',
                            generalTextColor,
                          ),
                          _buildUsageItem(
                            'Create Flashcards',
                            '3 credits',
                            generalTextColor,
                          ),
                          _buildUsageItem(
                            'Generate Quiz',
                            '4 credits',
                            generalTextColor,
                          ),
                          _buildUsageItem(
                            'Create Exam',
                            '6 credits',
                            generalTextColor,
                          ),
                          _buildUsageItem(
                            'Interactive Lesson',
                            '8 credits',
                            generalTextColor,
                          ),
                          _buildUsageItem(
                            'Scheme of Work',
                            '10 credits',
                            generalTextColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildCreditPackage(
    BuildContext context, {
    required int amount,
    required String price,
    required bool isPopular,
    required Color buttonBackground,
    required Color buttonTextColor,
    required Color generalTextColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isPopular
            ? (widget.isDarkMode ? Colors.white : Colors.black)
            : Theme.of(context).dividerColor,
          width: isPopular ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '$amount Credits',
                        style: GoogleFonts.notoSans(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: generalTextColor,
                        ),
                      ),
                      if (isPopular) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: widget.isDarkMode ? Colors.white : Colors.black,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'POPULAR',
                            style: GoogleFonts.notoSans(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: widget.isDarkMode ? Colors.black : Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$$price',
                    style: GoogleFonts.notoSans(
                      fontSize: 14,
                      color: generalTextColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => _buyCredits(amount),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBackground,
                foregroundColor: buttonTextColor,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              child: Text(
                'Buy Now',
                style: GoogleFonts.notoSans(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageItem(String task, String credits, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            task,
            style: GoogleFonts.notoSans(
              fontSize: 14,
              color: textColor,
            ),
          ),
          Text(
            credits,
            style: GoogleFonts.notoSans(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }
}
