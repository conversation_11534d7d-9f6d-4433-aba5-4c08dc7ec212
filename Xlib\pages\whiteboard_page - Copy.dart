import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter/rendering.dart';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_mobile_ads/google_mobile_ads.dart'; // Import Google Mobile Ads

import 'dart:io' as io;
import 'dart:typed_data';
import 'package:permission_handler/permission_handler.dart'; // Import permission_handler
import 'package:path/path.dart' as path; // Import path for combining paths

@immutable
class WhiteboardPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const WhiteboardPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<WhiteboardPage> createState() => _WhiteboardPageState();
}

class _WhiteboardPageState extends State<WhiteboardPage> {
  List<List<DrawingPoint?>> _boards = [[]];
  int _currentBoardIndex = 0;
  Color selectedColor = Colors.black; // Default pen color is black
  double strokeWidth = 1.0; // Default pen size is 1
  final GlobalKey _repaintKey = GlobalKey();
  bool _isErasing = false; // Track if the eraser is active

  // Key for saving and loading data
  static const String _boardsDataKey = 'whiteboard_boards_data';

  RewardedAd? _rewardedAd;
  bool _isAdLoaded = false;
  // **Replace with your actual AdMob Rewarded Ad unit ID (Test ID for now)**
  final String _adUnitId = 'ca-app-pub-3940256099942544/5224354917';

  @override
  void initState() {
    super.initState();
    _loadBoards();
    _loadRewardedAd();
  }

  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          setState(() {
            _rewardedAd = ad;
            _isAdLoaded = true;
          });
          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (RewardedAd ad) {
              ad.dispose();
              _loadRewardedAd(); // Load a new ad for the next time
            },
            onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
              print('Failed to show rewarded ad: $error');
              ad.dispose();
            },
          );
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
        },
      ),
    );
  }

  void _showRewardedAd() {
    if (_rewardedAd != null && _isAdLoaded) {
      _rewardedAd!.show(
        onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
          print('User earned reward: ${reward.amount} ${reward.type}');
          // You can add any logic here if you want to track rewards
        },
      );
    } else {
      print('Rewarded ad is not ready yet.');
      Navigator.pop(context); // Still navigate back if the ad isn't ready
    }
  }

  List<DrawingPoint?> get currentBoard => _boards[_currentBoardIndex];

  void _showCombinedPenSettings() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        Color pickerColor = selectedColor;
        double sliderStrokeWidth = strokeWidth;
        return AlertDialog(
          title: const Text('Pen Settings'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    const Text('Select Color:'),
                    ColorPicker(
                      pickerColor: pickerColor,
                      onColorChanged: (Color color) {
                        pickerColor = color;
                      },
                      pickerAreaHeightPercent: 0.8,
                    ),
                    const SizedBox(height: 20),
                    const Text('Select Size:'),
                    Slider(
                      value: sliderStrokeWidth,
                      min: 1,
                      max: 20,
                      onChanged: (double newValue) {
                        setState(() {
                          sliderStrokeWidth = newValue;
                        });
                      },
                    ),
                    Text('Pen size: ${sliderStrokeWidth.toStringAsFixed(1)}'),
                  ],
                ),
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Done'),
              onPressed: () {
                setState(() {
                  selectedColor = pickerColor;
                  strokeWidth = sliderStrokeWidth;
                  _isErasing = false; // Deactivate eraser when pen settings are applied
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _addNewBoard() {
    setState(() {
      _boards.add([]);
      _currentBoardIndex = _boards.length - 1;
      _saveBoards();
    });
  }

  void _clearCurrentBoard() {
    setState(() {
      _boards[_currentBoardIndex].clear();
      _saveBoards();
    });
  }

  void _deleteCurrentBoard() {
    if (_boards.length > 1) {
      setState(() {
        _boards.removeAt(_currentBoardIndex);
        _currentBoardIndex =
            _currentBoardIndex > 0 ? _currentBoardIndex - 1 : 0;
        _saveBoards();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cannot delete the only board.')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      final status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Storage permission is required to save PDF.')),
        );
        return false;
      }
    }
    return true;
  }

  Future<void> _saveAsPdf() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      if (!await _requestStoragePermission()) {
        return;
      }
    }

    final pdf = pw.Document();

    for (int i = 0; i < _boards.length; i++) {
      if (_boards[i].any((point) => point != null)) {
        // Check if the board is not blank
        ByteData? imageByteData = await _captureBoardAsImage(boardIndex: i);
        if (imageByteData != null) {
          final pdfImage = pw.MemoryImage(imageByteData.buffer.asUint8List());
          pdf.addPage(pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(pdfImage),
              );
            },
          ));
        }
      }
    }

    if (pdf.document.pdfPageList.pages.isNotEmpty) {
      try {
        final pdfBytes = await pdf.save();
        final fileName = 'notes_${DateTime.now().millisecondsSinceEpoch}.pdf';

        String? downloadsPath;
        if (io.Platform.isAndroid) {
          downloadsPath = (await getExternalStorageDirectory())?.path;
        } else if (io.Platform.isIOS) {
          // On iOS, we can save to the Documents directory which is accessible by the Files app.
          downloadsPath = (await getApplicationDocumentsDirectory()).path;
        }

        if (downloadsPath != null) {
          final file = io.File(path.join(downloadsPath, fileName));
          await file.writeAsBytes(pdfBytes);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('PDF saved to ${file.path}')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text(
                    'Could not determine the downloads directory. PDF not saved.')),
          );
        }
      } catch (e) {
        print("Error saving PDF: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save PDF.')),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No notes to save as PDF.')),
      );
    }
  }

  Future<ByteData?> _captureBoardAsImage({required int boardIndex}) async {
    final painter = _WhiteboardPainter(_boards[boardIndex]);
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder,
        Rect.fromPoints(const Offset(0.0, 0.0), const Offset(800.0, 1131.0))); // A4 size
    painter.paint(canvas, const Size(800, 1131));
    final picture = recorder.endRecording();
    final img = await picture.toImage(800, 1131);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData;
  }

  // Saving boards data to local storage
  Future<void> _saveBoards() async {
    final prefs = await SharedPreferences.getInstance();
    final List<dynamic> serializableBoards = _boards.map((board) {
      return board.map((point) => point?.toJson()).toList();
    }).toList();
    final String boardsJson = jsonEncode(serializableBoards);
    await prefs.setString(_boardsDataKey, boardsJson);
  }

  // Loading boards data from local storage
  Future<void> _loadBoards() async {
    final prefs = await SharedPreferences.getInstance();
    final String? boardsJson = prefs.getString(_boardsDataKey);
    if (boardsJson != null) {
      try {
        final dynamic decodedData = jsonDecode(boardsJson);
        if (decodedData is List) {
          List<List<DrawingPoint?>> loadedBoards =
              decodedData.map<List<DrawingPoint?>?>((boardData) {
            if (boardData is List) {
              return boardData.map<DrawingPoint?>((pointData) {
                if (pointData is Map<String, dynamic>) {
                  return DrawingPoint.fromJson(pointData);
                }
                return null; // Handle cases where pointData is not a Map
              }).toList();
            }
            return null; // Handle cases where boardData is not a List
          }).whereType<List<DrawingPoint?>>().toList();
          setState(() {
            _boards = loadedBoards;
          });
        } else {
          print("Error: Decoded data is not a List");
          // Handle the case where the top-level structure is incorrect
          setState(() {
            _boards = [[]];
          });
        }
      } catch (e) {
        print("Error loading boards: $e");
        setState(() {
          _boards = [[]];
        });
      }
    }
  }

  @override
  void dispose() {
    _rewardedAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return WillPopScope(
      onWillPop: () async {
        _showRewardedAd();
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.white, // White background
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () {
              _showRewardedAd();
            },
          ),
          title: Text(
            'Whiteboard ${_boards.length > 1 ? '(${_currentBoardIndex + 1}/${_boards.length})' : ''}', // Renamed to Whiteboard
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          actions: [
            IconButton(
              icon: Icon(Icons.palette, color: theme.colorScheme.onSurface), // Changed to palette icon
              tooltip: 'Pen Settings',
              onPressed: _showCombinedPenSettings,
            ),
            IconButton(
              icon: Icon(_isErasing ? Icons.edit : Icons.draw, color: theme.colorScheme.onSurface),
              tooltip: _isErasing ? 'Pen' : 'Eraser',
              onPressed: () {
                setState(() {
                  _isErasing = !_isErasing;
                });
              },
            ),
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Add Board',
              onPressed: _addNewBoard,
            ),
            if (_boards.length > 1)
              IconButton(
                icon: const Icon(Icons.chevron_left),
                tooltip: 'Previous Board',
                onPressed: _currentBoardIndex > 0
                    ? () => setState(() => _currentBoardIndex--)
                    : null,
              ),
            if (_boards.length > 1)
              IconButton(
                icon: const Icon(Icons.chevron_right),
                tooltip: 'Next Board',
                onPressed: _currentBoardIndex < _boards.length - 1
                    ? () => setState(() => _currentBoardIndex++)
                    : null,
              ),
            PopupMenuButton<String>(
              icon: Icon(Icons.delete, color: theme.colorScheme.onSurface),
              onSelected: (value) {
                if (value == 'clear') {
                  _clearCurrentBoard();
                } else if (value == 'delete') {
                  _deleteCurrentBoard();
                }
              },
              itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                const PopupMenuItem<String>(
                  value: 'clear',
                  child: Text('Clear Board'),
                ),
                const PopupMenuItem<String>(
                  value: 'delete',
                  child: Text('Delete Board'),
                ),
              ],
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'pdf') {
                  _saveAsPdf();
                }
              },
              itemBuilder: (BuildContext context) => [
                const PopupMenuItem<String>(
                  value: 'pdf',
                  child: Text('Save as PDF'),
                ),
              ],
            ),
          ],
        ),
        body: RepaintBoundary(
          key: _repaintKey,
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                currentBoard.add(
                  DrawingPoint(
                    details.localPosition,
                    Paint()
                      ..color = _isErasing ? const Color(0xFFFFFFFF) : selectedColor // Eraser is white
                      ..strokeWidth = _isErasing ? strokeWidth + 10 : strokeWidth
                      ..strokeCap = StrokeCap.round,
                    isErasing: _isErasing,
                  ),
                );
                _saveBoards();
              });
            },
            onPanUpdate: (details) {
              setState(() {
                currentBoard.add(
                  DrawingPoint(
                    details.localPosition,
                    Paint()
                      ..color = _isErasing ? const Color(0xFFFFFFFF) : selectedColor // Eraser is white
                      ..strokeWidth = _isErasing ? strokeWidth + 10 : strokeWidth
                      ..strokeCap = StrokeCap.round,
                    isErasing: _isErasing,
                  ),
                );
                _saveBoards();
              });
            },
            onPanEnd: (details) {
              setState(() {
                currentBoard.add(null);
                _saveBoards();
              });
            },
            child: CustomPaint(
              painter: _WhiteboardPainter(currentBoard),
              size: Size.infinite,
            ),
          ),
        ),
      ),
    );
  }
}

@immutable
class DrawingPoint {
  final Offset offset;
  final Paint paint;
  final bool isErasing;

  DrawingPoint(this.offset, this.paint, {this.isErasing = false});

  // Serialization helper
  Map<String, dynamic> toJson() => {
        'dx': offset.dx,
        'dy': offset.dy,
        'color': paint.color.value,
        'strokeWidth': paint.strokeWidth,
        'isErasing': isErasing,
      };

  // Deserialization helper
  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      Offset((json['dx'] as num).toDouble(), (json['dy'] as num).toDouble()),
      Paint()
        ..color = Color(json['color'] as int)
        ..strokeWidth = (json['strokeWidth'] as num).toDouble()
        ..strokeCap = StrokeCap.round,
      isErasing: json['isErasing'] as bool? ?? false,
    );
  }
}

class _WhiteboardPainter extends CustomPainter {
  final List<DrawingPoint?> points;

  _WhiteboardPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    // White background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.white,
    );

    // Draw user strokes first
    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        canvas.drawLine(
            points[i]!.offset, points[i + 1]!.offset, points[i]!.paint);
      } else if (points[i] != null && points[i + 1] == null) {
        canvas.drawPoints(
          ui.PointMode.points,
          [points[i]!.offset],
          points[i]!.paint,
        );
      }
    }

    // Ruled lines are drawn on top
    final Paint linePaint = Paint()
      ..color = Colors.grey.shade300 // Silver color
      ..strokeWidth = 0.5;
    const double spacing = 30.0; // Increased spacing between lines
    for (double i = spacing; i < size.height; i += spacing) {
      canvas.drawLine(Offset(0, i), Offset(size.width, i), linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}