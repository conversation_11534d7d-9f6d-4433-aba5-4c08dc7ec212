import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';
import 'counseling_service_detail_page.dart';
import 'tertiary_health_page.dart';

class CounselingServicesPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const CounselingServicesPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<CounselingServicesPage> createState() => _CounselingServicesPageState();
}

class _CounselingServicesPageState extends State<CounselingServicesPage> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<CounselingService> _services = [];
  List<CounselingService> _filteredServices = [];
  TextEditingController _searchController = TextEditingController();
  String _selectedDepartment = 'All';
  List<String> _departments = ['All'];

  @override
  void initState() {
    super.initState();
    _fetchCounselingServices();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchCounselingServices() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_counselingservices';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('fullname', ascending: true);

      final List<CounselingService> services = List<Map<String, dynamic>>.from(response)
          .map((json) => CounselingService.fromJson(json))
          .toList();
      
      // Extract unique departments for filters
      final Set<String> departments = {'All'};
      for (var service in services) {
        if (service.department.isNotEmpty) {
          departments.add(service.department);
        }
      }

      setState(() {
        _services = services;
        _filteredServices = List.from(services);
        _departments = departments.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading counseling services: $e';
      });
      print('Error fetching counseling services: $e');
    }
  }

  void _filterServices() {
    final String searchQuery = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredServices = _services.where((service) {
        // Filter by department
        if (_selectedDepartment != 'All' && service.department != _selectedDepartment) {
          return false;
        }
        
        // Filter by search query
        if (searchQuery.isNotEmpty) {
          return service.fullname.toLowerCase().contains(searchQuery) ||
                 service.about.toLowerCase().contains(searchQuery) ||
                 service.department.toLowerCase().contains(searchQuery) ||
                 service.requirements.toLowerCase().contains(searchQuery) ||
                 service.price.toLowerCase().contains(searchQuery);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Counseling Services',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: _fetchCounselingServices,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search counseling services...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _filterServices();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    _filterServices();
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Department filter
                if (_departments.length > 1) ...[
                  Text(
                    'Department:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _departments.map((department) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(department),
                            selected: _selectedDepartment == department,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedDepartment = department;
                                  _filterServices();
                                });
                              }
                            },
                            backgroundColor: theme.colorScheme.surface,
                            selectedColor: Colors.white,
                            labelStyle: TextStyle(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : (currentIsDarkMode ? Colors.white : Colors.black),
                            ),
                            side: BorderSide(
                              color: _selectedDepartment == department
                                  ? Colors.black
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Services list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 48, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage,
                              style: TextStyle(color: theme.colorScheme.error),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchCounselingServices,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredServices.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.healing,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _services.isEmpty
                                      ? 'No counseling services available'
                                      : 'No counseling services match your filters',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredServices.length,
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            itemBuilder: (context, index) {
                              final service = _filteredServices[index];
                              final bool hasLink = service.link.isNotEmpty;
                              
                              return Card(
                                margin: const EdgeInsets.only(bottom: 12.0),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.all(16.0),
                                  leading: CircleAvatar(
                                    backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
                                    child: Icon(
                                      Icons.healing,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  title: Text(
                                    service.fullname,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 8),
                                      if (service.department.isNotEmpty)
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.business, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Department: ${service.department}'),
                                            ],
                                          ),
                                        ),
                                      if (service.price.isNotEmpty && service.price != 'Price not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.attach_money, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Price: ${service.price}'),
                                            ],
                                          ),
                                        ),
                                      if (service.time.isNotEmpty && service.time != 'Time not specified')
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 4.0),
                                          child: Row(
                                            children: [
                                              Icon(Icons.access_time, size: 16, color: theme.colorScheme.primary),
                                              const SizedBox(width: 4),
                                              Text('Time: ${service.time}'),
                                            ],
                                          ),
                                        ),
                                      if (service.about.isNotEmpty && service.about != 'No description available')
                                        Padding(
                                          padding: const EdgeInsets.only(top: 4.0),
                                          child: Text(
                                            service.about.length > 100
                                                ? '${service.about.substring(0, 100)}...'
                                                : service.about,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  trailing: hasLink
                                      ? ElevatedButton(
                                          onPressed: () => _launchUrl(service.link),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: theme.colorScheme.primary,
                                            foregroundColor: Colors.white,
                                          ),
                                          child: const Text('BOOK'),
                                        )
                                      : Icon(
                                          Icons.arrow_forward_ios,
                                          size: 16,
                                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                                        ),
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => CounselingServiceDetailPage(
                                          service: service,
                                          institutionName: widget.institutionName,
                                          isDarkMode: currentIsDarkMode,
                                          toggleTheme: widget.toggleTheme,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
