// list_page.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'login_page.dart';
import 'todo_page.dart';
import 'shopping_page.dart';
import 'wallet_page.dart';
import 'notes_page.dart';
import 'bible_page.dart'; // Import BiblePage

class ListPage extends StatelessWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ListPage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool currentIsDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Navigate to the specific app page
          if (title == 'Wallet') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => WalletPage(toggleTheme: toggleTheme, isDarkMode: currentIsDarkMode)),
            );
          } else if (title == 'Todo List') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => TodoApp(toggleTheme: toggleTheme, isDarkMode: currentIsDarkMode)),
            );
          } else if (title == 'Shopping List') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => ShoppingApp(toggleTheme: toggleTheme, isDarkMode: currentIsDarkMode)),
            );
          } else if (title == 'Notes') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => NotesPage(toggleTheme: toggleTheme, isDarkMode: currentIsDarkMode)),
            );
          } else if (title == 'Bible') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => BiblePage(toggleTheme: toggleTheme, isDarkMode: currentIsDarkMode)),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: currentIsDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Lists',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Wallet', Icons.wallet, theme),
                _buildGridItem(context, 'Todo List', Icons.list, theme),
                _buildGridItem(context, 'Shopping List', Icons.shopping_cart_outlined, theme),
                _buildGridItem(context, 'Notes', Icons.note, theme),
                _buildGridItem(context, 'Bible', Icons.book, theme), // Added Bible
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    // Action for Home button
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    // Use the local isDarkMode variable here
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          // And pass the local isDarkMode and toggleTheme
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}