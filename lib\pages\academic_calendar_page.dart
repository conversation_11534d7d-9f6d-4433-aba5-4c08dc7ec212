import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'login_page.dart';

class AcademicCalendarPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AcademicCalendarPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AcademicCalendarPage> createState() => _AcademicCalendarPageState();
}

class _AcademicCalendarPageState extends State<AcademicCalendarPage> {
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<dynamic>> _events;
  late List<dynamic> _selectedEvents;
  bool _isLoading = true;
  String _errorMessage = '';
  List<Map<String, dynamic>> _academicCalendarEvents = [];
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = [];
    _fetchAcademicCalendar();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchAcademicCalendar() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final tableName = '${widget.institutionName.toLowerCase().replaceAll(' ', '')}_academiccalendar';
      
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .order('startday', ascending: true);

      final academicEvents = List<Map<String, dynamic>>.from(response);
      
      // Convert academic calendar events to calendar events
      final Map<DateTime, List<dynamic>> calendarEvents = {};
      
      for (var event in academicEvents) {
        // Check if the event has start date
        if (event['startday'] != null && 
            event['startmonth'] != null && 
            event['startyear'] != null) {
          
          final startDate = DateTime(
            event['startyear'],
            event['startmonth'],
            event['startday'],
          );
          
          // For multi-day events, add to each day
          if (event['endday'] != null && 
              event['endmonth'] != null && 
              event['endyear'] != null) {
            
            final endDate = DateTime(
              event['endyear'],
              event['endmonth'],
              event['endday'],
            );
            
            // Add event to each day between start and end
            DateTime currentDate = startDate;
            while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
              final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
              if (calendarEvents[key] == null) {
                calendarEvents[key] = [];
              }
              calendarEvents[key]!.add(event);
              
              // Move to next day
              currentDate = currentDate.add(const Duration(days: 1));
            }
          } else {
            // Single day event
            final key = DateTime(startDate.year, startDate.month, startDate.day);
            if (calendarEvents[key] == null) {
              calendarEvents[key] = [];
            }
            calendarEvents[key]!.add(event);
          }
        }
      }

      setState(() {
        _academicCalendarEvents = academicEvents;
        _events = calendarEvents;
        _selectedEvents = _getEventsForDay(_selectedDay);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading academic calendar: $e';
      });
      print('Error fetching academic calendar: $e');
    }
  }

  List<dynamic> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents = _getEventsForDay(selectedDay);
    });
  }

  void _updateFilteredEvents() {
    if (_searchController.text.isEmpty) {
      // If search is empty, show all events
      _fetchAcademicCalendar();
      return;
    }
    
    final Map<DateTime, List<dynamic>> filteredEvents = {};
    final searchTerm = _searchController.text.toLowerCase();
    
    for (var event in _academicCalendarEvents) {
      // Apply search filter
      final name = event['fullname']?.toString().toLowerCase() ?? '';
      
      if (!name.contains(searchTerm)) {
        continue;
      }
      
      // Check if the event has start date
      if (event['startday'] != null && 
          event['startmonth'] != null && 
          event['startyear'] != null) {
        
        final startDate = DateTime(
          event['startyear'],
          event['startmonth'],
          event['startday'],
        );
        
        // For multi-day events, add to each day
        if (event['endday'] != null && 
            event['endmonth'] != null && 
            event['endyear'] != null) {
          
          final endDate = DateTime(
            event['endyear'],
            event['endmonth'],
            event['endday'],
          );
          
          // Add event to each day between start and end
          DateTime currentDate = startDate;
          while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
            final key = DateTime(currentDate.year, currentDate.month, currentDate.day);
            if (filteredEvents[key] == null) {
              filteredEvents[key] = [];
            }
            filteredEvents[key]!.add(event);
            
            // Move to next day
            currentDate = currentDate.add(const Duration(days: 1));
          }
        } else {
          // Single day event
          final key = DateTime(startDate.year, startDate.month, startDate.day);
          if (filteredEvents[key] == null) {
            filteredEvents[key] = [];
          }
          filteredEvents[key]!.add(event);
        }
      }
    }
    
    setState(() {
      _events = filteredEvents;
      _selectedEvents = _getEventsForDay(_selectedDay);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Academic Calendar',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: TextStyle(color: theme.colorScheme.error),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchAcademicCalendar,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search academic calendar...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _searchController.clear();
                                      _fetchAcademicCalendar();
                                    });
                                  },
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          _updateFilteredEvents();
                        },
                      ),
                    ),
                    TableCalendar(
                      firstDay: DateTime.utc(2020, 1, 1),
                      lastDay: DateTime.utc(2030, 12, 31),
                      focusedDay: _focusedDay,
                      calendarFormat: _calendarFormat,
                      eventLoader: _getEventsForDay,
                      selectedDayPredicate: (day) {
                        return isSameDay(_selectedDay, day);
                      },
                      onDaySelected: _onDaySelected,
                      onFormatChanged: (format) {
                        setState(() {
                          _calendarFormat = format;
                        });
                      },
                      onPageChanged: (focusedDay) {
                        _focusedDay = focusedDay;
                      },
                      calendarStyle: CalendarStyle(
                        markersMaxCount: 3,
                        markerDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        todayDecoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        selectedDecoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      headerStyle: HeaderStyle(
                        formatButtonVisible: true,
                        titleCentered: true,
                        formatButtonShowsNext: false,
                        formatButtonDecoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        formatButtonTextStyle: TextStyle(
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _selectedEvents.isEmpty
                          ? Center(
                              child: Text(
                                'No academic calendar events for this day',
                                style: TextStyle(color: theme.colorScheme.onSurface),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _selectedEvents.length,
                              itemBuilder: (context, index) {
                                final event = _selectedEvents[index];
                                
                                // Format date range
                                String dateRange = '';
                                if (event['startday'] != null && 
                                    event['startmonth'] != null && 
                                    event['startyear'] != null) {
                                  
                                  final startDate = DateTime(
                                    event['startyear'],
                                    event['startmonth'],
                                    event['startday'],
                                  );
                                  
                                  if (event['endday'] != null && 
                                      event['endmonth'] != null && 
                                      event['endyear'] != null) {
                                    
                                    final endDate = DateTime(
                                      event['endyear'],
                                      event['endmonth'],
                                      event['endday'],
                                    );
                                    
                                    if (startDate == endDate) {
                                      dateRange = DateFormat('MMM d, yyyy').format(startDate);
                                    } else {
                                      dateRange = '${DateFormat('MMM d').format(startDate)} - ${DateFormat('MMM d, yyyy').format(endDate)}';
                                    }
                                  } else {
                                    dateRange = DateFormat('MMM d, yyyy').format(startDate);
                                  }
                                }
                                
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 4.0,
                                  ),
                                  child: ListTile(
                                    leading: Icon(
                                      Icons.event_note,
                                      color: theme.colorScheme.primary,
                                    ),
                                    title: Text(
                                      event['fullname'] ?? 'Unnamed Event',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(dateRange),
                                        if (event['starttime'] != null && event['endtime'] != null)
                                          Text('Time: ${event['starttime']} - ${event['endtime']}'),
                                      ],
                                    ),
                                    isThreeLine: event['starttime'] != null && event['endtime'] != null,
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
