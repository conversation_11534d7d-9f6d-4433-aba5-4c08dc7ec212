import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'local_dining_detail_page.dart';
import 'login_page.dart';

class LocalDiningPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedLocalDining;
  final bool isFromDetailPage;

  const LocalDiningPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedLocalDining,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _LocalDiningPageState createState() => _LocalDiningPageState();
}

class _LocalDiningPageState extends State<LocalDiningPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('local_dining_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _localDining = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("LocalDiningPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedLocalDining != null &&
        widget.preloadedLocalDining!.isNotEmpty) {
      setState(() {
        _localDining = List.from(widget.preloadedLocalDining!);
        _isLoading = false;
      });
    } else {
      _loadLocalDiningFromDatabase();
    }
  }

  void _setupRealtime() {
    final localDiningTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localareadining';
    _realtimeChannel = Supabase.instance.client
        .channel('local_dining_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: localDiningTableName,
      callback: (payload) async {
        print("Realtime update received for local dining: ${payload.eventType}");
        _loadLocalDiningFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadLocalDiningFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _localDining = [];
    });

    await _loadMoreLocalDining();
  }

  Future<void> _loadMoreLocalDining() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final localDiningTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_localareadining';
      final response = await Supabase.instance.client
          .from(localDiningTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _localDining.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading local dining: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading local dining: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreLocalDining();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Local Dining',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('local_dining_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _localDining.isEmpty && !_isLoading) {
            _loadLocalDiningFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadLocalDiningFromDatabase,
          child: _localDining.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No local dining options found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _localDining.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _localDining.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildLocalDiningCard(
                      _localDining[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildLocalDiningCard(
    Map<String, dynamic> dining,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = dining['fullname'] ?? 'Unknown';
    final String hours = dining['hours'] ?? '';
    final String payment = dining['payment'] ?? '';
    final String imgLink = dining['img_link'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LocalDiningDetailPage(
                localDining: dining,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (imgLink.isNotEmpty)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: Image.network(
                  imgLink,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 150,
                      color: theme.colorScheme.surfaceVariant,
                      child: Center(
                        child: Icon(
                          Icons.local_dining,
                          size: 50,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (imgLink.isEmpty)
                    CircleAvatar(
                      radius: 24,
                      backgroundColor: isDarkMode 
                          ? Colors.white.withOpacity(0.1) 
                          : Colors.black.withOpacity(0.1),
                      child: Icon(
                        Icons.local_dining,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  if (imgLink.isEmpty)
                    const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fullname,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        if (hours.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              hours,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        if (payment.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'Payment: $payment',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
