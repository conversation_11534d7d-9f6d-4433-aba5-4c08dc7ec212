import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'emergency_equipment_detail_page.dart';

class EmergencyEquipmentPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>> preloadedEmergencyEquipment;
  final bool isFromDetailPage;
  final String? roomFilter;
  final String? buildingFilter;

  const EmergencyEquipmentPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.preloadedEmergencyEquipment,
    this.isFromDetailPage = false,
    this.roomFilter,
    this.buildingFilter,
  }) : super(key: key);

  @override
  State<EmergencyEquipmentPage> createState() => _EmergencyEquipmentPageState();
}

class _EmergencyEquipmentPageState extends State<EmergencyEquipmentPage> {
  List<Map<String, dynamic>> _emergencyEquipment = [];
  List<Map<String, dynamic>> _filteredEmergencyEquipment = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late final RealtimeChannel _emergencyEquipmentChannel;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _loadEmergencyEquipment();
    _setupRealtimeListener();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _emergencyEquipmentChannel.unsubscribe();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterEmergencyEquipment();
    });
  }

  void _filterEmergencyEquipment() {
    if (_searchQuery.isEmpty && widget.roomFilter == null && widget.buildingFilter == null) {
      _filteredEmergencyEquipment = List.from(_emergencyEquipment);
      return;
    }

    _filteredEmergencyEquipment = _emergencyEquipment.where((equipment) {
      // Apply room filter if provided
      if (widget.roomFilter != null && equipment['room'] != widget.roomFilter) {
        return false;
      }

      // Apply building filter if provided
      if (widget.buildingFilter != null && equipment['building'] != widget.buildingFilter) {
        return false;
      }

      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          equipment['fullname'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (equipment['building']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['room']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase()) ||
          (equipment['about']?.toString().toLowerCase() ?? '').contains(_searchQuery.toLowerCase());

      return matchesSearch;
    }).toList();
  }

  Future<void> _loadEmergencyEquipment() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // First, use preloaded data if available
      if (widget.preloadedEmergencyEquipment.isNotEmpty) {
        setState(() {
          _emergencyEquipment = widget.preloadedEmergencyEquipment;
          _filteredEmergencyEquipment = widget.preloadedEmergencyEquipment;
          _isLoading = false;
        });
        print('Using preloaded emergency equipment data for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterEmergencyEquipment();
        }
        // Still fetch in background to refresh cache
        _fetchEmergencyEquipmentFromSupabase();
        return;
      }

      // Try to load from cache
      final cachedData = await _loadFromCache();
      if (cachedData != null && cachedData.isNotEmpty) {
        setState(() {
          _emergencyEquipment = cachedData;
          _filteredEmergencyEquipment = cachedData;
          _isLoading = false;
        });
        print('Loaded emergency equipment from cache for ${widget.collegeNameForTable}');
        // Apply filters if provided
        if (widget.roomFilter != null || widget.buildingFilter != null) {
          _filterEmergencyEquipment();
        }
      }

      // Then fetch from Supabase
      await _fetchEmergencyEquipmentFromSupabase();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading emergency equipment: $e';
      });
      print('Error in _loadEmergencyEquipment: $e');
    }
  }

  Future<void> _fetchEmergencyEquipmentFromSupabase() async {
    try {
      final emergencyEquipmentTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_emergencyequipment';
      print('Fetching from table: $emergencyEquipmentTableName');

      // Create the query
      String query = '*';

      // Create filter conditions if needed
      Map<String, Object> filterConditions = {};
      if (widget.roomFilter != null) {
        filterConditions['room'] = widget.roomFilter!;
      }
      if (widget.buildingFilter != null) {
        filterConditions['building'] = widget.buildingFilter!;
      }

      // Fetch the data
      List<Map<String, dynamic>> response;

      if (filterConditions.isNotEmpty) {
        print('Applying filters: $filterConditions');
        response = await Supabase.instance.client
            .from(emergencyEquipmentTableName)
            .select(query)
            .match(filterConditions)
            .order('fullname', ascending: true);
      } else {
        response = await Supabase.instance.client
            .from(emergencyEquipmentTableName)
            .select(query)
            .order('fullname', ascending: true);
      }

      final emergencyEquipment = List<Map<String, dynamic>>.from(response);
      print('Fetched ${emergencyEquipment.length} emergency equipment items from Supabase');

      // Cache the data
      await _saveToCache(emergencyEquipment);

      if (mounted) {
        setState(() {
          _emergencyEquipment = emergencyEquipment;
          _filteredEmergencyEquipment = emergencyEquipment;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching emergency equipment from Supabase: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (_emergencyEquipment.isEmpty) {
            _hasError = true;
            _errorMessage = 'Error loading emergency equipment: $e';
          }
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'emergencyequipment_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Loading from cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> decoded = jsonDecode(cachedJson);
        print('Found ${decoded.length} emergency equipment items in cache');
        return decoded.cast<Map<String, dynamic>>();
      } else {
        print('No cached emergency equipment found');
      }
    } catch (e) {
      print('Error loading emergency equipment from cache: $e');
    }
    return null;
  }

  Future<void> _saveToCache(List<Map<String, dynamic>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'emergencyequipment_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Saving ${data.length} emergency equipment items to cache with key: $cacheKey');
      await prefs.setString(cacheKey, jsonEncode(data));
      print('Successfully saved emergency equipment to cache');
    } catch (e) {
      print('Error saving emergency equipment to cache: $e');
    }
  }

  void _setupRealtimeListener() {
    final emergencyEquipmentTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_emergencyequipment';
    print('Setting up realtime listener for table: $emergencyEquipmentTableName');
    _emergencyEquipmentChannel = Supabase.instance.client
        .channel('emergencyequipment_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: emergencyEquipmentTableName,
          callback: (payload) {
            print('Realtime update received for emergency equipment');
            _fetchEmergencyEquipmentFromSupabase();
          },
        )
        .subscribe();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    String title = 'Emergency Equipment';
    if (widget.roomFilter != null) {
      title = 'Emergency Equipment in ${widget.roomFilter}';
    } else if (widget.buildingFilter != null) {
      title = 'Emergency Equipment in ${widget.buildingFilter}';
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('emergencyequipment-visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _emergencyEquipment.isEmpty && !_isLoading) {
            _loadEmergencyEquipment();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search emergency equipment...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
                ),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _hasError
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadEmergencyEquipment,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredEmergencyEquipment.isEmpty
                          ? const Center(
                              child: Text('No emergency equipment found'),
                            )
                          : ListView.builder(
                              itemCount: _filteredEmergencyEquipment.length,
                              itemBuilder: (context, index) {
                                final equipment = _filteredEmergencyEquipment[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  color: theme.colorScheme.surface,
                                  surfaceTintColor: Colors.transparent,
                                  child: ListTile(
                                    title: Text(
                                      equipment['fullname'] ?? 'Unnamed Equipment',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text('Building: ${equipment['building'] ?? 'N/A'}'),
                                        Text('Room: ${equipment['room'] ?? 'N/A'}'),
                                      ],
                                    ),
                                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => EmergencyEquipmentDetailPage(
                                            equipment: equipment,
                                            collegeNameForTable: widget.collegeNameForTable,
                                            isDarkMode: currentIsDarkMode,
                                            toggleTheme: widget.toggleTheme,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
