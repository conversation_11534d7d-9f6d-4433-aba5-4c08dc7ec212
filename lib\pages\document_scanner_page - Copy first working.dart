import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mlkit_document_scanner/google_mlkit_document_scanner.dart';
import 'package:permission_handler/permission_handler.dart';

class DocumentScannerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DocumentScannerPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DocumentScannerPage> createState() => _DocumentScannerPageState();
}

class _DocumentScannerPageState extends State<DocumentScannerPage> {
  List<String> _scannedDocuments = [];
  bool _isScanning = false;

  // Instantiate DocumentScanner with the required options.
  final DocumentScanner _documentScanner =
      DocumentScanner(options: DocumentScannerOptions());

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();
  }

  @override
  void dispose() {
    _documentScanner.close();
    super.dispose();
  }

  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera permission is required')),
      );
    }
  }

  Future<void> _scanDocument() async {
    setState(() {
      _isScanning = true;
      _scannedDocuments.clear();
    });

    try {
      // scanDocument() now returns a DocumentScanningResult.
      final DocumentScanningResult result = await _documentScanner.scanDocument();

      // --- Debugging Prints ---
      print("Result runtimeType: ${result.runtimeType}");
      print("Result toString(): ${result.toString()}");
      // --- End Debugging Prints ---

      if (result != null) { // Just check if result is not null for now
        // We will access the correct property after inspecting the output
        // _scannedDocuments = result.???; // Replace ??? with the correct property
        print("Document Scanning Result is not null, but property access needs to be determined.");
      } else {
        print('No documents scanned or scan cancelled, or result is null.');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error scanning document: ${e.toString()}')),
      );
    } finally {
      setState(() => _isScanning = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Scanner'),
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: Icon(widget.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isScanning ? null : _scanDocument,
              child: Text(_isScanning ? 'Scanning...' : 'Scan Document'),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: _scannedDocuments.isEmpty
                  ? const Center(child: Text('No documents scanned yet'))
                  : ListView.builder(
                      itemCount: _scannedDocuments.length,
                      itemBuilder: (context, index) {
                        final path = _scannedDocuments[index];
                        return Card(
                          color: theme.colorScheme.surface,
                          surfaceTintColor: Colors.transparent,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.file(File(path)),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('Page ${index + 1}', style: TextStyle(color: theme.colorScheme.onSurface)),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}