import 'package:flutter/material.dart';
import 'login_page.dart';

class WellnessPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const WellnessPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<WellnessPage> createState() => _WellnessPageState();
}

class _WellnessPageState extends State<WellnessPage> with AutomaticKeepAliveClientMixin {
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _handleItemTap(String category) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryDetailPage(category: category),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final bool isDarkMode = widget.isDarkMode;
    final VoidCallback toggleTheme = widget.toggleTheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Wellness',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Anxiety', Icons.psychology, theme, _handleItemTap),
                _buildGridItem(context, 'Depression', Icons.sentiment_very_dissatisfied, theme, _handleItemTap),
                _buildGridItem(context, 'Parenting and Pregnancy', Icons.pregnant_woman, theme, _handleItemTap),
                _buildGridItem(context, 'Physical Wellness', Icons.fitness_center, theme, _handleItemTap),
                _buildGridItem(context, 'Stress', Icons.warning_amber, theme, _handleItemTap),
                _buildGridItem(context, 'Eating and Body Image', Icons.restaurant, theme, _handleItemTap),
                _buildGridItem(context, 'Sexual Health', Icons.favorite, theme, _handleItemTap),
                _buildGridItem(context, 'Academic Concerns', Icons.school, theme, _handleItemTap),
                _buildGridItem(context, 'Trauma', Icons.healing, theme, _handleItemTap),
                _buildGridItem(context, 'Alcohol', Icons.local_bar, theme, _handleItemTap),
                _buildGridItem(context, 'Cannabis', Icons.grass, theme, _handleItemTap),
                _buildGridItem(context, 'Sleep', Icons.bedtime, theme, _handleItemTap),
                _buildGridItem(context, 'Relationship Concerns', Icons.people, theme, _handleItemTap),
                _buildGridItem(context, 'Financial Wellness', Icons.account_balance_wallet, theme, _handleItemTap),
                _buildGridItem(context, 'Nutrition', Icons.restaurant_menu, theme, _handleItemTap),
                _buildGridItem(context, 'Breakups', Icons.heart_broken, theme, _handleItemTap),
                _buildGridItem(context, 'Grief and Loss', Icons.sentiment_neutral, theme, _handleItemTap),
                _buildGridItem(context, 'Other Drug Use', Icons.medication, theme, _handleItemTap),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: isDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, Function handleTap) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          handleTap(title);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CategoryDetailPage extends StatelessWidget {
  final String category;

  CategoryDetailPage({Key? key, required this.category}) : super(key: key);

  // Dummy data - replace with actual content or data fetching logic
  static final Map<String, Map<String, String>> _categoryDetails = {
    'Anxiety': {
      'What it is': 'Anxiety is a normal human emotion that everyone experiences at times. However, when anxiety becomes excessive, persistent, and interferes with daily life, it may be a sign of an anxiety disorder.',
      'How to identify it': 'Symptoms can include excessive worry, restlessness, fatigue, difficulty concentrating, irritability, muscle tension, and sleep problems.',
      'Causes': 'Can be caused by a combination of genetic, environmental, psychological, and developmental factors.',
      'Prevention': 'Stress management techniques, regular exercise, a healthy diet, and sufficient sleep can help prevent excessive anxiety.',
      'Remedies': 'Therapy (such as CBT), medication, relaxation techniques, and lifestyle changes can be effective treatments.',
    },
    'Depression': {
      'What it is': 'Depression is a common and serious mood disorder that causes a persistent feeling of sadness and loss of interest. It can affect how you feel, think, and behave and can lead to a variety of emotional and physical problems.',
      'How to identify it': 'Symptoms include persistent sadness, loss of interest or pleasure, changes in appetite or weight, sleep disturbances, fatigue, feelings of worthlessness, and difficulty concentrating.',
      'Causes': 'Can be caused by a combination of genetic, biological, environmental, and psychological factors.',
      'Prevention': 'Maintaining a healthy lifestyle, strong social connections, and early intervention for mental health concerns can be helpful.',
      'Remedies': 'Therapy, medication, and lifestyle adjustments are common and effective treatments.',
    },
    'Parenting and Pregnancy': {
      'What it is': 'This category addresses the unique challenges and joys related to parenting and pregnancy, including mental and emotional well-being during these times.',
      'How to identify it': 'Experiencing stress, anxiety, or depression related to pregnancy, childbirth, or raising children.',
      'Causes': 'Hormonal changes, lack of sleep, financial stress, relationship changes, and the demands of caring for a child.',
      'Prevention': 'Building a strong support network, practicing self-care, and seeking professional guidance when needed.',
      'Remedies': 'Support groups, therapy, and resources focused on parenting and maternal mental health.',
    },
    'Physical Wellness': {
      'What it is': 'Encompasses the physical health and well-being of an individual, including exercise, nutrition, and general health practices.',
      'How to identify it': 'Focusing on maintaining a healthy body through regular activity, balanced diet, and preventive healthcare.',
      'Causes': 'Lack of physical activity, poor diet, insufficient sleep, and not engaging in preventive healthcare.',
      'Prevention': 'Regular exercise, healthy eating habits, adequate sleep, and routine check-ups.',
      'Remedies': 'Incorporating physical activity into your routine, adopting a balanced diet, and seeking medical attention when needed.',
    },
    'Stress': {
      'What it is': 'Stress is a feeling of emotional or physical tension. It can come from any event or thought that makes you feel frustrated, angry, or nervous.',
      'How to identify it': 'Symptoms can include irritability, muscle tension, headaches, sleep problems, and difficulty concentrating.',
      'Causes': 'Work or academic pressures, financial problems, relationship issues, and major life changes.',
      'Prevention': 'Time management, relaxation techniques, exercise, and maintaining a healthy lifestyle.',
      'Remedies': 'Mindfulness practices, deep breathing exercises, physical activity, and seeking support from friends or professionals.',
    },
    'Eating and Body Image': {
      'What it is': 'This category addresses concerns related to eating habits, body image, and potential eating disorders.',
      'How to identify it': 'Preoccupation with weight, body shape, and food intake; restrictive eating; binge eating; or other unhealthy patterns.',
      'Causes': 'Social pressures, media portrayals, low self-esteem, and emotional issues.',
      'Prevention': 'Promoting positive body image, fostering healthy relationships with food, and seeking support for emotional issues.',
      'Remedies': 'Therapy, nutritional counseling, and support groups.',
    },
    'Sexual Health': {
      'What it is': 'Encompasses physical, emotional, mental, and social aspects of sexuality. It is more than just the absence of disease or infirmity.',
      'How to identify it': 'Understanding and practicing safe sexual behaviors, seeking information and care related to sexual health.',
      'Causes': 'Lack of education, social stigma, and limited access to resources.',
      'Prevention': 'Comprehensive sex education, practicing safe sex, and regular check-ups.',
      'Remedies': 'Seeking medical advice and treatment for any sexual health concerns.',
    },
    'Academic Concerns': {
      'What it is': 'Deals with the stress and challenges related to academic performance, deadlines, and expectations.',
      'How to identify it': 'Feeling overwhelmed by coursework, experiencing test anxiety, and struggling with time management.',
      'Causes': 'Heavy workloads, pressure to succeed, and lack of effective study skills.',
      'Prevention': 'Developing good study habits, time management techniques, and seeking academic support.',
      'Remedies': 'Tutoring, study groups, and stress management techniques.',
    },
    'Trauma': {
      'What it is': 'Trauma is an emotional response to a terrible event like an accident, sexual assault, or natural disaster. Immediately after the event, shock and denial are typical. Longer term reactions include unpredictable emotions, flashbacks, strained relationships, and even physical symptoms like headaches or nausea.',
      'How to identify it': 'Symptoms can include flashbacks, nightmares, severe anxiety, and difficulty concentrating.',
      'Causes': 'Experiencing or witnessing a distressing or life-threatening event.',
      'Prevention': 'Building resilience and coping skills, seeking support after a traumatic event.',
      'Remedies': 'Therapy (such as EMDR and CBT), support groups, and sometimes medication.',
    },
    'Alcohol': {
      'What it is': 'This category addresses concerns related to alcohol use, abuse, and dependence.',
      'How to identify it': 'Frequent or excessive alcohol consumption, difficulty controlling drinking, and negative consequences due to alcohol use.',
      'Causes': 'Social factors, stress, genetic predisposition, and mental health issues.',
      'Prevention': 'Making informed decisions about alcohol use, understanding the risks, and developing healthy coping mechanisms.',
      'Remedies': 'Therapy, support groups (like AA), and medical intervention if needed.',
    },
    'Cannabis': {
      'What it is': 'This category addresses concerns related to cannabis use, abuse, and dependence.',
      'How to identify it': 'Frequent or excessive cannabis consumption, difficulty controlling use, and negative consequences due to cannabis use.',
      'Causes': 'Social factors, stress, coping mechanisms, and potential for dependence.',
      'Prevention': 'Making informed decisions about cannabis use, understanding the risks, and developing healthy coping mechanisms.',
      'Remedies': 'Therapy, support groups, and medical intervention if needed.',
    },
    'Sleep': {
      'What it is': 'This category focuses on the importance of sleep for overall health and well-being, and addresses sleep disorders.',
      'How to identify it': 'Difficulty falling asleep, staying asleep, or feeling tired despite getting enough sleep.',
      'Causes': 'Stress, anxiety, poor sleep habits, and underlying medical conditions.',
      'Prevention': 'Establishing a regular sleep schedule, creating a relaxing bedtime routine, and ensuring a comfortable sleep environment.',
      'Remedies': 'Improving sleep hygiene, therapy, and sometimes medication.',
    },
    'Relationship Concerns': {
      'What it is': 'Addresses challenges and difficulties in interpersonal relationships, including romantic, familial, and platonic connections.',
      'How to identify it': 'Frequent arguments, communication problems, feelings of isolation or dissatisfaction in relationships.',
      'Causes': 'Poor communication, differing expectations, lack of trust, and external stressors.',
      'Prevention': 'Open and honest communication, setting healthy boundaries, and seeking mediation when needed.',
      'Remedies': 'Couples or family therapy, communication skills training, and individual therapy.',
    },
    'Financial Wellness': {
      'What it is': 'Concerns the state of one\'s financial health and the sense of security and freedom from financial stress.',
      'How to identify it': 'Managing finances effectively, feeling secure about financial future, and having resources to handle financial shocks.',
      'Causes': 'Poor budgeting, excessive debt, lack of financial planning, and unexpected expenses.',
      'Prevention': 'Creating a budget, saving regularly, and seeking financial literacy resources.',
      'Remedies': 'Financial counseling, debt management strategies, and building an emergency fund.',
    },
    'Nutrition': {
      'What it is': 'Focuses on the importance of a balanced and healthy diet for physical and mental well-being.',
      'How to identify it': 'Making informed food choices, understanding nutritional needs, and maintaining a healthy eating pattern.',
      'Causes': 'Lack of access to healthy foods, poor dietary habits, and misinformation about nutrition.',
      'Prevention': 'Educating oneself about nutrition, meal planning, and making conscious food choices.',
      'Remedies': 'Consulting a nutritionist or dietitian, adopting a balanced diet, and addressing any underlying eating disorders.',
    },
    'Breakups': {
      'What it is': 'Addresses the emotional distress and challenges associated with the ending of a significant relationship.',
      'How to identify it': 'Feelings of sadness, grief, anger, and confusion following a breakup.',
      'Causes': 'Relationship incompatibility, communication breakdown, and changing life circumstances.',
      'Prevention': 'Building strong and healthy relationships based on mutual respect and understanding.',
      'Remedies': 'Allowing yourself to grieve, seeking support from friends and family, and focusing on self-care.',
    },
    'Grief and Loss': {
      'What it is': 'The natural reaction to loss, whether it\'s the death of a loved one, the end of a relationship, or another significant loss.',
      'How to identify it': 'Experiencing a range of emotions such as sadness, anger, denial, and acceptance after a loss.',
      'Causes': 'The death of a loved one, loss of a relationship, loss of a job, or other significant life changes.',
      'Prevention': 'While loss is inevitable, building strong support systems can help in coping with grief.',
      'Remedies': 'Allowing yourself to grieve, seeking support from friends, family, or support groups, and sometimes therapy.',
    },
    'Other Drug Use': {
      'What it is': 'Addresses concerns related to the use, abuse, and dependence on substances other than alcohol and cannabis.',
      'How to identify it': 'Frequent or excessive use of other drugs, difficulty controlling use, and negative consequences due to drug use.',
      'Causes': 'Social factors, stress, coping mechanisms, and potential for dependence.',
      'Prevention': 'Making informed decisions about substance use, understanding the risks, and developing healthy coping mechanisms.',
      'Remedies': 'Therapy, support groups, and medical intervention if needed.',
    },
  };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final details = CategoryDetailPage._categoryDetails[category] ?? {};

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          category,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: details.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          entry.key,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          entry.value,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}