import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';

class LocalDiningDetailPage extends StatefulWidget {
  final Map<String, dynamic> localDining;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const LocalDiningDetailPage({
    Key? key,
    required this.localDining,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<LocalDiningDetailPage> createState() => _LocalDiningDetailPageState();
}

class _LocalDiningDetailPageState extends State<LocalDiningDetailPage> {
  late RealtimeChannel _localDiningRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _localDiningRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _localDiningRealtimeChannel = Supabase.instance.client
        .channel('local_dining_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'localareadining',
      callback: (payload) async {
        // Manual filtering for the specific local dining
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.localDining['id']) {
          print("Realtime update received for local dining detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshLocalDining();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshLocalDining() async {
    try {
      final response = await Supabase.instance.client
          .from('localareadining')
          .select('*')
          .eq('id', widget.localDining['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's localDining with the new data
          widget.localDining.clear();
          widget.localDining.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing local dining: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      print('Could not launch $emailUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  Future<void> _launchWhatsapp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$phoneNumber',
    );
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $whatsappUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.localDining['fullname'] ?? 'Unknown';
    final String hours = widget.localDining['hours'] ?? '';
    final String payment = widget.localDining['payment'] ?? '';
    final String about = widget.localDining['about'] ?? '';
    final String imgLink = widget.localDining['img_link'] ?? '';
    
    // Contact information
    final String phone = widget.localDining['phone']?.toString() ?? '';
    final String email = widget.localDining['email']?.toString() ?? '';
    final String whatsapp = widget.localDining['whatsapp']?.toString() ?? '';
    final dynamic latitude = widget.localDining['latitude'];
    final dynamic longitude = widget.localDining['longitude'];

    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isWhatsappAvailable = whatsapp.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image if available
            if (imgLink.isNotEmpty)
              CachedNetworkImage(
                imageUrl: imgLink,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  height: 200,
                  color: theme.colorScheme.surfaceVariant,
                  child: Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  height: 200,
                  color: theme.colorScheme.surfaceVariant,
                  child: Center(
                    child: Icon(
                      Icons.local_dining,
                      size: 50,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Local dining details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode 
                                    ? Colors.white.withOpacity(0.1) 
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  Icons.local_dining,
                                  size: 30,
                                  color: currentIsDarkMode ? Colors.white : Colors.black,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Hours and payment information
                          if (hours.isNotEmpty)
                            _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                          if (payment.isNotEmpty)
                            _buildDetailRow(theme, Icons.payment, 'Payment Methods', payment),
                          
                          // Contact information
                          if (phone.isNotEmpty)
                            _buildDetailRow(theme, Icons.phone, 'Phone', phone, canCopy: true),
                          if (email.isNotEmpty)
                            _buildDetailRow(theme, Icons.email, 'Email', email, canCopy: true),
                          if (whatsapp.isNotEmpty)
                            _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsapp, canCopy: true),
                          
                          // About section
                          if (about.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              'About:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              about,
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: (isPhoneAvailable || isEmailAvailable || isWhatsappAvailable || isNavigationAvailable)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isEmailAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.email,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchEmail(email),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                    if (isWhatsappAvailable)
                      IconButton(
                        icon: FaIcon(
                          FontAwesomeIcons.whatsapp,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchWhatsapp(whatsapp),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
