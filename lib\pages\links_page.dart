import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'links_detail_page.dart';
import 'login_page.dart';

class LinksPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedLinks;
  final bool isFromDetailPage;

  const LinksPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedLinks,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _LinksPageState createState() => _LinksPageState();
}

class _LinksPageState extends State<LinksPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('links_list');

  bool _isDisposed = false;
  List<Map<String, dynamic>> _links = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    if (widget.preloadedLinks != null &&
        widget.preloadedLinks!.isNotEmpty) {
      setState(() {
        _links = List<Map<String, dynamic>>.from(widget.preloadedLinks!);
        _page = 1;
        _hasMore = widget.preloadedLinks!.length == _pageSize;
      });
    } else {
      // no preloaded → load first page
    }
    await _loadLinksFromSupabase(initialLoad: true);
  }

  Future<void> _loadLinksFromSupabase({bool initialLoad = false}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) return;

    setState(() => _isLoading = true);
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';

    try {
      final response = await Supabase.instance.client
          .from(table)
          .select('*')
          .order('fullname', ascending: true)
          .range(initialLoad ? 0 : _page * _pageSize,
                 initialLoad
                    ? _pageSize - 1
                    : (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;
      final fetched = List<Map<String, dynamic>>.from(response);

      setState(() {
        if (initialLoad) {
          _links = fetched;
          _page = 1;
        } else {
          _links.addAll(fetched);
          _page++;
        }
        _hasMore = fetched.length == _pageSize;
      });

      if (initialLoad) _cacheLinks(_links);
    } catch (error) {
      if (!_isDisposed) _showError(error.toString());
    } finally {
      if (!_isDisposed) setState(() => _isLoading = false);
    }
  }

  Future<void> _cacheLinks(List<Map<String, dynamic>> links) async {
    final prefs = await SharedPreferences.getInstance();
    final key =
        'links_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    await prefs.setString(key, jsonEncode(links));
  }

  void _setupRealtime() {
    final table =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_links';
    _realtimeChannel = Supabase.instance.client
        .channel('links_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: table,
      callback: (payload) async {
        if (_isDisposed) return;
        await _refreshLinks();
      },
    ).subscribe();
  }

  Future<void> _refreshLinks() async {
    setState(() {
      _page = 0;
      _hasMore = true;
      _links.clear();
    });
    await _loadLinksFromSupabase(initialLoad: true);
  }

  void _onScroll() {
    if (!_isLoading &&
        _hasMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadLinksFromSupabase();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController
      ..removeListener(_onScroll)
      ..dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message),
               backgroundColor: Colors.redAccent),
    );
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(Map<String, dynamic> link) {
    if (_isDisposed) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => LinksDetailPage(
          link: link,
          isDarkMode: widget.isDarkMode,
          toggleTheme: widget.toggleTheme,
          collegeNameForTable: widget.collegeNameForTable,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Links to Resources',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _links.isEmpty
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshLinks,
              child: _links.isEmpty
                  ? ListView(
                      physics:
                          const AlwaysScrollableScrollPhysics(),
                      children: [
                        SizedBox(height: 200),
                        Center(
                          child: Text(
                            'No links available.',
                            style: TextStyle(
                                color: theme.colorScheme
                                    .onSurfaceVariant),
                          ),
                        ),
                      ],
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      physics:
                          const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount:
                          _links.length + (_hasMore ? 1 : 0),
                      itemBuilder: (ctx, idx) {
                        if (idx < _links.length) {
                          final link = _links[idx];
                          final name =
                              link['fullname'] as String? ??
                                  'Unnamed Link';
                          final desc =
                              link['about'] as String? ?? '';
                          return Card(
                            color: theme.colorScheme.surface,
                            surfaceTintColor:
                                Colors.transparent,
                            margin: const EdgeInsets.only(
                                bottom: 16),
                            child: ListTile(
                              contentPadding:
                                  const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor: isDark
                                    ? Colors.white
                                        .withOpacity(0.1)
                                    : Colors.black
                                        .withOpacity(0.1),
                                child: Icon(Icons.link,
                                    color: isDark
                                        ? Colors.white
                                        : Colors.black),
                              ),
                              title: Text(name,
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight:
                                          FontWeight.bold,
                                      color: theme
                                          .colorScheme
                                          .onSurface),
                                  maxLines: 2,
                                  overflow:
                                      TextOverflow.ellipsis),
                              subtitle: desc.isNotEmpty
                                  ? Padding(
                                      padding:
                                          const EdgeInsets
                                                  .only(
                                              top: 4),
                                      child: Text(desc,
                                          style: TextStyle(
                                              color: theme
                                                  .colorScheme
                                                  .onSurfaceVariant),
                                          maxLines: 1,
                                          overflow:
                                              TextOverflow
                                                  .ellipsis),
                                    )
                                  : null,
                              onTap: () => _navigateToDetail(link),
                            ),
                          );
                        }
                        // loading indicator at bottom
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment:
                  MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(Icons.home_outlined,
                      color: theme.colorScheme.onSurface),
                  onPressed: () =>
                      Navigator.of(context)
                          .popUntil((r) => r.isFirst),
                ),
                IconButton(
                  icon: Icon(
                    isDark
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                IconButton(
                  icon: Icon(Icons.person_outline,
                      color: theme.colorScheme.onSurface),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
