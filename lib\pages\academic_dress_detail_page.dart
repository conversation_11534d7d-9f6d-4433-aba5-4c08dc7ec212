import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AcademicDressDetailPage extends StatefulWidget {
  final Map<String, dynamic> dress;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AcademicDressDetailPage({
    Key? key,
    required this.dress,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<AcademicDressDetailPage> createState() => _AcademicDressDetailPageState();
}

class _AcademicDressDetailPageState extends State<AcademicDressDetailPage> {
  late RealtimeChannel _dressRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _dressRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    _dressRealtimeChannel = Supabase.instance.client
        .channel('academic_dress_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'academicdress',
      callback: (payload) async {
        // Manual filtering for the specific dress
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.dress['id']) {
          print("Realtime update received for academic dress detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshDress();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshDress() async {
    try {
      final response = await Supabase.instance.client
          .from('academicdress')
          .select('*')
          .eq('id', widget.dress['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's dress with the new data
          widget.dress.clear();
          widget.dress.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing academic dress: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String fullname = widget.dress['fullname'] ?? 'Unknown';
    final String category = widget.dress['category'] ?? '';
    final String style = widget.dress['style'] ?? '';
    final String color = widget.dress['color'] ?? '';

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Dress details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              Icons.school_outlined,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              fullname,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Basic information
                      if (category.isNotEmpty)
                        _buildDetailRow(theme, Icons.category, 'Category', category),
                      
                      if (style.isNotEmpty)
                        _buildDetailRow(theme, Icons.style, 'Style', style),
                      
                      if (color.isNotEmpty)
                        _buildDetailRow(theme, Icons.color_lens, 'Color', color),
                      
                      // Color preview
                      if (color.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Color Preview:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildColorPreview(color),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, String value) {
    if (value.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorPreview(String colorName) {
    // This is a simple implementation that tries to map common color names to actual colors
    // In a real app, you might want to use a more comprehensive color mapping
    Map<String, Color> colorMap = {
      'red': Colors.red,
      'blue': Colors.blue,
      'green': Colors.green,
      'yellow': Colors.yellow,
      'purple': Colors.purple,
      'pink': Colors.pink,
      'orange': Colors.orange,
      'brown': Colors.brown,
      'grey': Colors.grey,
      'black': Colors.black,
      'white': Colors.white,
      'gold': Color(0xFFFFD700),
      'silver': Color(0xFFC0C0C0),
      'crimson': Color(0xFFDC143C),
      'navy': Color(0xFF000080),
      'maroon': Color(0xFF800000),
    };
    
    // Try to find the color in our map (case insensitive)
    Color? previewColor;
    for (var entry in colorMap.entries) {
      if (colorName.toLowerCase().contains(entry.key.toLowerCase())) {
        previewColor = entry.value;
        break;
      }
    }
    
    // If we couldn't find a matching color, use a default
    previewColor ??= Colors.grey;
    
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        color: previewColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey,
          width: 1,
        ),
      ),
    );
  }
}
