import 'dart:io' as io; // Import for io.Platform, io.File
import 'package:flutter/material.dart';
import 'package:google_mlkit_document_scanner/google_mlkit_document_scanner.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // Import kIsWeb
import 'package:pdf/widgets.dart' as pw; // Import pdf library
import 'package:pdf/pdf.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';
import 'dart:typed_data';
import 'dart:ui' as ui; // Import for ui.Codec, ui.FrameInfo, ui.Image, etc.
import 'package:flutter/services.dart'; // Import for MethodChannel
import '../utils/text_recognition_helper.dart';
import 'package:image/image.dart' as img; // For image compression
import 'dart:math' as math; // For min function and other math operations

class DocumentScannerPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const DocumentScannerPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<DocumentScannerPage> createState() => _DocumentScannerPageState();
}

class _DocumentScannerPageState extends State<DocumentScannerPage> {
  List<String> _scannedDocuments = [];
  bool _isScanning = false;
  bool _isExporting = false;
  bool _isWeb = kIsWeb; // Determine if running on web
  double _exportProgress = 0.0;
  String _exportStatus = '';

  // For export cancellation
  List<bool> _cancelExportFlag = [false];

  // For time estimation
  DateTime? _exportStartTime;

  // Instantiate DocumentScanner only if not on web
  late final DocumentScanner _documentScanner;
  static const platform = MethodChannel('com.refactrai.studybuddy/media_scanner'); // MethodChannel

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();
    if (!_isWeb) { // Initialize DocumentScanner only on mobile
      _documentScanner = DocumentScanner(options: DocumentScannerOptions());
    }
  }

  @override
  void dispose() {
    if (!_isWeb) {
      _documentScanner.close();
    }
    super.dispose();
  }

  Future<void> _requestCameraPermission() async {
    if (_isWeb) return; // No camera permission needed on web (and won't work)
    final status = await Permission.camera.request();
    if (status != PermissionStatus.granted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Camera permission is required for document scanning.')),
      );
    }
  }

  Future<void> _scanDocument() async {
    if (_isWeb) {
      // Show a message if document scanning is attempted on web
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document scanning is not supported on web.')),
      );
      return; // Do not proceed with scanning on web
    }

    setState(() {
      _isScanning = true;
    });

    try {
      final DocumentScanningResult result = await _documentScanner.scanDocument();
      if (result.images.isNotEmpty) {
        setState(() {
          // Use addAll to append the new scanned images to the existing list
          _scannedDocuments.addAll(result.images);
        });
      } else {
        print('No document pages found in scan result.');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No documents found in scan.')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error scanning document: ${e.toString()}')),
      );
    } finally {
      setState(() => _isScanning = false);
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      final status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Storage permission is required to save PDF.')),
        );
        return false;
      }
    }
    return true;
  }

  Future<void> _scanFileForGallery(String filePath) async {
    try {
      await platform.invokeMethod('scanFile', {"filePath": filePath});
      print("File scanned successfully for gallery: $filePath");
    } on PlatformException catch (e) {
      print("Failed to scan file: '${e.message}'.");
    }
  }

  // Cancel the PDF export process
  void _cancelExport() {
    if (_isExporting) {
      setState(() {
        _exportStatus = 'Cancelling...';
      });
      _cancelExportFlag[0] = true;
    }
  }

  // Calculate estimated time remaining based on progress and elapsed time
  String _getEstimatedTimeRemaining() {
    if (_exportStartTime == null || _exportProgress <= 0.01) {
      return 'calculating...';
    }

    final elapsedSeconds = DateTime.now().difference(_exportStartTime!).inSeconds;
    if (elapsedSeconds < 2) {
      return 'calculating...';
    }

    // Calculate remaining time based on progress so far
    final totalEstimatedSeconds = (elapsedSeconds / _exportProgress).round();
    final remainingSeconds = totalEstimatedSeconds - elapsedSeconds;

    if (remainingSeconds < 60) {
      return '$remainingSeconds seconds';
    } else if (remainingSeconds < 3600) {
      final minutes = (remainingSeconds / 60).round();
      return '$minutes minutes';
    } else {
      final hours = (remainingSeconds / 3600).floor();
      final minutes = ((remainingSeconds % 3600) / 60).round();
      return '$hours hours, $minutes minutes';
    }
  }

  Future<void> _saveAsPdf() async {
    if (_isExporting) {
      return; // Prevent multiple export operations
    }

    // Reset cancellation flag
    _cancelExportFlag[0] = false;

    // Record start time for estimation
    _exportStartTime = DateTime.now();

    if (io.Platform.isAndroid || io.Platform.isIOS) {
      if (!await _requestStoragePermission()) {
        return;
      }
    }

    String? outputFile;
    try {
      outputFile = await FilePicker.platform.getDirectoryPath();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open file picker.')),
      );
      return;
    }

    if (outputFile == null) {
      // User cancelled the picker
      return;
    }

    setState(() {
      _isExporting = true;
      _exportProgress = 0.0;
      _exportStatus = 'Preparing PDF...';
    });

    final pdf = pw.Document(
      compress: true, // Enable PDF compression
      version: PdfVersion.pdf_1_5, // Use PDF 1.5 for better compression
    );

    if (_scannedDocuments.isNotEmpty) {
      final int totalPages = _scannedDocuments.length;
      final List<int> processedPages = [0]; // Use a list to allow modification in closures

      // Create a map to store processed images by page index
      final Map<int, pw.MemoryImage?> processedImages = {};

      // Maximum number of concurrent processing tasks
      final int maxConcurrentTasks = 3; // Adjust based on device capabilities

      // Create a queue of pages to process
      final List<int> pageQueue = List.generate(totalPages, (index) => index);

      // Function to update progress
      void updateProgress() {
        if (!mounted || _cancelExportFlag[0]) return;
        setState(() {
          _exportProgress = processedPages[0] / totalPages;
          _exportStatus = 'Processing page ${processedPages[0]} of $totalPages';
        });
      }

      // Process pages in batches
      try {
        while (pageQueue.isNotEmpty && !_cancelExportFlag[0]) {
          final List<Future<void>> tasks = [];
          final List<int> currentBatch = [];

          // Take up to maxConcurrentTasks pages from the queue
          final int batchSize = math.min(maxConcurrentTasks, pageQueue.length);
          for (int i = 0; i < batchSize; i++) {
            if (pageQueue.isNotEmpty) {
              currentBatch.add(pageQueue.removeAt(0));
            }
          }

          // Process the current batch in parallel
          for (final pageIndex in currentBatch) {
            final task = () async {
              if (_cancelExportFlag[0]) return;

              final String docPath = _scannedDocuments[pageIndex];

              // Update status with more specific information
              if (mounted && !_cancelExportFlag[0]) {
                setState(() {
                  _exportStatus = 'Processing page ${pageIndex + 1} of $totalPages';
                });
              }

              // Process the image with retry logic
              ByteData? imageByteData;
              int retryCount = 0;
              while (imageByteData == null && retryCount < 3 && !_cancelExportFlag[0]) {
                try {
                  imageByteData = await _captureScannedDocumentAsPdfImage(docPath);
                } catch (e) {
                  print('Error processing page ${pageIndex + 1}, retry ${retryCount + 1}: $e');
                  retryCount++;
                  // Short delay before retry
                  await Future.delayed(const Duration(milliseconds: 200));
                }
              }

              if (imageByteData != null && !_cancelExportFlag[0]) {
                final pdfImage = pw.MemoryImage(imageByteData.buffer.asUint8List());
                processedImages[pageIndex] = pdfImage;
              }

              // Update progress
              if (!_cancelExportFlag[0]) {
                processedPages[0]++;
                updateProgress();
              }
            }();

            tasks.add(task);
          }

          // Wait for all tasks in this batch to complete
          await Future.wait(tasks);
        }

        // If operation was cancelled, clean up and return
        if (_cancelExportFlag[0]) {
          setState(() {
            _isExporting = false;
            _exportStatus = 'PDF export cancelled';
          });
          return;
        }

        // Add all processed images to the PDF in the correct order
        if (mounted) {
          setState(() {
            _exportStatus = 'Creating PDF document...';
            _exportProgress = 0.9;
          });
        }

        // Add pages to PDF in original order
        for (int i = 0; i < totalPages; i++) {
          final pdfImage = processedImages[i];
          if (pdfImage != null) {
            pdf.addPage(pw.Page(
              pageFormat: PdfPageFormat.a4,
              build: (pw.Context context) {
                return pw.Center(
                  child: pw.Image(
                    pdfImage,
                    fit: pw.BoxFit.contain,
                  ),
                );
              },
            ));
          }
        }

        if (pdf.document.pdfPageList.pages.isNotEmpty) {
          try {
            if (mounted) {
              setState(() {
                _exportStatus = 'Compressing and saving PDF...';
                _exportProgress = 0.95;
              });
            }

            // Save the PDF with optimized compression
            final Stopwatch saveTimer = Stopwatch()..start();
            final pdfBytes = await pdf.save();
            final saveTime = saveTimer.elapsedMilliseconds;

            if (mounted) {
              setState(() {
                _exportStatus = 'Writing file to storage...';
                _exportProgress = 0.98;
              });
            }

            final fileName = 'scanned_documents_${DateTime.now().millisecondsSinceEpoch}.pdf';
            final filePath = path.join(outputFile, fileName);
            final file = io.File(filePath);
            await file.writeAsBytes(pdfBytes);

            // Calculate file size in KB
            final fileSize = (pdfBytes.length / 1024).toStringAsFixed(2);
            final pagesPerSecond = (totalPages / (saveTime / 1000)).toStringAsFixed(1);

            if (io.Platform.isAndroid) { // Scan file for gallery on Android
              await _scanFileForGallery(file.path);
            }

            if (mounted) {
              setState(() {
                _isExporting = false;
                _exportProgress = 1.0;
                _exportStatus = 'PDF saved successfully!';
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('PDF saved to ${path.basename(outputFile)} ($fileSize KB, $totalPages pages)'),
                  duration: const Duration(seconds: 5),
                ),
              );

              // Log performance metrics
              print('PDF export performance: $totalPages pages in ${saveTime}ms ($pagesPerSecond pages/sec), size: $fileSize KB');
            }
          } catch (e) {
            print("Error saving PDF: $e");
            if (mounted) {
              setState(() {
                _isExporting = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Failed to save PDF: ${e.toString()}')),
              );
            }
          }
        } else {
          if (mounted) {
            setState(() {
              _isExporting = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('No scanned documents to save as PDF.')),
            );
          }
        }
      } catch (e) {
        print("Error during PDF creation: $e");
        if (mounted) {
          setState(() {
            _isExporting = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error creating PDF: ${e.toString()}')),
          );
        }
      }
    } else {
      setState(() {
        _isExporting = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No scanned documents available.')),
      );
    }
  }

  Future<ByteData?> _captureScannedDocumentAsPdfImage(String imagePath) async {
    try {
      final io.File imageFile = io.File(imagePath);
      if (await imageFile.exists()) {
        // Read the image file
        final Uint8List originalBytes = await imageFile.readAsBytes();

        // Decode the image using the image package
        img.Image? decodedImage = img.decodeImage(originalBytes);
        if (decodedImage == null) {
          print('Failed to decode image at path: $imagePath');
          return null;
        }

        // Apply image enhancement for document clarity
        decodedImage = _enhanceDocumentImage(decodedImage);

        // Resize the image if it's too large (maintain aspect ratio)
        // Using slightly lower resolution for faster processing while maintaining readability
        int targetWidth = 1240; // A4 at 150 DPI
        int targetHeight = 1754; // A4 at 150 DPI

        if (decodedImage.width > targetWidth || decodedImage.height > targetHeight) {
          double aspectRatio = decodedImage.width / decodedImage.height;
          if (aspectRatio > targetWidth / targetHeight) {
            // Width is the limiting factor
            targetHeight = (targetWidth / aspectRatio).round();
          } else {
            // Height is the limiting factor
            targetWidth = (targetHeight * aspectRatio).round();
          }
          decodedImage = img.copyResize(
            decodedImage,
            width: targetWidth,
            height: targetHeight,
            interpolation: img.Interpolation.linear
          );
        }

        // Optimize compression based on content type
        // Use adaptive quality - higher for text documents, lower for photos
        int quality = _isTextDocument(decodedImage) ? 80 : 65;

        // Compress the image as JPG with optimized quality setting
        final Uint8List compressedBytes = img.encodeJpg(
          decodedImage,
          quality: quality
        );

        // Convert to ByteData for PDF
        final ui.Codec codec = await ui.instantiateImageCodec(compressedBytes);
        final ui.FrameInfo frame = await codec.getNextFrame();
        final ui.Image image = frame.image;
        final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);

        // Calculate compression ratio for logging
        final compressionRatio = originalBytes.length / compressedBytes.length;
        print('Image compressed: ${(originalBytes.length / 1024).toStringAsFixed(2)}KB → ${(compressedBytes.length / 1024).toStringAsFixed(2)}KB (${compressionRatio.toStringAsFixed(2)}x)');

        return byteData;
      } else {
        print('Image file does not exist at path: $imagePath');
        return null;
      }
    } catch (e) {
      print('Error capturing image as PDF: $e');
      return null;
    }
  }

  // Enhance document image for better clarity and smaller file size
  img.Image _enhanceDocumentImage(img.Image image) {
    // Make a copy to avoid modifying the original
    img.Image enhanced = img.copyResize(image, width: image.width, height: image.height);

    // Check if the image is already grayscale
    bool isGrayscale = true;
    for (int y = 0; y < image.height; y += 10) { // Sample every 10 pixels for speed
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);
        // Extract RGB components using the correct methods
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;
        if (r != g || g != b) {
          isGrayscale = false;
          break;
        }
      }
      if (!isGrayscale) break;
    }

    // Convert to grayscale if not already
    if (!isGrayscale) {
      enhanced = img.grayscale(enhanced);
    }

    // Apply adaptive contrast enhancement
    enhanced = img.contrast(enhanced, contrast: 1.2);

    // Apply noise reduction for cleaner compression
    // Use integer radius for gaussianBlur
    enhanced = img.gaussianBlur(enhanced, radius: 1);

    return enhanced;
  }

  // Detect if an image is primarily text/document vs photo
  bool _isTextDocument(img.Image image) {
    // Sample the image to determine if it's primarily text
    int textPixels = 0;
    int totalSamples = 0;

    // Sample grid points throughout the image
    for (int y = 0; y < image.height; y += 20) {
      for (int x = 0; x < image.width; x += 20) {
        totalSamples++;

        // Get pixel and neighbors to detect edges (text has many edges)
        final pixel = image.getPixel(x, y);
        // Calculate luminance from RGB components
        final luminance = (0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b).round();

        // Check neighbors if within bounds
        if (x + 5 < image.width && y + 5 < image.height) {
          final neighborPixel = image.getPixel(x + 5, y + 5);
          // Calculate neighbor luminance
          final neighborLuminance = (0.299 * neighborPixel.r + 0.587 * neighborPixel.g + 0.114 * neighborPixel.b).round();

          // If there's significant contrast between nearby pixels, likely text
          if (((luminance - neighborLuminance).abs()) > 30) {
            textPixels++;
          }
        }
      }
    }

    // If more than 15% of sampled points have high contrast, likely a text document
    return (textPixels / totalSamples) > 0.15;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Scanner'),
        backgroundColor: theme.colorScheme.surface,
        actions: [
          PopupMenuButton<String>(
            icon: Icon(Icons.download_outlined, color: theme.colorScheme.onSurface),
            onSelected: (value) {
              if (value == 'pdf') {
                _saveAsPdf();
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'pdf',
                child: Text('Download PDF'),
              ),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    foregroundColor: theme.brightness == Brightness.light ? Colors.black : Colors.white,
                  ),
                  onPressed: _isWeb || _isExporting ? null : (_isScanning ? null : _scanDocument),
                  child: Text(_isWeb
                    ? 'Not Supported on Web'
                    : (_isScanning
                        ? 'Scanning...'
                        : (_isExporting
                            ? 'Exporting...'
                            : 'Scan Document'))),
                ),
                const SizedBox(height: 20),
                if (_isWeb)
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Document scanning is not supported in web browsers. Please run this feature on an Android or iOS device.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                Expanded(
                  child: _scannedDocuments.isEmpty
                      ? const Center(child: Text('No documents scanned yet'))
                      : ListView.builder(
                          itemCount: _scannedDocuments.length,
                          itemBuilder: (context, index) {
                            final path = _scannedDocuments[index];
                            return Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.file(io.File(path)),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text('Page ${index + 1}',
                                          style: TextStyle(color: theme.colorScheme.onSurface)),
                                        Text('Tap image to view',
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                            fontSize: 12,
                                          )),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
          // Export progress overlay
          if (_isExporting)
            Container(
              color: Colors.black.withOpacity(0.7),
              child: Center(
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _exportProgress < 0.9
                                ? Icons.document_scanner_outlined
                                : Icons.picture_as_pdf_outlined,
                              color: theme.colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 10),
                            Flexible(
                              child: Text(
                                _exportStatus,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        SizedBox(
                          width: 280, // Fixed width for progress indicator
                          child: LinearProgressIndicator(
                            value: _exportProgress,
                            backgroundColor: theme.colorScheme.surfaceVariant,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.primary,
                            ),
                            minHeight: 10,
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          '${(_exportProgress * 100).toInt()}%',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // Add estimated time remaining if we have enough data
                        if (_exportProgress > 0.05 && _exportProgress < 0.95)
                          Text(
                            'Estimated time remaining: ${_getEstimatedTimeRemaining()}',
                            style: TextStyle(
                              fontSize: 12,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        const SizedBox(height: 15),
                        // Cancel button
                        if (_exportProgress < 0.9) // Only show cancel button during processing
                          ElevatedButton.icon(
                            onPressed: _cancelExport,
                            icon: const Icon(Icons.cancel_outlined),
                            label: const Text('Cancel'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.errorContainer,
                              foregroundColor: theme.colorScheme.onErrorContainer,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}