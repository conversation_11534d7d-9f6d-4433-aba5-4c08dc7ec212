// temperature_converter_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TemperatureConverterPage extends StatefulWidget {
  const TemperatureConverterPage({Key? key}) : super(key: key);

  @override
  _TemperatureConverterPageState createState() => _TemperatureConverterPageState();
}

class _TemperatureConverterPageState extends State<TemperatureConverterPage> {
  String _fromUnit = 'Celsius';
  String _toUnit = 'Fahrenheit';
  double _inputValue = 0.0;
  double _outputValue = 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Temperature Converter',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            color: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Value to Convert',
                      border: const OutlineInputBorder(),
                      labelStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurfaceVariant)),
                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: theme.colorScheme.onSurface)),
                    ),
                    style: TextStyle(color: theme.colorScheme.onSurface),
                    onChanged: (value) {
                      setState(() {
                        _inputValue = double.tryParse(value) ?? 0.0;
                      });
                    },
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      DropdownButton<String>(
                        value: _fromUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: <String>['Celsius', 'Fahrenheit', 'Kelvin'].map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _fromUnit = value!;
                          });
                        },
                      ),
                      Icon(Icons.arrow_forward, color: theme.colorScheme.onSurfaceVariant),
                      DropdownButton<String>(
                        value: _toUnit,
                        dropdownColor: theme.colorScheme.surface,
                        style: TextStyle(color: theme.colorScheme.onSurface),
                        items: <String>['Celsius', 'Fahrenheit', 'Kelvin'].map((String unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit, style: TextStyle(color: theme.colorScheme.onSurface)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _toUnit = value!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondaryContainer,
                      foregroundColor: theme.brightness == Brightness.light
                          ? Colors.white
                          : theme.colorScheme.onSecondaryContainer,
                    ),
                    onPressed: () {
                      _convertTemperature();
                    },
                    child: const Text('Convert'),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Result: $_outputValue $_toUnit',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _convertTemperature() {
    setState(() {
      if (_fromUnit == 'Celsius') {
        if (_toUnit == 'Fahrenheit') {
          _outputValue = (_inputValue * 9 / 5) + 32;
        } else if (_toUnit == 'Kelvin') {
          _outputValue = _inputValue + 273.15;
        } else {
          _outputValue = _inputValue; // Celsius to Celsius
        }
      } else if (_fromUnit == 'Fahrenheit') {
        if (_toUnit == 'Celsius') {
          _outputValue = (_inputValue - 32) * 5 / 9;
        } else if (_toUnit == 'Kelvin') {
          _outputValue = (_inputValue - 32) * 5 / 9 + 273.15;
        } else {
          _outputValue = _inputValue; // Fahrenheit to Fahrenheit
        }
      } else if (_fromUnit == 'Kelvin') {
        if (_toUnit == 'Celsius') {
          _outputValue = _inputValue - 273.15;
        } else if (_toUnit == 'Fahrenheit') {
          _outputValue = (_inputValue - 273.15) * 9 / 5 + 32;
        } else {
          _outputValue = _inputValue; // Kelvin to Kelvin
        }
      }
    });
  }
}