import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'sustainability_detail_page.dart';
import 'login_page.dart';

class SustainabilityPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedSustainabilityInitiatives;
  final bool isFromDetailPage;

  const SustainabilityPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedSustainabilityInitiatives,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _SustainabilityPageState createState() => _SustainabilityPageState();
}

class _SustainabilityPageState extends State<SustainabilityPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('sustainability_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _sustainabilityInitiatives = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("SustainabilityPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedSustainabilityInitiatives != null &&
        widget.preloadedSustainabilityInitiatives!.isNotEmpty) {
      setState(() {
        _sustainabilityInitiatives = List.from(widget.preloadedSustainabilityInitiatives!);
        _isLoading = false;
      });
    } else {
      _loadSustainabilityInitiativesFromDatabase();
    }
  }

  void _setupRealtime() {
    final sustainabilityTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_sustainability';
    _realtimeChannel = Supabase.instance.client
        .channel('sustainability_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: sustainabilityTableName,
      callback: (payload) async {
        print("Realtime update received for sustainability initiatives: ${payload.eventType}");
        _loadSustainabilityInitiativesFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadSustainabilityInitiativesFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _sustainabilityInitiatives = [];
    });

    await _loadMoreSustainabilityInitiatives();
  }

  Future<void> _loadMoreSustainabilityInitiatives() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final sustainabilityTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_sustainability';
      final response = await Supabase.instance.client
          .from(sustainabilityTableName)
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _sustainabilityInitiatives.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading sustainability initiatives: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading sustainability initiatives: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreSustainabilityInitiatives();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Sustainability Initiatives',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: VisibilityDetector(
        key: const Key('sustainability_list_visibility'),
        onVisibilityChanged: (info) {
          if (info.visibleFraction > 0 && _sustainabilityInitiatives.isEmpty && !_isLoading) {
            _loadSustainabilityInitiativesFromDatabase();
          }
        },
        child: RefreshIndicator(
          onRefresh: _loadSustainabilityInitiativesFromDatabase,
          child: _sustainabilityInitiatives.isEmpty && !_isLoading
              ? Center(
                  child: Text(
                    'No sustainability initiatives found',
                    style: TextStyle(color: theme.colorScheme.onSurface),
                  ),
                )
              : ListView.builder(
                  key: _listKey,
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _sustainabilityInitiatives.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _sustainabilityInitiatives.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildSustainabilityCard(
                      _sustainabilityInitiatives[index],
                      theme,
                      currentIsDarkMode,
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildSustainabilityCard(
    Map<String, dynamic> initiative,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = initiative['fullname'] ?? 'Unknown';
    final String about = initiative['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SustainabilityDetailPage(
                sustainabilityInitiative: initiative,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.eco,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
