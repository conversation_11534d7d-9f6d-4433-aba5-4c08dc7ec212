import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';

class HelpdeskPeopleDetailPage extends StatelessWidget {
  final String personName;
  final String personTitle;
  final String detailPageName;
  final String helpdeskName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> person;

  const HelpdeskPeopleDetailPage({
    Key? key,
    required this.personName,
    required this.personTitle,
    required this.detailPageName,
    required this.helpdeskName,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.person,
  }) : super(key: key);

  Future<void> _launchEmail() async {
    final email = person['email'] as String? ?? '';
    if (email.isNotEmpty) {
      final Uri emailUri = Uri(
        scheme: 'mailto',
        path: email,
      );
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        print('Could not launch email: $email');
      }
    }
  }

  Future<void> _launchPhone() async {
    String phone = person['phone'] as String? ?? '';
    if (phone.isEmpty) return;

    // Clean up the phone number
    phone = phone.replaceAll(RegExp(r'[^\d+]'), '');

    final Uri phoneUri = Uri(
      scheme: 'tel',
      path: phone,
    );
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      print('Could not launch phone: $phone');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          personName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Person image container - always display
            Container(
              height: 200,
              color: theme.colorScheme.surfaceVariant,
              child: Center(
                child: CircleAvatar(
                  radius: 60,
                  backgroundColor: currentIsDarkMode
                      ? Colors.white.withOpacity(0.1)
                      : Colors.black.withOpacity(0.1),
                  child: Icon(
                    Icons.person,
                    size: 60,
                    color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Person details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  Icons.person,
                                  size: 30,
                                  color: currentIsDarkMode ? Colors.white : Colors.black,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      personName,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    if (personTitle.isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          personTitle,
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Department
                          if (person['department'] != null && person['department'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.business, 'Department', person['department'], currentIsDarkMode, canCopy: false),

                          // School
                          if (person['school'] != null && person['school'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.school, 'School', person['school'], currentIsDarkMode, canCopy: false),

                          // School2
                          if (person['school2'] != null && person['school2'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.school, 'School 2', person['school2'], currentIsDarkMode, canCopy: false),

                          // School3
                          if (person['school3'] != null && person['school3'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.school, 'School 3', person['school3'], currentIsDarkMode, canCopy: false),

                          // Building
                          if (person['building'] != null && person['building'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.domain, 'Building', person['building'], currentIsDarkMode, canCopy: false),

                          // Room
                          if (person['room'] != null && person['room'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.meeting_room, 'Room', person['room'], currentIsDarkMode, canCopy: false),

                          // Office Hours
                          if (person['officehours'] != null && person['officehours'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.access_time, 'Office Hours', person['officehours'], currentIsDarkMode, canCopy: false),

                          // Contact information with copy buttons
                          if (person['phone'] != null && person['phone'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.phone, 'Phone', person['phone'], currentIsDarkMode, canCopy: true),

                          // Extension
                          if (person['ext'] != null && person['ext'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.phone_forwarded, 'Extension', person['ext'], currentIsDarkMode, canCopy: true),

                          // Email
                          if (person['email'] != null && person['email'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.email, 'Email', person['email'], currentIsDarkMode, canCopy: true),

                          // Fax
                          if (person['fax'] != null && person['fax'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.fax, 'Fax', person['fax'], currentIsDarkMode, canCopy: true),

                          // About section
                          if (person['about'] != null && person['about'].toString().isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              'About:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              person['about'].toString(),
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: (person['phone'] != null && person['phone'].toString().isNotEmpty)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.call,
                        color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                      ),
                      onPressed: _launchPhone,
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(BuildContext context, ThemeData theme, IconData icon, String title, dynamic value, bool isDarkMode, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: isDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: isDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}