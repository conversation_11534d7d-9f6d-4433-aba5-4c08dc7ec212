// research_project_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class ResearchProjectDetailPage extends StatefulWidget {
  final Map<String, dynamic> researchProject;
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ResearchProjectDetailPage({
    Key? key,
    required this.researchProject,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ResearchProjectDetailPage> createState() => _ResearchProjectDetailPageState();
}

class _ResearchProjectDetailPageState extends State<ResearchProjectDetailPage> {
  late RealtimeChannel _researchProjectRealtimeChannel; // Realtime channel for research project updates

  @override
  void initState() {
    super.initState();
    _setupResearchProjectRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _researchProjectRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _setupResearchProjectRealtimeListener() {
    final researchProjectsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
    print('Setting up realtime listener for research project detail: $researchProjectsTableName');
    _researchProjectRealtimeChannel = Supabase.instance.client
        .channel('researchproject_detail_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: researchProjectsTableName,
          callback: (payload) {
            // Check if this change is for the current research project
            if (payload.newRecord != null &&
                payload.newRecord!['id'] == widget.researchProject['id']) {
              print('Received update for current research project: ${widget.researchProject['fullname']}');
              if (payload.eventType == PostgresChangeEvent.update) {
                _fetchUpdatedResearchProjectData();
              } else if (payload.eventType == PostgresChangeEvent.delete) {
                // If the research project is deleted, go back to the previous screen
                if (mounted) {
                  Navigator.pop(context);
                }
              }
            }
          },
        )
        .subscribe();
  }

  Future<void> _fetchUpdatedResearchProjectData() async {
    try {
      final researchProjectsTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_researchprojects';
      print('Fetching updated data from table: $researchProjectsTableName for project ID: ${widget.researchProject['id']}');

      final response = await Supabase.instance.client
          .from(researchProjectsTableName)
          .select('*')
          .eq('id', widget.researchProject['id'])
          .single();

      if (mounted && response != null) {
        Map<String, dynamic> updatedResearchProject = Map.from(response);
        print('Successfully fetched updated research project data');

        // Update the widget.researchProject with the new data
        setState(() {
          widget.researchProject.clear(); // Clear old data
          widget.researchProject.addAll(updatedResearchProject); // Add updated data
          print("Research project data updated in detail page for ${widget.researchProject['fullname']}");
          _updateResearchProjectsCache(updatedResearchProject); // Update cache
        });
      }
    } catch (e) {
      print('Error fetching updated research project data: $e');
    }
  }

  Future<void> _updateResearchProjectsCache(Map<String, dynamic> updatedResearchProject) async {
    try {
      // Get the current cache
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'researchprojects_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
      print('Updating cache with key: $cacheKey');
      final cachedJson = prefs.getString(cacheKey);

      if (cachedJson != null) {
        final List<dynamic> cachedResearchProjects = jsonDecode(cachedJson);
        final List<Map<String, dynamic>> updatedCache = [];

        // Update the specific research project in the cache
        bool found = false;
        for (var project in cachedResearchProjects) {
          if (project['id'] == updatedResearchProject['id']) {
            updatedCache.add(updatedResearchProject);
            found = true;
            print('Updated research project in cache: ${updatedResearchProject['fullname']}');
          } else {
            updatedCache.add(Map<String, dynamic>.from(project));
          }
        }

        if (!found) {
          print('Research project not found in cache, adding it');
          updatedCache.add(updatedResearchProject);
        }

        // Save the updated cache
        await prefs.setString(cacheKey, jsonEncode(updatedCache));
        print('Successfully updated research projects cache');
      } else {
        // If cache doesn't exist, create it with just this project
        print('Cache not found, creating new cache with this research project');
        await prefs.setString(cacheKey, jsonEncode([updatedResearchProject]));
      }
    } catch (e) {
      print('Error updating research projects cache: $e');
    }
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    // Add https:// if not present
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    final String title = widget.researchProject['fullname'] as String? ?? 'Research Project';
    final String year = widget.researchProject['year']?.toString() ?? 'N/A';
    final String researcher = widget.researchProject['researcher'] as String? ?? '';
    final String researcher2 = widget.researchProject['researcher2'] as String? ?? '';
    final String researcher3 = widget.researchProject['researcher3'] as String? ?? '';
    final String department = widget.researchProject['department'] as String? ?? '';
    final bool facultyOrStaffResearch = widget.researchProject['facultyorstaffresearch'] as bool? ?? false;
    final bool studentResearch = widget.researchProject['studentresearch'] as bool? ?? false;
    final String about = widget.researchProject['about'] as String? ?? '';
    final String link = widget.researchProject['link'] as String? ?? '';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Research Project Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Year: $year',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Researchers:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (researcher.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            researcher,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (researcher2.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            researcher2,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      if (researcher3.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            researcher3,
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        'Department: $department',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        children: [
                          if (facultyOrStaffResearch)
                            Chip(
                              label: const Text('Faculty/Staff Research'),
                              backgroundColor: theme.colorScheme.primaryContainer,
                            ),
                          if (studentResearch)
                            Chip(
                              label: const Text('Student Research'),
                              backgroundColor: theme.colorScheme.secondaryContainer,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (about.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'About',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (link.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Link',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                link,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.colorScheme.primary,
                                  decoration: TextDecoration.underline,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _copyToClipboard(link),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () => _launchURL(link),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
