import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class VisualMindmap extends StatefulWidget {
  final String jsonData;
  final bool isDarkMode;
  final double fontSize;

  const VisualMindmap({
    Key? key,
    required this.jsonData,
    required this.isDarkMode,
    this.fontSize = 14.0,
  }) : super(key: key);

  @override
  State<VisualMindmap> createState() => _VisualMindmapState();
}

class _VisualMindmapState extends State<VisualMindmap> {
  late Map<String, dynamic> _mindmapData;
  bool _isValidJson = true;

  @override
  void initState() {
    super.initState();
    _parseMindmapData();
  }

  @override
  void didUpdateWidget(VisualMindmap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.jsonData != widget.jsonData) {
      _parseMindmapData();
    }
  }

  void _parseMindmapData() {
    try {
      // Extract JSON from the markdown code block
      final jsonRegex = RegExp(r'```json\s*([\s\S]*?)\s*```');
      final match = jsonRegex.firstMatch(widget.jsonData);

      if (match != null && match.group(1) != null) {
        final jsonString = match.group(1)!;
        _mindmapData = json.decode(jsonString) as Map<String, dynamic>;
        _isValidJson = true;
      } else {
        // Try parsing the entire string as JSON
        try {
          _mindmapData = json.decode(widget.jsonData) as Map<String, dynamic>;
          _isValidJson = true;
        } catch (e) {
          print('Failed to parse JSON: $e');
          _isValidJson = false;
          _mindmapData = {};
        }
      }
    } catch (e) {
      print('Error parsing mindmap data: $e');
      _isValidJson = false;
      _mindmapData = {};
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isValidJson || _mindmapData.isEmpty) {
      return Center(
        child: Text(
          'Invalid mindmap data format',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontSize: widget.fontSize,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxWidth * 0.8, // Aspect ratio for the mindmap
          child: _buildMindmap(constraints.maxWidth),
        );
      },
    );
  }

  Widget _buildMindmap(double availableWidth) {
    final centralTopic = _mindmapData['central_topic'] as String? ?? 'Main Topic';
    final branches = _mindmapData['branches'] as List<dynamic>? ?? [];

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Center(
            child: Column(
              children: [
                // Central topic
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: widget.isDarkMode ? Colors.blueGrey[800] : Colors.blue[100],
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Text(
                    centralTopic,
                    style: GoogleFonts.notoSans(
                      fontSize: widget.fontSize * 1.5,
                      fontWeight: FontWeight.bold,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // Primary branches
                ...branches.map<Widget>((branch) {
                  final branchName = branch['name'] as String? ?? 'Branch';
                  final branchColor = _parseColor(branch['color'] as String? ?? '#4287f5');
                  final subBranches = branch['sub_branches'] as List<dynamic>? ?? [];

                  return _buildBranch(
                    branchName,
                    branchColor,
                    subBranches,
                    availableWidth * 0.8,
                    1, // Level 1 for primary branches
                  );
                }).toList(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBranch(
    String name,
    Color color,
    List<dynamic> subBranches,
    double width,
    int level,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 30.0 * level,
                height: 2,
                color: color.withOpacity(0.7),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(widget.isDarkMode ? 0.3 : 0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: color.withOpacity(0.7),
                      width: 2,
                    ),
                  ),
                  child: Text(
                    name,
                    style: GoogleFonts.notoSans(
                      fontSize: widget.fontSize * (1.2 - (level * 0.1)),
                      fontWeight: level == 1 ? FontWeight.bold : FontWeight.normal,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (subBranches.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(left: 40.0 * level),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: subBranches.map<Widget>((subBranch) {
                  final subBranchName = subBranch['name'] as String? ?? 'Sub-branch';
                  final subBranchColor = _parseColor(subBranch['color'] as String? ?? color.toHex());
                  final nextLevelBranches = subBranch['sub_branches'] as List<dynamic>? ?? [];

                  return _buildBranch(
                    subBranchName,
                    subBranchColor,
                    nextLevelBranches,
                    width * 0.8,
                    level + 1,
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      String hexColor = colorString.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      } else if (hexColor.length == 3) {
        hexColor = 'FF${hexColor[0]}${hexColor[0]}${hexColor[1]}${hexColor[1]}${hexColor[2]}${hexColor[2]}';
      }
      return Color(int.parse(hexColor, radix: 16));
    }
    return Colors.blue;
  }
}

// Extension to convert Color to hex string
extension ColorExtension on Color {
  String toHex() {
    return '#${(value & 0xFFFFFF).toRadixString(16).padLeft(6, '0')}';
  }
}
