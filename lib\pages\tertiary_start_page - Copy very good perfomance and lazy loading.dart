import 'package:flutter/material.dart';
import 'login_page.dart'; // Corrected import path for login_page.dart
import 'helpdesks_page.dart'; // Import the HelpdesksPage
import '../main.dart'; // Import main.dart for MyApp.preloadedHelpdesks (if needed later)

class TertiaryStartPage extends StatelessWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedHelpdesks; // Accept preloaded helpdesks

  const TertiaryStartPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedHelpdesks, // Make it optional
  }) : super(key: key);

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (title == 'Helpdesks') {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => HelpdesksPage(
                  collegeNameForTable: institutionName,
                  isDarkMode: isDarkMode,
                  toggleTheme: toggleTheme,
                  preloadedHelpdesks: preloadedHelpdesks, // Pass preloaded helpdesks here - Parameter is now correct
                ),
              ),
            );
          } else if (title == 'Accessibility') {
            // Navigate to Accessibility Page or perform action
          } else if (title == 'FAQs') {
            // Navigate to FAQs Page or perform action
          } else if (title == 'Links to Resources') {
            // Navigate to Links to Resources Page or perform action
          } else if (title == 'Construction & Maintenance') {
            // Navigate to Construction & Maintenance Page or perform action
          } else if (title == 'Printing') {
            // Navigate to Printing Page or perform action
          } else if (title == 'Daycares') {
            // Navigate to Daycares Page or perform action
          } else if (title == 'Sustainability') {
            // Navigate to Sustainability Page or perform action
          }
          // Add navigation for other grid items here if needed
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Start',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Helpdesks', Icons.help_center, theme),
                _buildGridItem(context, 'Accessibility', Icons.accessible, theme),
                _buildGridItem(context, 'FAQs', Icons.question_answer, theme),
                _buildGridItem(context, 'Links to Resources', Icons.link, theme),
                _buildGridItem(context, 'Construction & Maintenance', Icons.construction, theme),
                _buildGridItem(context, 'Printing', Icons.print, theme),
                _buildGridItem(context, 'Daycares', Icons.child_care, theme),
                _buildGridItem(context, 'Sustainability', Icons.eco, theme),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
         color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24), // Added space
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push( // Corrected LoginPage Navigation
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage( // Correctly instantiates LoginPage
                          isDarkMode: isDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}