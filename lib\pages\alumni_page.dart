// alumni_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'login_page.dart';
import 'alumni_events_page.dart';
import 'alumni_signature_events_page.dart';
import 'alumni_faq_page.dart';
import 'alumni_links_page.dart';
import 'alumni_departments_page.dart';

class AlumniPage extends StatefulWidget {
  final Map<String, dynamic> collegeData;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const AlumniPage({
    Key? key,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  _AlumniPageState createState() => _AlumniPageState();
}

class _AlumniPageState extends State<AlumniPage> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme) {
    final bool isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      child: InkWell(
        onTap: () {
          _navigateToPage(context, title);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 40,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String title) {
    final collegeNameForTable = widget.collegeData['fullname'];
    
    switch (title) {
      case 'Events':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniEventsPage(
              collegeData: widget.collegeData,
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
        break;
      case 'Signature Events':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniSignatureEventsPage(
              collegeData: widget.collegeData,
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
        break;
      case 'FAQs':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniFaqPage(
              collegeData: widget.collegeData,
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
        break;
      case 'Links':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniLinksPage(
              collegeData: widget.collegeData,
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
        break;
      case 'Departments':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniDepartmentsPage(
              collegeData: widget.collegeData,
              isDarkMode: widget.isDarkMode,
              toggleTheme: widget.toggleTheme,
            ),
          ),
        );
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Alumni',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Alumni Resources',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Explore alumni events, resources, and connections for ${widget.collegeData['fullname']}',
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 24),
                    LayoutBuilder(
                      builder: (context, constraints) {
                        int crossAxisCount = 2;
                        
                        if (constraints.maxWidth > 1200) {
                          crossAxisCount = 5;
                        } else if (constraints.maxWidth > 800) {
                          crossAxisCount = 3;
                        } else if (constraints.maxWidth > 600) {
                          crossAxisCount = 2;
                        }
                        
                        return GridView.count(
                          crossAxisCount: crossAxisCount,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          children: [
                            _buildGridItem(context, 'Events', Icons.event, theme),
                            _buildGridItem(context, 'Signature Events', Icons.event_available, theme),
                            _buildGridItem(context, 'FAQs', Icons.question_answer_outlined, theme),
                            _buildGridItem(context, 'Links', Icons.link, theme),
                            _buildGridItem(context, 'Departments', Icons.business, theme),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
