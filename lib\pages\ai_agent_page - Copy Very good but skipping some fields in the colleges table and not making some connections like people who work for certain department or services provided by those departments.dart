import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async'; // For TimeoutException
import 'dart:io';    // For SocketException
import 'package:collection/collection.dart'; // For firstWhereOrNull, sortedBy

// --- Helper Classes (Moved Outside State Class) ---
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isTyping; // Now used for multi-stage indicator
  ChatMessage({required this.text, required this.isUser, this.isTyping = false});

  @override bool operator ==(Object other) => identical(this, other) || other is ChatMessage && runtimeType == other.runtimeType && text == other.text && isUser == other.isUser && isTyping == other.isTyping;
  @override int get hashCode => text.hashCode ^ isUser.hashCode ^ isTyping.hashCode;
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;
  final bool isDarkMode;
  final ThemeData theme;
  // Corrected Constructor
  const ChatBubble({
    Key? key,
    required this.message,
    required this.isDarkMode,
    required this.theme,
  }) : super(key: key);

  @override Widget build(BuildContext context) {
    if (message.isTyping) {
       return Align( alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
            decoration: BoxDecoration( color: theme.colorScheme.surfaceVariant.withOpacity(0.8), borderRadius: const BorderRadius.only( topLeft: Radius.circular(4.0), topRight: Radius.circular(18.0), bottomLeft: Radius.circular(18.0), bottomRight: Radius.circular(18.0) ) ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                 Text( message.text, style: TextStyle( color: theme.colorScheme.onSurfaceVariant, fontStyle: FontStyle.italic, fontSize: 14 ) ),
              ],
            ),
          ),
       );
    }
    final Color userBubbleColor = isDarkMode ? Colors.grey[800]! : Colors.grey[300]!;
    final Color aiBubbleColor = theme.colorScheme.surfaceVariant;
    final Color userTextColor = theme.colorScheme.onSurface;
    final Color aiTextColor = theme.colorScheme.onSurfaceVariant;
    final bubbleColor = message.isUser ? userBubbleColor : aiBubbleColor;
    final textColor = message.isUser ? userTextColor : aiTextColor;
    final borderRadius = BorderRadius.only( topLeft: Radius.circular(message.isUser ? 18.0 : 4.0), topRight: Radius.circular(message.isUser ? 4.0 : 18.0), bottomLeft: const Radius.circular(18.0), bottomRight: const Radius.circular(18.0) );

    // If you want to try flutter_markdown, you'd replace SelectableText here
    // For now, we rely on Gemini's prompt to avoid complex markdown
    return Align( alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
        constraints: BoxConstraints( maxWidth: MediaQuery.of(context).size.width * 0.8 ),
        decoration: BoxDecoration( color: bubbleColor, borderRadius: borderRadius, boxShadow: [ BoxShadow( color: Colors.black.withOpacity(0.06), blurRadius: 3, offset: const Offset(1, 2) ) ] ),
        child: SelectableText( message.text, style: TextStyle( color: textColor, fontSize: 15, height: 1.35 ) ),
      ),
    );
  }
}

// --- Extensions (Moved Outside State Class) ---
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return split(' ')
       .map((word) {
         if (word.isEmpty) return "";
         if (word.length > 1 && word == word.toUpperCase()) return word; // Preserve acronyms
         return word[0].toUpperCase() + substring(1).toLowerCase();
       })
       .join(' ');
  }

  String readableFieldName() {
    if (isEmpty) return "";
    String spaced = replaceAll('_', ' ');
    spaced = spaced.replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}');
    spaced = spaced.replaceAllMapped(RegExp(r'([A-Z])([A-Z][a-z])'), (match) => '${match.group(1)} ${match.group(2)}');
    return spaced.capitalize();
  }
}


// --- Main Widget ---
class AiAgentPage extends StatefulWidget {
 final bool isDarkMode;
 final VoidCallback toggleTheme;
 final Map<String, dynamic>? collegeData;

 const AiAgentPage({
   Key? key,
   required this.isDarkMode,
   required this.toggleTheme,
   this.collegeData,
 }) : super(key: key);

 @override
 _AiAgentPageState createState() => _AiAgentPageState();
}

enum ProcessingStep { idle, processing }

class _AiAgentPageState extends State<AiAgentPage> {
 final TextEditingController _messageController = TextEditingController();
 final List<ChatMessage> _messages = [];
 ProcessingStep _processingStep = ProcessingStep.idle;
 final ScrollController _scrollController = ScrollController();

 bool _isListening = false;
 bool _isContinuousListening = false;

 final String _apiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // <<< CRITICAL SECURITY WARNING! Hardcoded API Key.
 final String _model = 'gemini-2.0-flash-lite';

 int _dailyTokenCount = 0;
 final int _maxDailyTokens = 300000;
 int _lastPromptTokenCount = 0;
 int _lastApiResponseCharCount = 0;

 final double _inputCostPer1kChars = 0.00013125;
 final double _outputCostPer1kChars = 0.00046875;
 final double _exchangeRate = 2000.0;

 late stt.SpeechToText _speechToText;
 late FlutterTts _flutterTts;
 bool _isMuted = false;

 List<Map<String, String>> _availableVoices = [];
 Map<String, String>? _selectedVoice;
 bool _voicesLoaded = false;

 final Map<String, List<String>> _tableKeywords = {
      'helpdesks': ['help desk', 'it support', 'tech support', 'library help', 'student services desk', 'assistance', 'computer problem', 'wifi issue', 'password reset', 'student support', 'get help'],
      'accessibility': ['accessibility', 'disability', 'disabled', 'ada', 'special needs', 'accomodation', 'access', 'mobility', 'learning support'],
      'faq': ['faq', 'frequently asked', 'questions', 'common questions', 'q&a', 'ask', 'wondering', 'general help', 'information', 'how do i', 'what about'],
      'links': ['link', 'website', 'url', 'resource', 'webpage', 'portal', 'online form', 'find'],
      'construction': ['construction', 'building work', 'renovation', 'campus updates', 'project', 'noise', 'closure'],
      'printing': ['print', 'printer', 'printing', 'copies', 'photocopy', 'scan', 'cost to print'],
      'daycares': ['daycare', 'childcare', 'nursery', 'kids', 'child care', 'preschool'],
      'sustainability': ['sustainability', 'green', 'environment', 'eco', 'recycling', 'conservation', 'solar'],
      'notices': ['notice', 'alert', 'announcement', 'update', 'important info', 'news', 'bulletin'],
      'socialmediafeeds': ['social media', 'twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube channel'],
      'admissionsprocess': ['admission', 'admissions', 'apply', 'application', 'how to apply', 'enroll', 'acceptance', 'get in', 'prospectus', 'entry requirements'],
      'registrationprocess': ['registration', 'register', 'enrollment', 'course selection', 'sign up', 'add drop class', 'choose courses', 'academic planning'],
      'selection': ['selection criteria', 'admission profile', 'student demographics', 'acceptance rate', 'average gpa', 'sat score', 'act score'],
      'costsorrates': ['cost', 'fee', 'fees', 'tuition', 'rate', 'price', 'how much', 'payment plan', 'expense', 'school fees'],
      'scholarships': [
          'scholarship', 'scholarships', 'grant', 'grants', 'financial aid',
          'bursary', 'funding', 'award', 'loan', 'fafsa', 'student aid',
          'tuition assistance', 'financial support', 'fellowship'
      ],
      'payments': ['payment', 'pay', 'billing', 'invoice', 'tuition payment', 'how to pay', 'deadline', 'finance office'],
      'orientations': ['orientation', 'welcome week', 'new student', 'campus tour', 'introduction', 'onboarding'],
      'symposiums': ['symposium', 'conference', 'seminar', 'lecture series', 'guest speaker', 'academic event'],
      'graduation': ['graduation', 'commencement', 'ceremony', 'degree', 'graduate', 'diploma', 'finish school'],
      'people': ['faculty', 'staff', 'directory', 'contact', 'professor', 'teacher', 'employee list', 'instructor', 'advisor', 'dean', 'department head', 'phone number for', 'email for'],
      'currentstudents': ['current student', 'student portal', 'student id', 'student life', 'enrolled student'],
      'housing': ['housing', 'residence', 'dorm', 'dormitory', 'accommodation', 'living on campus', 'room assignment', 'ra', 'res life'],
      'locallodging': ['hotel', 'lodging', 'bnb', 'accomodation', 'off-campus stay', 'nearby hotel', 'place to stay', 'visitor housing'],
      'shopsoreateries': ['store', 'shop', 'cafe', 'dining', 'eatery', 'food', 'restaurant', 'canteen', 'bookstore', 'campus store', 'merchandise', 'eat'],
      'mealplans': ['meal plan', 'dining plan', 'food points', 'swipes', 'how much is meal plan'],
      'localareadining': ['nearby restaurant', 'off-campus food', 'places to eat near campus', 'town dining'],
      'studentdiscounts': ['discount', 'student deal', 'coupon', 'offer', 'save money'],
      'inventory': ['bookstore inventory', 'item stock', 'merchandise', 'textbook', 'supplies'],
      'menus': ['menu', 'dining hall menu', 'whats for lunch', 'food options', 'cafeteria food', 'nutrition'],
      'campusshuttle': ['shuttle', 'bus', 'transport', 'campus bus', 'route', 'schedule', 'getting around'],
      'parkingspaces': ['parking', 'permit', 'lot', 'where to park', 'car park', 'vehicle registration', 'ticket'],
      'localtransport': ['public transport', 'local bus', 'train', 'metro', 'getting here', 'travel to campus'],
      'schools': ['school of', 'college of', 'division', 'faculty', 'academic school'],
      'departments': ['department', 'academic department', 'program contact', 'major department', 'minor department'],
      'centers': ['research center', 'institute', 'lab', 'facility', 'program center'],
      'documents': ['form', 'report', 'pdf', 'download', 'handbook', 'policy document', 'transcript request', 'application form'],
      'majors': ['major', 'degree program', 'field of study', 'course of study', 'what majors', 'list of majors'],
      'minors': ['minor', 'concentration', 'certificate program', 'list of minors'],
      'funding': ['research funding', 'grant opportunity', 'project support', 'financial support for research'], // Could be general, scholarships is more specific for students
      'coursecatalog': ['course catalog', 'course list', 'class description', 'module details', 'subject list', 'curriculum'],
      'enrollmentexercise': ['mock registration', 'practice enrollment', 'course signup simulation'],
      'academicresources': ['tutoring', 'academic support', 'writing center', 'library resources', 'study help', 'advisor', 'mentor', 'struggling academically'],
      'academichonors': ['honors', 'dean\'s list', 'gpa requirement', 'academic distinction', 'cum laude'],
      'academicprizes': ['prize', 'award', 'competition', 'student award', 'scholarship award'],
      'academicdress': ['graduation gown', 'regalia', 'cap and gown', 'hood', 'academic attire'],
      'entryrequirements': ['admission requirements', 'prerequisites', 'how to get in', 'application criteria', 'gpa needed', 'test scores', 'apply'],
      'gradingscale': ['grading system', 'gpa scale', 'how grades work', 'marks', 'pass fail'],
      'programs': ['student program', 'special program', 'initiative', 'extracurricular', 'leadership program'],
      'signatureevents': ['homecoming', 'annual event', 'campus tradition', 'key ceremony', 'founder day'],
      'traditions': ['campus traditions', 'rituals', 'history', 'customs'],
      'partnershipopportunities': ['partnership', 'collaboration', 'industry link', 'community engagement'],
      'athletics': ['athletics', 'sports', 'team', 'game schedule', 'varsity', 'intramural', 'coach', 'stadium', 'gym'],
      'orgsorclubs': ['clubs', 'student organizations', 'student group', 'society', 'join a club', 'extracurricular activities'],
      'researchgroups': ['research group', 'lab group', 'research team', 'project group'],
      'committees': ['committee', 'governance', 'board', 'student government', 'faculty senate'],
      'news': ['news', 'latest news', 'press release', 'campus updates', 'announcements'],
      'periodicals': ['campus newspaper', 'magazine', 'journal', 'student publication'],
      'radio': ['campus radio', 'radio station', 'broadcast'],
      'television': ['campus tv', 'tv station', 'student broadcast'],
      'photos': ['photos', 'gallery', 'images', 'pictures', 'campus scenery'],
      'videos': ['videos', 'youtube', 'promotional video', 'recordings'],
      'accelerators': ['startup accelerator', 'incubator', 'entrepreneurship program', 'business support'],
      'makerspaces': ['makerspace', 'fab lab', 'diy space', '3d printer', 'workshop'],
      'startupfunds': ['seed fund', 'venture capital', 'student startup funding', 'pitch competition'],
      'startups': ['student startup', 'campus venture', 'spin-off company'],
      'researchprojects': ['research project', 'faculty research', 'student research', 'study'],
      'theses': ['thesis', 'dissertation', 'capstone project', 'final paper', 'senior project'],
      'books': ['faculty books', 'alumni books', 'library books', 'textbooks'],
      'articles': ['journal article', 'research paper', 'faculty publication'],
      'patents': ['patent', 'invention', 'intellectual property', 'ip'],
      'building': ['building map', 'building directory', 'specific building', 'hall name', 'facility location'],
      'rooms': ['room schedule', 'classroom location', 'lab number', 'book a room', 'study space', 'lecture hall'],
      'roomequipment': ['projector', 'smartboard', 'av equipment', 'classroom tech', 'computer lab'],
      'roomassignments': ['dorm assignment', 'room key', 'housing placement', 'move-in'],
      'publicart': ['campus art', 'sculpture', 'mural', 'art installation', 'gallery exhibit'],
      'emergencyequipment': ['aed location', 'fire extinguisher', 'safety equipment', 'first aid kit'],
      'classschedules': ['class schedule', 'my schedule', 'course times', 'timetable', 'find class location'],
      'weeklyschedule': ['weekly events', 'this week schedule', 'regular meetings'],
      'events': ['events', 'calendar', 'upcoming events', 'activity', 'workshop', 'register for event', 'what happening'],
      'academiccalendar': ['academic calendar', 'term dates', 'semester dates', 'important dates', 'deadline', 'holiday', 'break'],
      'feedback': ['feedback', 'suggestion', 'complaint', 'survey', 'course evaluation'],
      'historicaltimeline': ['history', 'college history', 'timeline', 'milestones', 'founding'],
      'rentals': ['equipment rental', 'space rental', 'book equipment', 'reserve room'],
      'rentalequipmentcalendar': ['equipment availability', 'rental booking', 'gear schedule'],
      'jobs': ['job', 'campus job', 'student employment', 'work study', 'career services', 'vacancy', 'hiring'],
      'services': ['student services', 'support services', 'health services', 'it services', 'counseling services', 'career services'],
      'atms': ['atm', 'cash machine', 'bank machine', 'withdraw cash'],
      'clinicsorhospitals': ['health center', 'clinic', 'hospital', 'doctor', 'nurse', 'medical appointment', 'sick'],
      'counselingservices': ['counseling', 'mental health', 'therapist', 'psychologist', 'support group', 'wellness center', 'stress', 'anxiety'],
      'emergencycontacts': ['emergency number', 'campus security', 'campus police', 'report incident', 'hotline'],
      'safetyprocedures': ['safety plan', 'emergency procedure', 'evacuation', 'lockdown', 'fire safety'],
      'connectivity': ['wifi', 'internet access', 'network', 'eduroam', 'connect to wifi', 'internet down'],
      'giving': ['donate', 'donation', 'support', 'fundraising', 'alumni giving', 'gift'],
  };
 final Map<String, String> _tableManifest = {
      'helpdesks': 'Campus help desk locations and services (IT, library, student services, etc.) and general student support.',
      'accessibility': 'Disability support services and campus accessibility resources.',
      'faq': 'Frequently asked questions about various campus topics including admissions, academics, and campus life. Good for general queries.',
      'links': 'Important website links for departments, applications, and resources.',
      'construction': 'Current/pending campus construction projects with locations and timelines.',
      'printing': 'Printing service locations, costs, and availability.',
      'daycares': 'On-campus childcare facilities and registration information.',
      'sustainability': 'Environmental initiatives and green campus programs.',
      'notices': 'Campus-wide announcements and time-sensitive alerts.',
      'socialmediafeeds': 'Official college social media accounts and links.',
      'admissionsprocess': 'Step-by-step application procedures, admission requirements, and related information.',
      'registrationprocess': 'Course enrollment steps and academic planning.',
      'selection': 'Demographic/academic profiles of admitted students, acceptance rates, and selection criteria.',
      'costsorrates': 'Tuition fees, housing costs, school fees, and other financial rates.',
      'scholarships': 'Information on available grants, awards, scholarships, bursaries, loans, and other financial aid opportunities for students.',
      'payments': 'Payment methods, portals, and billing information for tuition and fees.',
      'orientations': 'New student orientation programs and schedules.',
      'symposiums': 'Academic conference details and participation info.',
      'graduation': 'Commencement ceremony logistics and graduate data.',
      'people': 'Faculty/staff directories with contact info and roles. Useful for finding specific individuals.',
      'currentstudents': 'Profiles of enrolled students (majors, housing, etc.).',
      'housing': 'Residence hall details, policies, and living arrangements on campus.',
      'locallodging': 'Off-campus hotels/B&Bs near the college for visitors.',
      'shopsoreateries': 'On-campus stores, cafes, and dining options.',
      'mealplans': 'Dining plan options and associated costs for students.',
      'localareadining': 'Nearby off-campus restaurants and food discounts.',
      'studentdiscounts': 'Local business offers for students.',
      'inventory': 'Campus store products, merchandise, and pricing.',
      'menus': 'Daily dining hall meal offerings and nutritional info.',
      'campusshuttle': 'Transportation routes and schedules for the campus bus/shuttle.',
      'parkingspaces': 'Parking lot locations, permits, and regulations.',
      'localtransport': 'Public transit options and regional travel to/from campus.',
      'schools': 'Academic divisions (e.g., School of Arts) and their leadership.',
      'departments': 'Academic department info and faculty contacts.',
      'centers': 'Research centers and special program facilities.',
      'documents': 'Official forms, reports, and policy PDFs for download.',
      'majors': 'Undergraduate degree programs and requirements.',
      'minors': 'Minor programs and certification details.',
      'funding': 'General research grants and project funding opportunities (distinct from student scholarships).',
      'coursecatalog': 'Course descriptions and class details for all academic offerings.',
      'enrollmentexercise': 'Registration practice simulations.',
      'academicresources': 'Tutoring, libraries, and study support services.',
      'academichonors': 'Dean’s list, honors programs, and GPA requirements for academic distinction.',
      'academicprizes': 'Student achievement awards and competitions.',
      'academicdress': 'Graduation regalia info and ordering.',
      'entryrequirements': 'Detailed admission criteria, prerequisites, and application guidelines. Often related to `admissionsprocess`.',
      'gradingscale': 'Letter grade definitions and GPA calculations.',
      'programs': 'Special academic initiatives, student programs, and partnerships.',
      'signatureevents': 'Major annual campus traditions/ceremonies.',
      'traditions': 'Historical campus customs and rituals.',
      'partnershipopportunities': 'Community/corporate collaboration programs.',
      'athletics': 'Sports teams, schedules, and athlete resources.',
      'orgsorclubs': 'Student organizations and club listings.',
      'researchgroups': 'Active academic research teams/projects.',
      'committees': 'Campus governance groups and their functions.',
      'news': 'College news articles and press releases.',
      'periodicals': 'Student-run publications and magazines.',
      'radio': 'Campus radio station programming and staff.',
      'television': 'Student-produced TV shows and content.',
      'photos': 'Campus photo archives and event galleries.',
      'videos': 'Official college videos and student projects.',
      'accelerators': 'Entrepreneurship programs and startup support.',
      'makerspaces': 'Creative labs with equipment/tech resources.',
      'startupfunds': 'Funding opportunities for student ventures.',
      'startups': 'Student-run businesses and their profiles.',
      'researchprojects': 'Ongoing faculty/student research studies.',
      'theses': 'Senior capstone projects and research papers.',
      'books': 'Publications by faculty/alumni.',
      'articles': 'Academic papers and journal contributions.',
      'patents': 'Innovations/IP created at the college.',
      'building': 'Campus building info, maps, and facilities directories.',
      'rooms': 'Classroom/lab specifications and reservations.',
      'roomequipment': 'AV/tech gear available in spaces.',
      'roomassignments': 'Student housing placements.',
      'publicart': 'Campus art installations and exhibits.',
      'emergencyequipment': 'Safety devices and their locations.',
      'classschedules': 'Course times, locations, and instructors.',
      'weeklyschedule': 'Recurring events and meetings.',
      'events': 'Campus activities calendar and RSVP info.',
      'academiccalendar': 'Term dates, holidays, and academic deadlines.',
      'feedback': 'Student surveys and feedback forms.',
      'historicaltimeline': 'Key moments in college history.',
      'rentals': 'Equipment/space rental options and policies.',
      'rentalequipmentcalendar': 'Reservation schedule for gear.',
      'jobs': 'Campus employment and career opportunities.',
      'services': 'Overview of student support services (IT, health, counseling, etc.).',
      'atms': 'On-campus cash machine locations.',
      'clinicsorhospitals': 'Health center services and hours.',
      'counselingservices': 'Mental health resources and appointments.',
      'emergencycontacts': 'Critical phone numbers and protocols.',
      'safetyprocedures': 'Emergency response guidelines.',
      'connectivity': 'WiFi, tech resources, and IT support.',
      'giving': 'Donation opportunities and alumni fundraising.',
  };
 final Map<String, List<String>> _collegeFieldKeywords = {
      'about': ['about', 'overview', 'information', 'general info', 'tell me about the college', 'history', 'background'],
      'address': ['address', 'location', 'located', 'where is', 'find you', 'campus address', 'physical address'],
      'daysnhours': ['hours', 'opening hours', 'closing time', 'open', 'close', 'days open', 'schedule', 'operating hours', 'business hours'],
      'postaladdress': ['postal address', 'mailing address', 'zip code', 'postcode', 'mail to'],
      'mission': ['mission', 'mission statement', 'purpose', 'college aim', 'institutional mission', 'our mission'],
      'vision': ['vision', 'vision statement', 'college aspiration', 'future goals', 'institutional vision', 'our vision'],
      'corevalues': ['values', 'core values', 'principles', 'ethics', 'guiding principles'],
      'motto': ['motto', 'tagline', 'slogan', 'college motto'],
      'goals': ['goals', 'objectives', 'targets', 'aims', 'strategic goals'],
      'mandate': ['mandate', 'authority', 'charge', 'official purpose'],
      'founded': ['founded', 'established', 'since', 'when was it founded', 'history start', 'year founded'],
      'accreditation': ['accreditation', 'accredited', 'certified', 'recognized', 'licensing'],
      'freewifi': ['wifi', 'internet', 'wireless', 'free wifi', 'connect to internet'],
      'objectives': ['objectives', 'aims', 'goals', 'key objectives'], // often used with mission/vision
      'aims': ['aims', 'objectives', 'goals'], // often used with mission/vision
      'pledge': ['pledge', 'commitment', 'promise', 'dedication', 'our pledge'],
      'statementoffaith': ['faith', 'belief', 'statement of faith', 'religious statement', 'creed'],
      'religiousaffiliation': ['religious', 'faith', 'denomination', 'affiliation', 'church associated'],
      'whychooseus': ['why choose', 'why us', 'advantages', 'benefits', 'choose us', 'selling points', 'why attend here', 'unique features'],
      'institutiontype': ['type', 'public', 'private', 'institution type', 'kind of school', 'college type', 'university type'],
      'campussetting': ['campus setting', 'setting', 'urban', 'rural', 'suburban', 'campus environment', 'location type'],
      'highestqualificationoffered': ['qualification', 'degree', 'certificate', 'highest degree', 'level of study', 'programs offered', 'diploma offered'],
      'studentpopulation': ['population', 'students', 'enrollment', 'how many students', 'student body size', 'number of students'],
      'academicyearcalendar': ['academic calendar', 'terms', 'semesters', 'academic year', 'school year schedule', 'term dates', 'session dates'],
      'website': ['website', 'site', 'url', 'web address', 'online', 'homepage', 'official website'],
      'city': ['city', 'town', 'located in which city'],
      'state': ['state', 'region', 'province', 'located in which state'],
      'fullname': ['name', 'full name', 'official name', 'college name'],
      'phone': ['phone', 'number', 'contact number', 'call', 'telephone', 'main phone'],
      'email': ['email', 'email address', 'contact email', 'mail address', 'main email'],
  };


 @override
 void initState() {
   super.initState();
   _speechToText = stt.SpeechToText();
   _flutterTts = FlutterTts();
   WidgetsBinding.instance.addPostFrameCallback((_) {
     _initializeAndLoadData();
     _addInitialGreeting();
   });
   _messageController.addListener(() {
      if(mounted) setState(() {});
   });
 }

 @override
 void dispose() {
   _messageController.removeListener(() { if(mounted) setState(() {}); });
   _messageController.dispose();
   _scrollController.dispose();
   _speechToText.stop();
   _flutterTts.stop();
   super.dispose();
 }

 Future<void> _initializeAndLoadData() async {
    await _initSpeech();
    await _configureTts();
    await _loadTtsVoices();
 }

 Future<void> _initSpeech() async {
     try {
       bool available = await _speechToText.initialize(
         onError: (errorNotification) { print('Speech Init Error: $errorNotification'); if (mounted) { setState(() => _isContinuousListening = false); _showError("Speech recognition error: ${errorNotification.errorMsg}"); } },
         onStatus: (status) {
           print('Speech Status: $status');
           if (mounted) {
             final isCurrentlyListening = status == stt.SpeechToText.listeningStatus;
             if (_isListening != isCurrentlyListening) { setState(() { _isListening = isCurrentlyListening; }); }
             if (status == stt.SpeechToText.notListeningStatus && _isContinuousListening && _processingStep == ProcessingStep.idle) {
               print("STT stopped naturally. Restarting listening loop.");
               Future.delayed(const Duration(milliseconds: 500), () { if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle) { _startListeningSession(); } });
             }
           }
         }
       );
       if (!available && mounted) { print("Speech recognition not available."); setState(() => _isContinuousListening = false); _showError("Speech recognition is not available on this device."); }
       else if (available) { print("Speech recognition initialized."); }
     } catch (e) { print("Exception initializing speech: $e"); if (mounted) { setState(() => _isContinuousListening = false); _showError("Failed to initialize speech recognition."); } }
 }

 Future<void> _configureTts() async {
     await _flutterTts.awaitSpeakCompletion(true);
     await _flutterTts.setVolume(1.0);
     await _flutterTts.setSpeechRate(0.5);
     await _flutterTts.setPitch(1.0);
     if (_selectedVoice != null && mounted) {
       try {
         List<dynamic>? voices = await _flutterTts.getVoices;
         bool voiceExists = voices?.any((v) => v is Map && v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale']) ?? false;
         if (voiceExists) {
           await _flutterTts.setVoice(_selectedVoice!);
           print("TTS voice set: ${_selectedVoice!['name']}");
         } else {
           print("Selected voice not found, using default.");
           if(mounted) setState(() => _selectedVoice = null);
           await _flutterTts.setLanguage("en-US");
         }
       } catch (e) {
         print("Error setting TTS voice: $e");
         if (mounted) setState(() => _selectedVoice = null);
         await _flutterTts.setLanguage("en-US");
       }
     } else {
       await _flutterTts.setLanguage("en-US");
       print("Using default US English TTS voice.");
     }
  }

 Future<void> _loadTtsVoices() async {
     if (!mounted) return;
     try {
       var voices = await _flutterTts.getVoices;
       if (voices != null && voices is List && mounted) {
         List<Map<String, String>> englishVoices = voices
               .map((v) => Map<String, String>.from(v as Map))
               .where((v) => v['locale']?.startsWith('en-') ?? false)
               .sortedBy<String>((v) => v['name'] ?? '')
               .toList();

         setState(() {
           _availableVoices = englishVoices;
           _voicesLoaded = true;
           if (_selectedVoice == null || !_availableVoices.any((v) => v['name'] == _selectedVoice!['name'] && v['locale'] == _selectedVoice!['locale'])) {
             _selectedVoice = _availableVoices.firstWhereOrNull( (v) => v['locale'] == 'en-US' && (v['name']?.toLowerCase().contains('female') ?? false))
                            ?? _availableVoices.firstWhereOrNull((v) => v['locale'] == 'en-US')
                            ?? _availableVoices.firstOrNull;
           }
         });
         await _configureTts();
         print("Loaded ${_availableVoices.length} English voices. Selected: ${_selectedVoice?['name']}");
       }
     } catch (e) {
       print("Error getting TTS voices: $e");
       if (mounted) setState(() => _voicesLoaded = false);
     }
   }

 void _toggleContinuousListening() async {
      if (_processingStep != ProcessingStep.idle) return;

      if (_isContinuousListening) {
        setState(() => _isContinuousListening = false);
        if (_speechToText.isListening) await _speechToText.stop();
        print("Continuous listening STOPPED by toggle.");
      } else {
        if (!await _speechToText.hasPermission) {
          _showError("Speech permission required to use microphone.");
          return;
        }
        if (!_speechToText.isAvailable) {
          bool initSuccess = await _speechToText.initialize();
          if(!initSuccess || !mounted){
             _showError("Could not start speech recognition service.");
             return;
          }
        }
        if(mounted){
           setState(() => _isContinuousListening = true);
           print("Continuous listening STARTED by toggle.");
           _startListeningSession();
        }
      }
   }

 Future<void> _startListeningSession() async {
      if (!_isContinuousListening || _isListening || _processingStep != ProcessingStep.idle || !mounted) {
         print("Skipping startListeningSession. Continuous: $_isContinuousListening, Listening: $_isListening, Processing: $_processingStep, Mounted: $mounted");
         return;
      }
      await _flutterTts.stop();
      if (!_speechToText.isAvailable) {
          if (mounted) {
              setState(() => _isContinuousListening = false);
              _showError("Speech recognition service became unavailable.");
          }
          return;
      }
      print("Starting STT listening session...");
      if(mounted) setState(() => _isListening = true);

      _speechToText.listen(
        listenFor: const Duration(seconds: 60),
        pauseFor: const Duration(seconds: 5),
        partialResults: true,
        onResult: (result) {
          if (!mounted) return;
          _messageController.text = result.recognizedWords;
          _messageController.selection = TextSelection.fromPosition(TextPosition(offset: _messageController.text.length));
          if (result.finalResult && _isContinuousListening) {
            final recognizedText = result.recognizedWords.trim();
            print("STT Final Result: '$recognizedText'");
             if (_speechToText.isListening && mounted) _speechToText.stop();
            if (recognizedText.isNotEmpty) {
               _sendMessage(recognizedText);
            } else {
               print("Empty final STT result.");
            }
          }
        },
        listenMode: stt.ListenMode.dictation,
      ).catchError((error) {
         print("STT listen error: $error");
         if (mounted) {
            setState(() {
              _isListening = false;
              _isContinuousListening = false;
            });
            _showError("Speech recognition failed during listening.");
         }
      });
   }

 void _handleManualInput(String value) {
     if (_isContinuousListening && _messageController.text.isNotEmpty && mounted) {
        print("Manual typing detected, stopping continuous listening.");
        setState(() => _isContinuousListening = false);
        _speechToText.stop();
     }
 }

 Future<void> _sendMessage(String message) async {
   final String userMessageText = message.trim();
   if (userMessageText.isEmpty || _processingStep != ProcessingStep.idle) return;

   if (_speechToText.isListening) await _speechToText.stop();

   String? quickAnswer = _getQuickAnswer(userMessageText);
   if (quickAnswer != null) {
     print("Quick Answer triggered for: '$userMessageText'");
     final userCharCount = _calculateTokenCount(userMessageText);
     final responseCharCount = _calculateTokenCount(quickAnswer);

     if (_dailyTokenCount + userCharCount > _maxDailyTokens) {
       _showError("Daily usage limit reached. Cannot send message.");
       if (mounted) setState(() => _isContinuousListening = false);
       return;
     }
     if (mounted) _messageController.clear();
     setState(() {
       _messages.add(ChatMessage(text: userMessageText, isUser: true));
       _messages.add(ChatMessage(text: quickAnswer, isUser: false));
       _dailyTokenCount += userCharCount;
       _lastPromptTokenCount = userCharCount;
       _lastApiResponseCharCount = responseCharCount;
     });
     _scrollToBottom();
     if (!_isMuted) {
       await _configureTts(); await Future.delayed(const Duration(milliseconds: 100));
       if (!_isMuted && mounted) await _flutterTts.speak(quickAnswer);
     }
     if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle) {
        print("Quick answer sent, restarting listening session.");
        Future.delayed(const Duration(milliseconds: 500), () => _startListeningSession());
     }
     return;
   }

   final int userMessageCharCount = _calculateTokenCount(userMessageText);
   if (_dailyTokenCount + userMessageCharCount > _maxDailyTokens) {
     _showError("Daily usage limit would be exceeded by this message.");
     if (mounted) setState(() => _isContinuousListening = false);
     return;
   }

   final userChatMessage = ChatMessage(text: userMessageText, isUser: true);
   setState(() {
     _processingStep = ProcessingStep.processing;
     _isListening = false;
     _messages.add(userChatMessage);
     _lastPromptTokenCount = userMessageCharCount;
     _lastApiResponseCharCount = 0;
   });
   _scrollToBottom();
   if (mounted) _messageController.clear();

   int currentTurnTotalChars = userMessageCharCount;
   int currentStepPromptChars = 0;
   int currentStepResponseChars = 0;
   List<String> tablesToFetch = [];
   String contextData = "";
   String finalResponseText = "";
   bool errorOccurred = false;

   try {
     _addOrUpdateTypingIndicator("Analyzing query...");
     _scrollToBottom();

     final tableSelectionPrompt = _buildTableSelectionPrompt(userMessageText);
     currentStepPromptChars = _calculateTokenCount(tableSelectionPrompt);
     if (_dailyTokenCount + currentTurnTotalChars + currentStepPromptChars > _maxDailyTokens) {
       throw Exception("Daily limit would be exceeded by table selection request.");
     }
     print("Sending table selection request to Gemini...");
     String tableSelectionResponse = await _callGeminiApi(tableSelectionPrompt);
     if (!mounted) return;
     currentStepResponseChars = _calculateTokenCount(tableSelectionResponse);
     currentTurnTotalChars += currentStepPromptChars + currentStepResponseChars;
     if (mounted) setState(() {
        _lastPromptTokenCount = currentStepPromptChars;
        _lastApiResponseCharCount = currentStepResponseChars;
     });
     tablesToFetch = tableSelectionResponse
         .split(',')
         .map((t) => t.trim().toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'),''))
         .where((t) => t.isNotEmpty && t != 'none' && _tableManifest.containsKey(t))
         .toSet().toList();
     print("Gemini suggested tables (validated): $tablesToFetch");

     _addOrUpdateTypingIndicator("Fetching relevant details...");
     _scrollToBottom();
     contextData = await _getCollegeDataForPrompt(userMessageText, tablesToFetch);
     if (!mounted) return;
     if (contextData.startsWith('No specific college data')) {
       throw Exception("Error loading essential college information.");
     }
     if (contextData == 'Component unmounted') return;

     _addOrUpdateTypingIndicator("Generating response...");
     _scrollToBottom();

     final history = _buildConversationHistory();
     final collegeName = widget.collegeData?['fullname'] ?? 'the institution';
     final mainPhoneNumber = widget.collegeData?['phone'] ?? 'the main college phone number';

     // --- UPDATED PROMPT FOR GEMINI ---
     final finalPrompt = '''SYSTEM: You are a specialized AI assistant for $collegeName. Answer the user's query based ONLY on the information provided in the 'Provided Context' section below.

Instructions:
- Use the 'Provided Context' which contains 'General Info' about the college and 'Relevant Specific Information' fetched based on the query.
- **Format your response for easy readability. Use clear paragraphs. For lists, use an asterisk (*) at the beginning of each item. Avoid using markdown like '##' for headers or '**' for bolding. Instead of bolding, you can emphasize key terms by stating they are important if necessary.**
- **If the user's query is very broad (e.g., "tell me everything about admissions"), politely ask them to be more specific about what aspect of admissions they are interested in to provide a more focused and helpful answer. For example: "Admissions is a broad topic. Could you please specify what you'd like to know? For example, are you interested in the application process, entry requirements, or deadlines?"**
- **If the information needed to answer a specific query is present but very extensive, provide a concise summary and offer to give more details on specific parts if the user asks.**
- If the information needed to answer the query accurately is NOT present in the 'Provided Context', explicitly state that the specific detail is unavailable in the current data and politely suggest the user call $mainPhoneNumber for further assistance.
- Do NOT use external knowledge or make assumptions beyond the provided context. Be concise and directly answer the question unless a summary or clarification request is more appropriate as described above.

--- Provided Context ---
$contextData
--- End Provided Context ---

--- Recent Conversation History ---
$history
--- End Conversation History ---

user: $userMessageText
model:''';
     // --- END UPDATED PROMPT ---

     currentStepPromptChars = _calculateTokenCount(finalPrompt);
     if (_dailyTokenCount + currentTurnTotalChars + currentStepPromptChars > _maxDailyTokens) {
       throw Exception("Daily limit would be exceeded by final response generation request.");
     }
     print("Sending final generation request to Gemini...");
     finalResponseText = await _callGeminiApi(finalPrompt);
     if (!mounted) return;
     currentStepResponseChars = _calculateTokenCount(finalResponseText);
     currentTurnTotalChars += currentStepPromptChars + currentStepResponseChars;
     if (mounted) setState(() {
        _lastPromptTokenCount = currentStepPromptChars;
        _lastApiResponseCharCount = currentStepResponseChars;
     });

     _removeTypingIndicator();
     final aiResponseMessage = ChatMessage(
         text: finalResponseText.isEmpty ? "Sorry, I couldn't generate a response for that query based on the available information." : finalResponseText,
         isUser: false
     );

     if (_dailyTokenCount + currentTurnTotalChars > _maxDailyTokens) {
       errorOccurred = true;
       _showError("Received response, but daily limit was exceeded during processing.");
       if (mounted) {
         setState(() {
           _messages.add(aiResponseMessage);
           _dailyTokenCount += currentTurnTotalChars;
           if (_dailyTokenCount > _maxDailyTokens) _dailyTokenCount = _maxDailyTokens;
         });
       }
     } else {
       if (mounted) {
         setState(() {
           _messages.add(aiResponseMessage);
           _dailyTokenCount += currentTurnTotalChars;
         });
       }
       if (!_isMuted && finalResponseText.isNotEmpty) {
         await _configureTts(); await Future.delayed(const Duration(milliseconds: 100));
         if (!_isMuted && mounted) await _flutterTts.speak(finalResponseText);
       }
     }

   } catch (e, stacktrace) {
     errorOccurred = true;
     print('Error in multi-step pipeline: $e\n$stacktrace');
     _removeTypingIndicator();
     if (mounted) {
       setState(() {
         _dailyTokenCount += currentTurnTotalChars;
         if (_dailyTokenCount > _maxDailyTokens) _dailyTokenCount = _maxDailyTokens;
       });
     }
     String errorMessageText = e.toString().replaceFirst("Exception: ", "");
     if (errorMessageText.contains("Daily limit exceeded") || errorMessageText.contains("Daily limit would be exceeded")) {
       _showError("Daily usage limit reached. Please try again tomorrow.");
       if(mounted) setState(() { _isContinuousListening = false; });
     } else if (errorMessageText.contains("Error loading essential college information")) {
       _showError("Sorry, there was a problem loading essential information about the college.");
     } else if (errorMessageText.contains("blocked by AI")) {
       _showError("Sorry, the request was blocked due to safety settings. I'll try to be less restrictive.");
     } else if (errorMessageText.contains("timed out")) {
        _showError("Sorry, the request to the AI service timed out.");
     } else if (errorMessageText.contains("Network error")) {
         _showError("Sorry, there seems to be a network issue. Please check your connection.");
     } else {
        _showError('Sorry, an unexpected error occurred: $errorMessageText');
     }
     if (mounted) setState(() => _isContinuousListening = false);

   } finally {
     if (mounted) {
       _removeTypingIndicator();
       setState(() { _processingStep = ProcessingStep.idle; });
       _scrollToBottom();
       if (!errorOccurred && _isContinuousListening && mounted && _processingStep == ProcessingStep.idle) {
         print("Multi-step finished, restarting listening session.");
         Future.delayed(const Duration(milliseconds: 500), () {
           if (_isContinuousListening && mounted && _processingStep == ProcessingStep.idle && !_speechToText.isListening) {
             _startListeningSession();
           }
         });
       }
     }
   }
 }

 String _buildTableSelectionPrompt(String userQuery) {
   final manifestFormatted = _tableManifest.entries
       .map((e) => '- ${e.key}: ${e.value}')
       .join('\n');
   return '''SYSTEM: You are an AI assistant helping to select relevant database tables to answer a user's query about a college.
User Query: "$userQuery"

Available Data Tables (Manifest):
Each line has the format: table_name: description
---
$manifestFormatted
---

Instruction: Based *only* on the User Query and the table descriptions in the manifest, list the short table names (e.g., 'admissionsprocess', 'housing', 'costs') from the manifest that are MOST likely to contain the information needed to answer the query.
- List ONLY the relevant table_names, separated by commas (e.g., housing,costs,mealplans).
- Prioritize tables that directly match keywords in the query and their descriptions. Consider synonyms and related concepts (e.g., 'financial aid' might relate to 'scholarships').
- If the query is very general (e.g., "tell me about the college"), list core tables like 'faq', 'links', and perhaps 'about' (if available as a table, otherwise rely on general info).
- If multiple tables seem relevant, list them all.
- If no specific table seems relevant based on the query and descriptions, respond with the single word 'NONE'.
- Do NOT add any explanation, preamble, or concluding text. Just provide the comma-separated list or 'NONE'.
model:''';
 }

 String? _getQuickAnswer(String query) {
     if (widget.collegeData == null) return null;
     final lowerQuery = query.toLowerCase();
     if (RegExp(r'\b(website|site|url|web address|homepage)\b').hasMatch(lowerQuery)) {
         return widget.collegeData!['website'] != null
             ? "The website is: ${widget.collegeData!['website']}"
             : null;
     }
     if (RegExp(r'\b(phone|contact number|call|telephone)\b').hasMatch(lowerQuery) &&
         !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff")) {
         return widget.collegeData!['phone'] != null
             ? "The main phone number is: ${widget.collegeData!['phone']}"
             : null;
     }
     if (RegExp(r'\b(email|email address|contact email)\b').hasMatch(lowerQuery) &&
          !lowerQuery.contains("department") && !lowerQuery.contains("office") && !lowerQuery.contains("faculty") && !lowerQuery.contains("staff")) {
         return widget.collegeData!['email'] != null
             ? "The main contact email is: ${widget.collegeData!['email']}"
             : null;
     }
     if (RegExp(r'\b(address|location|located|where is|find you|campus address)\b').hasMatch(lowerQuery)) {
         String? address = widget.collegeData!['address']?.toString();
         String? city = widget.collegeData!['city']?.toString();
         String? state = widget.collegeData!['state']?.toString();
         List<String?> parts = [address, city, state];
         List<String> validParts = parts.whereNotNull().where((s) => s.trim().isNotEmpty).toList();
         return validParts.isNotEmpty
             ? "The address is: ${validParts.join(', ')}"
             : null;
      }
     return null;
 }

 Future<String> _getCollegeDataForPrompt(String userQuery, List<String> specificTablesToFetch) async {
    if (!mounted) return 'Component unmounted';
    if (widget.collegeData == null || widget.collegeData!['fullname']?.isEmpty == true) {
      print("Error: _getCollegeDataForPrompt called with null or empty collegeData.");
      return 'No specific college data available.';
    }

    String collegeName = widget.collegeData!['fullname']!;
    String collegeTablePrefix = widget.collegeData!['tableprefix']?.toString().trim() ?? '';
    if (collegeTablePrefix.isEmpty) {
       collegeTablePrefix = collegeName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]+'), '').replaceAll(' ', '_');
    }
    if (collegeTablePrefix.isEmpty) collegeTablePrefix = "college";
    print("Using table prefix: $collegeTablePrefix");

    final sb = StringBuffer();
    int totalFetchedDataLength = 0;
    const maxTotalFetchedDataLength = 4000; // Slightly increased budget for context

    // Section 1: General College Information
    sb.writeln('## General Info ($collegeName):');
    bool baseDataAdded = false;

    List<String> essentialFields = [
      'fullname', 'about', 'mission', 'vision', 'motto',
      'institutiontype', 'accreditation', 'founded', 'website', 'phone', 'email',
      'address', 'city', 'state', 'postaladdress', 'daysnhours',
      'studentpopulation', 'campussetting', 'academicyearcalendar', 'highestqualificationoffered'
    ];
    Set<String> addedFields = {};

    for (String field in essentialFields) {
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final label = field.readableFieldName();
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        if (value.isNotEmpty && value.toLowerCase() != 'n/a' && value.toLowerCase() != 'null') {
          String displayValue = value;
          int maxLength = (['about', 'mission', 'vision'].contains(field)) ? 350 : 180;
          if (displayValue.length > maxLength) displayValue = displayValue.substring(0, maxLength) + '... (truncated)';
          sb.writeln('- $label: $displayValue');
          baseDataAdded = true;
          addedFields.add(field);
        }
      }
    }

    final relevantDynamicFields = _getRelevantCollegeFields(userQuery, max: 4);
    for (var field in relevantDynamicFields) {
      if (addedFields.contains(field)) continue;
      if (widget.collegeData!.containsKey(field) && widget.collegeData![field] != null) {
        final label = field.readableFieldName();
        final value = widget.collegeData![field]?.toString().trim() ?? '';
        if (value.isNotEmpty && value.toLowerCase() != 'n/a' && value.toLowerCase() != 'null') {
          String displayValue = value;
          int maxLength = 150;
          if (displayValue.length > maxLength) displayValue = displayValue.substring(0, maxLength) + '... (truncated)';
          sb.writeln('- $label: $displayValue');
          baseDataAdded = true;
        }
      }
    }

    if (!baseDataAdded) sb.writeln("(No relevant general details found in base info for this query's context)");
    sb.writeln('---');
    totalFetchedDataLength += sb.length;

    List<String> actuallyFetchedTableNames = [];
    if (specificTablesToFetch.isNotEmpty) {
       sb.writeln('\n## Relevant Specific Information (Data fetched based on query analysis):');
       List<Future<Map<String, String>>> fetchFutures = [];

       for (String tableNameShort in specificTablesToFetch) {
           if (totalFetchedDataLength >= maxTotalFetchedDataLength) {
               print("Context length budget ($maxTotalFetchedDataLength) hit before fetching table '$tableNameShort'.");
               break;
           }
           String fullTableName = "${collegeTablePrefix}_$tableNameShort";
           int budgetForThisTable = maxTotalFetchedDataLength - totalFetchedDataLength;
           if (budgetForThisTable < 100) {
               print("Skipping fetch for '$tableNameShort' due to low remaining budget ($budgetForThisTable).");
               break;
           }
           fetchFutures.add(
              _fetchSupabaseDataForRow(fullTableName, limit: 3, maxLengthBudget: budgetForThisTable)
                 .then((data) => {'tableName': tableNameShort, 'data': data})
                 .catchError((e) {
                    print("Error fetching data for $tableNameShort ($fullTableName): $e");
                    return {'tableName': tableNameShort, 'data': ''};
                 })
           );
       }

       final results = await Future.wait(fetchFutures);
       if (!mounted) return 'Component unmounted';

       bool specificDataAdded = false;
       for (var result in results) {
         final name = result['tableName']!;
         final data = result['data']!;
         if (data.isNotEmpty) {
           final sectionTitle = name.capitalize();
           sb.writeln('\n### From $sectionTitle Data:');
           sb.writeln(data);
           sb.writeln('---');
           totalFetchedDataLength += data.length + sectionTitle.length + 25;
           specificDataAdded = true;
           actuallyFetchedTableNames.add(name);
         }
       }
        if (!specificDataAdded && specificTablesToFetch.isNotEmpty) {
            sb.writeln("(No specific data retrieved for suggested tables: ${specificTablesToFetch.join(', ')}).");
        } else if (specificDataAdded) {
             sb.writeln("(Fetched data details from: ${actuallyFetchedTableNames.join(', ')})");
        }
    } else {
        sb.writeln('\n## Relevant Specific Information (Fetched):');
        sb.writeln("(No specific tables were identified as relevant for fetching additional data based on the query).");
    }

    sb.writeln('\n## Available Data Tables (Manifest):');
    sb.writeln('(This lists all possible data areas. Data above in "Relevant Specific Information" was fetched only for tables deemed relevant to the current query.)');
    _tableManifest.forEach((tableName, description) {
       String displayTableName = tableName.capitalize();
       sb.writeln('- **$displayTableName**: $description');
    });

    if (totalFetchedDataLength >= maxTotalFetchedDataLength) {
      sb.writeln("\n(Note: Fetched context details might be incomplete due to length limits.)");
    }

    print("Context built. Gemini suggested: ${specificTablesToFetch.join(', ')}. Actually fetched from: ${actuallyFetchedTableNames.join(', ')}. Total Context Chars (approx): $totalFetchedDataLength");
    return sb.toString();
  }

  Future<String> _fetchSupabaseDataForRow(String tableName, {required int limit, required int maxLengthBudget}) async {
      if (maxLengthBudget <= 0 || !mounted) return "";
      print("Fetching: $tableName (Limit: $limit, Budget: $maxLengthBudget)");
      try {
         final response = await Supabase.instance.client
             .from(tableName)
             .select()
             .limit(limit)
             .timeout(const Duration(seconds: 10));

         if (!mounted) return "";

         if (response is List && response.isNotEmpty) {
            final sb = StringBuffer();
            for (int i = 0; i < response.length; i++) {
               if (sb.length >= maxLengthBudget) break;
               final row = response[i] as Map<String, dynamic>;
               List<String> parts = [];
               row.forEach((key, value) {
                  if (value != null && key != 'id' && key != 'created_at' && key != 'updated_at') {
                     String valStr = value.toString().trim();
                     if (valStr.isNotEmpty && valStr.toLowerCase() != 'n/a' && valStr.toLowerCase() != 'null') {
                        int maxLen = 100; // Increased summary limit per field
                        if (valStr.length > maxLen) valStr = valStr.substring(0, maxLen) + '...';
                        parts.add('${key.readableFieldName()}: $valStr');
                     }
                  }
               });
               String rowSummary = parts.isNotEmpty ? parts.join('; ') : "(Item details unavailable or not applicable)";
               int estimatedAddedLength = rowSummary.length + 10;
               if (sb.length + estimatedAddedLength < maxLengthBudget) {
                  sb.writeln('- $rowSummary');
               } else {
                  print("Budget hit while processing row for $tableName");
                  break;
               }
            }
            String result = sb.toString().trim();
            print("Fetched ${result.length} chars from $tableName.");
            return result;
         } else {
            return '';
         }
      } on PostgrestException catch (e) {
         if (e.code == '42P01') {
            print("Info: Table '$tableName' not found (Supabase code 42P01). This might be expected if the table doesn't exist for this college.");
         } else {
            print("Supabase error fetching '$tableName': ${e.message} (Code: ${e.code})");
         }
         return '';
      } catch (e) {
         print("General error fetching '$tableName': $e");
         return '';
      }
   }

 Future<String> _callGeminiApi(String prompt) async {
    if (_apiKey == 'YOUR_API_KEY_HERE' || _apiKey.length < 30 || !_apiKey.startsWith('AIzaSy')) {
      print("ERROR: Gemini API Key is missing, invalid, or a placeholder. Ensure it is correctly configured.");
      throw Exception("Configuration error: Invalid API key.");
    }
    final url = Uri.parse('https://generativelanguage.googleapis.com/v1beta/models/$_model:generateContent?key=$_apiKey');
    final headers = {'Content-Type': 'application/json'};
    final generationConfig = {
      'temperature': 0.5, // Slightly lower for more factual recall
      'topP': 0.95,
      'topK': 40,
      'maxOutputTokens': 2048
    };
    final body = jsonEncode({
      'contents': [{'role': 'user', 'parts': [{'text': prompt}]}],
      'generationConfig': generationConfig,
      // --- MODIFIED SAFETY SETTINGS ---
      'safetySettings': [
        {'category': 'HARM_CATEGORY_HARASSMENT', 'threshold': 'BLOCK_ONLY_HIGH'},
        {'category': 'HARM_CATEGORY_HATE_SPEECH', 'threshold': 'BLOCK_ONLY_HIGH'},
        {'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold': 'BLOCK_ONLY_HIGH'},
        {'category': 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold': 'BLOCK_ONLY_HIGH'}
      ]
      // --- END MODIFIED SAFETY SETTINGS ---
    });

    print("Sending Generation request to $_model...");
    print("Prompt length (chars): ${_calculateTokenCount(prompt)}");

    try {
       final response = await http.post(url, headers: headers, body: body)
           .timeout(const Duration(seconds: 90));
       print("Gemini Response Status: ${response.statusCode}");
       String responseBody = response.body;

       if (response.statusCode == 200) {
          final decoded = jsonDecode(responseBody);
          if (decoded['promptFeedback'] != null && decoded['promptFeedback']['blockReason'] != null) {
            final reason = decoded['promptFeedback']['blockReason'];
            print("Prompt blocked by Gemini: $reason.");
            throw Exception("Request blocked by AI (input safety: $reason).");
          }
          final candidates = decoded['candidates'];
          if (candidates != null && candidates.isNotEmpty) {
             final candidate = candidates[0];
             final finishReason = candidate['finishReason'];
             if (finishReason != null && finishReason != 'STOP' && finishReason != 'MAX_TOKENS') {
               if (finishReason == 'SAFETY') {
                 print("Response blocked by Gemini (output safety).");
                 throw Exception("Response blocked by AI (output safety).");
               } else if (finishReason == 'RECITATION') {
                 print("Response blocked by Gemini (recitation).");
                 throw Exception("Response blocked by AI (recitation).");
               } else {
                 print("Warning: Gemini response incomplete or finished unexpectedly. Reason: $finishReason");
               }
             }
             final content = candidate['content'];
             if (content != null && content['parts'] != null && content['parts'].isNotEmpty) {
               final textPart = content['parts'][0]['text'];
               String resultText = textPart?.trim() ?? '';
               print("Gemini response OK (length: ${resultText.length}). Finish Reason: $finishReason");
               return resultText;
             } else {
                 print("Response OK, but content part is missing. Finish Reason: $finishReason");
                 return '';
             }
          } else {
              if (decoded['promptFeedback'] != null && decoded['promptFeedback']['blockReason'] != null) {
                 final reason = decoded['promptFeedback']['blockReason'];
                 print("Prompt was blocked, leading to no candidates. Reason: $reason.");
                 throw Exception("Request blocked by AI (input safety leading to no candidates: $reason).");
              } else {
                 print("Response OK, but no candidates returned by Gemini. This might indicate an issue with the prompt or a safety block not explicitly reported in promptFeedback. Raw body: $responseBody");
                 return '';
              }
          }
       } else {
          String errorMessage = 'AI API request failed (${response.statusCode}).';
          try {
            final errorJson = jsonDecode(responseBody);
            errorMessage += ' Error: ${errorJson['error']?['message'] ?? 'Details unavailable.'}';
          } catch (_) {
            errorMessage += ' Response Body: ${responseBody.length > 200 ? responseBody.substring(0, 200) + "..." : responseBody}';
          }
          print(errorMessage);
          if (response.statusCode == 400) throw Exception('AI service error: Bad request (check prompt/parameters). Details: $errorMessage');
          if (response.statusCode == 403) throw Exception('AI service error: Forbidden (Invalid API key or permissions?).');
          if (response.statusCode == 429) throw Exception('AI service error: Quota exceeded or rate limit hit.');
          if (response.statusCode >= 500) throw Exception('AI service error: Server error (${response.statusCode}).');
          throw Exception('AI service error (Code: ${response.statusCode}). Details: $errorMessage');
       }
    } on TimeoutException catch (_) {
       print("API call timed out after 90 seconds.");
       throw Exception("Request to AI service timed out.");
    } on SocketException catch (e) {
       print("Network error during API call: $e");
       throw Exception("Network error connecting to AI service. Check internet connection.");
    } on http.ClientException catch (e) {
       print("HTTP Client error during API call: $e");
       throw Exception("Network error contacting AI service.");
    } catch (e) {
       print("Unhandled error during Gemini API call: $e");
       if (e is Exception) {
            throw e;
       }
       throw Exception("An unexpected error occurred during the AI call: ${e.toString()}");
    }
  }

 @override
 Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    String appBarTitle = 'College AI Assistant';
    if (widget.collegeData?['fullname']?.isNotEmpty == true) {
      appBarTitle = "${widget.collegeData!['fullname']} AI";
    }
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    final bool canInteract = _processingStep == ProcessingStep.idle && !tokenLimitReached;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        elevation: 1.0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: iconColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          appBarTitle,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        actions: [
          if (_voicesLoaded && _availableVoices.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Map<String, String>>(
                  value: _selectedVoice,
                  hint: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)),
                  icon: Padding(padding: const EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Icons.arrow_drop_down, color: iconColor.withOpacity(0.7), size: 20)),
                  selectedItemBuilder: (context) => _availableVoices
                      .map((_) => Center(
                            child: Tooltip(
                                message: _selectedVoice != null ? "${_selectedVoice!['name']} (${_selectedVoice!['locale']})" : "Select Voice",
                                child: Icon(Icons.record_voice_over, color: iconColor.withOpacity(0.7), size: 20)
                             )
                          )).toList(),
                  items: _availableVoices
                      .map((voice) => DropdownMenuItem<Map<String, String>>(
                          value: voice,
                          child: Tooltip(
                              message: "${voice['name']} (${voice['locale']})",
                              child: SizedBox(
                                width: 180,
                                child: Text("${voice['name']} (${voice['locale']})",
                                    style: const TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis)
                              )
                          )
                       )).toList(),
                  onChanged: canInteract
                      ? (Map<String, String>? newValue) {
                          if (newValue != null && mounted) {
                            setState(() => _selectedVoice = newValue);
                            _configureTts();
                          }
                        }
                      : null,
                  style: TextStyle(color: theme.colorScheme.onSurface),
                  dropdownColor: theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                  elevation: 4,
                ),
              ),
            ),
          IconButton(
            icon: Icon(Icons.refresh, color: canInteract ? iconColor : theme.disabledColor),
            tooltip: "Start New Conversation",
            onPressed: canInteract ? _startNewConversation : null,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _messages.length,
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 8.0),
                itemBuilder: (context, index) {
                  final msg = _messages[index];
                  return ChatBubble(
                    key: ValueKey("msg-${msg.hashCode}-${msg.text.length}-$index"),
                    message: msg,
                    isDarkMode: isDark,
                    theme: theme,
                  );
                },
              ),
            ),
            _buildInputAreaReverted(theme),
          ],
        ),
      ),
    );
 }

 Widget _buildInputAreaReverted(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final iconColor = isDark ? Colors.white70 : Colors.black87;
    final sendProgressColor = isDark ? Colors.white : Colors.black;
    final bool tokenLimitReached = _dailyTokenCount >= _maxDailyTokens;
    final bool canInteract = _processingStep == ProcessingStep.idle && !tokenLimitReached;

    String hintText = 'Ask a question...';
    if (widget.collegeData?['fullname'] != null && widget.collegeData!['fullname']!.isNotEmpty) {
      hintText = 'Ask about ${widget.collegeData!['fullname']}...';
    }
    if (!canInteract && _processingStep != ProcessingStep.idle) {
       final typingMsg = _messages.lastWhereOrNull((m) => m.isTyping);
       hintText = typingMsg?.text ?? "Processing...";
    } else if (_isContinuousListening) {
       hintText = 'Listening... Speak now';
    } else if (tokenLimitReached){
       hintText = 'Daily limit reached';
    }

    final threadMetrics = _calculateThreadMetrics();
    double threadCostMkw = (threadMetrics['totalCost'] ?? 0.0) * _exchangeRate;

    return Container(
      padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 10.0),
      margin: const EdgeInsets.only(bottom: 4.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
              offset: const Offset(0, -1),
              blurRadius: 3,
              color: Colors.black.withOpacity(0.08))
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_messageController.text.isNotEmpty || (threadMetrics['totalChars'] ?? 0) > 0 || _lastPromptTokenCount > 0 || _dailyTokenCount > 0 || _lastApiResponseCharCount > 0)
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 8.0, right: 8.0),
              child: DefaultTextStyle(
                 style: TextStyle(
                     color: theme.colorScheme.onSurface.withOpacity(0.7),
                     fontSize: 11,
                 ),
                 child: Wrap(
                    spacing: 10.0,
                    runSpacing: 2.0,
                    children: [
                       ValueListenableBuilder<TextEditingValue>(
                          valueListenable: _messageController,
                          builder: (context, value, child) {
                             int chars = _calculateTokenCount(value.text);
                             if (chars == 0 && _processingStep == ProcessingStep.idle && _lastPromptTokenCount == 0 && _lastApiResponseCharCount == 0) return const SizedBox.shrink();
                             double cost = (chars / 1000.0) * _inputCostPer1kChars;
                             double costMkw = cost * _exchangeRate;
                             if (chars > 0) return Text("Msg: ~${chars}c (\$${cost.toStringAsFixed(6)}/MKW${costMkw.toStringAsFixed(2)})");
                             return const SizedBox.shrink();
                          },
                       ),
                       if (_lastPromptTokenCount > 0) Text("Last Prompt: ~${_lastPromptTokenCount}c"),
                       if (_lastApiResponseCharCount > 0) Text("Last Resp: ~${_lastApiResponseCharCount}c"),
                       if ((threadMetrics['totalChars'] ?? 0) > 0) Text("Thread: ~${threadMetrics['totalChars']}c (\$${threadMetrics['totalCost'].toStringAsFixed(6)}/MKW${threadCostMkw.toStringAsFixed(2)})"),
                       Text(
                          "Daily Chars: ${_dailyTokenCount}/${_maxDailyTokens}",
                          style: TextStyle(
                             color: tokenLimitReached ? Colors.redAccent : theme.colorScheme.onSurface.withOpacity(0.7),
                             fontWeight: tokenLimitReached ? FontWeight.bold : FontWeight.normal,
                          ),
                       ),
                    ],
                 ),
              ),
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  onChanged: _handleManualInput,
                  onTap: () {
                     if (_isContinuousListening && mounted && canInteract) {
                        print("TextField tapped, stopping continuous listening.");
                        setState(() => _isContinuousListening = false);
                        _speechToText.stop();
                     }
                  },
                  maxLines: 1,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.send,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(
                      color: !canInteract
                          ? theme.disabledColor.withOpacity(0.6)
                          : (_isContinuousListening
                             ? iconColor.withOpacity(0.9)
                             : theme.hintColor.withOpacity(0.6)),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  ),
                  style: TextStyle(
                    color: canInteract ? theme.colorScheme.onSurface : theme.disabledColor,
                    fontSize: 15,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: (value) {
                    if (canInteract && value.trim().isNotEmpty) {
                      _sendMessage(value);
                    }
                  },
                  enabled: canInteract,
                  cursorColor: canInteract ? theme.colorScheme.primary : Colors.transparent,
                ),
              ),
              IconButton(
                 icon: Icon(
                   _isContinuousListening ? Icons.mic : Icons.mic_none,
                   color: canInteract ? iconColor : theme.disabledColor,
                 ),
                 tooltip: _isContinuousListening ? "Stop Listening" : "Start Continuous Listening",
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 onPressed: canInteract ? _toggleContinuousListening : null,
              ),
              (_processingStep != ProcessingStep.idle)
                  ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        width: 24, height: 24,
                        child: CircularProgressIndicator( strokeWidth: 2.5, color: sendProgressColor, ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.send,
                        color: _messageController.text.trim().isEmpty || !canInteract
                            ? theme.disabledColor
                            : sendProgressColor,
                      ),
                      tooltip: "Send Message",
                      visualDensity: VisualDensity.compact,
                      splashRadius: 20,
                      onPressed: !canInteract || _messageController.text.trim().isEmpty
                          ? null
                          : () => _sendMessage(_messageController.text),
                    ),
              IconButton(
                 icon: Icon(
                    _isMuted ? Icons.volume_off_outlined : Icons.volume_up_outlined,
                    color: canInteract ? iconColor : theme.disabledColor,
                    size: 22,
                 ),
                 visualDensity: VisualDensity.compact,
                 splashRadius: 20,
                 tooltip: _isMuted ? "Unmute TTS" : "Mute TTS",
                 onPressed: canInteract ? () {
                    if (mounted) {
                       setState(() => _isMuted = !_isMuted);
                       if (_isMuted) _flutterTts.stop();
                    }
                 } : null,
              ),
            ],
          ),
        ],
      ),
    );
 }

 bool get tokenLimitReached => _dailyTokenCount >= _maxDailyTokens;

 Map<String, dynamic> _calculateThreadMetrics() {
     int inputChars = 0;
     int outputChars = 0;
     int totalChars = 0;
     for (int i = 0; i < _messages.length; i++) {
       final msg = _messages[i];
       if (!msg.isTyping && msg.text.trim().isNotEmpty) {
         int chars = _calculateTokenCount(msg.text);
         totalChars += chars;
         bool isInitialGreeting = i == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");
         if (msg.isUser) {
           inputChars += chars;
         } else if (!isInitialGreeting) {
           outputChars += chars;
         }
       }
     }
     double inCost = (inputChars / 1000.0) * _inputCostPer1kChars;
     double outCost = (outputChars / 1000.0) * _outputCostPer1kChars;
     return {
       'inputChars': inputChars,
       'outputChars': outputChars,
       'totalChars': totalChars,
       'inputCost': inCost,
       'outputCost': outCost,
       'totalCost': inCost + outCost,
     };
 }

 void _addOrUpdateTypingIndicator(String text) {
   if (!mounted) return;
   _messages.removeWhere((m) => m.isTyping);
   final newIndicator = ChatMessage(text: text, isUser: false, isTyping: true);
   _messages.add(newIndicator);
   setState(() {});
   _scrollToBottom();
 }

 void _removeTypingIndicator() {
   if (!mounted) return;
   final initialLength = _messages.length;
   _messages.removeWhere((m) => m.isTyping);
   if (_messages.length < initialLength) {
     setState(() {});
   }
 }

 String _buildConversationHistory() {
     final sb = StringBuffer();
     const maxHistoryMessages = 6;
     final startIndex = (_messages.length > maxHistoryMessages)
         ? _messages.length - maxHistoryMessages
         : 0;
     for (int i = startIndex; i < _messages.length; i++) {
        final msg = _messages[i];
        bool isCurrentUserMessageBeingProcessed = msg.isUser &&
                                                i == _messages.length - 1 &&
                                                _processingStep != ProcessingStep.idle;
        bool isInitialGreeting = i == 0 && !msg.isUser && msg.text.contains("Hi! I'm the AI Assistant");
        if (!msg.isTyping && msg.text.trim().isNotEmpty && !isCurrentUserMessageBeingProcessed && !isInitialGreeting) {
          sb.writeln(msg.isUser ? 'user: ${msg.text}' : 'model: ${msg.text}');
        }
     }
     return sb.toString();
 }

 void _addInitialGreeting() {
     String collegeName = widget.collegeData?['fullname']?.toString().isNotEmpty == true
         ? widget.collegeData!['fullname']!
         : 'the selected institution';
     String greeting = "Hi! I'm the AI Assistant for $collegeName. How can I help you today?";
     final initialMessage = ChatMessage(text: greeting, isUser: false);
     if(mounted){
       setState(() {
           _messages.add(initialMessage);
        });
       if (!_isMuted) {
           _configureTts().then((_) {
               if (!_isMuted && mounted) _flutterTts.speak(greeting);
           });
       }
     }
 }

 void _startNewConversation() {
     if(_processingStep != ProcessingStep.idle) return;
     if (mounted) {
       if (_isContinuousListening) {
           setState(() => _isContinuousListening = false);
           _speechToText.stop();
       }
       setState(() {
           _messages.clear();
           _lastPromptTokenCount = 0;
           _lastApiResponseCharCount = 0;
           // _dailyTokenCount = 0; // Daily count should persist across conversations within a day
       });
       _addInitialGreeting();
     }
 }

 void _showError(String errorMessageText) {
     final errorMsg = ChatMessage(text: errorMessageText, isUser: false);
     if (mounted) {
       _removeTypingIndicator();
       setState(() { _messages.add(errorMsg); });
       _scrollToBottom();
       if (!_isMuted) {
           _configureTts().then((_) {
               if(!_isMuted && mounted) _flutterTts.speak(errorMessageText);
           });
       }
     }
 }

 void _scrollToBottom() {
     WidgetsBinding.instance.addPostFrameCallback((_) {
         if (_scrollController.hasClients && mounted) {
             _scrollController.animateTo(
                 _scrollController.position.maxScrollExtent,
                 duration: const Duration(milliseconds: 300),
                 curve: Curves.easeOut);
         }
     });
 }

 List<String> _getRelevantCollegeFields(String query, {int max = 7}) {
      final lowerQuery = query.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), '');
      final scores = <String, int>{};

      scores['fullname'] = 100;
      scores['about'] = 50;
      scores['website'] = 50;
      scores['address'] = 40; scores['city'] = 40; scores['state'] = 40;
      scores['phone'] = 40; scores['email'] = 40;
      scores['mission'] = 30; scores['vision'] = 30; scores['motto'] = 25;
      scores['daysnhours'] = 25;
      scores['institutiontype'] = 20;
      scores['accreditation'] = 20;
      scores['founded'] = 15;
      scores['studentpopulation'] = 15;
      scores['campussetting'] = 10;

      _collegeFieldKeywords.forEach((field, keywords) {
        int currentScore = scores[field] ?? 0;
        for (var kw in keywords) {
          if (lowerQuery.contains(kw)) {
             currentScore += (kw.contains(' ') ? 3 : 1);
             if (RegExp(r'\b' + RegExp.escape(kw) + r'\b').hasMatch(lowerQuery)) {
                currentScore += 2;
             }
          }
        }
        if (RegExp(r'\b' + RegExp.escape(field.toLowerCase()) + r'\b', caseSensitive: false).hasMatch(lowerQuery)) {
           currentScore += 5;
        }
        if (currentScore > (scores[field] ?? 0) ) {
           scores[field] = currentScore;
        }
      });

      final scoredFields = scores.keys.where((key) => scores[key]! > 0).toList();
      scoredFields.sort((a, b) => scores[b]!.compareTo(scores[a]!));
      List<String> finalFields = scoredFields.take(max).toList();

      if (scores['fullname']! > 0 && !finalFields.contains('fullname')) {
          if (finalFields.length >= max) finalFields.removeLast();
          finalFields.insert(0, 'fullname');
      } else if (finalFields.contains('fullname')) {
          finalFields.remove('fullname');
          finalFields.insert(0, 'fullname');
      }
      final selectedScores = Map.fromEntries(finalFields.map((f) => MapEntry(f, scores[f] ?? 0)));
      print("Relevant college fields selected for query context: $finalFields (Scores: $selectedScores)");
      return finalFields;
 }

 int _calculateTokenCount(String text) {
    return text.trim().length;
 }

}