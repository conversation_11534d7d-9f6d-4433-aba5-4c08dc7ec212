// videos_page.dart
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:intl/intl.dart';

import 'login_page.dart';
import 'video_detail_page.dart';

class VideosPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedVideos;
  final bool isFromDetailPage;

  const VideosPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedVideos,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends State<VideosPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('videos_list');
  List<Map<String, dynamic>> _videos = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("VideosPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant VideosPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("VideosPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("VideosPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("VideosPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedVideos != null && widget.preloadedVideos!.isNotEmpty) {
      print("Preloaded videos found, using them.");
      setState(() {
        _videos = List<Map<String, dynamic>>.from(widget.preloadedVideos!);
        _videos.forEach((video) {
          video['_isImageLoading'] = false;
        });
        _videos.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _hasMore = widget.preloadedVideos!.length == _pageSize;
      });
      _loadVideosFromSupabase(initialLoad: false);
    } else {
      print("No preloaded videos or empty list, loading from database.");
      _loadVideosFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadVideosFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadVideosFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final videosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_videos';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(videosTableName)
          .select('*');
      
      // Apply ordering and pagination
      final response = await query
          .order('year', ascending: false)
          .order('month', ascending: false)
          .order('day', ascending: false)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedVideos =
          await _updateVideoImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _videos = updatedVideos;
        } else {
          _videos.addAll(updatedVideos);
        }
        _videos.forEach((video) {
          video['_isImageLoading'] = false;
        });
        _videos.sort((a, b) {
          final aYear = a['year'] ?? 0;
          final bYear = b['year'] ?? 0;
          if (aYear != bYear) return bYear.compareTo(aYear);
          
          final aMonth = a['month'] ?? 0;
          final bMonth = b['month'] ?? 0;
          if (aMonth != bMonth) return bMonth.compareTo(aMonth);
          
          final aDay = a['day'] ?? 0;
          final bDay = b['day'] ?? 0;
          return bDay.compareTo(aDay);
        });
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
      
      // Cache the videos
      _cacheVideos(_videos);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching videos: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }
  
  Future<List<Map<String, dynamic>>> _updateVideoImageUrls(
      List<Map<String, dynamic>> videos) async {
    List<Future<void>> futures = [];
    for (final video in videos) {
      if (video['image_url'] == null ||
          video['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(video));
      }
    }
    await Future.wait(futures);
    return videos;
  }

  Future<void> _cacheVideos(List<Map<String, dynamic>> videos) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String videosJson = jsonEncode(videos);
      await prefs.setString(
          'videos_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          videosJson);
    } catch (e) {
      print('Error caching videos: $e');
    }
  }
  
  void _setupRealtime() {
    final videosTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_videos';
    _realtimeChannel = Supabase.instance.client
        .channel('videos')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: videosTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newVideoId = payload.newRecord['id'];
          final newVideoResponse = await Supabase.instance.client
              .from(videosTableName)
              .select('*')
              .eq('id', newVideoId)
              .single();
          if (mounted) {
            Map<String, dynamic> newVideo = Map.from(newVideoResponse);
            final updatedVideo = await _updateVideoImageUrls([newVideo]);
            setState(() {
              _videos = [..._videos, updatedVideo.first];
              updatedVideo.first['_isImageLoading'] = false;
              _videos.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedVideoId = payload.newRecord['id'];
          final updatedVideoResponse = await Supabase.instance.client
              .from(videosTableName)
              .select('*')
              .eq('id', updatedVideoId)
              .single();
          if (mounted) {
            final updatedVideo = Map<String, dynamic>.from(updatedVideoResponse);
            setState(() {
              _videos = _videos.map((video) {
                return video['id'] == updatedVideo['id'] ? updatedVideo : video;
              }).toList();
              _videos.sort((a, b) {
                final aYear = a['year'] ?? 0;
                final bYear = b['year'] ?? 0;
                if (aYear != bYear) return bYear.compareTo(aYear);
                
                final aMonth = a['month'] ?? 0;
                final bMonth = b['month'] ?? 0;
                if (aMonth != bMonth) return bMonth.compareTo(aMonth);
                
                final aDay = a['day'] ?? 0;
                final bDay = b['day'] ?? 0;
                return bDay.compareTo(aDay);
              });
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedVideoId = payload.oldRecord['id'];
          setState(() {
            _videos.removeWhere((video) => video['id'] == deletedVideoId);
          });
        }
      },
    ).subscribe();
  }
  
  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreVideos();
    }
  }

  Future<void> _loadMoreVideos() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadVideosFromSupabase(initialLoad: false);
    }
  }
  
  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }
  
  Future<void> _fetchImageUrl(Map<String, dynamic> video) async {
    if (video['_isImageLoading'] == true) {
      print('Image loading already in progress for ${video['fullname']}, skipping.');
      return;
    }
    if (video['image_url'] != null &&
        video['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${video['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      video['_isImageLoading'] = true;
    });

    final fullname = video['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeVideoBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/videos';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeVideoBucket');
    print('Image URL before fetch: ${video['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeVideoBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeVideoBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        video['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        video['_isImageLoading'] = false;
        print('Setting image_url for ${video['fullname']} to: ${video['image_url']}');
      });
    } else {
      video['_isImageLoading'] = false;
    }
  }
  
  void _navigateToDetail(BuildContext context, Map<String, dynamic> video) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoDetailPage(
            video: video,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch $url')),
      );
    }
  }

  String _formatDate(Map<String, dynamic> video) {
    final day = video['day'] as int?;
    final month = video['month'] as int?;
    final year = video['year'] as int?;
    
    if (day == null || month == null || year == null) {
      return '';
    }
    
    try {
      final date = DateTime(year, month, day);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("VideosPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Videos',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _videos.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadVideosFromSupabase(initialLoad: true),
              child: _videos.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No videos available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _videos.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _videos.length) {
                          final video = _videos[index];
                          final hasLink = video['link'] != null && video['link'].toString().isNotEmpty;
                          final dateStr = _formatDate(video);
                          final platform = video['platform'] as String? ?? '';
                          
                          return VisibilityDetector(
                            key: Key('video_${video['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (video['image_url'] == null ||
                                      video['image_url'] == 'assets/placeholder_image.png') &&
                                  !video['_isImageLoading']) {
                                _fetchImageUrl(video);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                children: [
                                  Stack(
                                    children: [
                                      AspectRatio(
                                        aspectRatio: 16 / 9,
                                        child: CachedNetworkImage(
                                          imageUrl: video['image_url'] ??
                                              'assets/placeholder_image.png',
                                          errorWidget: (context, url, error) =>
                                              Image.asset('assets/placeholder_image.png'),
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                        ),
                                      ),
                                      Positioned.fill(
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: () => _navigateToDetail(context, video),
                                            child: Center(
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: Colors.black.withOpacity(0.5),
                                                  shape: BoxShape.circle,
                                                ),
                                                padding: const EdgeInsets.all(12),
                                                child: const Icon(
                                                  Icons.play_arrow,
                                                  color: Colors.white,
                                                  size: 32,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                video['fullname'] ?? 'Unnamed Video',
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                  color: theme.colorScheme.onSurface,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 2,
                                              ),
                                            ),
                                            if (hasLink)
                                              IconButton(
                                                icon: Icon(
                                                  Icons.link,
                                                  color: theme.colorScheme.primary,
                                                  size: 20,
                                                ),
                                                onPressed: () => _launchURL(video['link']),
                                                tooltip: 'Watch video',
                                              ),
                                          ],
                                        ),
                                        if (dateStr.isNotEmpty) ...[
                                          const SizedBox(height: 4),
                                          Text(
                                            dateStr,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: theme.colorScheme.secondary,
                                            ),
                                          ),
                                        ],
                                        if (platform.isNotEmpty) ...[
                                          const SizedBox(height: 4),
                                          Text(
                                            'Platform: $platform',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: theme.colorScheme.secondary,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
