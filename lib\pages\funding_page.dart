import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'funding_detail_page.dart';
import 'login_page.dart';

class FundingPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedFunding;
  final bool isFromDetailPage;

  const FundingPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedFunding,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _FundingPageState createState() => _FundingPageState();
}

class _FundingPageState extends State<FundingPage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('funding_list');
  bool _isDisposed = false;
  List<Map<String, dynamic>> _funding = [];
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  String _selectedMajor = 'All Majors';
  List<String> _majors = ['All Majors'];

  @override
  void initState() {
    super.initState();
    print("FundingPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  void _loadInitialData() {
    if (widget.preloadedFunding != null &&
        widget.preloadedFunding!.isNotEmpty) {
      setState(() {
        _funding = List.from(widget.preloadedFunding!);
        _isLoading = false;
        _extractMajors();
      });
    } else {
      _loadFundingFromDatabase();
    }
  }

  void _extractMajors() {
    Set<String> majorsSet = {'All Majors'};

    for (var fund in _funding) {
      if (fund['major'] != null && fund['major'].toString().isNotEmpty) {
        majorsSet.add(fund['major']);
      }
    }

    setState(() {
      _majors = majorsSet.toList()..sort();
    });
  }

  void _setupRealtime() {
    final fundingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_funding';
    _realtimeChannel = Supabase.instance.client
        .channel('funding_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: fundingTableName,
      callback: (payload) async {
        print("Realtime update received for funding: ${payload.eventType}");
        _loadFundingFromDatabase();
      },
    ).subscribe();
  }

  Future<void> _loadFundingFromDatabase() async {
    if (_isLoading || _isDisposed) return;

    setState(() {
      _isLoading = true;
      _page = 0;
      _hasMore = true;
      _funding = [];
    });

    await _loadMoreFunding();
  }

  Future<void> _loadMoreFunding() async {
    if (_isLoading || !_hasMore || _isDisposed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final fundingTableName = '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_funding';
      // Build the query string parts
      List<String> conditions = [];

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        conditions.add("fullname.ilike.%${_searchQuery}%");
      }

      // Apply major filter
      if (_selectedMajor != 'All Majors') {
        conditions.add("major.eq.${_selectedMajor}");
      }

      // Apply pagination
      final start = _page * _pageSize;
      final end = (_page + 1) * _pageSize - 1;

      // Execute the query
      final response = await Supabase.instance.client
          .from(fundingTableName)
          .select('*')
          .or(conditions.join(','))
          .order('fullname', ascending: true)
          .range(start, end);

      if (_isDisposed) return;

      setState(() {
        if (response.isEmpty) {
          _hasMore = false;
        } else {
          _funding.addAll(List<Map<String, dynamic>>.from(response));
          _page++;
        }
        _isLoading = false;
      });

      if (_page == 1) {
        _extractMajors();
      }
    } catch (e) {
      if (_isDisposed) return;
      setState(() {
        _isLoading = false;
      });
      print("Error loading funding: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading funding: $e')),
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreFunding();
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadFundingFromDatabase();
  }

  void _filterByMajor(String major) {
    setState(() {
      _selectedMajor = major;
    });
    _loadFundingFromDatabase();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Scholarships & Funding',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search scholarships and funding...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onSubmitted: _performSearch,
            ),
          ),

          // Filters
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Major',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              value: _selectedMajor,
              items: _majors.map((String major) {
                return DropdownMenuItem<String>(
                  value: major,
                  child: Text(
                    major,
                    style: TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  _filterByMajor(newValue);
                }
              },
            ),
          ),

          const SizedBox(height: 16),

          // Funding list
          Expanded(
            child: VisibilityDetector(
              key: const Key('funding_list_visibility'),
              onVisibilityChanged: (info) {
                if (info.visibleFraction > 0 && _funding.isEmpty && !_isLoading) {
                  _loadFundingFromDatabase();
                }
              },
              child: RefreshIndicator(
                onRefresh: _loadFundingFromDatabase,
                child: _funding.isEmpty && !_isLoading
                    ? Center(
                        child: Text(
                          'No scholarships or funding found',
                          style: TextStyle(color: theme.colorScheme.onSurface),
                        ),
                      )
                    : ListView.builder(
                        key: _listKey,
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _funding.length + (_hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _funding.length) {
                            return _buildLoadingIndicator();
                          }
                          return _buildFundingCard(
                            _funding[index],
                            theme,
                            currentIsDarkMode,
                          );
                        },
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildFundingCard(
    Map<String, dynamic> funding,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final String fullname = funding['fullname'] ?? 'Unknown';
    final String major = funding['major'] ?? '';
    final String about = funding['about'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FundingDetailPage(
                funding: funding,
                isDarkMode: isDarkMode,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.1),
                child: Icon(
                  Icons.monetization_on,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (major.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'For: $major',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
