import 'dart:io' as io;
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';  // Add this for Clipboard
import 'package:google_fonts/google_fonts.dart';
import 'package:file_picker/file_picker.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as syncfusion_pdf;
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:universal_html/html.dart' as html;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class ResumeTailoringPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const ResumeTailoringPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<ResumeTailoringPage> createState() => _ResumeTailoringPageState();
}

class _ResumeTailoringPageState extends State<ResumeTailoringPage> {
  final TextEditingController _jobDescriptionController = TextEditingController();
  final TextEditingController _jobLinkController = TextEditingController();

  PlatformFile? _resumeFile;
  Uint8List? _resumeBytes;
  String? _resumeText;

  PlatformFile? _jobDescriptionFile;
  Uint8List? _jobDescriptionBytes;
  String? _jobDescriptionText;

  String? _tailoredResumeText;
  String? _coverLetterText;

  // Interview preparation content
  String? _commonQuestionsContent;
  String? _behavioralQuestionsContent;
  String? _caseQuestionsContent;
  String? _technicalQuestionsContent;
  String? _referencesContent;

  bool _isProcessing = false;
  String _processingType = '';
  double _processingProgress = 0.0;

  // Initialize Gemini
  final _geminiApiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your API key
  late final GenerativeModel _geminiModel;

  @override
  void initState() {
    super.initState();
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash',
      apiKey: _geminiApiKey,
    );
  }

  @override
  void dispose() {
    _jobDescriptionController.dispose();
    _jobLinkController.dispose();
    super.dispose();
  }

  Future<void> _pickResumeFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _resumeFile = result.files.first;
          _resumeBytes = _resumeFile!.bytes;
          _resumeText = null; // Reset text when new file is picked
        });

        // Extract text from the resume file
        _extractTextFromFile(_resumeFile!).then((text) {
          setState(() {
            _resumeText = text;
          });
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking resume file: $e')),
      );
    }
  }

  Future<void> _pickJobDescriptionFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _jobDescriptionFile = result.files.first;
          _jobDescriptionBytes = _jobDescriptionFile!.bytes;
          _jobDescriptionText = null; // Reset text when new file is picked
        });

        // Extract text from the job description file
        _extractTextFromFile(_jobDescriptionFile!).then((text) {
          setState(() {
            _jobDescriptionText = text;
          });
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking job description file: $e')),
      );
    }
  }

  Future<String> _extractTextFromFile(PlatformFile file) async {
    final fileName = file.name.toLowerCase();
    final fileBytes = file.bytes;

    if (fileBytes == null) {
      return 'Unable to read file content';
    }

    try {
      if (fileName.endsWith('.pdf')) {
        // Extract text from PDF
        final syncfusion_pdf.PdfDocument document = syncfusion_pdf.PdfDocument(inputBytes: fileBytes);
        final String text = syncfusion_pdf.PdfTextExtractor(document).extractText();
        document.dispose();
        return text;
      } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
        // For Word documents, we would need a more complex solution
        // This is a placeholder - in a real app, you'd use a Word document parser
        return 'Word document parsing not implemented. Please copy and paste the content.';
      } else if (fileName.endsWith('.txt')) {
        // For text files, just convert bytes to string
        return String.fromCharCodes(fileBytes);
      } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png')) {
        // For images, we would need OCR
        // This is a placeholder - in a real app, you'd use an OCR service
        return 'Image text extraction not implemented. Please copy and paste the content.';
      } else {
        return 'Unsupported file format';
      }
    } catch (e) {
      return 'Error extracting text: $e';
    }
  }

  Future<void> _processResume() async {
    if (_resumeText == null || _resumeText!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please upload a resume or CV')),
      );
      return;
    }

    if ((_jobDescriptionText == null || _jobDescriptionText!.isEmpty) &&
        (_jobDescriptionController.text.isEmpty) &&
        (_jobLinkController.text.isEmpty)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a job description')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'resume_tailoring';
      _processingProgress = 0.1;
      _tailoredResumeText = null;
      _coverLetterText = null;
    });

    try {
      // Determine which job description source to use
      String jobDescription = '';
      if (_jobDescriptionText != null && _jobDescriptionText!.isNotEmpty) {
        jobDescription = _jobDescriptionText!;
      } else if (_jobDescriptionController.text.isNotEmpty) {
        jobDescription = _jobDescriptionController.text;
      } else if (_jobLinkController.text.isNotEmpty) {
        // In a real app, you would fetch the content from the URL
        // This is a placeholder
        jobDescription = 'Job description from link: ${_jobLinkController.text}';
      }

      setState(() {
        _processingProgress = 0.3;
      });

      // Generate tailored resume optimized for ATS systems
      final resumePrompt = '''
      I need to tailor my resume for a specific job and ensure it passes through Applicant Tracking Systems (ATS). Here is my current resume:

      ${_resumeText}

      And here is the job description:

      $jobDescription

      Please rewrite my resume to better match this job description and optimize it for ATS systems. Follow these guidelines:

      1. Use a clean, ATS-friendly format with standard section headings
      2. Include exact keywords and phrases from the job description (80-90% match rate)
      3. Avoid tables, columns, headers, footers, images, or complex formatting that ATS systems struggle with
      4. Use standard section titles like "Experience" instead of creative ones like "Career Journey"
      5. Include a skills section with relevant hard skills mentioned in the job description
      6. Quantify achievements with metrics where possible
      7. Use standard bullet points (•) rather than fancy symbols
      8. Ensure all dates are in a consistent format (MM/YYYY)

      Format it professionally with these sections:
      - Contact Information (keep my original contact info)
      - Professional Summary (tailored to the job with keywords)
      - Skills (prioritize relevant ones mentioned in the job description)
      - Work Experience (emphasize relevant achievements with keywords)
      - Education

      Return the result as a well-formatted document that will pass through ATS systems and also impress human recruiters.
      ''';

      final resumeResponse = await _geminiModel.generateContent([Content.text(resumePrompt)]);
      final tailoredResume = resumeResponse.text;

      setState(() {
        _processingProgress = 0.7;
        _tailoredResumeText = tailoredResume;
      });

      // Generate ATS-friendly cover letter
      final coverLetterPrompt = '''
      Based on my resume and a job description, create a professional cover letter that will pass through Applicant Tracking Systems (ATS) and impress human recruiters.

      My resume:
      ${_resumeText}

      Job description:
      $jobDescription

      Create a compelling, ATS-friendly cover letter that:
      1. Uses a clean, standard business letter format that ATS systems can parse
      2. Starts with a professional greeting (ideally with the hiring manager's name if available)
      3. Has an engaging introduction that mentions the specific position and job ID/reference number if provided
      4. Incorporates 8-10 exact keywords and phrases from the job description naturally throughout the text
      5. Highlights 3-4 of my most relevant qualifications for this role with specific examples
      6. Explains why I'm interested in this position and company
      7. Includes a call to action in the conclusion
      8. Ends with a professional sign-off
      9. Avoids graphics, tables, columns, or unusual formatting that could confuse ATS systems

      Format it as a proper business letter with appropriate spacing and structure, using plain text formatting that will be properly parsed by ATS systems.
      ''';

      final coverLetterResponse = await _geminiModel.generateContent([Content.text(coverLetterPrompt)]);
      final coverLetter = coverLetterResponse.text;

      setState(() {
        _processingProgress = 1.0;
        _coverLetterText = coverLetter;
        _isProcessing = false;
        _processingType = '';
      });

    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error processing resume: $e')),
      );
    }
  }

  Future<void> _exportToPdf(String content, String fileName) async {
    try {
      final pdf = pw.Document(
        title: fileName.replaceAll('.pdf', ''),
        author: 'Resume Tailoring Tool',
        creator: 'Harmonizr360',
        subject: 'Job Application Document',
        keywords: 'resume, cover letter, job application',
        producer: 'Harmonizr360 PDF Generator',
      );

      // Process content to ensure it's ATS-friendly
      // Split into paragraphs for better formatting
      final paragraphs = content.split('\n\n');

      // Add content to PDF in an ATS-friendly format
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(25.4), // 1-inch margins (25.4mm)
          build: (pw.Context context) {
            List<pw.Widget> widgets = [];

            // Process each paragraph
            for (var paragraph in paragraphs) {
              // Check if it's a heading (starts with # or is all caps)
              if (paragraph.trim().startsWith('#') ||
                  (paragraph.trim().toUpperCase() == paragraph.trim() &&
                   paragraph.trim().length > 3)) {
                // It's a heading - make it bold
                widgets.add(
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 10, bottom: 5),
                    child: pw.Text(
                      paragraph.replaceAll('#', '').trim(),
                      style: pw.TextStyle(
                        fontSize: 14,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  )
                );
              }
              // Check if it's a bullet point list
              else if (paragraph.contains('\n- ') || paragraph.contains('\n• ')) {
                final listItems = paragraph.split('\n')
                    .where((line) => line.trim().startsWith('- ') || line.trim().startsWith('• '))
                    .map((line) => line.replaceAll(RegExp(r'^[-•]\s+'), '').trim())
                    .toList();

                if (listItems.isNotEmpty) {
                  widgets.add(
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(top: 5, bottom: 5),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: listItems.map((item) =>
                          pw.Padding(
                            padding: const pw.EdgeInsets.only(bottom: 2),
                            child: pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.SizedBox(width: 10),
                                pw.Text('•', style: pw.TextStyle(fontSize: 12)),
                                pw.SizedBox(width: 5),
                                pw.Expanded(
                                  child: pw.Text(item, style: pw.TextStyle(fontSize: 12))
                                ),
                              ],
                            ),
                          )
                        ).toList(),
                      ),
                    )
                  );
                }
              }
              // Regular paragraph
              else {
                widgets.add(
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 5, bottom: 5),
                    child: pw.Text(
                      paragraph.trim(),
                      style: pw.TextStyle(fontSize: 12),
                    ),
                  )
                );
              }
            }

            return widgets;
          },
          footer: fileName.contains('resume') ? null : (pw.Context context) {
            // Only add page numbers for cover letters, not resumes
            return pw.Container(
              alignment: pw.Alignment.centerRight,
              margin: const pw.EdgeInsets.only(top: 0.5 * PdfPageFormat.cm),
              child: pw.Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: pw.TextStyle(
                  color: PdfColors.grey,
                  fontSize: 10,
                ),
              ),
            );
          },
        ),
      );

      // Save PDF
      final Uint8List pdfBytes = await pdf.save();

      if (kIsWeb) {
        // Web platform download
        final blob = html.Blob([pdfBytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', fileName)
          ..click();
        html.Url.revokeObjectUrl(url);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Downloading $fileName')),
        );
      } else {
        // Mobile platforms (Android and iOS)
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        // Get downloads directory or fallback to temporary directory
        final dir = await getTemporaryDirectory();
        final filePath = path.join(dir.path, fileName);

        // Write PDF to file
        final file = io.File(filePath);
        await file.writeAsBytes(pdfBytes);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF saved to $filePath')),
        );

        // Open the PDF file
        await _openFile(filePath);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting to PDF: $e')),
      );
    }
  }

  Future<void> _exportToWord(String content, String fileName) async {
    try {
      // In a real app, you would use a library to create a Word document
      // This is a simplified version that creates a text file with ATS-friendly formatting

      // Process content to ensure it's ATS-friendly
      // Add proper formatting for ATS systems
      String formattedContent = content;

      // Ensure proper line breaks for paragraphs
      formattedContent = formattedContent.replaceAll('\n\n', '\r\n\r\n');

      // Ensure bullet points are properly formatted
      formattedContent = formattedContent.replaceAll('\n- ', '\r\n• ');
      formattedContent = formattedContent.replaceAll('\n• ', '\r\n• ');

      // Convert to bytes with UTF-8 encoding to preserve special characters
      final Uint8List contentBytes = Uint8List.fromList(utf8.encode(formattedContent));

      if (kIsWeb) {
        // Web platform download
        final blob = html.Blob([contentBytes], 'text/plain');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', fileName)
          ..click();
        html.Url.revokeObjectUrl(url);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Downloading $fileName')),
        );
      } else {
        // Mobile platforms (Android and iOS)
        if (io.Platform.isAndroid || io.Platform.isIOS) {
          if (!await _requestStoragePermission()) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Storage permission denied')),
            );
            return;
          }
        }

        // Get temporary directory
        final tempDir = await getTemporaryDirectory();
        final filePath = path.join(tempDir.path, fileName);

        // Write content to file
        final file = io.File(filePath);
        await file.writeAsBytes(contentBytes);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Document saved as ${fileName}. Opening file...'),
            duration: const Duration(seconds: 2),
          ),
        );

        // Open the file
        await _openFile(filePath);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting to Word: $e')),
      );
    }
  }

  Future<void> _openFile(String filePath) async {
    try {
      final uri = Uri.file(filePath);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw 'Could not launch $uri';
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error opening file: $e')),
      );
    }
  }

  Future<bool> _requestStoragePermission() async {
    if (io.Platform.isAndroid || io.Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        return status.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<void> _generateCommonQuestions() async {
    final jobTitle = _jobDescriptionText ?? _jobDescriptionController.text.trim();

    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide job description information')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'common';
      _processingProgress = 0.2;
      _commonQuestionsContent = null;
    });

    try {
      final prompt = '''
      Based on this job description:

      $jobTitle

      Create a comprehensive list of 15 common interview questions for this position.

      For each question:
      1. Provide the question
      2. Explain why interviewers ask this question
      3. Give a strong sample answer (200-300 words)
      4. Include 3-4 key points that should be covered in the answer

      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';

      setState(() {
        _processingProgress = 0.5;
      });

      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;

      setState(() {
        _processingProgress = 1.0;
        _commonQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating common questions: $e')),
      );
    }
  }

  Future<void> _generateBehavioralQuestions() async {
    final jobTitle = _jobDescriptionText ?? _jobDescriptionController.text.trim();

    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide job description information')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'behavioral';
      _processingProgress = 0.2;
      _behavioralQuestionsContent = null;
    });

    try {
      final prompt = '''
      Based on this job description:

      $jobTitle

      Create a comprehensive list of 10 behavioral interview questions for this position.

      For each question:
      1. Provide the behavioral question
      2. Explain what skill or competency this question is assessing
      3. Give a strong sample answer using the STAR method (Situation, Task, Action, Result)
      4. Include tips for how to structure a compelling response

      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';

      setState(() {
        _processingProgress = 0.5;
      });

      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;

      setState(() {
        _processingProgress = 1.0;
        _behavioralQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating behavioral questions: $e')),
      );
    }
  }

  Future<void> _generateCaseQuestions() async {
    final jobTitle = _jobDescriptionText ?? _jobDescriptionController.text.trim();

    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide job description information')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'case';
      _processingProgress = 0.2;
      _caseQuestionsContent = null;
    });

    try {
      final prompt = '''
      Based on this job description:

      $jobTitle

      Create a comprehensive list of 5 case interview questions for this position.

      For each case question:
      1. Provide the case scenario and question
      2. Explain the purpose of this case question and what skills it tests
      3. Outline a structured approach to solving the case
      4. Provide a sample solution with key insights and recommendations
      5. Include common pitfalls to avoid

      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';

      setState(() {
        _processingProgress = 0.5;
      });

      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;

      setState(() {
        _processingProgress = 1.0;
        _caseQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating case questions: $e')),
      );
    }
  }

  Future<void> _generateTechnicalQuestions() async {
    final jobTitle = _jobDescriptionText ?? _jobDescriptionController.text.trim();

    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide job description information')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'technical';
      _processingProgress = 0.2;
      _technicalQuestionsContent = null;
    });

    try {
      final prompt = '''
      Based on this job description:

      $jobTitle

      Create a comprehensive list of 10 technical interview questions for this position.

      For each technical question:
      1. Provide the technical question
      2. Explain what specific knowledge or skill this question is testing
      3. Give a detailed sample answer that demonstrates expertise
      4. Include any relevant code snippets, formulas, or technical concepts if applicable

      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';

      setState(() {
        _processingProgress = 0.5;
      });

      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;

      setState(() {
        _processingProgress = 1.0;
        _technicalQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating technical questions: $e')),
      );
    }
  }

  Future<void> _generateReferences() async {
    final jobTitle = _jobDescriptionText ?? _jobDescriptionController.text.trim();

    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide job description information')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
      _processingType = 'references';
      _processingProgress = 0.2;
      _referencesContent = null;
    });

    try {
      final prompt = '''
      Based on this job description:

      $jobTitle

      Create 3 sample professional references for this position.

      For each reference:
      1. Create a fictional reference with name, title, company, and contact information
      2. Describe the professional relationship (e.g., former manager, colleague, client)
      3. List 5 specific questions an employer might ask this reference
      4. Provide detailed sample answers that the reference might give, highlighting the candidate's strengths and accomplishments

      Format the output with clear headings and well-structured content.
      ''';

      setState(() {
        _processingProgress = 0.5;
      });

      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;

      setState(() {
        _processingProgress = 1.0;
        _referencesContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating references: $e')),
      );
    }
  }

  Widget _buildInterviewQuestionsCard(String title, String content, String fileName, IconData icon, Color textColor, ThemeData theme) {
    return Card(
      color: theme.colorScheme.surface,
      margin: const EdgeInsets.only(top: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(icon, color: textColor),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.picture_as_pdf, color: textColor),
                      onPressed: () => _exportToPdf(content, fileName),
                      tooltip: 'Export as PDF',
                    ),
                    IconButton(
                      icon: Icon(Icons.description, color: textColor),
                      onPressed: () => _exportToWord(content, fileName.replaceAll('.pdf', '.docx')),
                      tooltip: 'Export as Word',
                    ),
                    IconButton(
                      icon: Icon(Icons.copy, color: textColor),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: content));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('$title copied to clipboard')),
                        );
                      },
                      tooltip: 'Copy to Clipboard',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey[850] : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              height: 300,
              child: SingleChildScrollView(
                child: MarkdownBody(
                  data: content,
                  styleSheet: MarkdownStyleSheet.fromTheme(theme).copyWith(
                    p: GoogleFonts.notoSans(
                      color: textColor,
                      fontSize: 14,
                      height: 1.5,
                    ),
                    h1: GoogleFonts.notoSans(
                      color: textColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    h2: GoogleFonts.notoSans(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    h3: GoogleFonts.notoSans(
                      color: textColor,
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                    listBullet: GoogleFonts.notoSans(
                      color: textColor,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Resume Tailoring',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
            tooltip: 'Toggle Theme',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Resume/CV Upload Section
            Card(
              color: theme.colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Upload Resume/CV',
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Center(
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.upload_file, color: buttonTextColor),
                        label: Text(
                          'Select Resume/CV',
                          style: GoogleFonts.notoSans(color: buttonTextColor),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: buttonBackground,
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                        ),
                        onPressed: _pickResumeFile,
                      ),
                    ),
                    if (_resumeFile != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.description,
                              color: generalTextColor.withOpacity(0.6),
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _resumeFile!.name,
                                style: GoogleFonts.notoSans(
                                  color: generalTextColor.withOpacity(0.6),
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Job Description Section
            Card(
              color: theme.colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Job Description',
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Upload Job Description File
                    Center(
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.upload_file, color: buttonTextColor),
                        label: Text(
                          'Upload Job Description',
                          style: GoogleFonts.notoSans(color: buttonTextColor),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: buttonBackground,
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                        ),
                        onPressed: _pickJobDescriptionFile,
                      ),
                    ),
                    if (_jobDescriptionFile != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.description,
                              color: generalTextColor.withOpacity(0.6),
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _jobDescriptionFile!.name,
                                style: GoogleFonts.notoSans(
                                  color: generalTextColor.withOpacity(0.6),
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 16),
                    Text(
                      'OR',
                      style: GoogleFonts.notoSans(
                        fontSize: 16,
                        color: generalTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    // Paste Job Description
                    Text(
                      'Paste Job Description',
                      style: GoogleFonts.notoSans(
                        fontSize: 16,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _jobDescriptionController,
                      maxLines: 5,
                      decoration: InputDecoration(
                        hintText: 'Paste job description here...',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.all(12),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),

                    const SizedBox(height: 16),
                    Text(
                      'OR',
                      style: GoogleFonts.notoSans(
                        fontSize: 16,
                        color: generalTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    // Job Description Link
                    Text(
                      'Job Description Link',
                      style: GoogleFonts.notoSans(
                        fontSize: 16,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _jobLinkController,
                      decoration: InputDecoration(
                        hintText: 'Enter job posting URL...',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.all(12),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // ATS Information Card
            Card(
              color: theme.colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: generalTextColor),
                        const SizedBox(width: 8),
                        Text(
                          'ATS-Optimized Documents',
                          style: GoogleFonts.notoSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: generalTextColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Your resume and cover letter will be optimized for Applicant Tracking Systems (ATS) to ensure they pass through automated screening. The documents will include relevant keywords from the job description while maintaining a clean, professional format that both ATS systems and human recruiters can easily read.',
                      style: GoogleFonts.notoSans(
                        color: generalTextColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Process Button
            ElevatedButton.icon(
              icon: _isProcessing && _processingType == 'resume_tailoring'
                  ? const SizedBox()
                  : Icon(Icons.auto_awesome, color: buttonTextColor),
              label: Text(
                _isProcessing && _processingType == 'resume_tailoring' ? 'Processing...' : 'Tailor Resume & Generate Cover Letter',
                style: GoogleFonts.notoSans(color: buttonTextColor),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: buttonBackground,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              onPressed: _isProcessing ? null : _processResume,
            ),

            const SizedBox(height: 24),

            // Interview Preparation Section
            Card(
              color: theme.colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Interview Preparation',
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Common Questions Button
                    ElevatedButton.icon(
                      icon: _isProcessing && _processingType == 'common'
                          ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                          : Icon(Icons.question_answer, color: buttonTextColor),
                      label: Text(
                        _isProcessing && _processingType == 'common' ? 'Generating...' : 'Generate Common Interview Questions',
                        style: GoogleFonts.notoSans(color: buttonTextColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBackground,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size(double.infinity, 45),
                      ),
                      onPressed: _isProcessing ? null : _generateCommonQuestions,
                    ),

                    const SizedBox(height: 12),

                    // Behavioral Questions Button
                    ElevatedButton.icon(
                      icon: _isProcessing && _processingType == 'behavioral'
                          ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                          : Icon(Icons.psychology, color: buttonTextColor),
                      label: Text(
                        _isProcessing && _processingType == 'behavioral' ? 'Generating...' : 'Generate Behavioral Questions',
                        style: GoogleFonts.notoSans(color: buttonTextColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBackground,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size(double.infinity, 45),
                      ),
                      onPressed: _isProcessing ? null : _generateBehavioralQuestions,
                    ),

                    const SizedBox(height: 12),

                    // Case Questions Button
                    ElevatedButton.icon(
                      icon: _isProcessing && _processingType == 'case'
                          ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                          : Icon(Icons.business_center, color: buttonTextColor),
                      label: Text(
                        _isProcessing && _processingType == 'case' ? 'Generating...' : 'Generate Case Interview Questions',
                        style: GoogleFonts.notoSans(color: buttonTextColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBackground,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size(double.infinity, 45),
                      ),
                      onPressed: _isProcessing ? null : _generateCaseQuestions,
                    ),

                    const SizedBox(height: 12),

                    // Technical Questions Button
                    ElevatedButton.icon(
                      icon: _isProcessing && _processingType == 'technical'
                          ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                          : Icon(Icons.code, color: buttonTextColor),
                      label: Text(
                        _isProcessing && _processingType == 'technical' ? 'Generating...' : 'Generate Technical Questions',
                        style: GoogleFonts.notoSans(color: buttonTextColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBackground,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size(double.infinity, 45),
                      ),
                      onPressed: _isProcessing ? null : _generateTechnicalQuestions,
                    ),

                    const SizedBox(height: 12),

                    // References Button
                    ElevatedButton.icon(
                      icon: _isProcessing && _processingType == 'references'
                          ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                          : Icon(Icons.people, color: buttonTextColor),
                      label: Text(
                        _isProcessing && _processingType == 'references' ? 'Generating...' : 'Generate Sample References',
                        style: GoogleFonts.notoSans(color: buttonTextColor),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonBackground,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size(double.infinity, 45),
                      ),
                      onPressed: _isProcessing ? null : _generateReferences,
                    ),
                  ],
                ),
              ),
            ),

            if (_isProcessing)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Column(
                  children: [
                    LinearProgressIndicator(value: _processingProgress),
                    const SizedBox(height: 4),
                    Text(
                      'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 24),

            // Results Section
            if (_tailoredResumeText != null || _coverLetterText != null)
              Card(
                color: theme.colorScheme.surface,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Resume & Cover Letter',
                        style: GoogleFonts.notoSans(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: generalTextColor,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Tailored Resume
                      if (_tailoredResumeText != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Tailored Resume',
                              style: GoogleFonts.notoSans(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: generalTextColor,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.picture_as_pdf, color: generalTextColor),
                                  onPressed: () => _exportToPdf(_tailoredResumeText!, 'tailored_resume.pdf'),
                                  tooltip: 'Export as PDF',
                                ),
                                IconButton(
                                  icon: Icon(Icons.description, color: generalTextColor),
                                  onPressed: () => _exportToWord(_tailoredResumeText!, 'tailored_resume.docx'),
                                  tooltip: 'Export as Word',
                                ),
                                IconButton(
                                  icon: Icon(Icons.copy, color: generalTextColor),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: _tailoredResumeText!));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('Resume copied to clipboard')),
                                    );
                                  },
                                  tooltip: 'Copy to Clipboard',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: widget.isDarkMode ? Colors.grey[850] : Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          height: 300,
                          child: SingleChildScrollView(
                            child: MarkdownBody(
                              data: _tailoredResumeText!,
                              styleSheet: MarkdownStyleSheet.fromTheme(theme).copyWith(
                                p: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 14,
                                  height: 1.5,
                                ),
                                h1: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                h2: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                h3: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                ),
                                listBullet: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Cover Letter
                      if (_coverLetterText != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Cover Letter',
                              style: GoogleFonts.notoSans(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: generalTextColor,
                              ),
                            ),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.picture_as_pdf, color: generalTextColor),
                                  onPressed: () => _exportToPdf(_coverLetterText!, 'cover_letter.pdf'),
                                  tooltip: 'Export as PDF',
                                ),
                                IconButton(
                                  icon: Icon(Icons.description, color: generalTextColor),
                                  onPressed: () => _exportToWord(_coverLetterText!, 'cover_letter.docx'),
                                  tooltip: 'Export as Word',
                                ),
                                IconButton(
                                  icon: Icon(Icons.copy, color: generalTextColor),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: _coverLetterText!));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('Cover letter copied to clipboard')),
                                    );
                                  },
                                  tooltip: 'Copy to Clipboard',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: widget.isDarkMode ? Colors.grey[850] : Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          height: 300,
                          child: SingleChildScrollView(
                            child: MarkdownBody(
                              data: _coverLetterText!,
                              styleSheet: MarkdownStyleSheet.fromTheme(theme).copyWith(
                                p: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 14,
                                  height: 1.5,
                                ),
                                h1: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                h2: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                h3: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                ),
                                listBullet: GoogleFonts.notoSans(
                                  color: generalTextColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

            // Interview Questions Results
            if (_commonQuestionsContent != null)
              _buildInterviewQuestionsCard('Common Interview Questions', _commonQuestionsContent!, 'common_questions.pdf', Icons.question_answer, generalTextColor, theme),

            if (_behavioralQuestionsContent != null)
              _buildInterviewQuestionsCard('Behavioral Interview Questions', _behavioralQuestionsContent!, 'behavioral_questions.pdf', Icons.psychology, generalTextColor, theme),

            if (_caseQuestionsContent != null)
              _buildInterviewQuestionsCard('Case Interview Questions', _caseQuestionsContent!, 'case_questions.pdf', Icons.business_center, generalTextColor, theme),

            if (_technicalQuestionsContent != null)
              _buildInterviewQuestionsCard('Technical Interview Questions', _technicalQuestionsContent!, 'technical_questions.pdf', Icons.code, generalTextColor, theme),

            if (_referencesContent != null)
              _buildInterviewQuestionsCard('Sample References', _referencesContent!, 'references.pdf', Icons.people, generalTextColor, theme),
          ],
        ),
      ),
    );
  }
}
