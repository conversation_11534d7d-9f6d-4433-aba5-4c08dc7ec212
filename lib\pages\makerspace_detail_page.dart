// makerspace_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For Clipboard
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'login_page.dart';

class MakerspaceDetailPage extends StatefulWidget {
  final Map<String, dynamic> makerspace;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForBucket;

  const MakerspaceDetailPage({
    Key? key,
    required this.makerspace,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForBucket,
  }) : super(key: key);

  @override
  State<MakerspaceDetailPage> createState() => _MakerspaceDetailPageState();
}

class _MakerspaceDetailPageState extends State<MakerspaceDetailPage> {
  String _imageUrl = 'assets/placeholder_image.png';
  bool _isLoadingImage = false;
  late RealtimeChannel _makerspaceRealtimeChannel; // Realtime channel for makerspace updates

  @override
  void initState() {
    super.initState();
    _loadImageFromPreloadedData();
    _setupMakerspaceRealtimeListener(); // Start listening for realtime updates
  }

  @override
  void dispose() {
    _makerspaceRealtimeChannel.unsubscribe(); // Unsubscribe from realtime channel
    super.dispose();
  }

  void _loadImageFromPreloadedData() {
    setState(() {
      _imageUrl = widget.makerspace['image_url'] ?? 'assets/placeholder_image.png';
      _isLoadingImage = false;
    });
  }



  void _setupMakerspaceRealtimeListener() {
    final makerspacesTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_makerspaces';
    _makerspaceRealtimeChannel = Supabase.instance.client
        .channel('makerspace_detail_channel') // Unique channel name
        .onPostgresChanges(
      event: PostgresChangeEvent.update, // Listen only for UPDATE events
      schema: 'public',
      table: makerspacesTableName,
      // Manual Filtering in Callback
      callback: (payload) async {
        // Manual filtering: Check if the updated ID matches the current makerspace's ID
        if (payload.newRecord['id'] == widget.makerspace['id']) {
          print("Realtime UPDATE event received for THIS makerspace (manual filter applied): ${widget.makerspace['fullname']}");
          _fetchUpdatedMakerspaceData(); // Proceed to update data
        } else {
          print("Realtime UPDATE event received for OTHER makerspace, ignoring.");
        }
      },
    ).subscribe();
  }

  Future<void> _fetchUpdatedMakerspaceData() async {
    final makerspacesTableName = '${widget.collegeNameForBucket.toLowerCase().replaceAll(' ', '')}_makerspaces';
    try {
      final updatedMakerspaceResponse = await Supabase.instance.client
          .from(makerspacesTableName)
          .select('*')
          .eq('id', widget.makerspace['id'])
          .single();

      if (mounted && updatedMakerspaceResponse != null) {
        Map<String, dynamic> updatedMakerspace = Map.from(updatedMakerspaceResponse);
        // Update the widget.makerspace with the new data
        setState(() {
          widget.makerspace.clear(); // Clear old data
          widget.makerspace.addAll(updatedMakerspace); // Add updated data
          _loadImageFromPreloadedData(); // Refresh image URL
          print("Makerspace data updated in detail page for ${widget.makerspace['fullname']}");
          _updateMakerspacesCache(updatedMakerspace); // Update cache
        });
      }
    } catch (error) {
      print("Error fetching updated makerspace data: $error");
    }
  }

  Future<void> _updateMakerspacesCache(Map<String, dynamic> updatedMakerspace) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final collegeNameForTable = widget.collegeNameForBucket;
    final cacheKey = 'makerspaces_${collegeNameForTable.toLowerCase().replaceAll(' ', '')}';
    String? cachedMakerspacesJson = prefs.getString(cacheKey);

    if (cachedMakerspacesJson != null) {
      List<Map<String, dynamic>> cachedMakerspaces = (jsonDecode(cachedMakerspacesJson) as List<dynamic>)
          .cast<Map<String, dynamic>>();

      // Find and update the makerspace in the cached list
      for (int i = 0; i < cachedMakerspaces.length; i++) {
        if (cachedMakerspaces[i]['id'] == updatedMakerspace['id']) {
          cachedMakerspaces[i] = updatedMakerspace;
          break;
        }
      }

      // Save the updated cache
      await prefs.setString(cacheKey, jsonEncode(cachedMakerspaces));
      print("Makerspaces cache updated with realtime change for ${updatedMakerspace['fullname']}");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (phoneNumber.isNotEmpty && await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not place a call.')),
      );
    }
  }

  Future<void> _launchWhatsapp(String whatsappNumber) async {
    final Uri url = Uri.parse('https://wa.me/$whatsappNumber');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch WhatsApp.')),
      );
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri url = Uri.parse('mailto:$email');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch email app.')),
      );
    }
  }

  Future<void> _launchMaps(double latitude, double longitude, String label) async {
    final url = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude&query_place_id=$label');
    if (!await launchUrl(url)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch maps.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final phone = widget.makerspace['phone'] as String? ?? '';
    final whatsappNumber = widget.makerspace['whatsapp'] as String? ?? '';
    final email = widget.makerspace['email'] as String? ?? '';
    final fax = widget.makerspace['fax'] as String? ?? '';
    final about = widget.makerspace['about'] as String? ?? '';
    final building = widget.makerspace['building'] as String? ?? '';
    final room = widget.makerspace['room'] as String? ?? '';
    final hours = widget.makerspace['hours'] as String? ?? '';
    final latitude = widget.makerspace['latitude'] as double?;
    final longitude = widget.makerspace['longitude'] as double?;

    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isWhatsappAvailable = whatsappNumber.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.makerspace['fullname'] ?? '',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _isLoadingImage
                ? const SizedBox(height: 200, child: Center(child: CircularProgressIndicator()))
                : CachedNetworkImage(
              imageUrl: _imageUrl,
              placeholder: (context, url) => Image.asset(
                'assets/placeholder_image.png', // Explicit placeholder as Image.asset
                fit: BoxFit.contain,
                height: 200,
              ),
              errorWidget: (context, url, error) => Image.asset(
                'assets/placeholder_image.png', // Explicit error placeholder as Image.asset
                fit: BoxFit.contain,
                height: 200,
              ),
              fit: BoxFit.cover,
              height: 200,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (about.isNotEmpty)
                        _buildDetailRow(theme, Icons.info_outline, 'About', about),
                      if (building.isNotEmpty || room.isNotEmpty)
                        _buildDetailRow(
                          theme,
                          Icons.location_on_outlined,
                          'Location',
                          [
                            if (building.isNotEmpty) building,
                            if (room.isNotEmpty) 'Room $room',
                          ].join(', ')
                        ),
                      if (hours.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Hours', hours),
                      if (email.isNotEmpty)
                        _buildDetailRow(theme, Icons.email_outlined, 'Email', email),
                      if (phone.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone_outlined, 'Phone', phone),
                      if (fax.isNotEmpty)
                        _buildDetailRow(theme, Icons.print_outlined, 'Fax', fax),
                      if (whatsappNumber.isNotEmpty)
                        _buildDetailRow(theme, FontAwesomeIcons.whatsapp, 'WhatsApp', whatsappNumber),
                    ],
                  ),
                ),
              ),
            ),
            if (latitude != null && longitude != null && latitude != 0 && longitude != 0)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Card(
                  color: theme.colorScheme.surface,
                  surfaceTintColor: Colors.transparent,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'Location',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: TextButton.icon(
                          icon: const Icon(Icons.directions),
                          label: const Text('Get Directions'),
                          onPressed: () {
                            if (latitude != null && longitude != null) {
                              _launchMaps(
                                latitude,
                                longitude,
                                widget.makerspace['fullname'] ?? 'Makerspace',
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Opacity(
                    opacity: isPhoneAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.call,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isPhoneAvailable ? () => _launchDialer(phone) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isEmailAvailable ? 1.0 : 0.5,
                    child: Icon(
                      Icons.email,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isEmailAvailable ? () => _launchEmail(email) : null,
                ),
                IconButton(
                  icon: Opacity(
                    opacity: isWhatsappAvailable ? 1.0 : 0.5,
                    child: FaIcon(
                      FontAwesomeIcons.whatsapp,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  onPressed: isWhatsappAvailable ? () => _launchWhatsapp(whatsappNumber) : null,
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if this is a field that should have copy functionality
    bool canCopy = title == 'Phone' || title == 'Email' || title == 'WhatsApp' || title == 'Fax';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
