// calculate_page.dart
import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:flutter/services.dart';
import 'basic_calculator_page.dart';
import 'scientific_calculator_page.dart';
import 'currency_converter_page.dart';
import 'length_converter_page.dart';
import 'weight_converter_page.dart';
import 'temperature_converter_page.dart';
import 'area_converter_page.dart';
import 'volume_converter_page.dart';

// Placeholder pages (create these files later, just for navigation now)
import 'percentage_calculator_page.dart';
import 'fraction_calculator_page.dart';
import 'ratio_proportion_calculator_page.dart';
import 'geometry_calculator_page.dart';
import 'algebra_solver_page.dart'; // Could be multiple types of solvers
import 'graphing_calculator_page.dart';
import 'statistics_calculator_page.dart';
import 'trigonometry_calculator_page.dart';
import 'speed_converter_page.dart';
import 'data_converter_page.dart';
import 'power_converter_page.dart';
import 'pressure_converter_page.dart';
import 'energy_converter_page.dart';
import 'time_converter_page.dart';
import 'angle_converter_page.dart';
import 'fuel_consumption_converter_page.dart';
import 'density_converter_page.dart';
import 'compound_interest_calculator_page.dart';
import 'simple_interest_calculator_page.dart';
import 'loan_calculator_page.dart';
import 'discount_calculator_page.dart';
import 'vat_calculator_page.dart';
import 'profit_margin_calculator_page.dart';
import 'bmi_calculator_page.dart';
import 'bmr_calculator_page.dart';
import 'date_time_calculator_page.dart';
import 'age_calculator_page.dart';
import 'number_system_converter_page.dart';
// import 'boolean_algebra_calculator_page.dart'; // Potentially more complex
// import 'logic_gate_simulator_page.dart';    // Potentially more complex


class CalculatePage extends StatefulWidget { // Make StatefulWidget
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const CalculatePage({Key? key, required this.isDarkMode, required this.toggleTheme}) : super(key: key);

  @override
  State<CalculatePage> createState() => _CalculatePageState();
}

class _CalculatePageState extends State<CalculatePage> { // State class
  String _filterType = 'All'; // Default filter is 'All'

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Calculators & Converters',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        bottom: PreferredSize( // Add filter buttons below AppBar
          preferredSize: const Size.fromHeight(40.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              FilterButton(
                text: 'All',
                isSelected: _filterType == 'All',
                onPressed: () {
                  setState(() {
                    _filterType = 'All';
                  });
                },
              ),
              FilterButton(
                text: 'Calculators',
                isSelected: _filterType == 'Calculators',
                onPressed: () {
                  setState(() {
                    _filterType = 'Calculators';
                  });
                },
              ),
              FilterButton(
                text: 'Converters',
                isSelected: _filterType == 'Converters',
                onPressed: () {
                  setState(() {
                    _filterType = 'Converters';
                  });
                },
              ),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 4;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 3;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Existing Items
                _buildGridItem(context, 'Basic Calculator', Icons.calculate_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'Scientific Calculator', Icons.science_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'Currency Converter', Icons.monetization_on_outlined, theme, 'Converter'),
                _buildGridItem(context, 'Length Converter', Icons.straighten, theme, 'Converter'),
                _buildGridItem(context, 'Weight Converter', Icons.fitness_center, theme, 'Converter'),
                _buildGridItem(context, 'Temperature Converter', Icons.thermostat_outlined, theme, 'Converter'),
                _buildGridItem(context, 'Area Converter', Icons.texture, theme, 'Converter'),
                _buildGridItem(context, 'Volume Converter', Icons.leaderboard, theme, 'Converter'),

                // New Items - Calculators
                _buildGridItem(context, 'Percentage Calculator', Icons.percent, theme, 'Calculator'),
                _buildGridItem(context, 'Fraction Calculator', Icons.pie_chart_outline, theme, 'Calculator'),
                _buildGridItem(context, 'Ratio & Proportion', Icons.trending_up, theme, 'Calculator'), // Or a more fitting icon
                _buildGridItem(context, 'Geometry Calculator', Icons.hexagon_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'Algebra Solver', Icons.calculate, theme, 'Calculator'),
                _buildGridItem(context, 'Graphing Calculator', Icons.show_chart, theme, 'Calculator'),
                _buildGridItem(context, 'Statistics Calculator', Icons.bar_chart, theme, 'Calculator'),
                _buildGridItem(context, 'Trigonometry Calculator', Icons.change_history, theme, 'Calculator'),
                _buildGridItem(context, 'Compound Interest', Icons.attach_money, theme, 'Calculator'),
                _buildGridItem(context, 'Simple Interest', Icons.money_off, theme, 'Calculator'),
                _buildGridItem(context, 'Loan Calculator', Icons.payments, theme, 'Calculator'),
                _buildGridItem(context, 'Discount Calculator', Icons.local_offer_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'VAT/Tax Calculator', Icons.receipt_long, theme, 'Calculator'),
                _buildGridItem(context, 'Profit Margin Calculator', Icons.analytics_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'BMI Calculator', Icons.monitor_weight_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'BMR & Calorie Calculator', Icons.food_bank_outlined, theme, 'Calculator'),
                _buildGridItem(context, 'Date & Time Calculator', Icons.calendar_today, theme, 'Calculator'),
                _buildGridItem(context, 'Age Calculator', Icons.calendar_today, theme, 'Calculator'),
                _buildGridItem(context, 'Number System Converter', Icons.numbers, theme, 'Converter'), // Could also be 'Calculator' category

                // New Items - Converters
                _buildGridItem(context, 'Speed Converter', Icons.speed, theme, 'Converter'),
                _buildGridItem(context, 'Data Converter', Icons.data_usage, theme, 'Converter'),
                _buildGridItem(context, 'Power Converter', Icons.bolt, theme, 'Converter'),
                _buildGridItem(context, 'Pressure Converter', Icons.compress, theme, 'Converter'),
                _buildGridItem(context, 'Energy Converter', Icons.flash_on, theme, 'Converter'),
                _buildGridItem(context, 'Time Converter', Icons.timer_outlined, theme, 'Converter'),
                _buildGridItem(context, 'Angle Converter', Icons.compass_calibration, theme, 'Converter'),
                _buildGridItem(context, 'Fuel Consumption Converter', Icons.local_gas_station, theme, 'Converter'),
                _buildGridItem(context, 'Density Converter', Icons.layers, theme, 'Converter'),

              ].where((item) { // Filter logic here
                if (_filterType == 'All') {
                  return true; // Show all items
                } else if (_filterType == 'Calculators') {
                  return item.key is ValueKey<String> && (item.key as ValueKey<String>).value.toString().startsWith('Calculator'); // Check category in _buildGridItem
                } else if (_filterType == 'Converters') {
                  return item.key is ValueKey<String> && (item.key as ValueKey<String>).value.toString().startsWith('Converter'); // Check category in _buildGridItem
                }
                return false; // Should not reach here, but for safety
              }).toList(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Theme.of(context).brightness == Brightness.dark ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: widget.isDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                 ),
               ],
             ),
           ),
         ),
       ),
     );
   }

   Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, String category) { // Added category parameter
     final bool isDarkMode = theme.brightness == Brightness.dark;
     return Card(
       key: ValueKey(category + title), // Unique key based on category and title for filtering
       color: theme.colorScheme.surface,
       surfaceTintColor: Colors.transparent,
       child: InkWell(
         onTap: () {
           // Navigation Logic - Expand this for all new calculators/converters
           if (title == 'Basic Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BasicCalculatorPage()),
             );
           } else if (title == 'Scientific Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const ScientificCalculatorPage()),
             );
           } else if (title == 'Currency Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const CurrencyConverterPage()),
             );
           } else if (title == 'Length Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const LengthConverterPage()),
             );
           } else if (title == 'Weight Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const WeightConverterPage()),
             );
           } else if (title == 'Temperature Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TemperatureConverterPage()),
             );
           } else if (title == 'Area Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AreaConverterPage()),
             );
           } else if (title == 'Volume Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const VolumeConverterPage()),
             );
           }
           // New Navigation Items - Add else if blocks for each new title
           else if (title == 'Percentage Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PercentageCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Fraction Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const FractionCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Ratio & Proportion') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const RatioProportionCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Geometry Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const GeometryCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Algebra Solver') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AlgebraSolverPage()), // Placeholder page
             );
           }
           else if (title == 'Graphing Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const GraphingCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Statistics Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const StatisticsCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Trigonometry Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TrigonometryCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Compound Interest') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const CompoundInterestCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Simple Interest') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const SimpleInterestCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Loan Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const LoanCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Discount Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DiscountCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'VAT/Tax Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const VatCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Profit Margin Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const ProfitMarginCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'BMI Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BmiCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'BMR & Calorie Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const BmrCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Date & Time Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DateTimeCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Age Calculator') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AgeCalculatorPage()), // Placeholder page
             );
           }
           else if (title == 'Number System Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const NumberSystemConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Speed Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const SpeedConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Data Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DataConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Power Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PowerConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Pressure Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const PressureConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Energy Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const EnergyConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Time Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const TimeConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Angle Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const AngleConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Fuel Consumption Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const FuelConsumptionConverterPage()), // Placeholder page
             );
           }
           else if (title == 'Density Converter') {
             Navigator.push(
               context,
               MaterialPageRoute(builder: (context) => const DensityConverterPage()), // Placeholder page
             );
           }
         },
         child: Padding(
           padding: const EdgeInsets.all(8.0),
           child: Column(
             mainAxisAlignment: MainAxisAlignment.center,
             mainAxisSize: MainAxisSize.min,
             children: [
               Icon(
                 icon,
                 size: 32,
                 color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
               ),
               const SizedBox(height: 8),
               Flexible(
                 child: Text(
                   title,
                   textAlign: TextAlign.center,
                   overflow: TextOverflow.ellipsis,
                   maxLines: 2,
                   style: TextStyle(
                     fontSize: 14,
                     fontWeight: FontWeight.bold,
                     color: theme.colorScheme.onSurface,
                   ),
                 ),
               ),
             ],
           ),
         ),
       ),
     );
   }
 }

class FilterButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;

  const FilterButton({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          foregroundColor: isSelected ? theme.colorScheme.onPrimary : theme.colorScheme.onSurface.withOpacity(0.8),
          backgroundColor: isSelected ? theme.colorScheme.primary : Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: isSelected ? BorderSide.none : BorderSide(color: theme.colorScheme.onSurface.withOpacity(0.2)),
          ),
        ),
        child: Text(text),
      ),
    );
  }
}