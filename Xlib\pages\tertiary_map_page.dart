import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map_cancellable_tile_provider/flutter_map_cancellable_tile_provider.dart';
import 'login_page.dart';

class TertiaryMapPage extends StatefulWidget {
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const TertiaryMapPage({
    Key? key,
    required this.institutionName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<TertiaryMapPage> createState() => _TertiaryMapPageState();
}

class _TertiaryMapPageState extends State<TertiaryMapPage> {
  final TextEditingController _searchController = TextEditingController();
  final MapController _mapController = MapController();
  String _selectedFilter = 'All';
  bool _isSatelliteView = false;
  
  final List<String> _filters = [
    'All',
    'Academic Buildings',
    'Residences',
    'Dining',
    'Athletics',
    'Parking',
    'Libraries',
    'Study Spaces',
  ];

  // Example location - replace with actual institution coordinates
  final LatLng _center = LatLng(-13.9626, 33.7741); // Malawi center coordinates

  String get _mapUrl {
    if (_isSatelliteView) {
      return widget.isDarkMode
          ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
          : 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
    } else {
      return widget.isDarkMode
          ? 'https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}.png'
          : 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Map',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          // Map Type Toggle
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: Icon(
                _isSatelliteView ? Icons.map_outlined : Icons.satellite_alt_outlined,
                color: theme.colorScheme.onSurface,
              ),
              onPressed: () {
                setState(() {
                  _isSatelliteView = !_isSatelliteView;
                });
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search locations...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                  onChanged: (value) {
                    // Implement search functionality
                  },
                ),
                const SizedBox(height: 16),
                // Filter Chips
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _filters.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: FilterChip(
                          label: Text(
                            _filters[index],
                            style: TextStyle(
                              color: _selectedFilter == _filters[index]
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.onSurface,
                            ),
                          ),
                          selected: _selectedFilter == _filters[index],
                          showCheckmark: false,
                          backgroundColor: theme.colorScheme.surface,
                          selectedColor: widget.isDarkMode 
                              ? theme.colorScheme.surface 
                              : Colors.white,
                          side: BorderSide(
                            color: _selectedFilter == _filters[index]
                                ? theme.colorScheme.primary
                                : Colors.grey.shade300,
                            width: 0.5,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          onSelected: (selected) {
                            setState(() {
                              _selectedFilter = _filters[index];
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Map
          Expanded(
            child: Stack(
              children: [
                FlutterMap(
                  mapController: _mapController,
                  options: MapOptions(
                    initialCenter: _center,
                    initialZoom: 13.0,
                    keepAlive: true,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: _mapUrl,
                      userAgentPackageName: 'com.harmonizr.app',
                      tileProvider: CancellableNetworkTileProvider(),
                      maxZoom: 20,
                      keepBuffer: 5,
                    ),
                    MarkerLayer(
                      markers: [
                        Marker(
                          point: _center,
                          width: 80,
                          height: 80,
                          child: Icon(
                            Icons.location_on,
                            color: theme.colorScheme.primary,
                            size: 40,
                          ),
                        ),
                      ],
                    ),
                    RichAttributionWidget(
                      attributions: [
                        TextSourceAttribution(
                          _isSatelliteView ? 'Esri World Imagery' : 'OpenStreetMap contributors',
                          onTap: () {},
                        ),
                      ],
                      showFlutterMapAttribution: false,
                    ),
                  ],
                ),
                // Zoom Controls
                Positioned(
                  right: 16,
                  bottom: 100,
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Zoom In Button
                            IconButton(
                              icon: Icon(
                                Icons.add,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () {
                                final currentZoom = _mapController.camera.zoom;
                                _mapController.move(
                                  _mapController.camera.center,
                                  currentZoom + 1
                                );
                              },
                            ),
                            Divider(
                              height: 1,
                              thickness: 1,
                              color: theme.colorScheme.surfaceContainerHighest,
                            ),
                            // Zoom Out Button
                            IconButton(
                              icon: Icon(
                                Icons.remove,
                                color: theme.colorScheme.onSurface,
                              ),
                              onPressed: () {
                                final currentZoom = _mapController.camera.zoom;
                                _mapController.move(
                                  _mapController.camera.center,
                                  currentZoom - 1
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: NavigationBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        height: 65,
        selectedIndex: 0,
        destinations: [
          NavigationDestination(
            icon: Icon(
              Icons.home_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.home,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode_outlined : Icons.dark_mode_outlined,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
          NavigationDestination(
            icon: Icon(
              Icons.person_outline,
              color: theme.colorScheme.secondary,
            ),
            selectedIcon: Icon(
              Icons.person,
              color: theme.colorScheme.onSurface,
            ),
            label: '',
          ),
        ],
        onDestinationSelected: (index) {
          if (index == 0) {
            Navigator.of(context).popUntil((route) => route.isFirst);
          } else if (index == 1) {
            widget.toggleTheme();
            setState(() {}); // Refresh to update map tiles
          } else if (index == 2) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginPage(
                  isDarkMode: widget.isDarkMode,
                  toggleTheme: widget.toggleTheme,
                ),
              ),
            );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
