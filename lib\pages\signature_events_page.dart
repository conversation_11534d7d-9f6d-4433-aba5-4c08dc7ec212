import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'login_page.dart';
import 'signature_event_detail_page.dart';

class SignatureEventsPage extends StatefulWidget {
  final String collegeNameForTable;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final List<Map<String, dynamic>>? preloadedSignatureEvents;
  final bool isFromDetailPage;

  const SignatureEventsPage({
    Key? key,
    required this.collegeNameForTable,
    required this.isDarkMode,
    required this.toggleTheme,
    this.preloadedSignatureEvents,
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  State<SignatureEventsPage> createState() => _SignatureEventsPageState();
}

class _SignatureEventsPageState extends State<SignatureEventsPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('signature_events_list');
  List<Map<String, dynamic>> _signatureEvents = [];
  bool _isLoading = false;
  bool _isDisposed = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    print("SignatureEventsPage initState called");
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(covariant SignatureEventsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    print("SignatureEventsPage didUpdateWidget called");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print("SignatureEventsPage didChangeDependencies called");
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    print("SignatureEventsPage dispose called");
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Future<void> _loadInitialData() async {
    print("_loadInitialData called");
    if (widget.preloadedSignatureEvents != null && widget.preloadedSignatureEvents!.isNotEmpty) {
      print("Preloaded signature events found, using them.");
      setState(() {
        _signatureEvents = List<Map<String, dynamic>>.from(widget.preloadedSignatureEvents!);
        _signatureEvents.forEach((event) {
          event['_isImageLoading'] = false;
        });
        _signatureEvents.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedSignatureEvents!.length == _pageSize;
      });
      _loadSignatureEventsFromSupabase(initialLoad: false);
    } else {
      print("No preloaded signature events or empty list, loading from database.");
      _loadSignatureEventsFromSupabase(initialLoad: true);
    }
  }

  Future<void> _loadSignatureEventsFromSupabase({bool initialLoad = true}) async {
    if (_isLoading || (!_hasMore && !initialLoad)) {
      return;
    }
    print(
        "_loadSignatureEventsFromSupabase called - fetching page $_page from database, initialLoad: $initialLoad");
    setState(() {
      _isLoading = true;
    });
    final signatureEventsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_signatureevents';

    try {
      // Build the query
      var query = Supabase.instance.client
          .from(signatureEventsTableName)
          .select('*');

      // Apply ordering and pagination
      final response = await query
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedEvents =
          await _updateEventImageUrls(List<Map<String, dynamic>>.from(response));

      setState(() {
        if (initialLoad) {
          _signatureEvents = updatedEvents;
        } else {
          _signatureEvents.addAll(updatedEvents);
        }
        _signatureEvents.forEach((event) {
          event['_isImageLoading'] = false;
        });
        _signatureEvents.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });

      // Cache the signature events
      _cacheSignatureEvents(_signatureEvents);
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Almost all data for this institution hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching signature events: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateEventImageUrls(
      List<Map<String, dynamic>> events) async {
    List<Future<void>> futures = [];
    for (final event in events) {
      if (event['image_url'] == null ||
          event['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(event));
      }
    }
    await Future.wait(futures);
    return events;
  }

  Future<void> _cacheSignatureEvents(List<Map<String, dynamic>> signatureEvents) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String signatureEventsJson = jsonEncode(signatureEvents);
      await prefs.setString(
          'signatureevents_${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}',
          signatureEventsJson);
    } catch (e) {
      print('Error caching signature events: $e');
    }
  }

  void _setupRealtime() {
    final signatureEventsTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_signatureevents';
    _realtimeChannel = Supabase.instance.client
        .channel('signature_events')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: signatureEventsTableName,
      callback: (payload) async {
        if (payload.eventType == PostgresChangeEvent.insert) {
          final newEventId = payload.newRecord['id'];
          final newEventResponse = await Supabase.instance.client
              .from(signatureEventsTableName)
              .select('*')
              .eq('id', newEventId)
              .single();
          if (mounted) {
            Map<String, dynamic> newEvent = Map.from(newEventResponse);
            final updatedEvent = await _updateEventImageUrls([newEvent]);
            setState(() {
              _signatureEvents = [..._signatureEvents, updatedEvent.first];
              updatedEvent.first['_isImageLoading'] = false;
              _signatureEvents.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.update) {
          final updatedEventId = payload.newRecord['id'];
          final updatedEventResponse = await Supabase.instance.client
              .from(signatureEventsTableName)
              .select('*')
              .eq('id', updatedEventId)
              .single();
          if (mounted) {
            final updatedEvent = Map<String, dynamic>.from(updatedEventResponse);
            setState(() {
              _signatureEvents = _signatureEvents.map((event) {
                return event['id'] == updatedEvent['id'] ? updatedEvent : event;
              }).toList();
              _signatureEvents.sort((a, b) =>
                  (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
            });
          }
        } else if (payload.eventType == PostgresChangeEvent.delete) {
          final deletedEventId = payload.oldRecord['id'];
          setState(() {
            _signatureEvents.removeWhere((event) => event['id'] == deletedEventId);
          });
        }
      },
    ).subscribe();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreEvents();
    }
  }

  Future<void> _loadMoreEvents() async {
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadSignatureEventsFromSupabase(initialLoad: false);
    }
  }

  // Helper method to show the offline snackbar.
  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  // Helper method to show an error snackbar with a custom message.
  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> event) async {
    if (event['_isImageLoading'] == true) {
      print('Image loading already in progress for ${event['fullname']}, skipping.');
      return;
    }
    if (event['image_url'] != null &&
        event['image_url'] != 'assets/placeholder_image.png') {
      print('Image URL already set for ${event['fullname']}, skipping fetch.');
      return;
    }

    setState(() {
      event['_isImageLoading'] = true;
    });

    final fullname = event['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    final collegeEventBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/signatureevents';

    print('Fetching WebP image URL for: $fullname, filename: $imageNameWebp from bucket: $collegeEventBucket');
    print('Image URL before fetch: ${event['image_url']}');

    try {
      final file = await Supabase.instance.client.storage.from(collegeEventBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeEventBucket).getPublicUrl(imageNameWebp);
        print('Successfully fetched WebP URL: $imageUrl');
      } else {
        print('Downloaded WebP file is empty for $imageNameWebp');
      }
    } catch (e) {
      print('Error fetching WebP image for $fullname: $e');
      // No error message is shown to the user.
    }

    if (mounted) {
      setState(() {
        event['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        event['_isImageLoading'] = false;
        print('Setting image_url for ${event['fullname']} to: ${event['image_url']}');
      });
    } else {
      event['_isImageLoading'] = false;
    }
  }

  void _navigateToDetail(BuildContext context, Map<String, dynamic> event) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SignatureEventDetailPage(
            event: event,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print("SignatureEventsPage build method called");
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Signature Events',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: _isLoading && _signatureEvents.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadSignatureEventsFromSupabase(initialLoad: true),
              child: _signatureEvents.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No signature events available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _signatureEvents.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _signatureEvents.length) {
                          final event = _signatureEvents[index];
                          final isAlumniEvent = event['alumnisignatureevent'] ?? false;
                          return VisibilityDetector(
                            key: Key('event_${event['id']}'),
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (event['image_url'] == null ||
                                      event['image_url'] == 'assets/placeholder_image.png') &&
                                  !event['_isImageLoading']) {
                                _fetchImageUrl(event);
                              }
                            },
                            child: Card(
                              color: theme.colorScheme.surface,
                              surfaceTintColor: Colors.transparent,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                leading: ClipOval(
                                  child: SizedBox(
                                    width: 40,
                                    height: 40,
                                    child: CachedNetworkImage(
                                      imageUrl: event['image_url'] ??
                                          'assets/placeholder_image.png',
                                      errorWidget: (context, url, error) =>
                                          Image.asset('assets/placeholder_image.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        event['fullname'] ?? 'Unnamed Event',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                    if (isAlumniEvent)
                                      Padding(
                                        padding: const EdgeInsets.only(left: 8.0),
                                        child: Chip(
                                          label: Text(
                                            'Alumni',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: theme.colorScheme.onPrimary,
                                            ),
                                          ),
                                          backgroundColor: theme.colorScheme.primary,
                                          padding: EdgeInsets.zero,
                                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                        ),
                                      ),
                                  ],
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    event['about'] ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.secondary,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                onTap: () => _navigateToDetail(context, event),
                              ),
                            ),
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
