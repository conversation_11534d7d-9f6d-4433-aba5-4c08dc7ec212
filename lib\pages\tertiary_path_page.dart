import 'package:flutter/material.dart';
import 'login_page.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'orientations_page.dart';
import 'symposiums_page.dart';
import 'graduation_page.dart';
import 'admissions_page.dart';
import 'affordability_page.dart';
import 'alumni_page.dart';

class TertiaryPathPage extends StatelessWidget {
  final Map<String, dynamic> collegeData;
  final String institutionName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isFromDetailPage;

  const TertiaryPathPage({
    Key? key,
    required this.institutionName,
    required this.collegeData,
    required this.isDarkMode,
    required this.toggleTheme,
    this.isFromDetailPage = false,
    }) : super(key: key);

  Widget _buildGridItem(BuildContext context, String title, IconData icon, ThemeData theme, bool isFromDetailPage) {
    final bool isDarkMode = theme.brightness == Brightness.dark;
    bool showGridItem = true; // Modified: Always show the grid item

    return Visibility(
      key: Key('path_grid_item_$title'),
      visible: showGridItem, // Using 'visible' parameter here
      child: Card(
        color: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        child: InkWell(
          onTap: () {
            _navigateToPage(context, title);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isDarkMode ? theme.colorScheme.secondary : Colors.black,
                ),
                if (showGridItem) ...[
                  const SizedBox(height: 8),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: isFromDetailPage ? FontWeight.bold : FontWeight.normal,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String title) {
    switch (title) {
      case 'Admissions':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AdmissionsPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      case 'Affordability':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AffordabilityPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      case 'Alumni':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlumniPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      case 'Orientation':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrientationsPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      case 'Symposium':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SymposiumsPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      case 'Graduation':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => GraduationPage(
              collegeData: collegeData,
              isDarkMode: isDarkMode,
              toggleTheme: toggleTheme,
            ),
          ),
        );
        break;
      // Other cases can be added here as needed
      default:
        // Do nothing for now
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    bool isFromDetailPage = this.isFromDetailPage;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Path',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: LayoutBuilder(
          builder: (context, BoxConstraints constraints) {
            int crossAxisCount = 2;
            double aspectRatio = 1.3;

            if (constraints.maxWidth > 1200) {
              crossAxisCount = 6;
              aspectRatio = 1.4;
            } else if (constraints.maxWidth > 900) {
              crossAxisCount = 3;
              aspectRatio = 1.3;
            } else if (constraints.maxWidth > 600) {
              crossAxisCount = 2;
              aspectRatio = 1.2;
            }

            return GridView.count(
              crossAxisCount: crossAxisCount,
              padding: const EdgeInsets.all(16),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildGridItem(context, 'Admissions', Icons.school, theme, isFromDetailPage),
                _buildGridItem(context, 'Affordability', Icons.attach_money, theme, isFromDetailPage),
                _buildGridItem(context, 'Orientation', Icons.people_outline, theme, isFromDetailPage),
                _buildGridItem(context, 'Symposium', Icons.event_note, theme, isFromDetailPage),
                _buildGridItem(context, 'Graduation', Icons.workspace_premium, theme, isFromDetailPage),
                _buildGridItem(context, 'Alumni', Icons.groups, theme, isFromDetailPage),
              ],
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}