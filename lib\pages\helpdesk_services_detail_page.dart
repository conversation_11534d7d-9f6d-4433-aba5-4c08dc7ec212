import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'login_page.dart';

class HelpdeskServicesDetailPage extends StatelessWidget {
  final String serviceDetailName;
  final String detailPageName;
  final String helpdeskName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final Map<String, dynamic> service;

  const HelpdeskServicesDetailPage({
    Key? key,
    required this.serviceDetailName,
    required this.detailPageName,
    required this.helpdeskName,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.service,
  }) : super(key: key);

  Future<void> _launchServiceLink() async {
    final url = service['link'] as String? ?? '';
    if (url.isNotEmpty) {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.inAppWebView);
      } else {
        print('Could not launch $url');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;
    final hasLink = service['link'] != null && service['link'].toString().isNotEmpty;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          serviceDetailName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service image container - always display
            Container(
              height: 200,
              color: theme.colorScheme.surfaceVariant,
              child: Center(
                child: Icon(
                  Icons.settings_suggest_outlined,
                  size: 60,
                  color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service details card
                  Card(
                    color: theme.colorScheme.surface,
                    surfaceTintColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: currentIsDarkMode
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.black.withOpacity(0.1),
                                child: Icon(
                                  Icons.settings_suggest_outlined,
                                  size: 30,
                                  color: currentIsDarkMode ? Colors.white : Colors.black,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      serviceDetailName,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    if (service['department'] != null && service['department'].toString().isNotEmpty)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          service['department'].toString(),
                                          style: TextStyle(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Price
                          if (service['price'] != null && service['price'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.attach_money, 'Price', service['price'], currentIsDarkMode, canCopy: false),

                          // Requirements
                          if (service['requirements'] != null && service['requirements'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.assignment, 'Requirements', service['requirements'], currentIsDarkMode, canCopy: false),

                          // Time
                          if (service['time'] != null && service['time'].toString().isNotEmpty)
                            _buildDetailRow(context, theme, Icons.access_time, 'Time', service['time'], currentIsDarkMode, canCopy: false),

                          // About section
                          if (service['about'] != null && service['about'].toString().isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Text(
                              'About:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              service['about'].toString(),
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: (service['link'] != null && service['link'].toString().isNotEmpty)
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.link,
                        color: currentIsDarkMode ? Colors.white : theme.colorScheme.onSurface,
                      ),
                      onPressed: _launchServiceLink,
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(BuildContext context, ThemeData theme, IconData icon, String title, dynamic value, bool isDarkMode, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: isDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: isDarkMode ? Colors.white : theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}