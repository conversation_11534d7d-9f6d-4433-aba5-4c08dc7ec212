import 'dart:io' as io;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';

class InterviewPrepPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const InterviewPrepPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<InterviewPrepPage> createState() => _InterviewPrepPageState();
}

class _InterviewPrepPageState extends State<InterviewPrepPage> {
  final TextEditingController _jobTitleController = TextEditingController();
  final TextEditingController _industryController = TextEditingController();
  
  String? _commonQuestionsContent;
  String? _behavioralQuestionsContent;
  String? _caseQuestionsContent;
  String? _technicalQuestionsContent;
  String? _referencesContent;
  
  bool _isProcessing = false;
  String _processingType = '';
  double _processingProgress = 0.0;
  
  // Initialize Gemini
  final _geminiApiKey = 'AIzaSyDfbjSdEbsvKLKqJkwUW06gMkEVPVeC89s'; // Replace with your API key
  late final GenerativeModel _geminiModel;
  
  @override
  void initState() {
    super.initState();
    _geminiModel = GenerativeModel(
      model: 'gemini-2.0-flash',
      apiKey: _geminiApiKey,
    );
  }
  
  @override
  void dispose() {
    _jobTitleController.dispose();
    _industryController.dispose();
    super.dispose();
  }
  
  Future<void> _generateCommonQuestions() async {
    final jobTitle = _jobTitleController.text.trim();
    final industry = _industryController.text.trim();
    
    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a job title')),
      );
      return;
    }
    
    setState(() {
      _isProcessing = true;
      _processingType = 'common';
      _processingProgress = 0.2;
      _commonQuestionsContent = null;
    });
    
    try {
      final prompt = '''
      Create a comprehensive list of 15 common interview questions for a ${jobTitle} ${industry.isNotEmpty ? 'in the $industry industry' : ''}.
      
      For each question:
      1. Provide the question
      2. Explain why interviewers ask this question
      3. Give a strong sample answer (200-300 words)
      4. Include 3-4 key points that should be covered in the answer
      
      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';
      
      setState(() {
        _processingProgress = 0.5;
      });
      
      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;
      
      setState(() {
        _processingProgress = 1.0;
        _commonQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating common questions: $e')),
      );
    }
  }
  
  Future<void> _generateBehavioralQuestions() async {
    final jobTitle = _jobTitleController.text.trim();
    final industry = _industryController.text.trim();
    
    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a job title')),
      );
      return;
    }
    
    setState(() {
      _isProcessing = true;
      _processingType = 'behavioral';
      _processingProgress = 0.2;
      _behavioralQuestionsContent = null;
    });
    
    try {
      final prompt = '''
      Create a comprehensive list of 10 behavioral interview questions for a ${jobTitle} ${industry.isNotEmpty ? 'in the $industry industry' : ''}.
      
      For each question:
      1. Provide the behavioral question
      2. Explain what skill or competency this question is assessing
      3. Give a strong sample answer using the STAR method (Situation, Task, Action, Result)
      4. Include tips for how to structure a compelling response
      
      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';
      
      setState(() {
        _processingProgress = 0.5;
      });
      
      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;
      
      setState(() {
        _processingProgress = 1.0;
        _behavioralQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating behavioral questions: $e')),
      );
    }
  }
  
  Future<void> _generateCaseQuestions() async {
    final jobTitle = _jobTitleController.text.trim();
    final industry = _industryController.text.trim();
    
    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a job title')),
      );
      return;
    }
    
    setState(() {
      _isProcessing = true;
      _processingType = 'case';
      _processingProgress = 0.2;
      _caseQuestionsContent = null;
    });
    
    try {
      final prompt = '''
      Create a comprehensive list of 5 case interview questions for a ${jobTitle} ${industry.isNotEmpty ? 'in the $industry industry' : ''}.
      
      For each case question:
      1. Provide the case scenario and question
      2. Explain the purpose of this case question and what skills it tests
      3. Outline a structured approach to solving the case
      4. Provide a sample solution with key insights and recommendations
      5. Include common pitfalls to avoid
      
      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';
      
      setState(() {
        _processingProgress = 0.5;
      });
      
      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;
      
      setState(() {
        _processingProgress = 1.0;
        _caseQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating case questions: $e')),
      );
    }
  }
  
  Future<void> _generateTechnicalQuestions() async {
    final jobTitle = _jobTitleController.text.trim();
    final industry = _industryController.text.trim();
    
    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a job title')),
      );
      return;
    }
    
    setState(() {
      _isProcessing = true;
      _processingType = 'technical';
      _processingProgress = 0.2;
      _technicalQuestionsContent = null;
    });
    
    try {
      final prompt = '''
      Create a comprehensive list of 10 technical interview questions for a ${jobTitle} ${industry.isNotEmpty ? 'in the $industry industry' : ''}.
      
      For each technical question:
      1. Provide the technical question
      2. Explain what specific knowledge or skill this question is testing
      3. Give a detailed sample answer that demonstrates expertise
      4. Include any relevant code snippets, formulas, or technical concepts if applicable
      
      Format the output with clear headings, numbered questions, and well-structured answers.
      ''';
      
      setState(() {
        _processingProgress = 0.5;
      });
      
      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;
      
      setState(() {
        _processingProgress = 1.0;
        _technicalQuestionsContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating technical questions: $e')),
      );
    }
  }
  
  Future<void> _generateReferences() async {
    final jobTitle = _jobTitleController.text.trim();
    final industry = _industryController.text.trim();
    
    if (jobTitle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a job title')),
      );
      return;
    }
    
    setState(() {
      _isProcessing = true;
      _processingType = 'references';
      _processingProgress = 0.2;
      _referencesContent = null;
    });
    
    try {
      final prompt = '''
      Create 3 sample professional references for a ${jobTitle} ${industry.isNotEmpty ? 'in the $industry industry' : ''}.
      
      For each reference:
      1. Create a fictional reference with name, title, company, and contact information
      2. Describe the professional relationship (e.g., former manager, colleague, client)
      3. List 5 specific questions an employer might ask this reference
      4. Provide detailed sample answers that the reference might give, highlighting the candidate's strengths and accomplishments
      
      Format the output with clear headings and well-structured content.
      ''';
      
      setState(() {
        _processingProgress = 0.5;
      });
      
      final response = await _geminiModel.generateContent([Content.text(prompt)]);
      final content = response.text;
      
      setState(() {
        _processingProgress = 1.0;
        _referencesContent = content;
        _isProcessing = false;
        _processingType = '';
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _processingType = '';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating references: $e')),
      );
    }
  }
  
  Future<void> _exportToPdf(String content, String fileName) async {
    try {
      final pdf = pw.Document();
      
      // Add content to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              pw.Text(
                content,
                style: pw.TextStyle(
                  fontSize: 12,
                ),
              ),
            ];
          },
          footer: (pw.Context context) {
            return pw.Container(
              alignment: pw.Alignment.centerRight,
              margin: const pw.EdgeInsets.only(top: 1.0 * PdfPageFormat.cm),
              child: pw.Text(
                'Page ${context.pageNumber} of ${context.pagesCount}',
                style: pw.TextStyle(
                  color: PdfColors.grey,
                  fontSize: 10,
                ),
              ),
            );
          },
        ),
      );
      
      // Save PDF
      final bytes = await pdf.save();
      
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final filePath = path.join(tempDir.path, fileName);
      
      // Write PDF to file
      final file = io.File(filePath);
      await file.writeAsBytes(bytes);
      
      // Open the PDF file
      await _openFile(filePath);
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error exporting to PDF: $e')),
      );
    }
  }
  
  Future<void> _openFile(String filePath) async {
    try {
      final uri = Uri.file(filePath);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw 'Could not launch $uri';
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error opening file: $e')),
      );
    }
  }
  
  Widget _buildContentCard(String title, String? content, String fileName, IconData icon) {
    final theme = Theme.of(context);
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;
    
    return Card(
      color: theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(icon, color: generalTextColor),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor,
                      ),
                    ),
                  ],
                ),
                if (content != null)
                  IconButton(
                    icon: Icon(Icons.picture_as_pdf, color: generalTextColor),
                    onPressed: () => _exportToPdf(content, fileName),
                    tooltip: 'Export as PDF',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (content == null)
              ElevatedButton.icon(
                icon: Icon(Icons.auto_awesome, color: buttonTextColor),
                label: Text(
                  _isProcessing && _processingType == fileName.split('.')[0]
                      ? 'Generating...'
                      : 'Generate $title',
                  style: GoogleFonts.notoSans(color: buttonTextColor),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonBackground,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  minimumSize: const Size(double.infinity, 48),
                ),
                onPressed: _isProcessing
                    ? null
                    : () {
                        switch (fileName.split('.')[0]) {
                          case 'common':
                            _generateCommonQuestions();
                            break;
                          case 'behavioral':
                            _generateBehavioralQuestions();
                            break;
                          case 'case':
                            _generateCaseQuestions();
                            break;
                          case 'technical':
                            _generateTechnicalQuestions();
                            break;
                          case 'references':
                            _generateReferences();
                            break;
                        }
                      },
              )
            else
              Container(
                height: 200,
                decoration: BoxDecoration(
                  color: widget.isDarkMode ? Colors.grey[850] : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: Text(
                    content,
                    style: GoogleFonts.notoSans(
                      color: generalTextColor,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            if (_isProcessing && _processingType == fileName.split('.')[0])
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Column(
                  children: [
                    LinearProgressIndicator(value: _processingProgress),
                    const SizedBox(height: 4),
                    Text(
                      'Processing: ${(_processingProgress * 100).toStringAsFixed(0)}%',
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Interview Preparation',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
            tooltip: 'Toggle Theme',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Job Information Section
            Card(
              color: theme.colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Job Information',
                      style: GoogleFonts.notoSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: generalTextColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _jobTitleController,
                      decoration: InputDecoration(
                        labelText: 'Job Title',
                        hintText: 'e.g., Software Engineer, Marketing Manager',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.all(12),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _industryController,
                      decoration: InputDecoration(
                        labelText: 'Industry (Optional)',
                        hintText: 'e.g., Healthcare, Finance, Technology',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.all(12),
                      ),
                      style: GoogleFonts.notoSans(color: generalTextColor),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Interview Materials Section
            Text(
              'Interview Preparation Materials',
              style: GoogleFonts.notoSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: generalTextColor,
              ),
            ),
            const SizedBox(height: 16),
            
            // Common Questions
            _buildContentCard(
              'Common Interview Questions',
              _commonQuestionsContent,
              'common.pdf',
              Icons.question_answer,
            ),
            
            const SizedBox(height: 16),
            
            // Behavioral Questions
            _buildContentCard(
              'Behavioral Questions',
              _behavioralQuestionsContent,
              'behavioral.pdf',
              Icons.psychology,
            ),
            
            const SizedBox(height: 16),
            
            // Case Questions
            _buildContentCard(
              'Case Questions',
              _caseQuestionsContent,
              'case.pdf',
              Icons.business_center,
            ),
            
            const SizedBox(height: 16),
            
            // Technical Questions
            _buildContentCard(
              'Technical Questions',
              _technicalQuestionsContent,
              'technical.pdf',
              Icons.code,
            ),
            
            const SizedBox(height: 16),
            
            // References
            _buildContentCard(
              'Sample References',
              _referencesContent,
              'references.pdf',
              Icons.people,
            ),
          ],
        ),
      ),
    );
  }
}
