import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class PersonDetailPage extends StatefulWidget {
  final Map<String, dynamic> person;
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final bool isStudent;

  const PersonDetailPage({
    Key? key,
    required this.person,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.isStudent,
  }) : super(key: key);

  @override
  State<PersonDetailPage> createState() => _PersonDetailPageState();
}

class _PersonDetailPageState extends State<PersonDetailPage> {
  late RealtimeChannel _personRealtimeChannel;

  @override
  void initState() {
    super.initState();
    _setupRealtimeListener();
  }

  @override
  void dispose() {
    _personRealtimeChannel.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeListener() {
    final tableName = widget.isStudent ? 'currentstudents' : 'people';
    _personRealtimeChannel = Supabase.instance.client
        .channel('person_detail_channel')
        .onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: tableName,
      callback: (payload) async {
        // Manual filtering for the specific person
        if (payload.newRecord != null &&
            payload.newRecord!['id'] == widget.person['id']) {
          print("Realtime update received for person detail: ${payload.eventType}");
          if (payload.eventType == PostgresChangeEvent.update) {
            _refreshPerson();
          }
        }
      },
    ).subscribe();
  }

  Future<void> _refreshPerson() async {
    try {
      final tableName = widget.isStudent ? 'currentstudents' : 'people';
      final response = await Supabase.instance.client
          .from(tableName)
          .select('*')
          .eq('id', widget.person['id'])
          .single();

      if (mounted) {
        setState(() {
          // Update the widget's person with the new data
          widget.person.clear();
          widget.person.addAll(response);
        });
      }
    } catch (e) {
      print("Error refreshing person: $e");
    }
  }

  Future<void> _launchDialer(String phoneNumber) async {
    final Uri telUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(telUri)) {
      await launchUrl(telUri);
    } else {
      print('Could not launch $telUri');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      print('Could not launch $emailUri');
    }
  }

  Future<void> _launchNavigation(dynamic latitude, dynamic longitude) async {
    double? lat;
    double? lng;
    
    // Handle different types of latitude and longitude
    if (latitude is double) {
      lat = latitude;
    } else if (latitude is String) {
      lat = double.tryParse(latitude);
    }
    
    if (longitude is double) {
      lng = longitude;
    } else if (longitude is String) {
      lng = double.tryParse(longitude);
    }
    
    if (lat == null || lng == null) {
      print('Invalid latitude or longitude');
      return;
    }

    final Uri mapUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
    );
    if (await canLaunchUrl(mapUri)) {
      await launchUrl(mapUri, mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $mapUri');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    // Extract person information
    final String fullname = widget.person['fullname'] ?? 'Unknown';
    
    // Student-specific information
    final bool isStudent = widget.isStudent;
    final int? year = isStudent ? widget.person['year'] : null;
    
    // Faculty/Staff-specific information
    final String title = !isStudent ? widget.person['title'] ?? '' : '';
    final String department = widget.person['department'] ?? '';
    final String building = widget.person['building'] ?? '';
    final String room = widget.person['room'] ?? '';
    final String officeHours = !isStudent ? widget.person['officehours'] ?? '' : '';
    final String about = !isStudent ? widget.person['about'] ?? '' : '';
    final String services = !isStudent ? widget.person['services'] ?? '' : '';
    
    // Schools (for faculty/staff)
    final String school = !isStudent ? widget.person['school'] ?? '' : '';
    final String school2 = !isStudent ? widget.person['school2'] ?? '' : '';
    final String school3 = !isStudent ? widget.person['school3'] ?? '' : '';
    
    // Contact information (for faculty/staff)
    final String phone = !isStudent ? widget.person['phone']?.toString() ?? '' : '';
    final String ext = !isStudent ? widget.person['ext']?.toString() ?? '' : '';
    final String email = !isStudent ? widget.person['email']?.toString() ?? '' : '';
    final String fax = !isStudent ? widget.person['fax']?.toString() ?? '' : '';
    final String hours = !isStudent ? widget.person['hours']?.toString() ?? '' : '';
    
    // Roles (for faculty/staff)
    final bool isFaculty = !isStudent && (widget.person['facultymember'] == true);
    final bool isStaff = !isStudent && (widget.person['staffmember'] == true);
    final bool isOrientationContact = !isStudent && (widget.person['orientationcontact'] == true);
    final bool isGraduationContact = !isStudent && (widget.person['graduationcontact'] == true);
    
    // Location information
    final dynamic latitude = !isStudent ? widget.person['latitude'] : null;
    final dynamic longitude = !isStudent ? widget.person['longitude'] : null;
    
    // Format location text
    String locationText = '';
    if (building.isNotEmpty && room.isNotEmpty) {
      locationText = '$building, Room $room';
    } else if (building.isNotEmpty) {
      locationText = building;
    } else if (room.isNotEmpty) {
      locationText = 'Room $room';
    }
    
    // Format phone with extension
    String phoneWithExt = phone;
    if (phone.isNotEmpty && ext.isNotEmpty) {
      phoneWithExt = '$phone ext. $ext';
    }
    
    // Determine role text
    String roleText = '';
    if (isFaculty && isStaff) {
      roleText = 'Faculty & Staff';
    } else if (isFaculty) {
      roleText = 'Faculty';
    } else if (isStaff) {
      roleText = 'Staff';
    } else if (isStudent) {
      roleText = 'Student';
    }
    
    // Check if contact methods are available
    final bool isPhoneAvailable = phone.isNotEmpty;
    final bool isEmailAvailable = email.isNotEmpty;
    final bool isNavigationAvailable = latitude != null && longitude != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          fullname,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Person details card
              Card(
                color: theme.colorScheme.surface,
                surfaceTintColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: currentIsDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                            child: Icon(
                              isStudent ? Icons.school : Icons.person,
                              size: 30,
                              color: currentIsDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  fullname,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                if (title.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      title,
                                      style: TextStyle(
                                        color: theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                                if (roleText.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      roleText,
                                      style: TextStyle(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Department and School information
                      if (department.isNotEmpty)
                        _buildDetailRow(theme, Icons.business, 'Department', department),
                      if (school.isNotEmpty)
                        _buildDetailRow(theme, Icons.school, 'School', school),
                      if (school2.isNotEmpty)
                        _buildDetailRow(theme, Icons.school, 'School', school2),
                      if (school3.isNotEmpty)
                        _buildDetailRow(theme, Icons.school, 'School', school3),
                      
                      // Student-specific information
                      if (isStudent && year != null)
                        _buildDetailRow(theme, Icons.calendar_today, 'Year', year.toString()),
                      
                      // Location information
                      if (locationText.isNotEmpty)
                        _buildDetailRow(theme, Icons.location_on, 'Location', locationText),
                      
                      // Office hours
                      if (officeHours.isNotEmpty)
                        _buildDetailRow(theme, Icons.access_time, 'Office Hours', officeHours),
                      
                      // Hours
                      if (hours.isNotEmpty)
                        _buildDetailRow(theme, Icons.schedule, 'Hours', hours),
                      
                      // Contact information
                      if (phoneWithExt.isNotEmpty)
                        _buildDetailRow(theme, Icons.phone, 'Phone', phoneWithExt, canCopy: true),
                      if (email.isNotEmpty)
                        _buildDetailRow(theme, Icons.email, 'Email', email, canCopy: true),
                      if (fax.isNotEmpty)
                        _buildDetailRow(theme, Icons.fax, 'Fax', fax, canCopy: true),
                      
                      // Special roles
                      if (isOrientationContact)
                        _buildDetailRow(theme, Icons.event, 'Orientation Contact', 'Yes'),
                      if (isGraduationContact)
                        _buildDetailRow(theme, Icons.school, 'Graduation Contact', 'Yes'),
                      
                      // About section
                      if (about.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'About:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      
                      // Services section
                      if (services.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Services:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          services,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: (!isStudent && (isPhoneAvailable || isEmailAvailable || isNavigationAvailable))
          ? Container(
              color: theme.colorScheme.surface,
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (isPhoneAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.call,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchDialer(phone),
                      ),
                    if (isEmailAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.email,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchEmail(email),
                      ),
                    if (isNavigationAvailable)
                      IconButton(
                        icon: Icon(
                          Icons.navigation,
                          color: theme.colorScheme.onSurface,
                        ),
                        onPressed: () => _launchNavigation(latitude, longitude),
                      ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildDetailRow(ThemeData theme, IconData icon, String title, dynamic value, {bool canCopy = false}) {
    if (value == null || value.toString().isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value.toString(),
                        style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                      ),
                    ),
                    if (canCopy)
                      IconButton(
                        icon: Icon(
                          Icons.content_copy,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: value.toString()));
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$title copied to clipboard'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
