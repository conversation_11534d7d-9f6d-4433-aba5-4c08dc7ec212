// helpdesk_links_detail_page.dart
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpdeskLinksDetailPage extends StatelessWidget {
  final String linkName;
  final String linkUrl;
  final String detailPageName;
  final String helpdeskName;
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const HelpdeskLinksDetailPage({
    Key? key,
    required this.linkName,
    required this.linkUrl,
    required this.detailPageName,
    required this.helpdeskName,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  _launchLink(BuildContext context, String url) async {
    final Uri uri = Uri.parse(url);
    if (await launchUrl(uri)) {
      await launchUrl(uri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch link.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // Get current theme
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface, // App bar background color from theme
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface, // Icon color from theme
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          linkName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface, // Title text color from theme
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          color: theme.colorScheme.surface, // Card background color from theme
          surfaceTintColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Icon(Icons.link, size: 60, color: theme.colorScheme.onSurfaceVariant), // Link icon
                SizedBox(height: 24),
                Text(
                  linkName,
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface), // Title text style from theme
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () => _launchLink(context, linkUrl),
                  child: Text('Go to $linkName'),
                ),
                SizedBox(height: 20),
                Text(
                  'Link URL: $linkUrl',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurfaceVariant), // Detail text style from theme
                ),
                SizedBox(height: 10),
                Text(
                  'Detail Page Identifier: $detailPageName',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurfaceVariant), // Detail text style from theme
                ),
                SizedBox(height: 10),
                Text(
                  'Helpdesk: $helpdeskName',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurfaceVariant), // Detail text style from theme
                ),
                SizedBox(height: 30),
                Text(
                  'You can add a description or more context about this link here.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: theme.colorScheme.onSurfaceVariant), // Hint text style from theme
                ),
                // You can add more details or actions related to the link here - customize this section
              ],
            ),
          ),
        ),
      ),
    );
  }
}